{"version": 3, "sources": ["../../.pnpm/@ctrl+tinycolor@3.6.1/node_modules/@ctrl/tinycolor/dist/module/util.js", "../../.pnpm/@ctrl+tinycolor@3.6.1/node_modules/@ctrl/tinycolor/dist/module/conversion.js", "../../.pnpm/@ctrl+tinycolor@3.6.1/node_modules/@ctrl/tinycolor/dist/module/css-color-names.js", "../../.pnpm/@ctrl+tinycolor@3.6.1/node_modules/@ctrl/tinycolor/dist/module/format-input.js", "../../.pnpm/@ctrl+tinycolor@3.6.1/node_modules/@ctrl/tinycolor/dist/module/index.js", "../../.pnpm/@ant-design+colors@6.0.0/node_modules/@ant-design/colors/dist/index.esm.js", "../../.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.13_typescript@4.9.5_/node_modules/@ant-design/icons-vue/es/components/Context.js", "../../.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.13_typescript@4.9.5_/node_modules/@ant-design/icons-vue/es/dynamicCSS.js", "../../.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.13_typescript@4.9.5_/node_modules/@ant-design/icons-vue/es/utils.js", "../../.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.13_typescript@4.9.5_/node_modules/@ant-design/icons-vue/es/components/IconBase.js", "../../.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.13_typescript@4.9.5_/node_modules/@ant-design/icons-vue/es/components/twoTonePrimaryColor.js", "../../.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.13_typescript@4.9.5_/node_modules/@ant-design/icons-vue/es/components/InsertStyle.js", "../../.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.13_typescript@4.9.5_/node_modules/@ant-design/icons-vue/es/components/AntdIcon.js"], "sourcesContent": ["/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nexport function bound01(n, max) {\n    if (isOnePointZero(n)) {\n        n = '100%';\n    }\n    var isPercent = isPercentage(n);\n    n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n    // Automatically convert percentage into number\n    if (isPercent) {\n        n = parseInt(String(n * max), 10) / 100;\n    }\n    // Handle floating point rounding errors\n    if (Math.abs(n - max) < 0.000001) {\n        return 1;\n    }\n    // Convert into [0, 1] range if it isn't already\n    if (max === 360) {\n        // If n is a hue given in degrees,\n        // wrap around out-of-range values into [0, 360] range\n        // then convert into [0, 1].\n        n = (n < 0 ? (n % max) + max : n % max) / parseFloat(String(max));\n    }\n    else {\n        // If n not a hue given in degrees\n        // Convert into [0, 1] range if it isn't already.\n        n = (n % max) / parseFloat(String(max));\n    }\n    return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nexport function clamp01(val) {\n    return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nexport function isOnePointZero(n) {\n    return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nexport function isPercentage(n) {\n    return typeof n === 'string' && n.indexOf('%') !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nexport function boundAlpha(a) {\n    a = parseFloat(a);\n    if (isNaN(a) || a < 0 || a > 1) {\n        a = 1;\n    }\n    return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nexport function convertToPercentage(n) {\n    if (n <= 1) {\n        return \"\".concat(Number(n) * 100, \"%\");\n    }\n    return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nexport function pad2(c) {\n    return c.length === 1 ? '0' + c : String(c);\n}\n", "import { bound01, pad2 } from './util.js';\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n/**\n * Handle bounds / percentage checking to conform to CSS color spec\n * <http://www.w3.org/TR/css3-color/>\n * *Assumes:* r, g, b in [0, 255] or [0, 1]\n * *Returns:* { r, g, b } in [0, 255]\n */\nexport function rgbToRgb(r, g, b) {\n    return {\n        r: bound01(r, 255) * 255,\n        g: bound01(g, 255) * 255,\n        b: bound01(b, 255) * 255,\n    };\n}\n/**\n * Converts an RGB color value to HSL.\n * *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n * *Returns:* { h, s, l } in [0,1]\n */\nexport function rgbToHsl(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var s = 0;\n    var l = (max + min) / 2;\n    if (max === min) {\n        s = 0;\n        h = 0; // achromatic\n    }\n    else {\n        var d = max - min;\n        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h, s: s, l: l };\n}\nfunction hue2rgb(p, q, t) {\n    if (t < 0) {\n        t += 1;\n    }\n    if (t > 1) {\n        t -= 1;\n    }\n    if (t < 1 / 6) {\n        return p + (q - p) * (6 * t);\n    }\n    if (t < 1 / 2) {\n        return q;\n    }\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n    return p;\n}\n/**\n * Converts an HSL color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hslToRgb(h, s, l) {\n    var r;\n    var g;\n    var b;\n    h = bound01(h, 360);\n    s = bound01(s, 100);\n    l = bound01(l, 100);\n    if (s === 0) {\n        // achromatic\n        g = l;\n        b = l;\n        r = l;\n    }\n    else {\n        var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n        var p = 2 * l - q;\n        r = hue2rgb(p, q, h + 1 / 3);\n        g = hue2rgb(p, q, h);\n        b = hue2rgb(p, q, h - 1 / 3);\n    }\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color value to HSV\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n * *Returns:* { h, s, v } in [0,1]\n */\nexport function rgbToHsv(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var v = max;\n    var d = max - min;\n    var s = max === 0 ? 0 : d / max;\n    if (max === min) {\n        h = 0; // achromatic\n    }\n    else {\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h, s: s, v: v };\n}\n/**\n * Converts an HSV color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hsvToRgb(h, s, v) {\n    h = bound01(h, 360) * 6;\n    s = bound01(s, 100);\n    v = bound01(v, 100);\n    var i = Math.floor(h);\n    var f = h - i;\n    var p = v * (1 - s);\n    var q = v * (1 - f * s);\n    var t = v * (1 - (1 - f) * s);\n    var mod = i % 6;\n    var r = [v, q, p, p, t, v][mod];\n    var g = [t, v, v, q, p, p][mod];\n    var b = [p, p, t, v, v, q][mod];\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color to hex\n *\n * Assumes r, g, and b are contained in the set [0, 255]\n * Returns a 3 or 6 character hex\n */\nexport function rgbToHex(r, g, b, allow3Char) {\n    var hex = [\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n    ];\n    // Return a 3 character hex if possible\n    if (allow3Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color plus alpha transparency to hex\n *\n * Assumes r, g, b are contained in the set [0, 255] and\n * a in [0, 1]. Returns a 4 or 8 character rgba hex\n */\n// eslint-disable-next-line max-params\nexport function rgbaToHex(r, g, b, a, allow4Char) {\n    var hex = [\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n        pad2(convertDecimalToHex(a)),\n    ];\n    // Return a 4 character hex if possible\n    if (allow4Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1)) &&\n        hex[3].startsWith(hex[3].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color to an ARGB Hex8 string\n * Rarely used, but required for \"toFilter()\"\n */\nexport function rgbaToArgbHex(r, g, b, a) {\n    var hex = [\n        pad2(convertDecimalToHex(a)),\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n    ];\n    return hex.join('');\n}\n/** Converts a decimal to a hex value */\nexport function convertDecimalToHex(d) {\n    return Math.round(parseFloat(d) * 255).toString(16);\n}\n/** Converts a hex value to a decimal */\nexport function convertHexToDecimal(h) {\n    return parseIntFromHex(h) / 255;\n}\n/** Parse a base-16 hex value into a base-10 integer */\nexport function parseIntFromHex(val) {\n    return parseInt(val, 16);\n}\nexport function numberInputToObject(color) {\n    return {\n        r: color >> 16,\n        g: (color & 0xff00) >> 8,\n        b: color & 0xff,\n    };\n}\n", "// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */\nexport var names = {\n    aliceblue: '#f0f8ff',\n    antiquewhite: '#faebd7',\n    aqua: '#00ffff',\n    aquamarine: '#7fffd4',\n    azure: '#f0ffff',\n    beige: '#f5f5dc',\n    bisque: '#ffe4c4',\n    black: '#000000',\n    blanchedalmond: '#ffebcd',\n    blue: '#0000ff',\n    blueviolet: '#8a2be2',\n    brown: '#a52a2a',\n    burlywood: '#deb887',\n    cadetblue: '#5f9ea0',\n    chartreuse: '#7fff00',\n    chocolate: '#d2691e',\n    coral: '#ff7f50',\n    cornflowerblue: '#6495ed',\n    cornsilk: '#fff8dc',\n    crimson: '#dc143c',\n    cyan: '#00ffff',\n    darkblue: '#00008b',\n    darkcyan: '#008b8b',\n    darkgoldenrod: '#b8860b',\n    darkgray: '#a9a9a9',\n    darkgreen: '#006400',\n    darkgrey: '#a9a9a9',\n    darkkhaki: '#bdb76b',\n    darkmagenta: '#8b008b',\n    darkolivegreen: '#556b2f',\n    darkorange: '#ff8c00',\n    darkorchid: '#9932cc',\n    darkred: '#8b0000',\n    darksalmon: '#e9967a',\n    darkseagreen: '#8fbc8f',\n    darkslateblue: '#483d8b',\n    darkslategray: '#2f4f4f',\n    darkslategrey: '#2f4f4f',\n    darkturquoise: '#00ced1',\n    darkviolet: '#9400d3',\n    deeppink: '#ff1493',\n    deepskyblue: '#00bfff',\n    dimgray: '#696969',\n    dimgrey: '#696969',\n    dodgerblue: '#1e90ff',\n    firebrick: '#b22222',\n    floralwhite: '#fffaf0',\n    forestgreen: '#228b22',\n    fuchsia: '#ff00ff',\n    gainsboro: '#dcdcdc',\n    ghostwhite: '#f8f8ff',\n    goldenrod: '#daa520',\n    gold: '#ffd700',\n    gray: '#808080',\n    green: '#008000',\n    greenyellow: '#adff2f',\n    grey: '#808080',\n    honeydew: '#f0fff0',\n    hotpink: '#ff69b4',\n    indianred: '#cd5c5c',\n    indigo: '#4b0082',\n    ivory: '#fffff0',\n    khaki: '#f0e68c',\n    lavenderblush: '#fff0f5',\n    lavender: '#e6e6fa',\n    lawngreen: '#7cfc00',\n    lemonchiffon: '#fffacd',\n    lightblue: '#add8e6',\n    lightcoral: '#f08080',\n    lightcyan: '#e0ffff',\n    lightgoldenrodyellow: '#fafad2',\n    lightgray: '#d3d3d3',\n    lightgreen: '#90ee90',\n    lightgrey: '#d3d3d3',\n    lightpink: '#ffb6c1',\n    lightsalmon: '#ffa07a',\n    lightseagreen: '#20b2aa',\n    lightskyblue: '#87cefa',\n    lightslategray: '#778899',\n    lightslategrey: '#778899',\n    lightsteelblue: '#b0c4de',\n    lightyellow: '#ffffe0',\n    lime: '#00ff00',\n    limegreen: '#32cd32',\n    linen: '#faf0e6',\n    magenta: '#ff00ff',\n    maroon: '#800000',\n    mediumaquamarine: '#66cdaa',\n    mediumblue: '#0000cd',\n    mediumorchid: '#ba55d3',\n    mediumpurple: '#9370db',\n    mediumseagreen: '#3cb371',\n    mediumslateblue: '#7b68ee',\n    mediumspringgreen: '#00fa9a',\n    mediumturquoise: '#48d1cc',\n    mediumvioletred: '#c71585',\n    midnightblue: '#191970',\n    mintcream: '#f5fffa',\n    mistyrose: '#ffe4e1',\n    moccasin: '#ffe4b5',\n    navajowhite: '#ffdead',\n    navy: '#000080',\n    oldlace: '#fdf5e6',\n    olive: '#808000',\n    olivedrab: '#6b8e23',\n    orange: '#ffa500',\n    orangered: '#ff4500',\n    orchid: '#da70d6',\n    palegoldenrod: '#eee8aa',\n    palegreen: '#98fb98',\n    paleturquoise: '#afeeee',\n    palevioletred: '#db7093',\n    papayawhip: '#ffefd5',\n    peachpuff: '#ffdab9',\n    peru: '#cd853f',\n    pink: '#ffc0cb',\n    plum: '#dda0dd',\n    powderblue: '#b0e0e6',\n    purple: '#800080',\n    rebeccapurple: '#663399',\n    red: '#ff0000',\n    rosybrown: '#bc8f8f',\n    royalblue: '#4169e1',\n    saddlebrown: '#8b4513',\n    salmon: '#fa8072',\n    sandybrown: '#f4a460',\n    seagreen: '#2e8b57',\n    seashell: '#fff5ee',\n    sienna: '#a0522d',\n    silver: '#c0c0c0',\n    skyblue: '#87ceeb',\n    slateblue: '#6a5acd',\n    slategray: '#708090',\n    slategrey: '#708090',\n    snow: '#fffafa',\n    springgreen: '#00ff7f',\n    steelblue: '#4682b4',\n    tan: '#d2b48c',\n    teal: '#008080',\n    thistle: '#d8bfd8',\n    tomato: '#ff6347',\n    turquoise: '#40e0d0',\n    violet: '#ee82ee',\n    wheat: '#f5deb3',\n    white: '#ffffff',\n    whitesmoke: '#f5f5f5',\n    yellow: '#ffff00',\n    yellowgreen: '#9acd32',\n};\n", "/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\nimport { convertHexToDecimal, hslToRgb, hsvToRgb, parseIntFromHex, rgbToRgb, } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { boundAlpha, convertToPercentage } from './util.js';\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * ```\n */\nexport function inputToRGB(color) {\n    var rgb = { r: 0, g: 0, b: 0 };\n    var a = 1;\n    var s = null;\n    var v = null;\n    var l = null;\n    var ok = false;\n    var format = false;\n    if (typeof color === 'string') {\n        color = stringInputToObject(color);\n    }\n    if (typeof color === 'object') {\n        if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n            rgb = rgbToRgb(color.r, color.g, color.b);\n            ok = true;\n            format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n            s = convertToPercentage(color.s);\n            v = convertToPercentage(color.v);\n            rgb = hsvToRgb(color.h, s, v);\n            ok = true;\n            format = 'hsv';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n            s = convertToPercentage(color.s);\n            l = convertToPercentage(color.l);\n            rgb = hslToRgb(color.h, s, l);\n            ok = true;\n            format = 'hsl';\n        }\n        if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n            a = color.a;\n        }\n    }\n    a = boundAlpha(a);\n    return {\n        ok: ok,\n        format: color.format || format,\n        r: Math.min(255, Math.max(rgb.r, 0)),\n        g: Math.min(255, Math.max(rgb.g, 0)),\n        b: Math.min(255, Math.max(rgb.b, 0)),\n        a: a,\n    };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nvar CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nvar CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nvar CSS_UNIT = \"(?:\".concat(CSS_NUMBER, \")|(?:\").concat(CSS_INTEGER, \")\");\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\nvar PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar matchers = {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n    rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n    hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n    hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n    hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n    hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n */\nexport function stringInputToObject(color) {\n    color = color.trim().toLowerCase();\n    if (color.length === 0) {\n        return false;\n    }\n    var named = false;\n    if (names[color]) {\n        color = names[color];\n        named = true;\n    }\n    else if (color === 'transparent') {\n        return { r: 0, g: 0, b: 0, a: 0, format: 'name' };\n    }\n    // Try to match string input using regular expressions.\n    // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n    // Just return an object and let the conversion functions handle that.\n    // This way the result will be the same whether the tinycolor is initialized with string or object.\n    var match = matchers.rgb.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3] };\n    }\n    match = matchers.rgba.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3], a: match[4] };\n    }\n    match = matchers.hsl.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3] };\n    }\n    match = matchers.hsla.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3], a: match[4] };\n    }\n    match = matchers.hsv.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3] };\n    }\n    match = matchers.hsva.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3], a: match[4] };\n    }\n    match = matchers.hex8.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            a: convertHexToDecimal(match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex6.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    match = matchers.hex4.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            a: convertHexToDecimal(match[4] + match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex3.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nexport function isValidCSSUnit(color) {\n    return Boolean(matchers.CSS_UNIT.exec(String(color)));\n}\n", "import { numberInputToObject, rgbaToHex, rgbToHex, rgbToHsl, rgbToHsv } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { inputToRGB } from './format-input';\nimport { bound01, boundAlpha, clamp01 } from './util.js';\nvar TinyColor = /** @class */ (function () {\n    function TinyColor(color, opts) {\n        if (color === void 0) { color = ''; }\n        if (opts === void 0) { opts = {}; }\n        var _a;\n        // If input is already a tinycolor, return itself\n        if (color instanceof TinyColor) {\n            // eslint-disable-next-line no-constructor-return\n            return color;\n        }\n        if (typeof color === 'number') {\n            color = numberInputToObject(color);\n        }\n        this.originalInput = color;\n        var rgb = inputToRGB(color);\n        this.originalInput = color;\n        this.r = rgb.r;\n        this.g = rgb.g;\n        this.b = rgb.b;\n        this.a = rgb.a;\n        this.roundA = Math.round(100 * this.a) / 100;\n        this.format = (_a = opts.format) !== null && _a !== void 0 ? _a : rgb.format;\n        this.gradientType = opts.gradientType;\n        // Don't let the range of [0,255] come back in [0,1].\n        // Potentially lose a little bit of precision here, but will fix issues where\n        // .5 gets interpreted as half of the total, instead of half of 1\n        // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n        if (this.r < 1) {\n            this.r = Math.round(this.r);\n        }\n        if (this.g < 1) {\n            this.g = Math.round(this.g);\n        }\n        if (this.b < 1) {\n            this.b = Math.round(this.b);\n        }\n        this.isValid = rgb.ok;\n    }\n    TinyColor.prototype.isDark = function () {\n        return this.getBrightness() < 128;\n    };\n    TinyColor.prototype.isLight = function () {\n        return !this.isDark();\n    };\n    /**\n     * Returns the perceived brightness of the color, from 0-255.\n     */\n    TinyColor.prototype.getBrightness = function () {\n        // http://www.w3.org/TR/AERT#color-contrast\n        var rgb = this.toRgb();\n        return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n    };\n    /**\n     * Returns the perceived luminance of a color, from 0-1.\n     */\n    TinyColor.prototype.getLuminance = function () {\n        // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n        var rgb = this.toRgb();\n        var R;\n        var G;\n        var B;\n        var RsRGB = rgb.r / 255;\n        var GsRGB = rgb.g / 255;\n        var BsRGB = rgb.b / 255;\n        if (RsRGB <= 0.03928) {\n            R = RsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (GsRGB <= 0.03928) {\n            G = GsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (BsRGB <= 0.03928) {\n            B = BsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n        }\n        return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n    };\n    /**\n     * Returns the alpha value of a color, from 0-1.\n     */\n    TinyColor.prototype.getAlpha = function () {\n        return this.a;\n    };\n    /**\n     * Sets the alpha value on the current color.\n     *\n     * @param alpha - The new alpha value. The accepted range is 0-1.\n     */\n    TinyColor.prototype.setAlpha = function (alpha) {\n        this.a = boundAlpha(alpha);\n        this.roundA = Math.round(100 * this.a) / 100;\n        return this;\n    };\n    /**\n     * Returns whether the color is monochrome.\n     */\n    TinyColor.prototype.isMonochrome = function () {\n        var s = this.toHsl().s;\n        return s === 0;\n    };\n    /**\n     * Returns the object as a HSVA object.\n     */\n    TinyColor.prototype.toHsv = function () {\n        var hsv = rgbToHsv(this.r, this.g, this.b);\n        return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this.a };\n    };\n    /**\n     * Returns the hsva values interpolated into a string with the following format:\n     * \"hsva(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHsvString = function () {\n        var hsv = rgbToHsv(this.r, this.g, this.b);\n        var h = Math.round(hsv.h * 360);\n        var s = Math.round(hsv.s * 100);\n        var v = Math.round(hsv.v * 100);\n        return this.a === 1 ? \"hsv(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%)\") : \"hsva(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a HSLA object.\n     */\n    TinyColor.prototype.toHsl = function () {\n        var hsl = rgbToHsl(this.r, this.g, this.b);\n        return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this.a };\n    };\n    /**\n     * Returns the hsla values interpolated into a string with the following format:\n     * \"hsla(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHslString = function () {\n        var hsl = rgbToHsl(this.r, this.g, this.b);\n        var h = Math.round(hsl.h * 360);\n        var s = Math.round(hsl.s * 100);\n        var l = Math.round(hsl.l * 100);\n        return this.a === 1 ? \"hsl(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%)\") : \"hsla(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the hex value of the color.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHex = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return rgbToHex(this.r, this.g, this.b, allow3Char);\n    };\n    /**\n     * Returns the hex value of the color -with a # prefixed.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHexString = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return '#' + this.toHex(allow3Char);\n    };\n    /**\n     * Returns the hex 8 value of the color.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8 = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);\n    };\n    /**\n     * Returns the hex 8 value of the color -with a # prefixed.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8String = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return '#' + this.toHex8(allow4Char);\n    };\n    /**\n     * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n     * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n     */\n    TinyColor.prototype.toHexShortString = function (allowShortChar) {\n        if (allowShortChar === void 0) { allowShortChar = false; }\n        return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toRgb = function () {\n        return {\n            r: Math.round(this.r),\n            g: Math.round(this.g),\n            b: Math.round(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA values interpolated into a string with the following format:\n     * \"RGBA(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toRgbString = function () {\n        var r = Math.round(this.r);\n        var g = Math.round(this.g);\n        var b = Math.round(this.b);\n        return this.a === 1 ? \"rgb(\".concat(r, \", \").concat(g, \", \").concat(b, \")\") : \"rgba(\".concat(r, \", \").concat(g, \", \").concat(b, \", \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toPercentageRgb = function () {\n        var fmt = function (x) { return \"\".concat(Math.round(bound01(x, 255) * 100), \"%\"); };\n        return {\n            r: fmt(this.r),\n            g: fmt(this.g),\n            b: fmt(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA relative values interpolated into a string\n     */\n    TinyColor.prototype.toPercentageRgbString = function () {\n        var rnd = function (x) { return Math.round(bound01(x, 255) * 100); };\n        return this.a === 1\n            ? \"rgb(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%)\")\n            : \"rgba(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * The 'real' name of the color -if there is one.\n     */\n    TinyColor.prototype.toName = function () {\n        if (this.a === 0) {\n            return 'transparent';\n        }\n        if (this.a < 1) {\n            return false;\n        }\n        var hex = '#' + rgbToHex(this.r, this.g, this.b, false);\n        for (var _i = 0, _a = Object.entries(names); _i < _a.length; _i++) {\n            var _b = _a[_i], key = _b[0], value = _b[1];\n            if (hex === value) {\n                return key;\n            }\n        }\n        return false;\n    };\n    TinyColor.prototype.toString = function (format) {\n        var formatSet = Boolean(format);\n        format = format !== null && format !== void 0 ? format : this.format;\n        var formattedString = false;\n        var hasAlpha = this.a < 1 && this.a >= 0;\n        var needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n        if (needsAlphaFormat) {\n            // Special case for \"transparent\", all other non-alpha formats\n            // will return rgba when there is transparency.\n            if (format === 'name' && this.a === 0) {\n                return this.toName();\n            }\n            return this.toRgbString();\n        }\n        if (format === 'rgb') {\n            formattedString = this.toRgbString();\n        }\n        if (format === 'prgb') {\n            formattedString = this.toPercentageRgbString();\n        }\n        if (format === 'hex' || format === 'hex6') {\n            formattedString = this.toHexString();\n        }\n        if (format === 'hex3') {\n            formattedString = this.toHexString(true);\n        }\n        if (format === 'hex4') {\n            formattedString = this.toHex8String(true);\n        }\n        if (format === 'hex8') {\n            formattedString = this.toHex8String();\n        }\n        if (format === 'name') {\n            formattedString = this.toName();\n        }\n        if (format === 'hsl') {\n            formattedString = this.toHslString();\n        }\n        if (format === 'hsv') {\n            formattedString = this.toHsvString();\n        }\n        return formattedString || this.toHexString();\n    };\n    TinyColor.prototype.toNumber = function () {\n        return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n    };\n    TinyColor.prototype.clone = function () {\n        return new TinyColor(this.toString());\n    };\n    /**\n     * Lighten the color a given amount. Providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.lighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l += amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Brighten the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.brighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var rgb = this.toRgb();\n        rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n        rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n        rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n        return new TinyColor(rgb);\n    };\n    /**\n     * Darken the color a given amount, from 0 to 100.\n     * Providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.darken = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l -= amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the color with pure white, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.tint = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('white', amount);\n    };\n    /**\n     * Mix the color with pure black, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.shade = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('black', amount);\n    };\n    /**\n     * Desaturate the color a given amount, from 0 to 100.\n     * Providing 100 will is the same as calling greyscale\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.desaturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s -= amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Saturate the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.saturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s += amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Completely desaturates a color into greyscale.\n     * Same as calling `desaturate(100)`\n     */\n    TinyColor.prototype.greyscale = function () {\n        return this.desaturate(100);\n    };\n    /**\n     * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n     * Values outside of this range will be wrapped into this range.\n     */\n    TinyColor.prototype.spin = function (amount) {\n        var hsl = this.toHsl();\n        var hue = (hsl.h + amount) % 360;\n        hsl.h = hue < 0 ? 360 + hue : hue;\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the current color a given amount with another color, from 0 to 100.\n     * 0 means no mixing (return current color).\n     */\n    TinyColor.prototype.mix = function (color, amount) {\n        if (amount === void 0) { amount = 50; }\n        var rgb1 = this.toRgb();\n        var rgb2 = new TinyColor(color).toRgb();\n        var p = amount / 100;\n        var rgba = {\n            r: (rgb2.r - rgb1.r) * p + rgb1.r,\n            g: (rgb2.g - rgb1.g) * p + rgb1.g,\n            b: (rgb2.b - rgb1.b) * p + rgb1.b,\n            a: (rgb2.a - rgb1.a) * p + rgb1.a,\n        };\n        return new TinyColor(rgba);\n    };\n    TinyColor.prototype.analogous = function (results, slices) {\n        if (results === void 0) { results = 6; }\n        if (slices === void 0) { slices = 30; }\n        var hsl = this.toHsl();\n        var part = 360 / slices;\n        var ret = [this];\n        for (hsl.h = (hsl.h - ((part * results) >> 1) + 720) % 360; --results;) {\n            hsl.h = (hsl.h + part) % 360;\n            ret.push(new TinyColor(hsl));\n        }\n        return ret;\n    };\n    /**\n     * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n     */\n    TinyColor.prototype.complement = function () {\n        var hsl = this.toHsl();\n        hsl.h = (hsl.h + 180) % 360;\n        return new TinyColor(hsl);\n    };\n    TinyColor.prototype.monochromatic = function (results) {\n        if (results === void 0) { results = 6; }\n        var hsv = this.toHsv();\n        var h = hsv.h;\n        var s = hsv.s;\n        var v = hsv.v;\n        var res = [];\n        var modification = 1 / results;\n        while (results--) {\n            res.push(new TinyColor({ h: h, s: s, v: v }));\n            v = (v + modification) % 1;\n        }\n        return res;\n    };\n    TinyColor.prototype.splitcomplement = function () {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        return [\n            this,\n            new TinyColor({ h: (h + 72) % 360, s: hsl.s, l: hsl.l }),\n            new TinyColor({ h: (h + 216) % 360, s: hsl.s, l: hsl.l }),\n        ];\n    };\n    /**\n     * Compute how the color would appear on a background\n     */\n    TinyColor.prototype.onBackground = function (background) {\n        var fg = this.toRgb();\n        var bg = new TinyColor(background).toRgb();\n        var alpha = fg.a + bg.a * (1 - fg.a);\n        return new TinyColor({\n            r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n            g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n            b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n            a: alpha,\n        });\n    };\n    /**\n     * Alias for `polyad(3)`\n     */\n    TinyColor.prototype.triad = function () {\n        return this.polyad(3);\n    };\n    /**\n     * Alias for `polyad(4)`\n     */\n    TinyColor.prototype.tetrad = function () {\n        return this.polyad(4);\n    };\n    /**\n     * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n     * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n     */\n    TinyColor.prototype.polyad = function (n) {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        var result = [this];\n        var increment = 360 / n;\n        for (var i = 1; i < n; i++) {\n            result.push(new TinyColor({ h: (h + i * increment) % 360, s: hsl.s, l: hsl.l }));\n        }\n        return result;\n    };\n    /**\n     * compare color vs current color\n     */\n    TinyColor.prototype.equals = function (color) {\n        return this.toRgbString() === new TinyColor(color).toRgbString();\n    };\n    return TinyColor;\n}());\nexport { TinyColor };\n// kept for backwards compatability with v1\nexport function tinycolor(color, opts) {\n    if (color === void 0) { color = ''; }\n    if (opts === void 0) { opts = {}; }\n    return new TinyColor(color, opts);\n}\n", "import { inputToRGB, rgbToHex, rgbToHsv } from '@ctrl/tinycolor';\n\nvar hueStep = 2; // 色相阶梯\n\nvar saturationStep = 0.16; // 饱和度阶梯，浅色部分\n\nvar saturationStep2 = 0.05; // 饱和度阶梯，深色部分\n\nvar brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\n\nvar brightnessStep2 = 0.15; // 亮度阶梯，深色部分\n\nvar lightColorCount = 5; // 浅色数量，主色上\n\nvar darkColorCount = 4; // 深色数量，主色下\n// 暗色主题颜色映射关系表\n\nvar darkColorMap = [{\n  index: 7,\n  opacity: 0.15\n}, {\n  index: 6,\n  opacity: 0.25\n}, {\n  index: 5,\n  opacity: 0.3\n}, {\n  index: 5,\n  opacity: 0.45\n}, {\n  index: 5,\n  opacity: 0.65\n}, {\n  index: 5,\n  opacity: 0.85\n}, {\n  index: 4,\n  opacity: 0.9\n}, {\n  index: 3,\n  opacity: 0.95\n}, {\n  index: 2,\n  opacity: 0.97\n}, {\n  index: 1,\n  opacity: 0.98\n}]; // Wrapper function ported from TinyColor.prototype.toHsv\n// Keep it here because of `hsv.h * 360`\n\nfunction toHsv(_ref) {\n  var r = _ref.r,\n      g = _ref.g,\n      b = _ref.b;\n  var hsv = rgbToHsv(r, g, b);\n  return {\n    h: hsv.h * 360,\n    s: hsv.s,\n    v: hsv.v\n  };\n} // Wrapper function ported from TinyColor.prototype.toHexString\n// Keep it here because of the prefix `#`\n\n\nfunction toHex(_ref2) {\n  var r = _ref2.r,\n      g = _ref2.g,\n      b = _ref2.b;\n  return \"#\".concat(rgbToHex(r, g, b, false));\n} // Wrapper function ported from TinyColor.prototype.mix, not treeshakable.\n// Amount in range [0, 1]\n// Assume color1 & color2 has no alpha, since the following src code did so.\n\n\nfunction mix(rgb1, rgb2, amount) {\n  var p = amount / 100;\n  var rgb = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b\n  };\n  return rgb;\n}\n\nfunction getHue(hsv, i, light) {\n  var hue; // 根据色相不同，色相转向不同\n\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n\n  return hue;\n}\n\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n\n  var saturation;\n\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  } // 边界值修正\n\n\n  if (saturation > 1) {\n    saturation = 1;\n  } // 第一格的 s 限制在 0.06-0.1 之间\n\n\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n\n  return Number(saturation.toFixed(2));\n}\n\nfunction getValue(hsv, i, light) {\n  var value;\n\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n\n  if (value > 1) {\n    value = 1;\n  }\n\n  return Number(value.toFixed(2));\n}\n\nfunction generate(color) {\n  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var patterns = [];\n  var pColor = inputToRGB(color);\n\n  for (var i = lightColorCount; i > 0; i -= 1) {\n    var hsv = toHsv(pColor);\n    var colorString = toHex(inputToRGB({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    }));\n    patterns.push(colorString);\n  }\n\n  patterns.push(toHex(pColor));\n\n  for (var _i = 1; _i <= darkColorCount; _i += 1) {\n    var _hsv = toHsv(pColor);\n\n    var _colorString = toHex(inputToRGB({\n      h: getHue(_hsv, _i),\n      s: getSaturation(_hsv, _i),\n      v: getValue(_hsv, _i)\n    }));\n\n    patterns.push(_colorString);\n  } // dark theme patterns\n\n\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(function (_ref3) {\n      var index = _ref3.index,\n          opacity = _ref3.opacity;\n      var darkColorString = toHex(mix(inputToRGB(opts.backgroundColor || '#141414'), inputToRGB(patterns[index]), opacity * 100));\n      return darkColorString;\n    });\n  }\n\n  return patterns;\n}\n\nvar presetPrimaryColors = {\n  red: '#F5222D',\n  volcano: '#FA541C',\n  orange: '#FA8C16',\n  gold: '#FAAD14',\n  yellow: '#FADB14',\n  lime: '#A0D911',\n  green: '#52C41A',\n  cyan: '#13C2C2',\n  blue: '#1890FF',\n  geekblue: '#2F54EB',\n  purple: '#722ED1',\n  magenta: '#EB2F96',\n  grey: '#666666'\n};\nvar presetPalettes = {};\nvar presetDarkPalettes = {};\nObject.keys(presetPrimaryColors).forEach(function (key) {\n  presetPalettes[key] = generate(presetPrimaryColors[key]);\n  presetPalettes[key].primary = presetPalettes[key][5]; // dark presetPalettes\n\n  presetDarkPalettes[key] = generate(presetPrimaryColors[key], {\n    theme: 'dark',\n    backgroundColor: '#141414'\n  });\n  presetDarkPalettes[key].primary = presetDarkPalettes[key][5];\n});\nvar red = presetPalettes.red;\nvar volcano = presetPalettes.volcano;\nvar gold = presetPalettes.gold;\nvar orange = presetPalettes.orange;\nvar yellow = presetPalettes.yellow;\nvar lime = presetPalettes.lime;\nvar green = presetPalettes.green;\nvar cyan = presetPalettes.cyan;\nvar blue = presetPalettes.blue;\nvar geekblue = presetPalettes.geekblue;\nvar purple = presetPalettes.purple;\nvar magenta = presetPalettes.magenta;\nvar grey = presetPalettes.grey;\n\nexport { blue, cyan, geekblue, generate, gold, green, grey, lime, magenta, orange, presetDarkPalettes, presetPalettes, presetPrimaryColors, purple, red, volcano, yellow };\n", "import { inject, provide, ref } from 'vue';\nvar contextKey = Symbol('iconContext');\nexport var useProvideIconContext = function useProvideIconContext(props) {\n  provide(contextKey, props);\n  return props;\n};\nexport var useInjectIconContext = function useInjectIconContext() {\n  return inject(contextKey, {\n    prefixCls: ref('anticon'),\n    rootClassName: ref(''),\n    csp: ref()\n  });\n};", "export function canUseDom() {\n  return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n}\n\nfunction contains(root, n) {\n  if (!root) {\n    return false;\n  } // Use native if support\n\n\n  if (root.contains) {\n    return root.contains(n);\n  }\n\n  return false;\n}\n\nvar APPEND_ORDER = 'data-vc-order';\nvar MARK_KEY = \"vc-icon-key\";\nvar containerCache = new Map();\n\nfunction getMark() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n      mark = _ref.mark;\n\n  if (mark) {\n    return mark.startsWith('data-') ? mark : \"data-\".concat(mark);\n  }\n\n  return MARK_KEY;\n}\n\nfunction getContainer(option) {\n  if (option.attachTo) {\n    return option.attachTo;\n  }\n\n  var head = document.querySelector('head');\n  return head || document.body;\n}\n\nfunction getOrder(prepend) {\n  if (prepend === 'queue') {\n    return 'prependQueue';\n  }\n\n  return prepend ? 'prepend' : 'append';\n}\n/**\n * Find style which inject by rc-util\n */\n\n\nfunction findStyles(container) {\n  return Array.from((containerCache.get(container) || container).children).filter(function (node) {\n    return node.tagName === 'STYLE';\n  });\n}\n\nexport function injectCSS(css) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  if (!canUseDom()) {\n    return null;\n  }\n\n  var csp = option.csp,\n      prepend = option.prepend;\n  var styleNode = document.createElement('style');\n  styleNode.setAttribute(APPEND_ORDER, getOrder(prepend));\n\n  if (csp && csp.nonce) {\n    styleNode.nonce = csp.nonce;\n  }\n\n  styleNode.innerHTML = css;\n  var container = getContainer(option);\n  var firstChild = container.firstChild;\n\n  if (prepend) {\n    // If is queue `prepend`, it will prepend first style and then append rest style\n    if (prepend === 'queue') {\n      var existStyle = findStyles(container).filter(function (node) {\n        return ['prepend', 'prependQueue'].includes(node.getAttribute(APPEND_ORDER));\n      });\n\n      if (existStyle.length) {\n        container.insertBefore(styleNode, existStyle[existStyle.length - 1].nextSibling);\n        return styleNode;\n      }\n    } // Use `insertBefore` as `prepend`\n\n\n    container.insertBefore(styleNode, firstChild);\n  } else {\n    container.appendChild(styleNode);\n  }\n\n  return styleNode;\n}\n\nfunction findExistNode(key) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var container = getContainer(option);\n  return findStyles(container).find(function (node) {\n    return node.getAttribute(getMark(option)) === key;\n  });\n}\n\nexport function removeCSS(key) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var existNode = findExistNode(key, option);\n\n  if (existNode) {\n    var container = getContainer(option);\n    container.removeChild(existNode);\n  }\n}\n/**\n * qiankun will inject `appendChild` to insert into other\n */\n\nfunction syncRealContainer(container, option) {\n  var cachedRealContainer = containerCache.get(container); // Find real container when not cached or cached container removed\n\n  if (!cachedRealContainer || !contains(document, cachedRealContainer)) {\n    var placeholderStyle = injectCSS('', option);\n    var parentNode = placeholderStyle.parentNode;\n    containerCache.set(container, parentNode);\n    container.removeChild(placeholderStyle);\n  }\n}\n/**\n * manually clear container cache to avoid global cache in unit testes\n */\n\n\nexport function clearContainerCache() {\n  containerCache.clear();\n}\nexport function updateCSS(css, key) {\n  var option = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var container = getContainer(option); // Sync real parent\n\n  syncRealContainer(container, option);\n  var existNode = findExistNode(key, option);\n\n  if (existNode) {\n    if (option.csp && option.csp.nonce && existNode.nonce !== option.csp.nonce) {\n      existNode.nonce = option.csp.nonce;\n    }\n\n    if (existNode.innerHTML !== css) {\n      existNode.innerHTML = css;\n    }\n\n    return existNode;\n  }\n\n  var newNode = injectCSS(css, option);\n  newNode.setAttribute(getMark(option), key);\n  return newNode;\n}", "function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? Object(arguments[i]) : {}; var ownKeys = Object.keys(source); if (typeof Object.getOwnPropertySymbols === 'function') { ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) { return Object.getOwnPropertyDescriptor(source, sym).enumerable; })); } ownKeys.forEach(function (key) { _defineProperty(target, key, source[key]); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { nextTick, h, getCurrentInstance } from 'vue';\nimport { generate as generateColor } from '@ant-design/colors';\nimport { useInjectIconContext } from './components/Context';\nimport { updateCSS, canUseDom } from './dynamicCSS';\nexport function warn(valid, message) {\n  // Support uglify\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    console.error(\"Warning: \".concat(message));\n  }\n}\nexport function warning(valid, message) {\n  warn(valid, \"[@ant-design/icons-vue] \".concat(message));\n}\n\nfunction camelCase(input) {\n  return input.replace(/-(.)/g, function (_match, g) {\n    return g.toUpperCase();\n  });\n} // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n\n\nexport function isIconDefinition(target) {\n  return typeof target === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && (typeof target.icon === 'object' || typeof target.icon === 'function');\n}\nexport function normalizeAttrs() {\n  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return Object.keys(attrs).reduce(function (acc, key) {\n    var val = attrs[key];\n\n    switch (key) {\n      case 'class':\n        acc.className = val;\n        delete acc[\"class\"];\n        break;\n\n      default:\n        delete acc[key];\n        acc[camelCase(key)] = val;\n    }\n\n    return acc;\n  }, {});\n}\nexport function generate(node, key, rootProps) {\n  if (!rootProps) {\n    return h(node.tag, _objectSpread({\n      key: key\n    }, node.attrs), (node.children || []).map(function (child, index) {\n      return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n    }));\n  }\n\n  return h(node.tag, _objectSpread({\n    key: key\n  }, rootProps, node.attrs), (node.children || []).map(function (child, index) {\n    return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n  }));\n}\nexport function getSecondaryColor(primaryColor) {\n  // choose the second color\n  return generateColor(primaryColor)[0];\n}\nexport function normalizeTwoToneColors(twoToneColor) {\n  if (!twoToneColor) {\n    return [];\n  }\n\n  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];\n} // These props make sure that the SVG behaviours like general text.\n// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4\n\nexport var svgBaseProps = {\n  width: '1em',\n  height: '1em',\n  fill: 'currentColor',\n  'aria-hidden': 'true',\n  focusable: 'false'\n};\nexport var iconStyles = \"\\n.anticon {\\n  display: inline-block;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\";\n\nfunction getRoot(ele) {\n  return ele && ele.getRootNode && ele.getRootNode();\n}\n/**\n * Check if is in shadowRoot\n */\n\n\nfunction inShadow(ele) {\n  if (!canUseDom()) {\n    return false;\n  }\n\n  return getRoot(ele) instanceof ShadowRoot;\n}\n/**\n * Return shadowRoot if possible\n */\n\n\nfunction getShadowRoot(ele) {\n  return inShadow(ele) ? getRoot(ele) : null;\n}\n\nexport var useInsertStyles = function useInsertStyles() {\n  var _useInjectIconContext = useInjectIconContext(),\n      prefixCls = _useInjectIconContext.prefixCls,\n      csp = _useInjectIconContext.csp;\n\n  var instance = getCurrentInstance();\n  var mergedStyleStr = iconStyles;\n\n  if (prefixCls) {\n    mergedStyleStr = mergedStyleStr.replace(/anticon/g, prefixCls.value);\n  }\n\n  nextTick(function () {\n    if (!canUseDom()) {\n      return;\n    }\n\n    var ele = instance.vnode.el;\n    var shadowRoot = getShadowRoot(ele);\n    updateCSS(mergedStyleStr, '@ant-design-vue-icons', {\n      prepend: true,\n      csp: csp.value,\n      attachTo: shadowRoot\n    });\n  });\n};", "var _excluded = [\"icon\", \"primaryColor\", \"secondaryColor\"];\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? Object(arguments[i]) : {}; var ownKeys = Object.keys(source); if (typeof Object.getOwnPropertySymbols === 'function') { ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) { return Object.getOwnPropertyDescriptor(source, sym).enumerable; })); } ownKeys.forEach(function (key) { _defineProperty(target, key, source[key]); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { generate, getSecondaryColor, isIconDefinition, warning } from '../utils';\nimport { reactive } from 'vue';\nvar twoToneColorPalette = reactive({\n  primaryColor: '#333',\n  secondaryColor: '#E6E6E6',\n  calculated: false\n});\n\nfunction setTwoToneColors(_ref) {\n  var primaryColor = _ref.primaryColor,\n      secondaryColor = _ref.secondaryColor;\n  twoToneColorPalette.primaryColor = primaryColor;\n  twoToneColorPalette.secondaryColor = secondaryColor || getSecondaryColor(primaryColor);\n  twoToneColorPalette.calculated = !!secondaryColor;\n}\n\nfunction getTwoToneColors() {\n  return _objectSpread({}, twoToneColorPalette);\n}\n\nvar IconBase = function IconBase(props, context) {\n  var _props$context$attrs = _objectSpread({}, props, context.attrs),\n      icon = _props$context$attrs.icon,\n      primaryColor = _props$context$attrs.primaryColor,\n      secondaryColor = _props$context$attrs.secondaryColor,\n      restProps = _objectWithoutProperties(_props$context$attrs, _excluded);\n\n  var colors = twoToneColorPalette;\n\n  if (primaryColor) {\n    colors = {\n      primaryColor: primaryColor,\n      secondaryColor: secondaryColor || getSecondaryColor(primaryColor)\n    };\n  }\n\n  warning(isIconDefinition(icon), \"icon should be icon definiton, but got \".concat(icon));\n\n  if (!isIconDefinition(icon)) {\n    return null;\n  }\n\n  var target = icon;\n\n  if (target && typeof target.icon === 'function') {\n    target = _objectSpread({}, target, {\n      icon: target.icon(colors.primaryColor, colors.secondaryColor)\n    });\n  }\n\n  return generate(target.icon, \"svg-\".concat(target.name), _objectSpread({}, restProps, {\n    'data-icon': target.name,\n    width: '1em',\n    height: '1em',\n    fill: 'currentColor',\n    'aria-hidden': 'true'\n  })); // },\n};\n\nIconBase.props = {\n  icon: Object,\n  primaryColor: String,\n  secondaryColor: String,\n  focusable: String\n};\nIconBase.inheritAttrs = false;\nIconBase.displayName = 'IconBase';\nIconBase.getTwoToneColors = getTwoToneColors;\nIconBase.setTwoToneColors = setTwoToneColors;\nexport default IconBase;", "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport VueIcon from './IconBase';\nimport { normalizeTwoToneColors } from '../utils';\nexport function setTwoToneColor(twoToneColor) {\n  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),\n      _normalizeTwoToneColo2 = _slicedToArray(_normalizeTwoToneColo, 2),\n      primaryColor = _normalizeTwoToneColo2[0],\n      secondaryColor = _normalizeTwoToneColo2[1];\n\n  return VueIcon.setTwoToneColors({\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor\n  });\n}\nexport function getTwoToneColor() {\n  var colors = VueIcon.getTwoToneColors();\n\n  if (!colors.calculated) {\n    return colors.primaryColor;\n  }\n\n  return [colors.primaryColor, colors.secondaryColor];\n}", "import { defineComponent } from 'vue';\nimport { useInsertStyles } from '../utils';\nexport var InsertStyles = defineComponent({\n  name: 'InsertStyles',\n  setup: function setup() {\n    useInsertStyles();\n    return function () {\n      return null;\n    };\n  }\n});", "var _excluded = [\"class\", \"icon\", \"spin\", \"rotate\", \"tabindex\", \"twoToneColor\", \"onClick\"];\nimport { createVNode as _createVNode } from \"vue\";\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? Object(arguments[i]) : {}; var ownKeys = Object.keys(source); if (typeof Object.getOwnPropertySymbols === 'function') { ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) { return Object.getOwnPropertyDescriptor(source, sym).enumerable; })); } ownKeys.forEach(function (key) { _defineProperty(target, key, source[key]); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport VueIcon from './IconBase';\nimport { getTwoToneColor, setTwoToneColor } from './twoTonePrimaryColor';\nimport { normalizeTwoToneColors } from '../utils';\nimport { blue } from '@ant-design/colors';\nimport { useInjectIconContext } from './Context';\nimport { InsertStyles } from './InsertStyle'; // Initial setting\n\nsetTwoToneColor(blue.primary);\n\nvar Icon = function Icon(props, context) {\n  var _classObj;\n\n  var _props$context$attrs = _objectSpread({}, props, context.attrs),\n      cls = _props$context$attrs[\"class\"],\n      icon = _props$context$attrs.icon,\n      spin = _props$context$attrs.spin,\n      rotate = _props$context$attrs.rotate,\n      tabindex = _props$context$attrs.tabindex,\n      twoToneColor = _props$context$attrs.twoToneColor,\n      onClick = _props$context$attrs.onClick,\n      restProps = _objectWithoutProperties(_props$context$attrs, _excluded);\n\n  var _useInjectIconContext = useInjectIconContext(),\n      prefixCls = _useInjectIconContext.prefixCls,\n      rootClassName = _useInjectIconContext.rootClassName;\n\n  var classObj = (_classObj = {}, _defineProperty(_classObj, rootClassName.value, !!rootClassName.value), _defineProperty(_classObj, prefixCls.value, true), _defineProperty(_classObj, \"\".concat(prefixCls.value, \"-\").concat(icon.name), Boolean(icon.name)), _defineProperty(_classObj, \"\".concat(prefixCls.value, \"-spin\"), !!spin || icon.name === 'loading'), _classObj);\n  var iconTabIndex = tabindex;\n\n  if (iconTabIndex === undefined && onClick) {\n    iconTabIndex = -1;\n  }\n\n  var svgStyle = rotate ? {\n    msTransform: \"rotate(\".concat(rotate, \"deg)\"),\n    transform: \"rotate(\".concat(rotate, \"deg)\")\n  } : undefined;\n\n  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),\n      _normalizeTwoToneColo2 = _slicedToArray(_normalizeTwoToneColo, 2),\n      primaryColor = _normalizeTwoToneColo2[0],\n      secondaryColor = _normalizeTwoToneColo2[1];\n\n  return _createVNode(\"span\", _objectSpread({\n    \"role\": \"img\",\n    \"aria-label\": icon.name\n  }, restProps, {\n    \"onClick\": onClick,\n    \"class\": [classObj, cls],\n    \"tabindex\": iconTabIndex\n  }), [_createVNode(VueIcon, {\n    \"icon\": icon,\n    \"primaryColor\": primaryColor,\n    \"secondaryColor\": secondaryColor,\n    \"style\": svgStyle\n  }, null), _createVNode(InsertStyles, null, null)]);\n};\n\nIcon.props = {\n  spin: Boolean,\n  rotate: Number,\n  icon: Object,\n  twoToneColor: [String, Array]\n};\nIcon.displayName = 'AntdIcon';\nIcon.inheritAttrs = false;\nIcon.getTwoToneColor = getTwoToneColor;\nIcon.setTwoToneColor = setTwoToneColor;\nexport default Icon;"], "mappings": ";;;;;;;;;;;;AAIO,SAAS,QAAQ,GAAG,KAAK;AAC5B,MAAI,eAAe,CAAC,GAAG;AACnB,QAAI;AAAA,EACR;AACA,MAAI,YAAY,aAAa,CAAC;AAC9B,MAAI,QAAQ,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC;AAE9D,MAAI,WAAW;AACX,QAAI,SAAS,OAAO,IAAI,GAAG,GAAG,EAAE,IAAI;AAAA,EACxC;AAEA,MAAI,KAAK,IAAI,IAAI,GAAG,IAAI,MAAU;AAC9B,WAAO;AAAA,EACX;AAEA,MAAI,QAAQ,KAAK;AAIb,SAAK,IAAI,IAAK,IAAI,MAAO,MAAM,IAAI,OAAO,WAAW,OAAO,GAAG,CAAC;AAAA,EACpE,OACK;AAGD,QAAK,IAAI,MAAO,WAAW,OAAO,GAAG,CAAC;AAAA,EAC1C;AACA,SAAO;AACX;AAKO,SAAS,QAAQ,KAAK;AACzB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC;AACvC;AAMO,SAAS,eAAe,GAAG;AAC9B,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM,MAAM,WAAW,CAAC,MAAM;AAC/E;AAKO,SAAS,aAAa,GAAG;AAC5B,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM;AACvD;AAKO,SAAS,WAAW,GAAG;AAC1B,MAAI,WAAW,CAAC;AAChB,MAAI,MAAM,CAAC,KAAK,IAAI,KAAK,IAAI,GAAG;AAC5B,QAAI;AAAA,EACR;AACA,SAAO;AACX;AAKO,SAAS,oBAAoB,GAAG;AACnC,MAAI,KAAK,GAAG;AACR,WAAO,GAAG,OAAO,OAAO,CAAC,IAAI,KAAK,GAAG;AAAA,EACzC;AACA,SAAO;AACX;AAKO,SAAS,KAAK,GAAG;AACpB,SAAO,EAAE,WAAW,IAAI,MAAM,IAAI,OAAO,CAAC;AAC9C;;;ACxEO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,SAAO;AAAA,IACH,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,EACzB;AACJ;AAMO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,MAAIA,KAAI;AACR,MAAI,IAAI;AACR,MAAI,KAAK,MAAM,OAAO;AACtB,MAAI,QAAQ,KAAK;AACb,QAAI;AACJ,IAAAA,KAAI;AAAA,EACR,OACK;AACD,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM;AAC/C,YAAQ,KAAK;AAAA,MACT,KAAK;AACD,QAAAA,MAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,MACJ,KAAK;AACD,QAAAA,MAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ,KAAK;AACD,QAAAA,MAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ;AACI;AAAA,IACR;AACA,IAAAA,MAAK;AAAA,EACT;AACA,SAAO,EAAE,GAAGA,IAAG,GAAM,EAAK;AAC9B;AACA,SAAS,QAAQ,GAAG,GAAG,GAAG;AACtB,MAAI,IAAI,GAAG;AACP,SAAK;AAAA,EACT;AACA,MAAI,IAAI,GAAG;AACP,SAAK;AAAA,EACT;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO,KAAK,IAAI,MAAM,IAAI;AAAA,EAC9B;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO;AAAA,EACX;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AAAA,EACvC;AACA,SAAO;AACX;AAOO,SAAS,SAASA,IAAG,GAAG,GAAG;AAC9B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,EAAAA,KAAI,QAAQA,IAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,GAAG;AAET,QAAI;AACJ,QAAI;AACJ,QAAI;AAAA,EACR,OACK;AACD,QAAI,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;AAC5C,QAAI,IAAI,IAAI,IAAI;AAChB,QAAI,QAAQ,GAAG,GAAGA,KAAI,IAAI,CAAC;AAC3B,QAAI,QAAQ,GAAG,GAAGA,EAAC;AACnB,QAAI,QAAQ,GAAG,GAAGA,KAAI,IAAI,CAAC;AAAA,EAC/B;AACA,SAAO,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,IAAI;AAChD;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,MAAIA,KAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI,MAAM;AACd,MAAI,IAAI,QAAQ,IAAI,IAAI,IAAI;AAC5B,MAAI,QAAQ,KAAK;AACb,IAAAA,KAAI;AAAA,EACR,OACK;AACD,YAAQ,KAAK;AAAA,MACT,KAAK;AACD,QAAAA,MAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,MACJ,KAAK;AACD,QAAAA,MAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ,KAAK;AACD,QAAAA,MAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ;AACI;AAAA,IACR;AACA,IAAAA,MAAK;AAAA,EACT;AACA,SAAO,EAAE,GAAGA,IAAG,GAAM,EAAK;AAC9B;AAOO,SAAS,SAASA,IAAG,GAAG,GAAG;AAC9B,EAAAA,KAAI,QAAQA,IAAG,GAAG,IAAI;AACtB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,IAAI,KAAK,MAAMA,EAAC;AACpB,MAAI,IAAIA,KAAI;AACZ,MAAI,IAAI,KAAK,IAAI;AACjB,MAAI,IAAI,KAAK,IAAI,IAAI;AACrB,MAAI,IAAI,KAAK,KAAK,IAAI,KAAK;AAC3B,MAAI,MAAM,IAAI;AACd,MAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAC9B,MAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAC9B,MAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAC9B,SAAO,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,IAAI;AAChD;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG,YAAY;AAC1C,MAAI,MAAM;AAAA,IACN,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,EACnC;AAEA,MAAI,cACA,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;AACrC,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EAChE;AACA,SAAO,IAAI,KAAK,EAAE;AACtB;AAQO,SAAS,UAAU,GAAG,GAAG,GAAG,GAAG,YAAY;AAC9C,MAAI,MAAM;AAAA,IACN,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,oBAAoB,CAAC,CAAC;AAAA,EAC/B;AAEA,MAAI,cACA,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;AACrC,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EACnF;AACA,SAAO,IAAI,KAAK,EAAE;AACtB;AAeO,SAAS,oBAAoB,GAAG;AACnC,SAAO,KAAK,MAAM,WAAW,CAAC,IAAI,GAAG,EAAE,SAAS,EAAE;AACtD;AAEO,SAAS,oBAAoBC,IAAG;AACnC,SAAO,gBAAgBA,EAAC,IAAI;AAChC;AAEO,SAAS,gBAAgB,KAAK;AACjC,SAAO,SAAS,KAAK,EAAE;AAC3B;AACO,SAAS,oBAAoB,OAAO;AACvC,SAAO;AAAA,IACH,GAAG,SAAS;AAAA,IACZ,IAAI,QAAQ,UAAW;AAAA,IACvB,GAAG,QAAQ;AAAA,EACf;AACJ;;;ACtOO,IAAI,QAAQ;AAAA,EACf,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,KAAK;AAAA,EACL,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,aAAa;AACjB;;;ACnIO,SAAS,WAAW,OAAO;AAC9B,MAAI,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AAC7B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,SAAS;AACb,MAAI,OAAO,UAAU,UAAU;AAC3B,YAAQ,oBAAoB,KAAK;AAAA,EACrC;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AAC/E,YAAM,SAAS,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACxC,WAAK;AACL,eAAS,OAAO,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,MAAM,SAAS;AAAA,IAC3D,WACS,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACpF,UAAI,oBAAoB,MAAM,CAAC;AAC/B,UAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAG,GAAG,CAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACb,WACS,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACpF,UAAI,oBAAoB,MAAM,CAAC;AAC/B,UAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAG,GAAG,CAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACb;AACA,QAAI,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,GAAG;AAClD,UAAI,MAAM;AAAA,IACd;AAAA,EACJ;AACA,MAAI,WAAW,CAAC;AAChB,SAAO;AAAA,IACH;AAAA,IACA,QAAQ,MAAM,UAAU;AAAA,IACxB,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC;AAAA,EACJ;AACJ;AAEA,IAAI,cAAc;AAElB,IAAI,aAAa;AAEjB,IAAI,WAAW,MAAM,OAAO,YAAY,OAAO,EAAE,OAAO,aAAa,GAAG;AAIxE,IAAI,oBAAoB,cAAc,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,WAAW;AAChI,IAAI,oBAAoB,cAAc,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,WAAW;AAC/J,IAAI,WAAW;AAAA,EACX,UAAU,IAAI,OAAO,QAAQ;AAAA,EAC7B,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,EACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,EACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,EACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACV;AAKO,SAAS,oBAAoB,OAAO;AACvC,UAAQ,MAAM,KAAK,EAAE,YAAY;AACjC,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AACZ,MAAI,MAAM,KAAK,GAAG;AACd,YAAQ,MAAM,KAAK;AACnB,YAAQ;AAAA,EACZ,WACS,UAAU,eAAe;AAC9B,WAAO,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,QAAQ,OAAO;AAAA,EACpD;AAKA,MAAI,QAAQ,SAAS,IAAI,KAAK,KAAK;AACnC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EACnD;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EAChE;AACA,UAAQ,SAAS,IAAI,KAAK,KAAK;AAC/B,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EACnD;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EAChE;AACA,UAAQ,SAAS,IAAI,KAAK,KAAK;AAC/B,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EACnD;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EAChE;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,oBAAoB,MAAM,CAAC,CAAC;AAAA,MAC/B,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,oBAAoB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MAC1C,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,SAAO;AACX;AAKO,SAAS,eAAe,OAAO;AAClC,SAAO,QAAQ,SAAS,SAAS,KAAK,OAAO,KAAK,CAAC,CAAC;AACxD;;;AClLA,IAAI;AAAA;AAAA,EAA2B,WAAY;AACvC,aAASC,WAAU,OAAO,MAAM;AAC5B,UAAI,UAAU,QAAQ;AAAE,gBAAQ;AAAA,MAAI;AACpC,UAAI,SAAS,QAAQ;AAAE,eAAO,CAAC;AAAA,MAAG;AAClC,UAAI;AAEJ,UAAI,iBAAiBA,YAAW;AAE5B,eAAO;AAAA,MACX;AACA,UAAI,OAAO,UAAU,UAAU;AAC3B,gBAAQ,oBAAoB,KAAK;AAAA,MACrC;AACA,WAAK,gBAAgB;AACrB,UAAI,MAAM,WAAW,KAAK;AAC1B,WAAK,gBAAgB;AACrB,WAAK,IAAI,IAAI;AACb,WAAK,IAAI,IAAI;AACb,WAAK,IAAI,IAAI;AACb,WAAK,IAAI,IAAI;AACb,WAAK,SAAS,KAAK,MAAM,MAAM,KAAK,CAAC,IAAI;AACzC,WAAK,UAAU,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,KAAK,IAAI;AACtE,WAAK,eAAe,KAAK;AAKzB,UAAI,KAAK,IAAI,GAAG;AACZ,aAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,MAC9B;AACA,UAAI,KAAK,IAAI,GAAG;AACZ,aAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,MAC9B;AACA,UAAI,KAAK,IAAI,GAAG;AACZ,aAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,MAC9B;AACA,WAAK,UAAU,IAAI;AAAA,IACvB;AACA,IAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,aAAO,KAAK,cAAc,IAAI;AAAA,IAClC;AACA,IAAAA,WAAU,UAAU,UAAU,WAAY;AACtC,aAAO,CAAC,KAAK,OAAO;AAAA,IACxB;AAIA,IAAAA,WAAU,UAAU,gBAAgB,WAAY;AAE5C,UAAI,MAAM,KAAK,MAAM;AACrB,cAAQ,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,OAAO;AAAA,IACvD;AAIA,IAAAA,WAAU,UAAU,eAAe,WAAY;AAE3C,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,QAAQ,IAAI,IAAI;AACpB,UAAI,QAAQ,IAAI,IAAI;AACpB,UAAI,QAAQ,IAAI,IAAI;AACpB,UAAI,SAAS,SAAS;AAClB,YAAI,QAAQ;AAAA,MAChB,OACK;AAED,YAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,MAC7C;AACA,UAAI,SAAS,SAAS;AAClB,YAAI,QAAQ;AAAA,MAChB,OACK;AAED,YAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,MAC7C;AACA,UAAI,SAAS,SAAS;AAClB,YAAI,QAAQ;AAAA,MAChB,OACK;AAED,YAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,MAC7C;AACA,aAAO,SAAS,IAAI,SAAS,IAAI,SAAS;AAAA,IAC9C;AAIA,IAAAA,WAAU,UAAU,WAAW,WAAY;AACvC,aAAO,KAAK;AAAA,IAChB;AAMA,IAAAA,WAAU,UAAU,WAAW,SAAU,OAAO;AAC5C,WAAK,IAAI,WAAW,KAAK;AACzB,WAAK,SAAS,KAAK,MAAM,MAAM,KAAK,CAAC,IAAI;AACzC,aAAO;AAAA,IACX;AAIA,IAAAA,WAAU,UAAU,eAAe,WAAY;AAC3C,UAAI,IAAI,KAAK,MAAM,EAAE;AACrB,aAAO,MAAM;AAAA,IACjB;AAIA,IAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,UAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,aAAO,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,EAAE;AAAA,IAC3D;AAKA,IAAAA,WAAU,UAAU,cAAc,WAAY;AAC1C,UAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,UAAIC,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,UAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,UAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,aAAO,KAAK,MAAM,IAAI,OAAO,OAAOA,IAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,IAAI,IAAI,QAAQ,OAAOA,IAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,IACrK;AAIA,IAAAD,WAAU,UAAU,QAAQ,WAAY;AACpC,UAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,aAAO,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,EAAE;AAAA,IAC3D;AAKA,IAAAA,WAAU,UAAU,cAAc,WAAY;AAC1C,UAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,UAAIC,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,UAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,UAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,aAAO,KAAK,MAAM,IAAI,OAAO,OAAOA,IAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,IAAI,IAAI,QAAQ,OAAOA,IAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,IACrK;AAKA,IAAAD,WAAU,UAAU,QAAQ,SAAU,YAAY;AAC9C,UAAI,eAAe,QAAQ;AAAE,qBAAa;AAAA,MAAO;AACjD,aAAO,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,UAAU;AAAA,IACtD;AAKA,IAAAA,WAAU,UAAU,cAAc,SAAU,YAAY;AACpD,UAAI,eAAe,QAAQ;AAAE,qBAAa;AAAA,MAAO;AACjD,aAAO,MAAM,KAAK,MAAM,UAAU;AAAA,IACtC;AAKA,IAAAA,WAAU,UAAU,SAAS,SAAU,YAAY;AAC/C,UAAI,eAAe,QAAQ;AAAE,qBAAa;AAAA,MAAO;AACjD,aAAO,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,UAAU;AAAA,IAC/D;AAKA,IAAAA,WAAU,UAAU,eAAe,SAAU,YAAY;AACrD,UAAI,eAAe,QAAQ;AAAE,qBAAa;AAAA,MAAO;AACjD,aAAO,MAAM,KAAK,OAAO,UAAU;AAAA,IACvC;AAKA,IAAAA,WAAU,UAAU,mBAAmB,SAAU,gBAAgB;AAC7D,UAAI,mBAAmB,QAAQ;AAAE,yBAAiB;AAAA,MAAO;AACzD,aAAO,KAAK,MAAM,IAAI,KAAK,YAAY,cAAc,IAAI,KAAK,aAAa,cAAc;AAAA,IAC7F;AAIA,IAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,aAAO;AAAA,QACH,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,QACpB,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,QACpB,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,QACpB,GAAG,KAAK;AAAA,MACZ;AAAA,IACJ;AAKA,IAAAA,WAAU,UAAU,cAAc,WAAY;AAC1C,UAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACzB,UAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACzB,UAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACzB,aAAO,KAAK,MAAM,IAAI,OAAO,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,GAAG,IAAI,QAAQ,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,IACjK;AAIA,IAAAA,WAAU,UAAU,kBAAkB,WAAY;AAC9C,UAAI,MAAM,SAAU,GAAG;AAAE,eAAO,GAAG,OAAO,KAAK,MAAM,QAAQ,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG;AAAA,MAAG;AACnF,aAAO;AAAA,QACH,GAAG,IAAI,KAAK,CAAC;AAAA,QACb,GAAG,IAAI,KAAK,CAAC;AAAA,QACb,GAAG,IAAI,KAAK,CAAC;AAAA,QACb,GAAG,KAAK;AAAA,MACZ;AAAA,IACJ;AAIA,IAAAA,WAAU,UAAU,wBAAwB,WAAY;AACpD,UAAI,MAAM,SAAU,GAAG;AAAE,eAAO,KAAK,MAAM,QAAQ,GAAG,GAAG,IAAI,GAAG;AAAA,MAAG;AACnE,aAAO,KAAK,MAAM,IACZ,OAAO,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,IAAI,IACrF,QAAQ,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,IAC1H;AAIA,IAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,UAAI,KAAK,MAAM,GAAG;AACd,eAAO;AAAA,MACX;AACA,UAAI,KAAK,IAAI,GAAG;AACZ,eAAO;AAAA,MACX;AACA,UAAI,MAAM,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AACtD,eAAS,KAAK,GAAG,KAAK,OAAO,QAAQ,KAAK,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC/D,YAAI,KAAK,GAAG,EAAE,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AAC1C,YAAI,QAAQ,OAAO;AACf,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,WAAW,SAAU,QAAQ;AAC7C,UAAI,YAAY,QAAQ,MAAM;AAC9B,eAAS,WAAW,QAAQ,WAAW,SAAS,SAAS,KAAK;AAC9D,UAAI,kBAAkB;AACtB,UAAI,WAAW,KAAK,IAAI,KAAK,KAAK,KAAK;AACvC,UAAI,mBAAmB,CAAC,aAAa,aAAa,OAAO,WAAW,KAAK,KAAK,WAAW;AACzF,UAAI,kBAAkB;AAGlB,YAAI,WAAW,UAAU,KAAK,MAAM,GAAG;AACnC,iBAAO,KAAK,OAAO;AAAA,QACvB;AACA,eAAO,KAAK,YAAY;AAAA,MAC5B;AACA,UAAI,WAAW,OAAO;AAClB,0BAAkB,KAAK,YAAY;AAAA,MACvC;AACA,UAAI,WAAW,QAAQ;AACnB,0BAAkB,KAAK,sBAAsB;AAAA,MACjD;AACA,UAAI,WAAW,SAAS,WAAW,QAAQ;AACvC,0BAAkB,KAAK,YAAY;AAAA,MACvC;AACA,UAAI,WAAW,QAAQ;AACnB,0BAAkB,KAAK,YAAY,IAAI;AAAA,MAC3C;AACA,UAAI,WAAW,QAAQ;AACnB,0BAAkB,KAAK,aAAa,IAAI;AAAA,MAC5C;AACA,UAAI,WAAW,QAAQ;AACnB,0BAAkB,KAAK,aAAa;AAAA,MACxC;AACA,UAAI,WAAW,QAAQ;AACnB,0BAAkB,KAAK,OAAO;AAAA,MAClC;AACA,UAAI,WAAW,OAAO;AAClB,0BAAkB,KAAK,YAAY;AAAA,MACvC;AACA,UAAI,WAAW,OAAO;AAClB,0BAAkB,KAAK,YAAY;AAAA,MACvC;AACA,aAAO,mBAAmB,KAAK,YAAY;AAAA,IAC/C;AACA,IAAAA,WAAU,UAAU,WAAW,WAAY;AACvC,cAAQ,KAAK,MAAM,KAAK,CAAC,KAAK,OAAO,KAAK,MAAM,KAAK,CAAC,KAAK,KAAK,KAAK,MAAM,KAAK,CAAC;AAAA,IACrF;AACA,IAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,aAAO,IAAIA,WAAU,KAAK,SAAS,CAAC;AAAA,IACxC;AAKA,IAAAA,WAAU,UAAU,UAAU,SAAU,QAAQ;AAC5C,UAAI,WAAW,QAAQ;AAAE,iBAAS;AAAA,MAAI;AACtC,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,SAAS;AAClB,UAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC5B;AAKA,IAAAA,WAAU,UAAU,WAAW,SAAU,QAAQ;AAC7C,UAAI,WAAW,QAAQ;AAAE,iBAAS;AAAA,MAAI;AACtC,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,UAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,UAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC5B;AAMA,IAAAA,WAAU,UAAU,SAAS,SAAU,QAAQ;AAC3C,UAAI,WAAW,QAAQ;AAAE,iBAAS;AAAA,MAAI;AACtC,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,SAAS;AAClB,UAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC5B;AAMA,IAAAA,WAAU,UAAU,OAAO,SAAU,QAAQ;AACzC,UAAI,WAAW,QAAQ;AAAE,iBAAS;AAAA,MAAI;AACtC,aAAO,KAAK,IAAI,SAAS,MAAM;AAAA,IACnC;AAMA,IAAAA,WAAU,UAAU,QAAQ,SAAU,QAAQ;AAC1C,UAAI,WAAW,QAAQ;AAAE,iBAAS;AAAA,MAAI;AACtC,aAAO,KAAK,IAAI,SAAS,MAAM;AAAA,IACnC;AAMA,IAAAA,WAAU,UAAU,aAAa,SAAU,QAAQ;AAC/C,UAAI,WAAW,QAAQ;AAAE,iBAAS;AAAA,MAAI;AACtC,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,SAAS;AAClB,UAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC5B;AAKA,IAAAA,WAAU,UAAU,WAAW,SAAU,QAAQ;AAC7C,UAAI,WAAW,QAAQ;AAAE,iBAAS;AAAA,MAAI;AACtC,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,SAAS;AAClB,UAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC5B;AAKA,IAAAA,WAAU,UAAU,YAAY,WAAY;AACxC,aAAO,KAAK,WAAW,GAAG;AAAA,IAC9B;AAKA,IAAAA,WAAU,UAAU,OAAO,SAAU,QAAQ;AACzC,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,OAAO,IAAI,IAAI,UAAU;AAC7B,UAAI,IAAI,MAAM,IAAI,MAAM,MAAM;AAC9B,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC5B;AAKA,IAAAA,WAAU,UAAU,MAAM,SAAU,OAAO,QAAQ;AAC/C,UAAI,WAAW,QAAQ;AAAE,iBAAS;AAAA,MAAI;AACtC,UAAI,OAAO,KAAK,MAAM;AACtB,UAAI,OAAO,IAAIA,WAAU,KAAK,EAAE,MAAM;AACtC,UAAI,IAAI,SAAS;AACjB,UAAI,OAAO;AAAA,QACP,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,QAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,QAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,QAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,MACpC;AACA,aAAO,IAAIA,WAAU,IAAI;AAAA,IAC7B;AACA,IAAAA,WAAU,UAAU,YAAY,SAAU,SAAS,QAAQ;AACvD,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAG;AACvC,UAAI,WAAW,QAAQ;AAAE,iBAAS;AAAA,MAAI;AACtC,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,OAAO,MAAM;AACjB,UAAI,MAAM,CAAC,IAAI;AACf,WAAK,IAAI,KAAK,IAAI,KAAM,OAAO,WAAY,KAAK,OAAO,KAAK,EAAE,WAAU;AACpE,YAAI,KAAK,IAAI,IAAI,QAAQ;AACzB,YAAI,KAAK,IAAIA,WAAU,GAAG,CAAC;AAAA,MAC/B;AACA,aAAO;AAAA,IACX;AAIA,IAAAA,WAAU,UAAU,aAAa,WAAY;AACzC,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,IAAI,IAAI,OAAO;AACxB,aAAO,IAAIA,WAAU,GAAG;AAAA,IAC5B;AACA,IAAAA,WAAU,UAAU,gBAAgB,SAAU,SAAS;AACnD,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAG;AACvC,UAAI,MAAM,KAAK,MAAM;AACrB,UAAIC,KAAI,IAAI;AACZ,UAAI,IAAI,IAAI;AACZ,UAAI,IAAI,IAAI;AACZ,UAAI,MAAM,CAAC;AACX,UAAI,eAAe,IAAI;AACvB,aAAO,WAAW;AACd,YAAI,KAAK,IAAID,WAAU,EAAE,GAAGC,IAAG,GAAM,EAAK,CAAC,CAAC;AAC5C,aAAK,IAAI,gBAAgB;AAAA,MAC7B;AACA,aAAO;AAAA,IACX;AACA,IAAAD,WAAU,UAAU,kBAAkB,WAAY;AAC9C,UAAI,MAAM,KAAK,MAAM;AACrB,UAAIC,KAAI,IAAI;AACZ,aAAO;AAAA,QACH;AAAA,QACA,IAAID,WAAU,EAAE,IAAIC,KAAI,MAAM,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;AAAA,QACvD,IAAID,WAAU,EAAE,IAAIC,KAAI,OAAO,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;AAAA,MAC5D;AAAA,IACJ;AAIA,IAAAD,WAAU,UAAU,eAAe,SAAU,YAAY;AACrD,UAAI,KAAK,KAAK,MAAM;AACpB,UAAI,KAAK,IAAIA,WAAU,UAAU,EAAE,MAAM;AACzC,UAAI,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG;AAClC,aAAO,IAAIA,WAAU;AAAA,QACjB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,QAC9C,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,QAC9C,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,QAC9C,GAAG;AAAA,MACP,CAAC;AAAA,IACL;AAIA,IAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,aAAO,KAAK,OAAO,CAAC;AAAA,IACxB;AAIA,IAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,aAAO,KAAK,OAAO,CAAC;AAAA,IACxB;AAKA,IAAAA,WAAU,UAAU,SAAS,SAAU,GAAG;AACtC,UAAI,MAAM,KAAK,MAAM;AACrB,UAAIC,KAAI,IAAI;AACZ,UAAI,SAAS,CAAC,IAAI;AAClB,UAAI,YAAY,MAAM;AACtB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,eAAO,KAAK,IAAID,WAAU,EAAE,IAAIC,KAAI,IAAI,aAAa,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;AAAA,MACnF;AACA,aAAO;AAAA,IACX;AAIA,IAAAD,WAAU,UAAU,SAAS,SAAU,OAAO;AAC1C,aAAO,KAAK,YAAY,MAAM,IAAIA,WAAU,KAAK,EAAE,YAAY;AAAA,IACnE;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;;;AClfF,IAAI,UAAU;AAEd,IAAI,iBAAiB;AAErB,IAAI,kBAAkB;AAEtB,IAAI,kBAAkB;AAEtB,IAAI,kBAAkB;AAEtB,IAAI,kBAAkB;AAEtB,IAAI,iBAAiB;AAGrB,IAAI,eAAe,CAAC;AAAA,EAClB,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,GAAG;AAAA,EACD,OAAO;AAAA,EACP,SAAS;AACX,CAAC;AAGD,SAAS,MAAM,MAAM;AACnB,MAAI,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK;AACb,MAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAC1B,SAAO;AAAA,IACL,GAAG,IAAI,IAAI;AAAA,IACX,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT;AACF;AAIA,SAAS,MAAM,OAAO;AACpB,MAAI,IAAI,MAAM,GACV,IAAI,MAAM,GACV,IAAI,MAAM;AACd,SAAO,IAAI,OAAO,SAAS,GAAG,GAAG,GAAG,KAAK,CAAC;AAC5C;AAKA,SAAS,IAAI,MAAM,MAAM,QAAQ;AAC/B,MAAI,IAAI,SAAS;AACjB,MAAI,MAAM;AAAA,IACR,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,EAClC;AACA,SAAO;AACT;AAEA,SAAS,OAAO,KAAK,GAAG,OAAO;AAC7B,MAAI;AAEJ,MAAI,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC,KAAK,KAAK;AACvD,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU;AAAA,EAChF,OAAO;AACL,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU;AAAA,EAChF;AAEA,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT,WAAW,OAAO,KAAK;AACrB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,SAAS,cAAc,KAAK,GAAG,OAAO;AAEpC,MAAI,IAAI,MAAM,KAAK,IAAI,MAAM,GAAG;AAC9B,WAAO,IAAI;AAAA,EACb;AAEA,MAAI;AAEJ,MAAI,OAAO;AACT,iBAAa,IAAI,IAAI,iBAAiB;AAAA,EACxC,WAAW,MAAM,gBAAgB;AAC/B,iBAAa,IAAI,IAAI;AAAA,EACvB,OAAO;AACL,iBAAa,IAAI,IAAI,kBAAkB;AAAA,EACzC;AAGA,MAAI,aAAa,GAAG;AAClB,iBAAa;AAAA,EACf;AAGA,MAAI,SAAS,MAAM,mBAAmB,aAAa,KAAK;AACtD,iBAAa;AAAA,EACf;AAEA,MAAI,aAAa,MAAM;AACrB,iBAAa;AAAA,EACf;AAEA,SAAO,OAAO,WAAW,QAAQ,CAAC,CAAC;AACrC;AAEA,SAAS,SAAS,KAAK,GAAG,OAAO;AAC/B,MAAI;AAEJ,MAAI,OAAO;AACT,YAAQ,IAAI,IAAI,kBAAkB;AAAA,EACpC,OAAO;AACL,YAAQ,IAAI,IAAI,kBAAkB;AAAA,EACpC;AAEA,MAAI,QAAQ,GAAG;AACb,YAAQ;AAAA,EACV;AAEA,SAAO,OAAO,MAAM,QAAQ,CAAC,CAAC;AAChC;AAEA,SAAS,SAAS,OAAO;AACvB,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAChF,MAAI,WAAW,CAAC;AAChB,MAAI,SAAS,WAAW,KAAK;AAE7B,WAAS,IAAI,iBAAiB,IAAI,GAAG,KAAK,GAAG;AAC3C,QAAI,MAAM,MAAM,MAAM;AACtB,QAAI,cAAc,MAAM,WAAW;AAAA,MACjC,GAAG,OAAO,KAAK,GAAG,IAAI;AAAA,MACtB,GAAG,cAAc,KAAK,GAAG,IAAI;AAAA,MAC7B,GAAG,SAAS,KAAK,GAAG,IAAI;AAAA,IAC1B,CAAC,CAAC;AACF,aAAS,KAAK,WAAW;AAAA,EAC3B;AAEA,WAAS,KAAK,MAAM,MAAM,CAAC;AAE3B,WAAS,KAAK,GAAG,MAAM,gBAAgB,MAAM,GAAG;AAC9C,QAAI,OAAO,MAAM,MAAM;AAEvB,QAAI,eAAe,MAAM,WAAW;AAAA,MAClC,GAAG,OAAO,MAAM,EAAE;AAAA,MAClB,GAAG,cAAc,MAAM,EAAE;AAAA,MACzB,GAAG,SAAS,MAAM,EAAE;AAAA,IACtB,CAAC,CAAC;AAEF,aAAS,KAAK,YAAY;AAAA,EAC5B;AAGA,MAAI,KAAK,UAAU,QAAQ;AACzB,WAAO,aAAa,IAAI,SAAU,OAAO;AACvC,UAAI,QAAQ,MAAM,OACd,UAAU,MAAM;AACpB,UAAI,kBAAkB,MAAM,IAAI,WAAW,KAAK,mBAAmB,SAAS,GAAG,WAAW,SAAS,KAAK,CAAC,GAAG,UAAU,GAAG,CAAC;AAC1H,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,IAAI,sBAAsB;AAAA,EACxB,KAAK;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AACR;AACA,IAAI,iBAAiB,CAAC;AACtB,IAAI,qBAAqB,CAAC;AAC1B,OAAO,KAAK,mBAAmB,EAAE,QAAQ,SAAU,KAAK;AACtD,iBAAe,GAAG,IAAI,SAAS,oBAAoB,GAAG,CAAC;AACvD,iBAAe,GAAG,EAAE,UAAU,eAAe,GAAG,EAAE,CAAC;AAEnD,qBAAmB,GAAG,IAAI,SAAS,oBAAoB,GAAG,GAAG;AAAA,IAC3D,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB,CAAC;AACD,qBAAmB,GAAG,EAAE,UAAU,mBAAmB,GAAG,EAAE,CAAC;AAC7D,CAAC;AACD,IAAI,MAAM,eAAe;AACzB,IAAI,UAAU,eAAe;AAC7B,IAAI,OAAO,eAAe;AAC1B,IAAI,SAAS,eAAe;AAC5B,IAAI,SAAS,eAAe;AAC5B,IAAI,OAAO,eAAe;AAC1B,IAAI,QAAQ,eAAe;AAC3B,IAAI,OAAO,eAAe;AAC1B,IAAI,OAAO,eAAe;AAC1B,IAAI,WAAW,eAAe;AAC9B,IAAI,SAAS,eAAe;AAC5B,IAAI,UAAU,eAAe;AAC7B,IAAI,OAAO,eAAe;;;ACvO1B,IAAI,aAAa,OAAO,aAAa;AAK9B,IAAI,uBAAuB,SAASE,wBAAuB;AAChE,SAAO,OAAO,YAAY;AAAA,IACxB,WAAW,IAAI,SAAS;AAAA,IACxB,eAAe,IAAI,EAAE;AAAA,IACrB,KAAK,IAAI;AAAA,EACX,CAAC;AACH;;;ACZO,SAAS,YAAY;AAC1B,SAAO,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS;AAChF;AAEA,SAAS,SAAS,MAAM,GAAG;AACzB,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AAGA,MAAI,KAAK,UAAU;AACjB,WAAO,KAAK,SAAS,CAAC;AAAA,EACxB;AAEA,SAAO;AACT;AAEA,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,iBAAiB,oBAAI,IAAI;AAE7B,SAAS,UAAU;AACjB,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC,GAC5E,OAAO,KAAK;AAEhB,MAAI,MAAM;AACR,WAAO,KAAK,WAAW,OAAO,IAAI,OAAO,QAAQ,OAAO,IAAI;AAAA,EAC9D;AAEA,SAAO;AACT;AAEA,SAAS,aAAa,QAAQ;AAC5B,MAAI,OAAO,UAAU;AACnB,WAAO,OAAO;AAAA,EAChB;AAEA,MAAI,OAAO,SAAS,cAAc,MAAM;AACxC,SAAO,QAAQ,SAAS;AAC1B;AAEA,SAAS,SAAS,SAAS;AACzB,MAAI,YAAY,SAAS;AACvB,WAAO;AAAA,EACT;AAEA,SAAO,UAAU,YAAY;AAC/B;AAMA,SAAS,WAAW,WAAW;AAC7B,SAAO,MAAM,MAAM,eAAe,IAAI,SAAS,KAAK,WAAW,QAAQ,EAAE,OAAO,SAAU,MAAM;AAC9F,WAAO,KAAK,YAAY;AAAA,EAC1B,CAAC;AACH;AAEO,SAAS,UAAU,KAAK;AAC7B,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAElF,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,OAAO,KACb,UAAU,OAAO;AACrB,MAAI,YAAY,SAAS,cAAc,OAAO;AAC9C,YAAU,aAAa,cAAc,SAAS,OAAO,CAAC;AAEtD,MAAI,OAAO,IAAI,OAAO;AACpB,cAAU,QAAQ,IAAI;AAAA,EACxB;AAEA,YAAU,YAAY;AACtB,MAAI,YAAY,aAAa,MAAM;AACnC,MAAI,aAAa,UAAU;AAE3B,MAAI,SAAS;AAEX,QAAI,YAAY,SAAS;AACvB,UAAI,aAAa,WAAW,SAAS,EAAE,OAAO,SAAU,MAAM;AAC5D,eAAO,CAAC,WAAW,cAAc,EAAE,SAAS,KAAK,aAAa,YAAY,CAAC;AAAA,MAC7E,CAAC;AAED,UAAI,WAAW,QAAQ;AACrB,kBAAU,aAAa,WAAW,WAAW,WAAW,SAAS,CAAC,EAAE,WAAW;AAC/E,eAAO;AAAA,MACT;AAAA,IACF;AAGA,cAAU,aAAa,WAAW,UAAU;AAAA,EAC9C,OAAO;AACL,cAAU,YAAY,SAAS;AAAA,EACjC;AAEA,SAAO;AACT;AAEA,SAAS,cAAc,KAAK;AAC1B,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,MAAI,YAAY,aAAa,MAAM;AACnC,SAAO,WAAW,SAAS,EAAE,KAAK,SAAU,MAAM;AAChD,WAAO,KAAK,aAAa,QAAQ,MAAM,CAAC,MAAM;AAAA,EAChD,CAAC;AACH;AAeA,SAAS,kBAAkB,WAAW,QAAQ;AAC5C,MAAI,sBAAsB,eAAe,IAAI,SAAS;AAEtD,MAAI,CAAC,uBAAuB,CAAC,SAAS,UAAU,mBAAmB,GAAG;AACpE,QAAI,mBAAmB,UAAU,IAAI,MAAM;AAC3C,QAAI,aAAa,iBAAiB;AAClC,mBAAe,IAAI,WAAW,UAAU;AACxC,cAAU,YAAY,gBAAgB;AAAA,EACxC;AACF;AASO,SAAS,UAAU,KAAK,KAAK;AAClC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,MAAI,YAAY,aAAa,MAAM;AAEnC,oBAAkB,WAAW,MAAM;AACnC,MAAI,YAAY,cAAc,KAAK,MAAM;AAEzC,MAAI,WAAW;AACb,QAAI,OAAO,OAAO,OAAO,IAAI,SAAS,UAAU,UAAU,OAAO,IAAI,OAAO;AAC1E,gBAAU,QAAQ,OAAO,IAAI;AAAA,IAC/B;AAEA,QAAI,UAAU,cAAc,KAAK;AAC/B,gBAAU,YAAY;AAAA,IACxB;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,UAAU,KAAK,MAAM;AACnC,UAAQ,aAAa,QAAQ,MAAM,GAAG,GAAG;AACzC,SAAO;AACT;;;AClKA,SAAS,cAAc,QAAQ;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,OAAO,UAAU,CAAC,CAAC,IAAI,CAAC;AAAG,QAAI,UAAU,OAAO,KAAK,MAAM;AAAG,QAAI,OAAO,OAAO,0BAA0B,YAAY;AAAE,gBAAU,QAAQ,OAAO,OAAO,sBAAsB,MAAM,EAAE,OAAO,SAAU,KAAK;AAAE,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MAAY,CAAC,CAAC;AAAA,IAAG;AAAE,YAAQ,QAAQ,SAAU,KAAK;AAAE,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAQ;AAExe,SAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,EAAG,OAAO;AAAE,QAAI,GAAG,IAAI;AAAA,EAAO;AAAE,SAAO;AAAK;AAMzM,SAAS,KAAK,OAAO,SAAS;AAEnC,MAA6C,CAAC,SAAS,YAAY,QAAW;AAC5E,YAAQ,MAAM,YAAY,OAAO,OAAO,CAAC;AAAA,EAC3C;AACF;AACO,SAAS,QAAQ,OAAO,SAAS;AACtC,OAAK,OAAO,2BAA2B,OAAO,OAAO,CAAC;AACxD;AASO,SAAS,iBAAiB,QAAQ;AACvC,SAAO,OAAO,WAAW,YAAY,OAAO,OAAO,SAAS,YAAY,OAAO,OAAO,UAAU,aAAa,OAAO,OAAO,SAAS,YAAY,OAAO,OAAO,SAAS;AACzK;AAoBO,SAASC,UAAS,MAAM,KAAK,WAAW;AAC7C,MAAI,CAAC,WAAW;AACd,WAAO,EAAE,KAAK,KAAK,cAAc;AAAA,MAC/B;AAAA,IACF,GAAG,KAAK,KAAK,IAAI,KAAK,YAAY,CAAC,GAAG,IAAI,SAAU,OAAO,OAAO;AAChE,aAAOA,UAAS,OAAO,GAAG,OAAO,KAAK,GAAG,EAAE,OAAO,KAAK,KAAK,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IAChF,CAAC,CAAC;AAAA,EACJ;AAEA,SAAO,EAAE,KAAK,KAAK,cAAc;AAAA,IAC/B;AAAA,EACF,GAAG,WAAW,KAAK,KAAK,IAAI,KAAK,YAAY,CAAC,GAAG,IAAI,SAAU,OAAO,OAAO;AAC3E,WAAOA,UAAS,OAAO,GAAG,OAAO,KAAK,GAAG,EAAE,OAAO,KAAK,KAAK,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,EAChF,CAAC,CAAC;AACJ;AACO,SAAS,kBAAkB,cAAc;AAE9C,SAAO,SAAc,YAAY,EAAE,CAAC;AACtC;AACO,SAAS,uBAAuB,cAAc;AACnD,MAAI,CAAC,cAAc;AACjB,WAAO,CAAC;AAAA,EACV;AAEA,SAAO,MAAM,QAAQ,YAAY,IAAI,eAAe,CAAC,YAAY;AACnE;AAGO,IAAI,eAAe;AAAA,EACxB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,eAAe;AAAA,EACf,WAAW;AACb;AACO,IAAI,aAAa;AAExB,SAAS,QAAQ,KAAK;AACpB,SAAO,OAAO,IAAI,eAAe,IAAI,YAAY;AACnD;AAMA,SAAS,SAAS,KAAK;AACrB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ,GAAG,aAAa;AACjC;AAMA,SAAS,cAAc,KAAK;AAC1B,SAAO,SAAS,GAAG,IAAI,QAAQ,GAAG,IAAI;AACxC;AAEO,IAAI,kBAAkB,SAASC,mBAAkB;AACtD,MAAI,wBAAwB,qBAAqB,GAC7C,YAAY,sBAAsB,WAClC,MAAM,sBAAsB;AAEhC,MAAI,WAAW,mBAAmB;AAClC,MAAI,iBAAiB;AAErB,MAAI,WAAW;AACb,qBAAiB,eAAe,QAAQ,YAAY,UAAU,KAAK;AAAA,EACrE;AAEA,WAAS,WAAY;AACnB,QAAI,CAAC,UAAU,GAAG;AAChB;AAAA,IACF;AAEA,QAAI,MAAM,SAAS,MAAM;AACzB,QAAI,aAAa,cAAc,GAAG;AAClC,cAAU,gBAAgB,yBAAyB;AAAA,MACjD,SAAS;AAAA,MACT,KAAK,IAAI;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC;AACH;;;ACrIA,IAAI,YAAY,CAAC,QAAQ,gBAAgB,gBAAgB;AAEzD,SAAS,yBAAyB,QAAQ,UAAU;AAAE,MAAI,UAAU,KAAM,QAAO,CAAC;AAAG,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAAG,MAAI,KAAK;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAAG,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAAE,YAAM,iBAAiB,CAAC;AAAG,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAAU,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAAG;AAAA,EAAE;AAAE,SAAO;AAAQ;AAE3e,SAAS,8BAA8B,QAAQ,UAAU;AAAE,MAAI,UAAU,KAAM,QAAO,CAAC;AAAG,MAAI,SAAS,CAAC;AAAG,MAAI,aAAa,OAAO,KAAK,MAAM;AAAG,MAAI,KAAK;AAAG,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAAE,UAAM,WAAW,CAAC;AAAG,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAAG;AAAE,SAAO;AAAQ;AAElT,SAASC,eAAc,QAAQ;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,OAAO,UAAU,CAAC,CAAC,IAAI,CAAC;AAAG,QAAI,UAAU,OAAO,KAAK,MAAM;AAAG,QAAI,OAAO,OAAO,0BAA0B,YAAY;AAAE,gBAAU,QAAQ,OAAO,OAAO,sBAAsB,MAAM,EAAE,OAAO,SAAU,KAAK;AAAE,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MAAY,CAAC,CAAC;AAAA,IAAG;AAAE,YAAQ,QAAQ,SAAU,KAAK;AAAE,MAAAC,iBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAQ;AAExe,SAASA,iBAAgB,KAAK,KAAK,OAAO;AAAE,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,EAAG,OAAO;AAAE,QAAI,GAAG,IAAI;AAAA,EAAO;AAAE,SAAO;AAAK;AAIhN,IAAI,sBAAsB,SAAS;AAAA,EACjC,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,YAAY;AACd,CAAC;AAED,SAAS,iBAAiB,MAAM;AAC9B,MAAI,eAAe,KAAK,cACpB,iBAAiB,KAAK;AAC1B,sBAAoB,eAAe;AACnC,sBAAoB,iBAAiB,kBAAkB,kBAAkB,YAAY;AACrF,sBAAoB,aAAa,CAAC,CAAC;AACrC;AAEA,SAAS,mBAAmB;AAC1B,SAAOD,eAAc,CAAC,GAAG,mBAAmB;AAC9C;AAEA,IAAI,WAAW,SAASE,UAAS,OAAO,SAAS;AAC/C,MAAI,uBAAuBF,eAAc,CAAC,GAAG,OAAO,QAAQ,KAAK,GAC7D,OAAO,qBAAqB,MAC5B,eAAe,qBAAqB,cACpC,iBAAiB,qBAAqB,gBACtC,YAAY,yBAAyB,sBAAsB,SAAS;AAExE,MAAI,SAAS;AAEb,MAAI,cAAc;AAChB,aAAS;AAAA,MACP;AAAA,MACA,gBAAgB,kBAAkB,kBAAkB,YAAY;AAAA,IAClE;AAAA,EACF;AAEA,UAAQ,iBAAiB,IAAI,GAAG,0CAA0C,OAAO,IAAI,CAAC;AAEtF,MAAI,CAAC,iBAAiB,IAAI,GAAG;AAC3B,WAAO;AAAA,EACT;AAEA,MAAI,SAAS;AAEb,MAAI,UAAU,OAAO,OAAO,SAAS,YAAY;AAC/C,aAASA,eAAc,CAAC,GAAG,QAAQ;AAAA,MACjC,MAAM,OAAO,KAAK,OAAO,cAAc,OAAO,cAAc;AAAA,IAC9D,CAAC;AAAA,EACH;AAEA,SAAOG,UAAS,OAAO,MAAM,OAAO,OAAO,OAAO,IAAI,GAAGH,eAAc,CAAC,GAAG,WAAW;AAAA,IACpF,aAAa,OAAO;AAAA,IACpB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,eAAe;AAAA,EACjB,CAAC,CAAC;AACJ;AAEA,SAAS,QAAQ;AAAA,EACf,MAAM;AAAA,EACN,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,WAAW;AACb;AACA,SAAS,eAAe;AACxB,SAAS,cAAc;AACvB,SAAS,mBAAmB;AAC5B,SAAS,mBAAmB;AAC5B,IAAO,mBAAQ;;;AC/Ef,SAAS,eAAe,KAAK,GAAG;AAAE,SAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAAG;AAE7J,SAAS,mBAAmB;AAAE,QAAM,IAAI,UAAU,2IAA2I;AAAG;AAEhM,SAAS,4BAA4B,GAAG,QAAQ;AAAE,MAAI,CAAC,EAAG;AAAQ,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAAG,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AAAM,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AAAG,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AAAG;AAE/Z,SAAS,kBAAkB,KAAK,KAAK;AAAE,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAAQ,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,KAAK;AAAE,SAAK,CAAC,IAAI,IAAI,CAAC;AAAA,EAAG;AAAE,SAAO;AAAM;AAEtL,SAAS,sBAAsB,KAAK,GAAG;AAAE,MAAI,KAAK,OAAO,OAAO,OAAO,OAAO,WAAW,eAAe,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AAAG,MAAI,MAAM,KAAM;AAAQ,MAAI,OAAO,CAAC;AAAG,MAAI,KAAK;AAAM,MAAI,KAAK;AAAO,MAAI,IAAI;AAAI,MAAI;AAAE,SAAK,KAAK,GAAG,KAAK,GAAG,GAAG,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAAE,WAAK,KAAK,GAAG,KAAK;AAAG,UAAI,KAAK,KAAK,WAAW,EAAG;AAAA,IAAO;AAAA,EAAE,SAAS,KAAK;AAAE,SAAK;AAAM,SAAK;AAAA,EAAK,UAAE;AAAU,QAAI;AAAE,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK,KAAM,IAAG,QAAQ,EAAE;AAAA,IAAG,UAAE;AAAU,UAAI,GAAI,OAAM;AAAA,IAAI;AAAA,EAAE;AAAE,SAAO;AAAM;AAEhgB,SAAS,gBAAgB,KAAK;AAAE,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AAAK;AAI7D,SAAS,gBAAgB,cAAc;AAC5C,MAAI,wBAAwB,uBAAuB,YAAY,GAC3D,yBAAyB,eAAe,uBAAuB,CAAC,GAChE,eAAe,uBAAuB,CAAC,GACvC,iBAAiB,uBAAuB,CAAC;AAE7C,SAAO,iBAAQ,iBAAiB;AAAA,IAC9B;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACO,SAAS,kBAAkB;AAChC,MAAI,SAAS,iBAAQ,iBAAiB;AAEtC,MAAI,CAAC,OAAO,YAAY;AACtB,WAAO,OAAO;AAAA,EAChB;AAEA,SAAO,CAAC,OAAO,cAAc,OAAO,cAAc;AACpD;;;AC/BO,IAAI,eAAe,gBAAgB;AAAA,EACxC,MAAM;AAAA,EACN,OAAO,SAAS,QAAQ;AACtB,oBAAgB;AAChB,WAAO,WAAY;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;;;ACVD,IAAII,aAAY,CAAC,SAAS,QAAQ,QAAQ,UAAU,YAAY,gBAAgB,SAAS;AAGzF,SAASC,gBAAe,KAAK,GAAG;AAAE,SAAOC,iBAAgB,GAAG,KAAKC,uBAAsB,KAAK,CAAC,KAAKC,6BAA4B,KAAK,CAAC,KAAKC,kBAAiB;AAAG;AAE7J,SAASA,oBAAmB;AAAE,QAAM,IAAI,UAAU,2IAA2I;AAAG;AAEhM,SAASD,6BAA4B,GAAG,QAAQ;AAAE,MAAI,CAAC,EAAG;AAAQ,MAAI,OAAO,MAAM,SAAU,QAAOE,mBAAkB,GAAG,MAAM;AAAG,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AAAM,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AAAG,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAOA,mBAAkB,GAAG,MAAM;AAAG;AAE/Z,SAASA,mBAAkB,KAAK,KAAK;AAAE,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAAQ,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,KAAK;AAAE,SAAK,CAAC,IAAI,IAAI,CAAC;AAAA,EAAG;AAAE,SAAO;AAAM;AAEtL,SAASH,uBAAsB,KAAK,GAAG;AAAE,MAAI,KAAK,OAAO,OAAO,OAAO,OAAO,WAAW,eAAe,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AAAG,MAAI,MAAM,KAAM;AAAQ,MAAI,OAAO,CAAC;AAAG,MAAI,KAAK;AAAM,MAAI,KAAK;AAAO,MAAI,IAAI;AAAI,MAAI;AAAE,SAAK,KAAK,GAAG,KAAK,GAAG,GAAG,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAAE,WAAK,KAAK,GAAG,KAAK;AAAG,UAAI,KAAK,KAAK,WAAW,EAAG;AAAA,IAAO;AAAA,EAAE,SAAS,KAAK;AAAE,SAAK;AAAM,SAAK;AAAA,EAAK,UAAE;AAAU,QAAI;AAAE,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK,KAAM,IAAG,QAAQ,EAAE;AAAA,IAAG,UAAE;AAAU,UAAI,GAAI,OAAM;AAAA,IAAI;AAAA,EAAE;AAAE,SAAO;AAAM;AAEhgB,SAASD,iBAAgB,KAAK;AAAE,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AAAK;AAEpE,SAASK,eAAc,QAAQ;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,OAAO,UAAU,CAAC,CAAC,IAAI,CAAC;AAAG,QAAI,UAAU,OAAO,KAAK,MAAM;AAAG,QAAI,OAAO,OAAO,0BAA0B,YAAY;AAAE,gBAAU,QAAQ,OAAO,OAAO,sBAAsB,MAAM,EAAE,OAAO,SAAU,KAAK;AAAE,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MAAY,CAAC,CAAC;AAAA,IAAG;AAAE,YAAQ,QAAQ,SAAU,KAAK;AAAE,MAAAC,iBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAQ;AAExe,SAASA,iBAAgB,KAAK,KAAK,OAAO;AAAE,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,EAAG,OAAO;AAAE,QAAI,GAAG,IAAI;AAAA,EAAO;AAAE,SAAO;AAAK;AAEhN,SAASC,0BAAyB,QAAQ,UAAU;AAAE,MAAI,UAAU,KAAM,QAAO,CAAC;AAAG,MAAI,SAASC,+BAA8B,QAAQ,QAAQ;AAAG,MAAI,KAAK;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAAG,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAAE,YAAM,iBAAiB,CAAC;AAAG,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAAU,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAAG;AAAA,EAAE;AAAE,SAAO;AAAQ;AAE3e,SAASA,+BAA8B,QAAQ,UAAU;AAAE,MAAI,UAAU,KAAM,QAAO,CAAC;AAAG,MAAI,SAAS,CAAC;AAAG,MAAI,aAAa,OAAO,KAAK,MAAM;AAAG,MAAI,KAAK;AAAG,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAAE,UAAM,WAAW,CAAC;AAAG,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAAG;AAAE,SAAO;AAAQ;AASlT,gBAAgB,KAAK,OAAO;AAE5B,IAAI,OAAO,SAASC,MAAK,OAAO,SAAS;AACvC,MAAI;AAEJ,MAAI,uBAAuBJ,eAAc,CAAC,GAAG,OAAO,QAAQ,KAAK,GAC7D,MAAM,qBAAqB,OAAO,GAClC,OAAO,qBAAqB,MAC5B,OAAO,qBAAqB,MAC5B,SAAS,qBAAqB,QAC9B,WAAW,qBAAqB,UAChC,eAAe,qBAAqB,cACpC,UAAU,qBAAqB,SAC/B,YAAYE,0BAAyB,sBAAsBT,UAAS;AAExE,MAAI,wBAAwB,qBAAqB,GAC7C,YAAY,sBAAsB,WAClC,gBAAgB,sBAAsB;AAE1C,MAAI,YAAY,YAAY,CAAC,GAAGQ,iBAAgB,WAAW,cAAc,OAAO,CAAC,CAAC,cAAc,KAAK,GAAGA,iBAAgB,WAAW,UAAU,OAAO,IAAI,GAAGA,iBAAgB,WAAW,GAAG,OAAO,UAAU,OAAO,GAAG,EAAE,OAAO,KAAK,IAAI,GAAG,QAAQ,KAAK,IAAI,CAAC,GAAGA,iBAAgB,WAAW,GAAG,OAAO,UAAU,OAAO,OAAO,GAAG,CAAC,CAAC,QAAQ,KAAK,SAAS,SAAS,GAAG;AAClW,MAAI,eAAe;AAEnB,MAAI,iBAAiB,UAAa,SAAS;AACzC,mBAAe;AAAA,EACjB;AAEA,MAAI,WAAW,SAAS;AAAA,IACtB,aAAa,UAAU,OAAO,QAAQ,MAAM;AAAA,IAC5C,WAAW,UAAU,OAAO,QAAQ,MAAM;AAAA,EAC5C,IAAI;AAEJ,MAAI,wBAAwB,uBAAuB,YAAY,GAC3D,yBAAyBP,gBAAe,uBAAuB,CAAC,GAChE,eAAe,uBAAuB,CAAC,GACvC,iBAAiB,uBAAuB,CAAC;AAE7C,SAAO,YAAa,QAAQM,eAAc;AAAA,IACxC,QAAQ;AAAA,IACR,cAAc,KAAK;AAAA,EACrB,GAAG,WAAW;AAAA,IACZ,WAAW;AAAA,IACX,SAAS,CAAC,UAAU,GAAG;AAAA,IACvB,YAAY;AAAA,EACd,CAAC,GAAG,CAAC,YAAa,kBAAS;AAAA,IACzB,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,SAAS;AAAA,EACX,GAAG,IAAI,GAAG,YAAa,cAAc,MAAM,IAAI,CAAC,CAAC;AACnD;AAEA,KAAK,QAAQ;AAAA,EACX,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,cAAc,CAAC,QAAQ,KAAK;AAC9B;AACA,KAAK,cAAc;AACnB,KAAK,eAAe;AACpB,KAAK,kBAAkB;AACvB,KAAK,kBAAkB;AACvB,IAAO,mBAAQ;", "names": ["h", "h", "TinyColor", "h", "useInjectIconContext", "generate", "useInsertStyles", "_objectSpread", "_defineProperty", "IconBase", "generate", "_excluded", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "_arrayLikeToArray", "_objectSpread", "_defineProperty", "_objectWithoutProperties", "_objectWithoutPropertiesLoose", "Icon"]}