import {
  require_xml
} from "./chunk-3VALU7CI.js";
import {
  require_codemirror
} from "./chunk-GRUOQSZ6.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/codemirror@5.65.19/node_modules/codemirror/mode/meta.js
var require_meta = __commonJS({
  "node_modules/.pnpm/codemirror@5.65.19/node_modules/codemirror/mode/meta.js"(exports, module) {
    (function(mod) {
      if (typeof exports == "object" && typeof module == "object")
        mod(require_codemirror());
      else if (typeof define == "function" && define.amd)
        define(["../lib/codemirror"], mod);
      else
        mod(CodeMirror);
    })(function(CodeMirror2) {
      "use strict";
      CodeMirror2.modeInfo = [
        { name: "APL", mime: "text/apl", mode: "apl", ext: ["dyalog", "apl"] },
        { name: "PGP", mimes: ["application/pgp", "application/pgp-encrypted", "application/pgp-keys", "application/pgp-signature"], mode: "asciiarmor", ext: ["asc", "pgp", "sig"] },
        { name: "ASN.1", mime: "text/x-ttcn-asn", mode: "asn.1", ext: ["asn", "asn1"] },
        { name: "Asterisk", mime: "text/x-asterisk", mode: "asterisk", file: /^extensions\.conf$/i },
        { name: "Brainfuck", mime: "text/x-brainfuck", mode: "brainfuck", ext: ["b", "bf"] },
        { name: "C", mime: "text/x-csrc", mode: "clike", ext: ["c", "h", "ino"] },
        { name: "C++", mime: "text/x-c++src", mode: "clike", ext: ["cpp", "c++", "cc", "cxx", "hpp", "h++", "hh", "hxx"], alias: ["cpp"] },
        { name: "Cobol", mime: "text/x-cobol", mode: "cobol", ext: ["cob", "cpy", "cbl"] },
        { name: "C#", mime: "text/x-csharp", mode: "clike", ext: ["cs"], alias: ["csharp", "cs"] },
        { name: "Clojure", mime: "text/x-clojure", mode: "clojure", ext: ["clj", "cljc", "cljx"] },
        { name: "ClojureScript", mime: "text/x-clojurescript", mode: "clojure", ext: ["cljs"] },
        { name: "Closure Stylesheets (GSS)", mime: "text/x-gss", mode: "css", ext: ["gss"] },
        { name: "CMake", mime: "text/x-cmake", mode: "cmake", ext: ["cmake", "cmake.in"], file: /^CMakeLists\.txt$/ },
        { name: "CoffeeScript", mimes: ["application/vnd.coffeescript", "text/coffeescript", "text/x-coffeescript"], mode: "coffeescript", ext: ["coffee"], alias: ["coffee", "coffee-script"] },
        { name: "Common Lisp", mime: "text/x-common-lisp", mode: "commonlisp", ext: ["cl", "lisp", "el"], alias: ["lisp"] },
        { name: "Cypher", mime: "application/x-cypher-query", mode: "cypher", ext: ["cyp", "cypher"] },
        { name: "Cython", mime: "text/x-cython", mode: "python", ext: ["pyx", "pxd", "pxi"] },
        { name: "Crystal", mime: "text/x-crystal", mode: "crystal", ext: ["cr"] },
        { name: "CSS", mime: "text/css", mode: "css", ext: ["css"] },
        { name: "CQL", mime: "text/x-cassandra", mode: "sql", ext: ["cql"] },
        { name: "D", mime: "text/x-d", mode: "d", ext: ["d"] },
        { name: "Dart", mimes: ["application/dart", "text/x-dart"], mode: "dart", ext: ["dart"] },
        { name: "diff", mime: "text/x-diff", mode: "diff", ext: ["diff", "patch"] },
        { name: "Django", mime: "text/x-django", mode: "django" },
        { name: "Dockerfile", mime: "text/x-dockerfile", mode: "dockerfile", file: /^Dockerfile$/ },
        { name: "DTD", mime: "application/xml-dtd", mode: "dtd", ext: ["dtd"] },
        { name: "Dylan", mime: "text/x-dylan", mode: "dylan", ext: ["dylan", "dyl", "intr"] },
        { name: "EBNF", mime: "text/x-ebnf", mode: "ebnf" },
        { name: "ECL", mime: "text/x-ecl", mode: "ecl", ext: ["ecl"] },
        { name: "edn", mime: "application/edn", mode: "clojure", ext: ["edn"] },
        { name: "Eiffel", mime: "text/x-eiffel", mode: "eiffel", ext: ["e"] },
        { name: "Elm", mime: "text/x-elm", mode: "elm", ext: ["elm"] },
        { name: "Embedded JavaScript", mime: "application/x-ejs", mode: "htmlembedded", ext: ["ejs"] },
        { name: "Embedded Ruby", mime: "application/x-erb", mode: "htmlembedded", ext: ["erb"] },
        { name: "Erlang", mime: "text/x-erlang", mode: "erlang", ext: ["erl"] },
        { name: "Esper", mime: "text/x-esper", mode: "sql" },
        { name: "Factor", mime: "text/x-factor", mode: "factor", ext: ["factor"] },
        { name: "FCL", mime: "text/x-fcl", mode: "fcl" },
        { name: "Forth", mime: "text/x-forth", mode: "forth", ext: ["forth", "fth", "4th"] },
        { name: "Fortran", mime: "text/x-fortran", mode: "fortran", ext: ["f", "for", "f77", "f90", "f95"] },
        { name: "F#", mime: "text/x-fsharp", mode: "mllike", ext: ["fs"], alias: ["fsharp"] },
        { name: "Gas", mime: "text/x-gas", mode: "gas", ext: ["s"] },
        { name: "Gherkin", mime: "text/x-feature", mode: "gherkin", ext: ["feature"] },
        { name: "GitHub Flavored Markdown", mime: "text/x-gfm", mode: "gfm", file: /^(readme|contributing|history)\.md$/i },
        { name: "Go", mime: "text/x-go", mode: "go", ext: ["go"] },
        { name: "Groovy", mime: "text/x-groovy", mode: "groovy", ext: ["groovy", "gradle"], file: /^Jenkinsfile$/ },
        { name: "HAML", mime: "text/x-haml", mode: "haml", ext: ["haml"] },
        { name: "Haskell", mime: "text/x-haskell", mode: "haskell", ext: ["hs"] },
        { name: "Haskell (Literate)", mime: "text/x-literate-haskell", mode: "haskell-literate", ext: ["lhs"] },
        { name: "Haxe", mime: "text/x-haxe", mode: "haxe", ext: ["hx"] },
        { name: "HXML", mime: "text/x-hxml", mode: "haxe", ext: ["hxml"] },
        { name: "ASP.NET", mime: "application/x-aspx", mode: "htmlembedded", ext: ["aspx"], alias: ["asp", "aspx"] },
        { name: "HTML", mime: "text/html", mode: "htmlmixed", ext: ["html", "htm", "handlebars", "hbs"], alias: ["xhtml"] },
        { name: "HTTP", mime: "message/http", mode: "http" },
        { name: "IDL", mime: "text/x-idl", mode: "idl", ext: ["pro"] },
        { name: "Pug", mime: "text/x-pug", mode: "pug", ext: ["jade", "pug"], alias: ["jade"] },
        { name: "Java", mime: "text/x-java", mode: "clike", ext: ["java"] },
        { name: "Java Server Pages", mime: "application/x-jsp", mode: "htmlembedded", ext: ["jsp"], alias: ["jsp"] },
        {
          name: "JavaScript",
          mimes: ["text/javascript", "text/ecmascript", "application/javascript", "application/x-javascript", "application/ecmascript"],
          mode: "javascript",
          ext: ["js"],
          alias: ["ecmascript", "js", "node"]
        },
        { name: "JSON", mimes: ["application/json", "application/x-json"], mode: "javascript", ext: ["json", "map"], alias: ["json5"] },
        { name: "JSON-LD", mime: "application/ld+json", mode: "javascript", ext: ["jsonld"], alias: ["jsonld"] },
        { name: "JSX", mime: "text/jsx", mode: "jsx", ext: ["jsx"] },
        { name: "Jinja2", mime: "text/jinja2", mode: "jinja2", ext: ["j2", "jinja", "jinja2"] },
        { name: "Julia", mime: "text/x-julia", mode: "julia", ext: ["jl"], alias: ["jl"] },
        { name: "Kotlin", mime: "text/x-kotlin", mode: "clike", ext: ["kt"] },
        { name: "LESS", mime: "text/x-less", mode: "css", ext: ["less"] },
        { name: "LiveScript", mime: "text/x-livescript", mode: "livescript", ext: ["ls"], alias: ["ls"] },
        { name: "Lua", mime: "text/x-lua", mode: "lua", ext: ["lua"] },
        { name: "Markdown", mime: "text/x-markdown", mode: "markdown", ext: ["markdown", "md", "mkd"] },
        { name: "mIRC", mime: "text/mirc", mode: "mirc" },
        { name: "MariaDB SQL", mime: "text/x-mariadb", mode: "sql" },
        { name: "Mathematica", mime: "text/x-mathematica", mode: "mathematica", ext: ["m", "nb", "wl", "wls"] },
        { name: "Modelica", mime: "text/x-modelica", mode: "modelica", ext: ["mo"] },
        { name: "MUMPS", mime: "text/x-mumps", mode: "mumps", ext: ["mps"] },
        { name: "MS SQL", mime: "text/x-mssql", mode: "sql" },
        { name: "mbox", mime: "application/mbox", mode: "mbox", ext: ["mbox"] },
        { name: "MySQL", mime: "text/x-mysql", mode: "sql" },
        { name: "Nginx", mime: "text/x-nginx-conf", mode: "nginx", file: /nginx.*\.conf$/i },
        { name: "NSIS", mime: "text/x-nsis", mode: "nsis", ext: ["nsh", "nsi"] },
        {
          name: "NTriples",
          mimes: ["application/n-triples", "application/n-quads", "text/n-triples"],
          mode: "ntriples",
          ext: ["nt", "nq"]
        },
        { name: "Objective-C", mime: "text/x-objectivec", mode: "clike", ext: ["m"], alias: ["objective-c", "objc"] },
        { name: "Objective-C++", mime: "text/x-objectivec++", mode: "clike", ext: ["mm"], alias: ["objective-c++", "objc++"] },
        { name: "OCaml", mime: "text/x-ocaml", mode: "mllike", ext: ["ml", "mli", "mll", "mly"] },
        { name: "Octave", mime: "text/x-octave", mode: "octave", ext: ["m"] },
        { name: "Oz", mime: "text/x-oz", mode: "oz", ext: ["oz"] },
        { name: "Pascal", mime: "text/x-pascal", mode: "pascal", ext: ["p", "pas"] },
        { name: "PEG.js", mime: "null", mode: "pegjs", ext: ["jsonld"] },
        { name: "Perl", mime: "text/x-perl", mode: "perl", ext: ["pl", "pm"] },
        { name: "PHP", mimes: ["text/x-php", "application/x-httpd-php", "application/x-httpd-php-open"], mode: "php", ext: ["php", "php3", "php4", "php5", "php7", "phtml"] },
        { name: "Pig", mime: "text/x-pig", mode: "pig", ext: ["pig"] },
        { name: "Plain Text", mime: "text/plain", mode: "null", ext: ["txt", "text", "conf", "def", "list", "log"] },
        { name: "PLSQL", mime: "text/x-plsql", mode: "sql", ext: ["pls"] },
        { name: "PostgreSQL", mime: "text/x-pgsql", mode: "sql" },
        { name: "PowerShell", mime: "application/x-powershell", mode: "powershell", ext: ["ps1", "psd1", "psm1"] },
        { name: "Properties files", mime: "text/x-properties", mode: "properties", ext: ["properties", "ini", "in"], alias: ["ini", "properties"] },
        { name: "ProtoBuf", mime: "text/x-protobuf", mode: "protobuf", ext: ["proto"] },
        { name: "Python", mime: "text/x-python", mode: "python", ext: ["BUILD", "bzl", "py", "pyw"], file: /^(BUCK|BUILD)$/ },
        { name: "Puppet", mime: "text/x-puppet", mode: "puppet", ext: ["pp"] },
        { name: "Q", mime: "text/x-q", mode: "q", ext: ["q"] },
        { name: "R", mime: "text/x-rsrc", mode: "r", ext: ["r", "R"], alias: ["rscript"] },
        { name: "reStructuredText", mime: "text/x-rst", mode: "rst", ext: ["rst"], alias: ["rst"] },
        { name: "RPM Changes", mime: "text/x-rpm-changes", mode: "rpm" },
        { name: "RPM Spec", mime: "text/x-rpm-spec", mode: "rpm", ext: ["spec"] },
        { name: "Ruby", mime: "text/x-ruby", mode: "ruby", ext: ["rb"], alias: ["jruby", "macruby", "rake", "rb", "rbx"] },
        { name: "Rust", mime: "text/x-rustsrc", mode: "rust", ext: ["rs"] },
        { name: "SAS", mime: "text/x-sas", mode: "sas", ext: ["sas"] },
        { name: "Sass", mime: "text/x-sass", mode: "sass", ext: ["sass"] },
        { name: "Scala", mime: "text/x-scala", mode: "clike", ext: ["scala"] },
        { name: "Scheme", mime: "text/x-scheme", mode: "scheme", ext: ["scm", "ss"] },
        { name: "SCSS", mime: "text/x-scss", mode: "css", ext: ["scss"] },
        { name: "Shell", mimes: ["text/x-sh", "application/x-sh"], mode: "shell", ext: ["sh", "ksh", "bash"], alias: ["bash", "sh", "zsh"], file: /^PKGBUILD$/ },
        { name: "Sieve", mime: "application/sieve", mode: "sieve", ext: ["siv", "sieve"] },
        { name: "Slim", mimes: ["text/x-slim", "application/x-slim"], mode: "slim", ext: ["slim"] },
        { name: "Smalltalk", mime: "text/x-stsrc", mode: "smalltalk", ext: ["st"] },
        { name: "Smarty", mime: "text/x-smarty", mode: "smarty", ext: ["tpl"] },
        { name: "Solr", mime: "text/x-solr", mode: "solr" },
        { name: "SML", mime: "text/x-sml", mode: "mllike", ext: ["sml", "sig", "fun", "smackspec"] },
        { name: "Soy", mime: "text/x-soy", mode: "soy", ext: ["soy"], alias: ["closure template"] },
        { name: "SPARQL", mime: "application/sparql-query", mode: "sparql", ext: ["rq", "sparql"], alias: ["sparul"] },
        { name: "Spreadsheet", mime: "text/x-spreadsheet", mode: "spreadsheet", alias: ["excel", "formula"] },
        { name: "SQL", mime: "text/x-sql", mode: "sql", ext: ["sql"] },
        { name: "SQLite", mime: "text/x-sqlite", mode: "sql" },
        { name: "Squirrel", mime: "text/x-squirrel", mode: "clike", ext: ["nut"] },
        { name: "Stylus", mime: "text/x-styl", mode: "stylus", ext: ["styl"] },
        { name: "Swift", mime: "text/x-swift", mode: "swift", ext: ["swift"] },
        { name: "sTeX", mime: "text/x-stex", mode: "stex" },
        { name: "LaTeX", mime: "text/x-latex", mode: "stex", ext: ["text", "ltx", "tex"], alias: ["tex"] },
        { name: "SystemVerilog", mime: "text/x-systemverilog", mode: "verilog", ext: ["v", "sv", "svh"] },
        { name: "Tcl", mime: "text/x-tcl", mode: "tcl", ext: ["tcl"] },
        { name: "Textile", mime: "text/x-textile", mode: "textile", ext: ["textile"] },
        { name: "TiddlyWiki", mime: "text/x-tiddlywiki", mode: "tiddlywiki" },
        { name: "Tiki wiki", mime: "text/tiki", mode: "tiki" },
        { name: "TOML", mime: "text/x-toml", mode: "toml", ext: ["toml"] },
        { name: "Tornado", mime: "text/x-tornado", mode: "tornado" },
        { name: "troff", mime: "text/troff", mode: "troff", ext: ["1", "2", "3", "4", "5", "6", "7", "8", "9"] },
        { name: "TTCN", mime: "text/x-ttcn", mode: "ttcn", ext: ["ttcn", "ttcn3", "ttcnpp"] },
        { name: "TTCN_CFG", mime: "text/x-ttcn-cfg", mode: "ttcn-cfg", ext: ["cfg"] },
        { name: "Turtle", mime: "text/turtle", mode: "turtle", ext: ["ttl"] },
        { name: "TypeScript", mime: "application/typescript", mode: "javascript", ext: ["ts"], alias: ["ts"] },
        { name: "TypeScript-JSX", mime: "text/typescript-jsx", mode: "jsx", ext: ["tsx"], alias: ["tsx"] },
        { name: "Twig", mime: "text/x-twig", mode: "twig" },
        { name: "Web IDL", mime: "text/x-webidl", mode: "webidl", ext: ["webidl"] },
        { name: "VB.NET", mime: "text/x-vb", mode: "vb", ext: ["vb"] },
        { name: "VBScript", mime: "text/vbscript", mode: "vbscript", ext: ["vbs"] },
        { name: "Velocity", mime: "text/velocity", mode: "velocity", ext: ["vtl"] },
        { name: "Verilog", mime: "text/x-verilog", mode: "verilog", ext: ["v"] },
        { name: "VHDL", mime: "text/x-vhdl", mode: "vhdl", ext: ["vhd", "vhdl"] },
        { name: "Vue.js Component", mimes: ["script/x-vue", "text/x-vue"], mode: "vue", ext: ["vue"] },
        { name: "XML", mimes: ["application/xml", "text/xml"], mode: "xml", ext: ["xml", "xsl", "xsd", "svg"], alias: ["rss", "wsdl", "xsd"] },
        { name: "XQuery", mime: "application/xquery", mode: "xquery", ext: ["xy", "xquery"] },
        { name: "Yacas", mime: "text/x-yacas", mode: "yacas", ext: ["ys"] },
        { name: "YAML", mimes: ["text/x-yaml", "text/yaml"], mode: "yaml", ext: ["yaml", "yml"], alias: ["yml"] },
        { name: "Z80", mime: "text/x-z80", mode: "z80", ext: ["z80"] },
        { name: "mscgen", mime: "text/x-mscgen", mode: "mscgen", ext: ["mscgen", "mscin", "msc"] },
        { name: "xu", mime: "text/x-xu", mode: "mscgen", ext: ["xu"] },
        { name: "msgenny", mime: "text/x-msgenny", mode: "mscgen", ext: ["msgenny"] },
        { name: "WebAssembly", mime: "text/webassembly", mode: "wast", ext: ["wat", "wast"] }
      ];
      for (var i = 0; i < CodeMirror2.modeInfo.length; i++) {
        var info = CodeMirror2.modeInfo[i];
        if (info.mimes) info.mime = info.mimes[0];
      }
      CodeMirror2.findModeByMIME = function(mime) {
        mime = mime.toLowerCase();
        for (var i2 = 0; i2 < CodeMirror2.modeInfo.length; i2++) {
          var info2 = CodeMirror2.modeInfo[i2];
          if (info2.mime == mime) return info2;
          if (info2.mimes) {
            for (var j = 0; j < info2.mimes.length; j++)
              if (info2.mimes[j] == mime) return info2;
          }
        }
        if (/\+xml$/.test(mime)) return CodeMirror2.findModeByMIME("application/xml");
        if (/\+json$/.test(mime)) return CodeMirror2.findModeByMIME("application/json");
      };
      CodeMirror2.findModeByExtension = function(ext) {
        ext = ext.toLowerCase();
        for (var i2 = 0; i2 < CodeMirror2.modeInfo.length; i2++) {
          var info2 = CodeMirror2.modeInfo[i2];
          if (info2.ext) {
            for (var j = 0; j < info2.ext.length; j++)
              if (info2.ext[j] == ext) return info2;
          }
        }
      };
      CodeMirror2.findModeByFileName = function(filename) {
        for (var i2 = 0; i2 < CodeMirror2.modeInfo.length; i2++) {
          var info2 = CodeMirror2.modeInfo[i2];
          if (info2.file && info2.file.test(filename)) return info2;
        }
        var dot = filename.lastIndexOf(".");
        var ext = dot > -1 && filename.substring(dot + 1, filename.length);
        if (ext) return CodeMirror2.findModeByExtension(ext);
      };
      CodeMirror2.findModeByName = function(name) {
        name = name.toLowerCase();
        for (var i2 = 0; i2 < CodeMirror2.modeInfo.length; i2++) {
          var info2 = CodeMirror2.modeInfo[i2];
          if (info2.name.toLowerCase() == name) return info2;
          if (info2.alias) {
            for (var j = 0; j < info2.alias.length; j++)
              if (info2.alias[j].toLowerCase() == name) return info2;
          }
        }
      };
    });
  }
});

// node_modules/.pnpm/codemirror@5.65.19/node_modules/codemirror/mode/markdown/markdown.js
var require_markdown = __commonJS({
  "node_modules/.pnpm/codemirror@5.65.19/node_modules/codemirror/mode/markdown/markdown.js"(exports, module) {
    (function(mod) {
      if (typeof exports == "object" && typeof module == "object")
        mod(require_codemirror(), require_xml(), require_meta());
      else if (typeof define == "function" && define.amd)
        define(["../../lib/codemirror", "../xml/xml", "../meta"], mod);
      else
        mod(CodeMirror);
    })(function(CodeMirror2) {
      "use strict";
      CodeMirror2.defineMode("markdown", function(cmCfg, modeCfg) {
        var htmlMode = CodeMirror2.getMode(cmCfg, "text/html");
        var htmlModeMissing = htmlMode.name == "null";
        function getMode(name) {
          if (CodeMirror2.findModeByName) {
            var found = CodeMirror2.findModeByName(name);
            if (found) name = found.mime || found.mimes[0];
          }
          var mode2 = CodeMirror2.getMode(cmCfg, name);
          return mode2.name == "null" ? null : mode2;
        }
        if (modeCfg.highlightFormatting === void 0)
          modeCfg.highlightFormatting = false;
        if (modeCfg.maxBlockquoteDepth === void 0)
          modeCfg.maxBlockquoteDepth = 0;
        if (modeCfg.taskLists === void 0) modeCfg.taskLists = false;
        if (modeCfg.strikethrough === void 0)
          modeCfg.strikethrough = false;
        if (modeCfg.emoji === void 0)
          modeCfg.emoji = false;
        if (modeCfg.fencedCodeBlockHighlighting === void 0)
          modeCfg.fencedCodeBlockHighlighting = true;
        if (modeCfg.fencedCodeBlockDefaultMode === void 0)
          modeCfg.fencedCodeBlockDefaultMode = "text/plain";
        if (modeCfg.xml === void 0)
          modeCfg.xml = true;
        if (modeCfg.tokenTypeOverrides === void 0)
          modeCfg.tokenTypeOverrides = {};
        var tokenTypes = {
          header: "header",
          code: "comment",
          quote: "quote",
          list1: "variable-2",
          list2: "variable-3",
          list3: "keyword",
          hr: "hr",
          image: "image",
          imageAltText: "image-alt-text",
          imageMarker: "image-marker",
          formatting: "formatting",
          linkInline: "link",
          linkEmail: "link",
          linkText: "link",
          linkHref: "string",
          em: "em",
          strong: "strong",
          strikethrough: "strikethrough",
          emoji: "builtin"
        };
        for (var tokenType in tokenTypes) {
          if (tokenTypes.hasOwnProperty(tokenType) && modeCfg.tokenTypeOverrides[tokenType]) {
            tokenTypes[tokenType] = modeCfg.tokenTypeOverrides[tokenType];
          }
        }
        var hrRE = /^([*\-_])(?:\s*\1){2,}\s*$/, listRE = /^(?:[*\-+]|^[0-9]+([.)]))\s+/, taskListRE = /^\[(x| )\](?=\s)/i, atxHeaderRE = modeCfg.allowAtxHeaderWithoutSpace ? /^(#+)/ : /^(#+)(?: |$)/, setextHeaderRE = /^ {0,3}(?:\={1,}|-{2,})\s*$/, textRE = /^[^#!\[\]*_\\<>` "'(~:]+/, fencedCodeRE = /^(~~~+|```+)[ \t]*([\w\/+#-]*)[^\n`]*$/, linkDefRE = /^\s*\[[^\]]+?\]:.*$/, punctuation = /[!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u0AF0\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E42\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC9\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDF3C-\uDF3E]|\uD809[\uDC70-\uDC74]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]/, expandedTab = "    ";
        function switchInline(stream, state, f) {
          state.f = state.inline = f;
          return f(stream, state);
        }
        function switchBlock(stream, state, f) {
          state.f = state.block = f;
          return f(stream, state);
        }
        function lineIsEmpty(line) {
          return !line || !/\S/.test(line.string);
        }
        function blankLine(state) {
          state.linkTitle = false;
          state.linkHref = false;
          state.linkText = false;
          state.em = false;
          state.strong = false;
          state.strikethrough = false;
          state.quote = 0;
          state.indentedCode = false;
          if (state.f == htmlBlock) {
            var exit = htmlModeMissing;
            if (!exit) {
              var inner = CodeMirror2.innerMode(htmlMode, state.htmlState);
              exit = inner.mode.name == "xml" && inner.state.tagStart === null && (!inner.state.context && inner.state.tokenize.isInText);
            }
            if (exit) {
              state.f = inlineNormal;
              state.block = blockNormal;
              state.htmlState = null;
            }
          }
          state.trailingSpace = 0;
          state.trailingSpaceNewLine = false;
          state.prevLine = state.thisLine;
          state.thisLine = { stream: null };
          return null;
        }
        function blockNormal(stream, state) {
          var firstTokenOnLine = stream.column() === state.indentation;
          var prevLineLineIsEmpty = lineIsEmpty(state.prevLine.stream);
          var prevLineIsIndentedCode = state.indentedCode;
          var prevLineIsHr = state.prevLine.hr;
          var prevLineIsList = state.list !== false;
          var maxNonCodeIndentation = (state.listStack[state.listStack.length - 1] || 0) + 3;
          state.indentedCode = false;
          var lineIndentation = state.indentation;
          if (state.indentationDiff === null) {
            state.indentationDiff = state.indentation;
            if (prevLineIsList) {
              state.list = null;
              while (lineIndentation < state.listStack[state.listStack.length - 1]) {
                state.listStack.pop();
                if (state.listStack.length) {
                  state.indentation = state.listStack[state.listStack.length - 1];
                } else {
                  state.list = false;
                }
              }
              if (state.list !== false) {
                state.indentationDiff = lineIndentation - state.listStack[state.listStack.length - 1];
              }
            }
          }
          var allowsInlineContinuation = !prevLineLineIsEmpty && !prevLineIsHr && !state.prevLine.header && (!prevLineIsList || !prevLineIsIndentedCode) && !state.prevLine.fencedCodeEnd;
          var isHr = (state.list === false || prevLineIsHr || prevLineLineIsEmpty) && state.indentation <= maxNonCodeIndentation && stream.match(hrRE);
          var match = null;
          if (state.indentationDiff >= 4 && (prevLineIsIndentedCode || state.prevLine.fencedCodeEnd || state.prevLine.header || prevLineLineIsEmpty)) {
            stream.skipToEnd();
            state.indentedCode = true;
            return tokenTypes.code;
          } else if (stream.eatSpace()) {
            return null;
          } else if (firstTokenOnLine && state.indentation <= maxNonCodeIndentation && (match = stream.match(atxHeaderRE)) && match[1].length <= 6) {
            state.quote = 0;
            state.header = match[1].length;
            state.thisLine.header = true;
            if (modeCfg.highlightFormatting) state.formatting = "header";
            state.f = state.inline;
            return getType(state);
          } else if (state.indentation <= maxNonCodeIndentation && stream.eat(">")) {
            state.quote = firstTokenOnLine ? 1 : state.quote + 1;
            if (modeCfg.highlightFormatting) state.formatting = "quote";
            stream.eatSpace();
            return getType(state);
          } else if (!isHr && !state.setext && firstTokenOnLine && state.indentation <= maxNonCodeIndentation && (match = stream.match(listRE))) {
            var listType = match[1] ? "ol" : "ul";
            state.indentation = lineIndentation + stream.current().length;
            state.list = true;
            state.quote = 0;
            state.listStack.push(state.indentation);
            state.em = false;
            state.strong = false;
            state.code = false;
            state.strikethrough = false;
            if (modeCfg.taskLists && stream.match(taskListRE, false)) {
              state.taskList = true;
            }
            state.f = state.inline;
            if (modeCfg.highlightFormatting) state.formatting = ["list", "list-" + listType];
            return getType(state);
          } else if (firstTokenOnLine && state.indentation <= maxNonCodeIndentation && (match = stream.match(fencedCodeRE, true))) {
            state.quote = 0;
            state.fencedEndRE = new RegExp(match[1] + "+ *$");
            state.localMode = modeCfg.fencedCodeBlockHighlighting && getMode(match[2] || modeCfg.fencedCodeBlockDefaultMode);
            if (state.localMode) state.localState = CodeMirror2.startState(state.localMode);
            state.f = state.block = local;
            if (modeCfg.highlightFormatting) state.formatting = "code-block";
            state.code = -1;
            return getType(state);
          } else if (
            // if setext set, indicates line after ---/===
            state.setext || // line before ---/===
            (!allowsInlineContinuation || !prevLineIsList) && !state.quote && state.list === false && !state.code && !isHr && !linkDefRE.test(stream.string) && (match = stream.lookAhead(1)) && (match = match.match(setextHeaderRE))
          ) {
            if (!state.setext) {
              state.header = match[0].charAt(0) == "=" ? 1 : 2;
              state.setext = state.header;
            } else {
              state.header = state.setext;
              state.setext = 0;
              stream.skipToEnd();
              if (modeCfg.highlightFormatting) state.formatting = "header";
            }
            state.thisLine.header = true;
            state.f = state.inline;
            return getType(state);
          } else if (isHr) {
            stream.skipToEnd();
            state.hr = true;
            state.thisLine.hr = true;
            return tokenTypes.hr;
          } else if (stream.peek() === "[") {
            return switchInline(stream, state, footnoteLink);
          }
          return switchInline(stream, state, state.inline);
        }
        function htmlBlock(stream, state) {
          var style = htmlMode.token(stream, state.htmlState);
          if (!htmlModeMissing) {
            var inner = CodeMirror2.innerMode(htmlMode, state.htmlState);
            if (inner.mode.name == "xml" && inner.state.tagStart === null && (!inner.state.context && inner.state.tokenize.isInText) || state.md_inside && stream.current().indexOf(">") > -1) {
              state.f = inlineNormal;
              state.block = blockNormal;
              state.htmlState = null;
            }
          }
          return style;
        }
        function local(stream, state) {
          var currListInd = state.listStack[state.listStack.length - 1] || 0;
          var hasExitedList = state.indentation < currListInd;
          var maxFencedEndInd = currListInd + 3;
          if (state.fencedEndRE && state.indentation <= maxFencedEndInd && (hasExitedList || stream.match(state.fencedEndRE))) {
            if (modeCfg.highlightFormatting) state.formatting = "code-block";
            var returnType;
            if (!hasExitedList) returnType = getType(state);
            state.localMode = state.localState = null;
            state.block = blockNormal;
            state.f = inlineNormal;
            state.fencedEndRE = null;
            state.code = 0;
            state.thisLine.fencedCodeEnd = true;
            if (hasExitedList) return switchBlock(stream, state, state.block);
            return returnType;
          } else if (state.localMode) {
            return state.localMode.token(stream, state.localState);
          } else {
            stream.skipToEnd();
            return tokenTypes.code;
          }
        }
        function getType(state) {
          var styles = [];
          if (state.formatting) {
            styles.push(tokenTypes.formatting);
            if (typeof state.formatting === "string") state.formatting = [state.formatting];
            for (var i = 0; i < state.formatting.length; i++) {
              styles.push(tokenTypes.formatting + "-" + state.formatting[i]);
              if (state.formatting[i] === "header") {
                styles.push(tokenTypes.formatting + "-" + state.formatting[i] + "-" + state.header);
              }
              if (state.formatting[i] === "quote") {
                if (!modeCfg.maxBlockquoteDepth || modeCfg.maxBlockquoteDepth >= state.quote) {
                  styles.push(tokenTypes.formatting + "-" + state.formatting[i] + "-" + state.quote);
                } else {
                  styles.push("error");
                }
              }
            }
          }
          if (state.taskOpen) {
            styles.push("meta");
            return styles.length ? styles.join(" ") : null;
          }
          if (state.taskClosed) {
            styles.push("property");
            return styles.length ? styles.join(" ") : null;
          }
          if (state.linkHref) {
            styles.push(tokenTypes.linkHref, "url");
          } else {
            if (state.strong) {
              styles.push(tokenTypes.strong);
            }
            if (state.em) {
              styles.push(tokenTypes.em);
            }
            if (state.strikethrough) {
              styles.push(tokenTypes.strikethrough);
            }
            if (state.emoji) {
              styles.push(tokenTypes.emoji);
            }
            if (state.linkText) {
              styles.push(tokenTypes.linkText);
            }
            if (state.code) {
              styles.push(tokenTypes.code);
            }
            if (state.image) {
              styles.push(tokenTypes.image);
            }
            if (state.imageAltText) {
              styles.push(tokenTypes.imageAltText, "link");
            }
            if (state.imageMarker) {
              styles.push(tokenTypes.imageMarker);
            }
          }
          if (state.header) {
            styles.push(tokenTypes.header, tokenTypes.header + "-" + state.header);
          }
          if (state.quote) {
            styles.push(tokenTypes.quote);
            if (!modeCfg.maxBlockquoteDepth || modeCfg.maxBlockquoteDepth >= state.quote) {
              styles.push(tokenTypes.quote + "-" + state.quote);
            } else {
              styles.push(tokenTypes.quote + "-" + modeCfg.maxBlockquoteDepth);
            }
          }
          if (state.list !== false) {
            var listMod = (state.listStack.length - 1) % 3;
            if (!listMod) {
              styles.push(tokenTypes.list1);
            } else if (listMod === 1) {
              styles.push(tokenTypes.list2);
            } else {
              styles.push(tokenTypes.list3);
            }
          }
          if (state.trailingSpaceNewLine) {
            styles.push("trailing-space-new-line");
          } else if (state.trailingSpace) {
            styles.push("trailing-space-" + (state.trailingSpace % 2 ? "a" : "b"));
          }
          return styles.length ? styles.join(" ") : null;
        }
        function handleText(stream, state) {
          if (stream.match(textRE, true)) {
            return getType(state);
          }
          return void 0;
        }
        function inlineNormal(stream, state) {
          var style = state.text(stream, state);
          if (typeof style !== "undefined")
            return style;
          if (state.list) {
            state.list = null;
            return getType(state);
          }
          if (state.taskList) {
            var taskOpen = stream.match(taskListRE, true)[1] === " ";
            if (taskOpen) state.taskOpen = true;
            else state.taskClosed = true;
            if (modeCfg.highlightFormatting) state.formatting = "task";
            state.taskList = false;
            return getType(state);
          }
          state.taskOpen = false;
          state.taskClosed = false;
          if (state.header && stream.match(/^#+$/, true)) {
            if (modeCfg.highlightFormatting) state.formatting = "header";
            return getType(state);
          }
          var ch = stream.next();
          if (state.linkTitle) {
            state.linkTitle = false;
            var matchCh = ch;
            if (ch === "(") {
              matchCh = ")";
            }
            matchCh = (matchCh + "").replace(/([.?*+^\[\]\\(){}|-])/g, "\\$1");
            var regex = "^\\s*(?:[^" + matchCh + "\\\\]+|\\\\\\\\|\\\\.)" + matchCh;
            if (stream.match(new RegExp(regex), true)) {
              return tokenTypes.linkHref;
            }
          }
          if (ch === "`") {
            var previousFormatting = state.formatting;
            if (modeCfg.highlightFormatting) state.formatting = "code";
            stream.eatWhile("`");
            var count = stream.current().length;
            if (state.code == 0 && (!state.quote || count == 1)) {
              state.code = count;
              return getType(state);
            } else if (count == state.code) {
              var t = getType(state);
              state.code = 0;
              return t;
            } else {
              state.formatting = previousFormatting;
              return getType(state);
            }
          } else if (state.code) {
            return getType(state);
          }
          if (ch === "\\") {
            stream.next();
            if (modeCfg.highlightFormatting) {
              var type = getType(state);
              var formattingEscape = tokenTypes.formatting + "-escape";
              return type ? type + " " + formattingEscape : formattingEscape;
            }
          }
          if (ch === "!" && stream.match(/\[[^\]]*\] ?(?:\(|\[)/, false)) {
            state.imageMarker = true;
            state.image = true;
            if (modeCfg.highlightFormatting) state.formatting = "image";
            return getType(state);
          }
          if (ch === "[" && state.imageMarker && stream.match(/[^\]]*\](\(.*?\)| ?\[.*?\])/, false)) {
            state.imageMarker = false;
            state.imageAltText = true;
            if (modeCfg.highlightFormatting) state.formatting = "image";
            return getType(state);
          }
          if (ch === "]" && state.imageAltText) {
            if (modeCfg.highlightFormatting) state.formatting = "image";
            var type = getType(state);
            state.imageAltText = false;
            state.image = false;
            state.inline = state.f = linkHref;
            return type;
          }
          if (ch === "[" && !state.image) {
            if (state.linkText && stream.match(/^.*?\]/)) return getType(state);
            state.linkText = true;
            if (modeCfg.highlightFormatting) state.formatting = "link";
            return getType(state);
          }
          if (ch === "]" && state.linkText) {
            if (modeCfg.highlightFormatting) state.formatting = "link";
            var type = getType(state);
            state.linkText = false;
            state.inline = state.f = stream.match(/\(.*?\)| ?\[.*?\]/, false) ? linkHref : inlineNormal;
            return type;
          }
          if (ch === "<" && stream.match(/^(https?|ftps?):\/\/(?:[^\\>]|\\.)+>/, false)) {
            state.f = state.inline = linkInline;
            if (modeCfg.highlightFormatting) state.formatting = "link";
            var type = getType(state);
            if (type) {
              type += " ";
            } else {
              type = "";
            }
            return type + tokenTypes.linkInline;
          }
          if (ch === "<" && stream.match(/^[^> \\]+@(?:[^\\>]|\\.)+>/, false)) {
            state.f = state.inline = linkInline;
            if (modeCfg.highlightFormatting) state.formatting = "link";
            var type = getType(state);
            if (type) {
              type += " ";
            } else {
              type = "";
            }
            return type + tokenTypes.linkEmail;
          }
          if (modeCfg.xml && ch === "<" && stream.match(/^(!--|\?|!\[CDATA\[|[a-z][a-z0-9-]*(?:\s+[a-z_:.\-]+(?:\s*=\s*[^>]+)?)*\s*(?:>|$))/i, false)) {
            var end = stream.string.indexOf(">", stream.pos);
            if (end != -1) {
              var atts = stream.string.substring(stream.start, end);
              if (/markdown\s*=\s*('|"){0,1}1('|"){0,1}/.test(atts)) state.md_inside = true;
            }
            stream.backUp(1);
            state.htmlState = CodeMirror2.startState(htmlMode);
            return switchBlock(stream, state, htmlBlock);
          }
          if (modeCfg.xml && ch === "<" && stream.match(/^\/\w*?>/)) {
            state.md_inside = false;
            return "tag";
          } else if (ch === "*" || ch === "_") {
            var len = 1, before = stream.pos == 1 ? " " : stream.string.charAt(stream.pos - 2);
            while (len < 3 && stream.eat(ch)) len++;
            var after = stream.peek() || " ";
            var leftFlanking = !/\s/.test(after) && (!punctuation.test(after) || /\s/.test(before) || punctuation.test(before));
            var rightFlanking = !/\s/.test(before) && (!punctuation.test(before) || /\s/.test(after) || punctuation.test(after));
            var setEm = null, setStrong = null;
            if (len % 2) {
              if (!state.em && leftFlanking && (ch === "*" || !rightFlanking || punctuation.test(before)))
                setEm = true;
              else if (state.em == ch && rightFlanking && (ch === "*" || !leftFlanking || punctuation.test(after)))
                setEm = false;
            }
            if (len > 1) {
              if (!state.strong && leftFlanking && (ch === "*" || !rightFlanking || punctuation.test(before)))
                setStrong = true;
              else if (state.strong == ch && rightFlanking && (ch === "*" || !leftFlanking || punctuation.test(after)))
                setStrong = false;
            }
            if (setStrong != null || setEm != null) {
              if (modeCfg.highlightFormatting) state.formatting = setEm == null ? "strong" : setStrong == null ? "em" : "strong em";
              if (setEm === true) state.em = ch;
              if (setStrong === true) state.strong = ch;
              var t = getType(state);
              if (setEm === false) state.em = false;
              if (setStrong === false) state.strong = false;
              return t;
            }
          } else if (ch === " ") {
            if (stream.eat("*") || stream.eat("_")) {
              if (stream.peek() === " ") {
                return getType(state);
              } else {
                stream.backUp(1);
              }
            }
          }
          if (modeCfg.strikethrough) {
            if (ch === "~" && stream.eatWhile(ch)) {
              if (state.strikethrough) {
                if (modeCfg.highlightFormatting) state.formatting = "strikethrough";
                var t = getType(state);
                state.strikethrough = false;
                return t;
              } else if (stream.match(/^[^\s]/, false)) {
                state.strikethrough = true;
                if (modeCfg.highlightFormatting) state.formatting = "strikethrough";
                return getType(state);
              }
            } else if (ch === " ") {
              if (stream.match("~~", true)) {
                if (stream.peek() === " ") {
                  return getType(state);
                } else {
                  stream.backUp(2);
                }
              }
            }
          }
          if (modeCfg.emoji && ch === ":" && stream.match(/^(?:[a-z_\d+][a-z_\d+-]*|\-[a-z_\d+][a-z_\d+-]*):/)) {
            state.emoji = true;
            if (modeCfg.highlightFormatting) state.formatting = "emoji";
            var retType = getType(state);
            state.emoji = false;
            return retType;
          }
          if (ch === " ") {
            if (stream.match(/^ +$/, false)) {
              state.trailingSpace++;
            } else if (state.trailingSpace) {
              state.trailingSpaceNewLine = true;
            }
          }
          return getType(state);
        }
        function linkInline(stream, state) {
          var ch = stream.next();
          if (ch === ">") {
            state.f = state.inline = inlineNormal;
            if (modeCfg.highlightFormatting) state.formatting = "link";
            var type = getType(state);
            if (type) {
              type += " ";
            } else {
              type = "";
            }
            return type + tokenTypes.linkInline;
          }
          stream.match(/^[^>]+/, true);
          return tokenTypes.linkInline;
        }
        function linkHref(stream, state) {
          if (stream.eatSpace()) {
            return null;
          }
          var ch = stream.next();
          if (ch === "(" || ch === "[") {
            state.f = state.inline = getLinkHrefInside(ch === "(" ? ")" : "]");
            if (modeCfg.highlightFormatting) state.formatting = "link-string";
            state.linkHref = true;
            return getType(state);
          }
          return "error";
        }
        var linkRE = {
          ")": /^(?:[^\\\(\)]|\\.|\((?:[^\\\(\)]|\\.)*\))*?(?=\))/,
          "]": /^(?:[^\\\[\]]|\\.|\[(?:[^\\\[\]]|\\.)*\])*?(?=\])/
        };
        function getLinkHrefInside(endChar) {
          return function(stream, state) {
            var ch = stream.next();
            if (ch === endChar) {
              state.f = state.inline = inlineNormal;
              if (modeCfg.highlightFormatting) state.formatting = "link-string";
              var returnState = getType(state);
              state.linkHref = false;
              return returnState;
            }
            stream.match(linkRE[endChar]);
            state.linkHref = true;
            return getType(state);
          };
        }
        function footnoteLink(stream, state) {
          if (stream.match(/^([^\]\\]|\\.)*\]:/, false)) {
            state.f = footnoteLinkInside;
            stream.next();
            if (modeCfg.highlightFormatting) state.formatting = "link";
            state.linkText = true;
            return getType(state);
          }
          return switchInline(stream, state, inlineNormal);
        }
        function footnoteLinkInside(stream, state) {
          if (stream.match("]:", true)) {
            state.f = state.inline = footnoteUrl;
            if (modeCfg.highlightFormatting) state.formatting = "link";
            var returnType = getType(state);
            state.linkText = false;
            return returnType;
          }
          stream.match(/^([^\]\\]|\\.)+/, true);
          return tokenTypes.linkText;
        }
        function footnoteUrl(stream, state) {
          if (stream.eatSpace()) {
            return null;
          }
          stream.match(/^[^\s]+/, true);
          if (stream.peek() === void 0) {
            state.linkTitle = true;
          } else {
            stream.match(/^(?:\s+(?:"(?:[^"\\]|\\.)+"|'(?:[^'\\]|\\.)+'|\((?:[^)\\]|\\.)+\)))?/, true);
          }
          state.f = state.inline = inlineNormal;
          return tokenTypes.linkHref + " url";
        }
        var mode = {
          startState: function() {
            return {
              f: blockNormal,
              prevLine: { stream: null },
              thisLine: { stream: null },
              block: blockNormal,
              htmlState: null,
              indentation: 0,
              inline: inlineNormal,
              text: handleText,
              formatting: false,
              linkText: false,
              linkHref: false,
              linkTitle: false,
              code: 0,
              em: false,
              strong: false,
              header: 0,
              setext: 0,
              hr: false,
              taskList: false,
              list: false,
              listStack: [],
              quote: 0,
              trailingSpace: 0,
              trailingSpaceNewLine: false,
              strikethrough: false,
              emoji: false,
              fencedEndRE: null
            };
          },
          copyState: function(s) {
            return {
              f: s.f,
              prevLine: s.prevLine,
              thisLine: s.thisLine,
              block: s.block,
              htmlState: s.htmlState && CodeMirror2.copyState(htmlMode, s.htmlState),
              indentation: s.indentation,
              localMode: s.localMode,
              localState: s.localMode ? CodeMirror2.copyState(s.localMode, s.localState) : null,
              inline: s.inline,
              text: s.text,
              formatting: false,
              linkText: s.linkText,
              linkTitle: s.linkTitle,
              linkHref: s.linkHref,
              code: s.code,
              em: s.em,
              strong: s.strong,
              strikethrough: s.strikethrough,
              emoji: s.emoji,
              header: s.header,
              setext: s.setext,
              hr: s.hr,
              taskList: s.taskList,
              list: s.list,
              listStack: s.listStack.slice(0),
              quote: s.quote,
              indentedCode: s.indentedCode,
              trailingSpace: s.trailingSpace,
              trailingSpaceNewLine: s.trailingSpaceNewLine,
              md_inside: s.md_inside,
              fencedEndRE: s.fencedEndRE
            };
          },
          token: function(stream, state) {
            state.formatting = false;
            if (stream != state.thisLine.stream) {
              state.header = 0;
              state.hr = false;
              if (stream.match(/^\s*$/, true)) {
                blankLine(state);
                return null;
              }
              state.prevLine = state.thisLine;
              state.thisLine = { stream };
              state.taskList = false;
              state.trailingSpace = 0;
              state.trailingSpaceNewLine = false;
              if (!state.localState) {
                state.f = state.block;
                if (state.f != htmlBlock) {
                  var indentation = stream.match(/^\s*/, true)[0].replace(/\t/g, expandedTab).length;
                  state.indentation = indentation;
                  state.indentationDiff = null;
                  if (indentation > 0) return null;
                }
              }
            }
            return state.f(stream, state);
          },
          innerMode: function(state) {
            if (state.block == htmlBlock) return { state: state.htmlState, mode: htmlMode };
            if (state.localState) return { state: state.localState, mode: state.localMode };
            return { state, mode };
          },
          indent: function(state, textAfter, line) {
            if (state.block == htmlBlock && htmlMode.indent) return htmlMode.indent(state.htmlState, textAfter, line);
            if (state.localState && state.localMode.indent) return state.localMode.indent(state.localState, textAfter, line);
            return CodeMirror2.Pass;
          },
          blankLine,
          getType,
          blockCommentStart: "<!--",
          blockCommentEnd: "-->",
          closeBrackets: "()[]{}''\"\"``",
          fold: "markdown"
        };
        return mode;
      }, "xml");
      CodeMirror2.defineMIME("text/markdown", "markdown");
      CodeMirror2.defineMIME("text/x-markdown", "markdown");
    });
  }
});
export default require_markdown();
//# sourceMappingURL=codemirror_mode_markdown_markdown__js.js.map
