{"version": 3, "sources": ["../../.pnpm/@antv+x6-plugin-history@2.2.4_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-history/src/api.ts", "../../.pnpm/@antv+x6-plugin-history@2.2.4_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-history/src/index.ts"], "sourcesContent": ["import { Graph, KeyValue } from '@antv/x6'\nimport { History } from './index'\n\ndeclare module '@antv/x6/lib/graph/graph' {\n  interface Graph {\n    isHistoryEnabled: () => boolean\n    enableHistory: () => Graph\n    disableHistory: () => Graph\n    toggleHistory: (enabled?: boolean) => Graph\n    undo: (options?: KeyValue) => Graph\n    redo: (options?: KeyValue) => Graph\n    undoAndCancel: (options?: KeyValue) => Graph\n    canUndo: () => boolean\n    canRedo: () => boolean\n    getHistoryStackSize: () => number\n    getUndoStackSize: () => number\n    getRedoStackSize: () => number\n    getUndoRemainSize: () => number\n    cleanHistory: (options?: KeyValue) => Graph\n  }\n}\n\ndeclare module '@antv/x6/lib/graph/events' {\n  interface EventArgs {\n    'history:undo': History.Args\n    'history:redo': History.Args\n    'history:cancel': History.Args\n    'history:add': History.Args\n    'history:clean': History.Args<null>\n    'history:change': History.Args<null>\n    'history:batch': { cmd: History.Command; options: KeyValue }\n  }\n}\n\nGraph.prototype.isHistoryEnabled = function () {\n  const history = this.getPlugin('history') as History\n  if (history) {\n    return history.isEnabled()\n  }\n  return false\n}\n\nGraph.prototype.enableHistory = function () {\n  const history = this.getPlugin('history') as History\n  if (history) {\n    history.enable()\n  }\n  return this\n}\n\nGraph.prototype.disableHistory = function () {\n  const history = this.getPlugin('history') as History\n  if (history) {\n    history.disable()\n  }\n  return this\n}\n\nGraph.prototype.toggleHistory = function (enabled?: boolean) {\n  const history = this.getPlugin('history') as History\n  if (history) {\n    history.toggleEnabled(enabled)\n  }\n  return this\n}\n\nGraph.prototype.undo = function (options?: KeyValue) {\n  const history = this.getPlugin('history') as History\n  if (history) {\n    history.undo(options)\n  }\n  return this\n}\n\nGraph.prototype.redo = function (options?: KeyValue) {\n  const history = this.getPlugin('history') as History\n  if (history) {\n    history.redo(options)\n  }\n  return this\n}\n\nGraph.prototype.undoAndCancel = function (options?: KeyValue) {\n  const history = this.getPlugin('history') as History\n  if (history) {\n    history.cancel(options)\n  }\n  return this\n}\n\nGraph.prototype.canUndo = function () {\n  const history = this.getPlugin('history') as History\n  if (history) {\n    return history.canUndo()\n  }\n  return false\n}\n\nGraph.prototype.canRedo = function () {\n  const history = this.getPlugin('history') as History\n  if (history) {\n    return history.canRedo()\n  }\n  return false\n}\n\nGraph.prototype.cleanHistory = function (options?: KeyValue) {\n  const history = this.getPlugin('history') as History\n  if (history) {\n    history.clean(options)\n  }\n  return this\n}\n\nGraph.prototype.getHistoryStackSize = function () {\n  const history = this.getPlugin('history') as History\n  return history.getSize()\n}\n\nGraph.prototype.getUndoStackSize = function () {\n  const history = this.getPlugin('history') as History\n  return history.getUndoSize()\n}\n\nGraph.prototype.getRedoStackSize = function () {\n  const history = this.getPlugin('history') as History\n  return history.getRedoSize()\n}\n\nGraph.prototype.getUndoRemainSize = function () {\n  const history = this.getPlugin('history') as History\n  return history.getUndoRemainSize()\n}\n", "import {\n  KeyValue,\n  ObjectExt,\n  FunctionExt,\n  Basecoat,\n  Cell,\n  Model,\n  Graph,\n} from '@antv/x6'\nimport './api'\n\nexport class History\n  extends Basecoat<History.EventArgs>\n  implements Graph.Plugin\n{\n  public name = 'history'\n  public graph: Graph\n  public model: Model\n  public readonly options: History.CommonOptions\n  public readonly validator: History.Validator\n  protected redoStack: History.Commands[]\n  protected undoStack: History.Commands[]\n  protected batchCommands: History.Command[] | null = null\n  protected batchLevel = 0\n  protected lastBatchIndex = -1\n  protected freezed = false\n  protected stackSize = 0 // 0: not limit\n\n  protected readonly handlers: (<T extends History.ModelEvents>(\n    event: T,\n    args: Model.EventArgs[T],\n  ) => any)[] = []\n\n  constructor(options: History.Options = {}) {\n    super()\n    const { stackSize = 0 } = options\n    this.stackSize = stackSize\n    this.options = Util.getOptions(options)\n    this.validator = new History.Validator({\n      history: this,\n      cancelInvalid: this.options.cancelInvalid,\n    })\n  }\n\n  init(graph: Graph) {\n    this.graph = graph\n    this.model = this.graph.model\n\n    this.clean()\n    this.startListening()\n  }\n\n  // #region api\n\n  isEnabled() {\n    return !this.disabled\n  }\n\n  enable() {\n    if (this.disabled) {\n      this.options.enabled = true\n    }\n  }\n\n  disable() {\n    if (!this.disabled) {\n      this.options.enabled = false\n    }\n  }\n\n  toggleEnabled(enabled?: boolean) {\n    if (enabled != null) {\n      if (enabled !== this.isEnabled()) {\n        if (enabled) {\n          this.enable()\n        } else {\n          this.disable()\n        }\n      }\n    } else if (this.isEnabled()) {\n      this.disable()\n    } else {\n      this.enable()\n    }\n\n    return this\n  }\n\n  undo(options: KeyValue = {}) {\n    if (!this.disabled) {\n      const cmd = this.undoStack.pop()\n      if (cmd) {\n        this.revertCommand(cmd, options)\n        this.redoStack.push(cmd)\n        this.notify('undo', cmd, options)\n      }\n    }\n    return this\n  }\n\n  redo(options: KeyValue = {}) {\n    if (!this.disabled) {\n      const cmd = this.redoStack.pop()\n      if (cmd) {\n        this.applyCommand(cmd, options)\n        this.undoStackPush(cmd)\n        this.notify('redo', cmd, options)\n      }\n    }\n    return this\n  }\n\n  /**\n   * Same as `undo()` but does not store the undo-ed command to the\n   * `redoStack`. Canceled command therefore cannot be redo-ed.\n   */\n  cancel(options: KeyValue = {}) {\n    if (!this.disabled) {\n      const cmd = this.undoStack.pop()\n      if (cmd) {\n        this.revertCommand(cmd, options)\n        this.redoStack = []\n        this.notify('cancel', cmd, options)\n      }\n    }\n    return this\n  }\n\n  getSize() {\n    return this.stackSize\n  }\n\n  getUndoRemainSize() {\n    const ul = this.undoStack.length\n    return this.stackSize - ul\n  }\n\n  getUndoSize() {\n    return this.undoStack.length\n  }\n\n  getRedoSize() {\n    return this.redoStack.length\n  }\n\n  canUndo() {\n    return !this.disabled && this.undoStack.length > 0\n  }\n\n  canRedo() {\n    return !this.disabled && this.redoStack.length > 0\n  }\n\n  clean(options: KeyValue = {}) {\n    this.undoStack = []\n    this.redoStack = []\n    this.notify('clean', null, options)\n    return this\n  }\n\n  // #endregion\n\n  get disabled() {\n    return this.options.enabled !== true\n  }\n\n  protected validate(\n    events: string | string[],\n    ...callbacks: History.Validator.Callback[]\n  ) {\n    this.validator.validate(events, ...callbacks)\n    return this\n  }\n\n  protected startListening() {\n    this.model.on('batch:start', this.initBatchCommand, this)\n    this.model.on('batch:stop', this.storeBatchCommand, this)\n    if (this.options.eventNames) {\n      this.options.eventNames.forEach((name, index) => {\n        this.handlers[index] = this.addCommand.bind(this, name)\n        this.model.on(name, this.handlers[index])\n      })\n    }\n\n    this.validator.on('invalid', (args) => this.trigger('invalid', args))\n  }\n\n  protected stopListening() {\n    this.model.off('batch:start', this.initBatchCommand, this)\n    this.model.off('batch:stop', this.storeBatchCommand, this)\n    if (this.options.eventNames) {\n      this.options.eventNames.forEach((name, index) => {\n        this.model.off(name, this.handlers[index])\n      })\n      this.handlers.length = 0\n    }\n    this.validator.off('invalid')\n  }\n\n  protected createCommand(options?: { batch: boolean }): History.Command {\n    return {\n      batch: options ? options.batch : false,\n      data: {} as History.CreationData,\n    }\n  }\n\n  protected revertCommand(cmd: History.Commands, options?: KeyValue) {\n    this.freezed = true\n\n    const cmds = Array.isArray(cmd) ? Util.sortBatchCommands(cmd) : [cmd]\n    for (let i = cmds.length - 1; i >= 0; i -= 1) {\n      const cmd = cmds[i]\n      const localOptions = {\n        ...options,\n        ...ObjectExt.pick(cmd.options, this.options.revertOptionsList || []),\n      }\n      this.executeCommand(cmd, true, localOptions)\n    }\n\n    this.freezed = false\n  }\n\n  protected applyCommand(cmd: History.Commands, options?: KeyValue) {\n    this.freezed = true\n\n    const cmds = Array.isArray(cmd) ? Util.sortBatchCommands(cmd) : [cmd]\n    for (let i = 0; i < cmds.length; i += 1) {\n      const cmd = cmds[i]\n      const localOptions = {\n        ...options,\n        ...ObjectExt.pick(cmd.options, this.options.applyOptionsList || []),\n      }\n      this.executeCommand(cmd, false, localOptions)\n    }\n\n    this.freezed = false\n  }\n\n  protected executeCommand(\n    cmd: History.Command,\n    revert: boolean,\n    options: KeyValue,\n  ) {\n    const model = this.model\n    // const cell = cmd.modelChange ? model : model.getCell(cmd.data.id!)\n    const cell = model.getCell(cmd.data.id!)\n    const event = cmd.event\n\n    if (\n      (Util.isAddEvent(event) && revert) ||\n      (Util.isRemoveEvent(event) && !revert)\n    ) {\n      cell && cell.remove(options)\n    } else if (\n      (Util.isAddEvent(event) && !revert) ||\n      (Util.isRemoveEvent(event) && revert)\n    ) {\n      const data = cmd.data as History.CreationData\n      if (data.node) {\n        model.addNode(data.props, options)\n      } else if (data.edge) {\n        model.addEdge(data.props, options)\n      }\n    } else if (Util.isChangeEvent(event)) {\n      const data = cmd.data as History.ChangingData\n      const key = data.key\n      if (key && cell) {\n        const value = revert ? data.prev[key] : data.next[key]\n\n        if (data.key === 'attrs') {\n          const hasUndefinedAttr = this.ensureUndefinedAttrs(\n            value,\n            revert ? data.next[key] : data.prev[key],\n          )\n          if (hasUndefinedAttr) {\n            // recognize a `dirty` flag and re-render itself in order to remove\n            // the attribute from SVGElement.\n            options.dirty = true\n          }\n        }\n\n        cell.prop(key, value, options)\n      }\n    } else {\n      const executeCommand = this.options.executeCommand\n      if (executeCommand) {\n        FunctionExt.call(executeCommand, this, cmd, revert, options)\n      }\n    }\n  }\n\n  protected addCommand<T extends keyof Model.EventArgs>(\n    event: T,\n    args: Model.EventArgs[T],\n  ) {\n    if (this.freezed || this.disabled) {\n      return\n    }\n\n    const eventArgs = args as Model.EventArgs['cell:change:*']\n    const options = eventArgs.options || {}\n    if (options.dryrun) {\n      return\n    }\n\n    if (\n      (Util.isAddEvent(event) && this.options.ignoreAdd) ||\n      (Util.isRemoveEvent(event) && this.options.ignoreRemove) ||\n      (Util.isChangeEvent(event) && this.options.ignoreChange)\n    ) {\n      return\n    }\n\n    // before\n    // ------\n    const before = this.options.beforeAddCommand\n    if (\n      before != null &&\n      FunctionExt.call(before, this, event, args) === false\n    ) {\n      return\n    }\n\n    if (event === 'cell:change:*') {\n      // eslint-disable-next-line\n      event = `cell:change:${eventArgs.key}` as T\n    }\n\n    const cell = eventArgs.cell\n    const isModelChange = Model.isModel(cell)\n    let cmd: History.Command\n\n    if (this.batchCommands) {\n      // In most cases we are working with same object, doing\n      // same action etc. translate an object piece by piece.\n      cmd = this.batchCommands[Math.max(this.lastBatchIndex, 0)]\n\n      // Check if we are start working with new object or performing different\n      // action with it. Note, that command is uninitialized when lastCmdIndex\n      // equals -1. In that case we are done, command we were looking for is\n      // already set\n\n      const diffId =\n        (isModelChange && !cmd.modelChange) || cmd.data.id !== cell.id\n      const diffName = cmd.event !== event\n\n      if (this.lastBatchIndex >= 0 && (diffId || diffName)) {\n        // Trying to find command first, which was performing same\n        // action with the object as we are doing now with cell.\n        const index = this.batchCommands.findIndex(\n          (cmd) =>\n            ((isModelChange && cmd.modelChange) || cmd.data.id === cell.id) &&\n            cmd.event === event,\n        )\n\n        if (index < 0 || Util.isAddEvent(event) || Util.isRemoveEvent(event)) {\n          cmd = this.createCommand({ batch: true })\n        } else {\n          cmd = this.batchCommands[index]\n          this.batchCommands.splice(index, 1)\n        }\n        this.batchCommands.push(cmd)\n        this.lastBatchIndex = this.batchCommands.length - 1\n      }\n    } else {\n      cmd = this.createCommand({ batch: false })\n    }\n\n    // add & remove\n    // ------------\n    if (Util.isAddEvent(event) || Util.isRemoveEvent(event)) {\n      const data = cmd.data as History.CreationData\n      cmd.event = event\n      cmd.options = options\n      data.id = cell.id\n      data.props = ObjectExt.cloneDeep(cell.toJSON())\n      if (cell.isEdge()) {\n        data.edge = true\n      } else if (cell.isNode()) {\n        data.node = true\n      }\n\n      return this.push(cmd, options)\n    }\n\n    // change:*\n    // --------\n    if (Util.isChangeEvent(event)) {\n      const key = (args as Model.EventArgs['cell:change:*']).key\n      const data = cmd.data as History.ChangingData\n\n      if (!cmd.batch || !cmd.event) {\n        // Do this only once. Set previous data and action (also\n        // serves as a flag so that we don't repeat this branche).\n        cmd.event = event\n        cmd.options = options\n        data.key = key as string\n        if (data.prev == null) {\n          data.prev = {}\n        }\n        data.prev[key] = ObjectExt.cloneDeep(cell.previous(key))\n\n        if (isModelChange) {\n          cmd.modelChange = true\n        } else {\n          data.id = cell.id\n        }\n      }\n\n      if (data.next == null) {\n        data.next = {}\n      }\n      data.next[key] = ObjectExt.cloneDeep(cell.prop(key))\n      return this.push(cmd, options)\n    }\n\n    // others\n    // ------\n    const afterAddCommand = this.options.afterAddCommand\n    if (afterAddCommand) {\n      FunctionExt.call(afterAddCommand, this, event, args, cmd)\n    }\n    this.push(cmd, options)\n  }\n\n  /**\n   * Gather multiple changes into a single command. These commands could\n   * be reverted with single `undo()` call. From the moment the function\n   * is called every change made on model is not stored into the undoStack.\n   * Changes are temporarily kept until `storeBatchCommand()` is called.\n   */\n  // eslint-disable-next-line\n  protected initBatchCommand(options: KeyValue) {\n    if (this.freezed) {\n      return\n    }\n    if (this.batchCommands) {\n      this.batchLevel += 1\n    } else {\n      this.batchCommands = [this.createCommand({ batch: true })]\n      this.batchLevel = 0\n      this.lastBatchIndex = -1\n    }\n  }\n\n  /**\n   * Store changes temporarily kept in the undoStack. You have to call this\n   * function as many times as `initBatchCommand()` been called.\n   */\n  protected storeBatchCommand(options: KeyValue) {\n    if (this.freezed) {\n      return\n    }\n\n    if (this.batchCommands && this.batchLevel <= 0) {\n      const cmds = this.filterBatchCommand(this.batchCommands)\n      if (cmds.length > 0) {\n        this.redoStack = []\n        this.undoStackPush(cmds)\n        this.consolidateCommands()\n        this.notify('add', cmds, options)\n      }\n      this.batchCommands = null\n      this.lastBatchIndex = -1\n      this.batchLevel = 0\n    } else if (this.batchCommands && this.batchLevel > 0) {\n      this.batchLevel -= 1\n    }\n  }\n\n  protected filterBatchCommand(batchCommands: History.Command[]) {\n    let cmds = batchCommands.slice()\n    const result = []\n\n    while (cmds.length > 0) {\n      const cmd = cmds.shift()!\n      const evt = cmd.event\n      const id = cmd.data.id\n\n      if (evt != null && (id != null || cmd.modelChange)) {\n        if (Util.isAddEvent(evt)) {\n          const index = cmds.findIndex(\n            (c) => Util.isRemoveEvent(c.event) && c.data.id === id,\n          )\n\n          if (index >= 0) {\n            cmds = cmds.filter((c, i) => index < i || c.data.id !== id)\n            continue\n          }\n        } else if (Util.isRemoveEvent(evt)) {\n          const index = cmds.findIndex(\n            (c) => Util.isAddEvent(c.event) && c.data.id === id,\n          )\n          if (index >= 0) {\n            cmds.splice(index, 1)\n            continue\n          }\n        } else if (Util.isChangeEvent(evt)) {\n          const data = cmd.data as History.ChangingData\n\n          if (ObjectExt.isEqual(data.prev, data.next)) {\n            continue\n          }\n        } else {\n          // pass\n        }\n\n        result.push(cmd)\n      }\n    }\n\n    return result\n  }\n\n  protected notify(\n    event: keyof History.EventArgs,\n    cmd: History.Commands | null,\n    options: KeyValue,\n  ) {\n    const cmds = cmd == null ? null : Array.isArray(cmd) ? cmd : [cmd]\n    this.emit(event, { cmds, options })\n    this.graph.trigger(`history:${event}`, { cmds, options })\n    this.emit('change', { cmds, options })\n    this.graph.trigger('history:change', { cmds, options })\n  }\n\n  protected push(cmd: History.Command, options: KeyValue) {\n    this.redoStack = []\n    if (cmd.batch) {\n      this.lastBatchIndex = Math.max(this.lastBatchIndex, 0)\n      this.emit('batch', { cmd, options })\n    } else {\n      this.undoStackPush(cmd)\n      this.consolidateCommands()\n      this.notify('add', cmd, options)\n    }\n  }\n\n  /**\n   * Conditionally combine multiple undo items into one.\n   *\n   * Currently this is only used combine a `cell:changed:position` event\n   * followed by multiple `cell:change:parent` and `cell:change:children`\n   * events, such that a \"move + embed\" action can be undone in one step.\n   *\n   * See https://github.com/antvis/X6/issues/2421\n   *\n   * This is an ugly WORKAROUND. It does not solve deficiencies in the batch\n   * system itself.\n   */\n  protected consolidateCommands() {\n    const lastCommandGroup = this.undoStack[this.undoStack.length - 1]\n    const penultimateCommandGroup = this.undoStack[this.undoStack.length - 2]\n\n    // We are looking for at least one cell:change:parent\n    // and one cell:change:children\n    if (!Array.isArray(lastCommandGroup)) {\n      return\n    }\n    const eventTypes = new Set(lastCommandGroup.map((cmd) => cmd.event))\n    if (\n      eventTypes.size !== 2 ||\n      !eventTypes.has('cell:change:parent') ||\n      !eventTypes.has('cell:change:children')\n    ) {\n      return\n    }\n\n    // We are looking for events from user interactions\n    if (!lastCommandGroup.every((cmd) => cmd.batch && cmd.options?.ui)) {\n      return\n    }\n\n    // We are looking for a command group with exactly one event, whose event\n    // type is cell:change:position, and is from user interactions\n    if (\n      !Array.isArray(penultimateCommandGroup) ||\n      penultimateCommandGroup.length !== 1\n    ) {\n      return\n    }\n    const maybePositionChange = penultimateCommandGroup[0]\n    if (\n      maybePositionChange.event !== 'cell:change:position' ||\n      !maybePositionChange.options?.ui\n    ) {\n      return\n    }\n\n    // Actually consolidating the commands we get\n    penultimateCommandGroup.push(...lastCommandGroup)\n    this.undoStack.pop()\n  }\n\n  protected undoStackPush(cmd: History.Commands) {\n    if (this.stackSize === 0) {\n      this.undoStack.push(cmd)\n      return\n    }\n    if (this.undoStack.length >= this.stackSize) {\n      this.undoStack.shift()\n    }\n    this.undoStack.push(cmd)\n  }\n\n  protected ensureUndefinedAttrs(\n    newAttrs: Record<string, any>,\n    oldAttrs: Record<string, any>,\n  ) {\n    let hasUndefinedAttr = false\n    if (\n      newAttrs !== null &&\n      oldAttrs !== null &&\n      typeof newAttrs === 'object' &&\n      typeof oldAttrs === 'object'\n    ) {\n      Object.keys(oldAttrs).forEach((key) => {\n        // eslint-disable-next-line no-prototype-builtins\n        if (newAttrs[key] === undefined && oldAttrs[key] !== undefined) {\n          newAttrs[key] = undefined\n          hasUndefinedAttr = true\n        } else if (\n          typeof newAttrs[key] === 'object' &&\n          typeof oldAttrs[key] === 'object'\n        ) {\n          hasUndefinedAttr = this.ensureUndefinedAttrs(\n            newAttrs[key],\n            oldAttrs[key],\n          )\n        }\n      })\n    }\n    return hasUndefinedAttr\n  }\n\n  @Basecoat.dispose()\n  dispose() {\n    this.validator.dispose()\n    this.clean()\n    this.stopListening()\n    this.off()\n  }\n}\n\nexport namespace History {\n  export type ModelEvents = keyof Model.EventArgs\n\n  export interface CommonOptions {\n    enabled?: boolean\n    ignoreAdd?: boolean\n    ignoreRemove?: boolean\n    ignoreChange?: boolean\n    eventNames?: (keyof Model.EventArgs)[]\n    /**\n     * A function evaluated before any command is added. If the function\n     * returns `false`, the command does not get stored. This way you can\n     * control which commands do not get registered for undo/redo.\n     */\n    beforeAddCommand?: <T extends ModelEvents>(\n      this: History,\n      event: T,\n      args: Model.EventArgs[T],\n    ) => any\n    afterAddCommand?: <T extends ModelEvents>(\n      this: History,\n      event: T,\n      args: Model.EventArgs[T],\n      cmd: Command,\n    ) => any\n    executeCommand?: (\n      this: History,\n      cmd: Command,\n      revert: boolean,\n      options: KeyValue,\n    ) => any\n    /**\n     * An array of options property names that passed in undo actions.\n     */\n    revertOptionsList?: string[]\n    /**\n     * An array of options property names that passed in redo actions.\n     */\n    applyOptionsList?: string[]\n    /**\n     * Determine whether to cancel an invalid command or not.\n     */\n    cancelInvalid?: boolean\n  }\n\n  export interface Options extends Partial<CommonOptions> {\n    stackSize?: number\n  }\n\n  interface Data {\n    id?: string\n  }\n\n  export interface CreationData extends Data {\n    edge?: boolean\n    node?: boolean\n    props: Cell.Properties\n  }\n\n  export interface ChangingData extends Data {\n    key: string\n    prev: KeyValue\n    next: KeyValue\n  }\n\n  export interface Command {\n    batch: boolean\n    modelChange?: boolean\n    event?: ModelEvents\n    data: CreationData | ChangingData\n    options?: KeyValue\n  }\n\n  export type Commands = History.Command[] | History.Command\n}\n\nexport namespace History {\n  export interface Args<T = never> {\n    cmds: Command[] | T\n    options: KeyValue\n  }\n\n  export interface EventArgs extends Validator.EventArgs {\n    /**\n     * Triggered when a command was undone.\n     */\n    undo: Args\n    /**\n     * Triggered when a command were redone.\n     */\n    redo: Args\n    /**\n     * Triggered when a command was canceled.\n     */\n    cancel: Args\n    /**\n     * Triggered when command(s) were added to the stack.\n     */\n    add: Args\n    /**\n     * Triggered when all commands were clean.\n     */\n    clean: Args<null>\n    /**\n     * Triggered when any change was made to stacks.\n     */\n    change: Args<null>\n    /**\n     * Triggered when a batch command received.\n     */\n    batch: { cmd: Command; options: KeyValue }\n  }\n}\n\nexport namespace History {\n  /**\n   * Runs a set of callbacks to determine if a command is valid. This is\n   * useful for checking if a certain action in your application does\n   * lead to an invalid state of the graph.\n   */\n  export class Validator extends Basecoat<Validator.EventArgs> {\n    protected readonly command: History\n\n    protected readonly cancelInvalid: boolean\n\n    protected readonly map: { [event: string]: Validator.Callback[][] }\n\n    constructor(options: Validator.Options) {\n      super()\n      this.map = {}\n      this.command = options.history\n      this.cancelInvalid = options.cancelInvalid !== false\n      this.command.on('add', this.onCommandAdded, this)\n    }\n\n    protected onCommandAdded({ cmds }: History.EventArgs['add']) {\n      return Array.isArray(cmds)\n        ? cmds.every((cmd) => this.isValidCommand(cmd))\n        : this.isValidCommand(cmds)\n    }\n\n    protected isValidCommand(cmd: History.Command) {\n      if (cmd.options && cmd.options.validation === false) {\n        return true\n      }\n\n      const callbacks = (cmd.event && this.map[cmd.event]) || []\n\n      let handoverErr: Error | null = null\n\n      callbacks.forEach((routes) => {\n        let i = 0\n\n        const rollup = (err: Error | null) => {\n          const fn = routes[i]\n          i += 1\n\n          try {\n            if (fn) {\n              fn(err, cmd, rollup)\n            } else {\n              handoverErr = err\n              return\n            }\n          } catch (err) {\n            rollup(err)\n          }\n        }\n\n        rollup(handoverErr)\n      })\n\n      if (handoverErr) {\n        if (this.cancelInvalid) {\n          this.command.cancel()\n        }\n        this.emit('invalid', { err: handoverErr })\n        return false\n      }\n\n      return true\n    }\n\n    validate(events: string | string[], ...callbacks: Validator.Callback[]) {\n      const evts = Array.isArray(events) ? events : events.split(/\\s+/)\n\n      callbacks.forEach((callback) => {\n        if (typeof callback !== 'function') {\n          throw new Error(`${evts.join(' ')} requires callback functions.`)\n        }\n      })\n\n      evts.forEach((event) => {\n        if (this.map[event] == null) {\n          this.map[event] = []\n        }\n        this.map[event].push(callbacks)\n      })\n\n      return this\n    }\n\n    @Basecoat.dispose()\n    dispose() {\n      this.command.off('add', this.onCommandAdded, this)\n    }\n  }\n\n  export namespace Validator {\n    export interface Options {\n      history: History\n      /**\n       * To cancel (= undo + delete from redo stack) a command if is not valid.\n       */\n      cancelInvalid?: boolean\n    }\n\n    export type Callback = (\n      err: Error | null,\n      cmd: History.Command,\n      next: (err: Error | null) => any,\n    ) => any\n\n    export interface EventArgs {\n      invalid: { err: Error }\n    }\n  }\n}\n\nnamespace Util {\n  export function isAddEvent(event?: History.ModelEvents) {\n    return event === 'cell:added'\n  }\n\n  export function isRemoveEvent(event?: History.ModelEvents) {\n    return event === 'cell:removed'\n  }\n\n  export function isChangeEvent(event?: History.ModelEvents) {\n    return event != null && event.startsWith('cell:change:')\n  }\n\n  export function getOptions(options: History.Options): History.CommonOptions {\n    const reservedNames: History.ModelEvents[] = [\n      'cell:added',\n      'cell:removed',\n      'cell:change:*',\n    ]\n\n    const batchEvents: History.ModelEvents[] = ['batch:start', 'batch:stop']\n\n    const eventNames = options.eventNames\n      ? options.eventNames.filter(\n          (event) =>\n            !(\n              Util.isChangeEvent(event) ||\n              reservedNames.includes(event) ||\n              batchEvents.includes(event)\n            ),\n        )\n      : reservedNames\n\n    return {\n      enabled: true,\n      ...options,\n      eventNames,\n      applyOptionsList: options.applyOptionsList || ['propertyPath'],\n      revertOptionsList: options.revertOptionsList || ['propertyPath'],\n    }\n  }\n\n  export function sortBatchCommands(cmds: History.Command[]) {\n    const results: History.Command[] = []\n    for (let i = 0, ii = cmds.length; i < ii; i += 1) {\n      const cmd = cmds[i]\n      let index: number | null = null\n\n      if (Util.isAddEvent(cmd.event)) {\n        const id = cmd.data.id\n        for (let j = 0; j < i; j += 1) {\n          if (cmds[j].data.id === id) {\n            index = j\n            break\n          }\n        }\n      }\n\n      if (index !== null) {\n        results.splice(index, 0, cmd)\n      } else {\n        results.push(cmd)\n      }\n    }\n    return results\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AAkCA,MAAM,UAAU,mBAAmB,WAAA;AACjC,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,MAAI,SAAS;AACX,WAAO,QAAQ,UAAS;;AAE1B,SAAO;AACT;AAEA,MAAM,UAAU,gBAAgB,WAAA;AAC9B,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,MAAI,SAAS;AACX,YAAQ,OAAM;;AAEhB,SAAO;AACT;AAEA,MAAM,UAAU,iBAAiB,WAAA;AAC/B,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,MAAI,SAAS;AACX,YAAQ,QAAO;;AAEjB,SAAO;AACT;AAEA,MAAM,UAAU,gBAAgB,SAAU,SAAiB;AACzD,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,MAAI,SAAS;AACX,YAAQ,cAAc,OAAO;;AAE/B,SAAO;AACT;AAEA,MAAM,UAAU,OAAO,SAAU,SAAkB;AACjD,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,MAAI,SAAS;AACX,YAAQ,KAAK,OAAO;;AAEtB,SAAO;AACT;AAEA,MAAM,UAAU,OAAO,SAAU,SAAkB;AACjD,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,MAAI,SAAS;AACX,YAAQ,KAAK,OAAO;;AAEtB,SAAO;AACT;AAEA,MAAM,UAAU,gBAAgB,SAAU,SAAkB;AAC1D,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,MAAI,SAAS;AACX,YAAQ,OAAO,OAAO;;AAExB,SAAO;AACT;AAEA,MAAM,UAAU,UAAU,WAAA;AACxB,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,MAAI,SAAS;AACX,WAAO,QAAQ,QAAO;;AAExB,SAAO;AACT;AAEA,MAAM,UAAU,UAAU,WAAA;AACxB,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,MAAI,SAAS;AACX,WAAO,QAAQ,QAAO;;AAExB,SAAO;AACT;AAEA,MAAM,UAAU,eAAe,SAAU,SAAkB;AACzD,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,MAAI,SAAS;AACX,YAAQ,MAAM,OAAO;;AAEvB,SAAO;AACT;AAEA,MAAM,UAAU,sBAAsB,WAAA;AACpC,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,SAAO,QAAQ,QAAO;AACxB;AAEA,MAAM,UAAU,mBAAmB,WAAA;AACjC,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,SAAO,QAAQ,YAAW;AAC5B;AAEA,MAAM,UAAU,mBAAmB,WAAA;AACjC,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,SAAO,QAAQ,YAAW;AAC5B;AAEA,MAAM,UAAU,oBAAoB,WAAA;AAClC,QAAM,UAAU,KAAK,UAAU,SAAS;AACxC,SAAO,QAAQ,kBAAiB;AAClC;;;;;;;;;ACzHM,IAAO,UAAP,MAAO,iBACH,SAA2B;EAqBnC,YAAY,UAA2B,CAAA,GAAE;AACvC,UAAK;AAnBA,SAAA,OAAO;AAOJ,SAAA,gBAA0C;AAC1C,SAAA,aAAa;AACb,SAAA,iBAAiB;AACjB,SAAA,UAAU;AACV,SAAA,YAAY;AAEH,SAAA,WAGL,CAAA;AAIZ,UAAM,EAAE,YAAY,EAAC,IAAK;AAC1B,SAAK,YAAY;AACjB,SAAK,UAAU,KAAK,WAAW,OAAO;AACtC,SAAK,YAAY,IAAI,SAAQ,UAAU;MACrC,SAAS;MACT,eAAe,KAAK,QAAQ;KAC7B;EACH;EAEA,KAAK,OAAY;AACf,SAAK,QAAQ;AACb,SAAK,QAAQ,KAAK,MAAM;AAExB,SAAK,MAAK;AACV,SAAK,eAAc;EACrB;;EAIA,YAAS;AACP,WAAO,CAAC,KAAK;EACf;EAEA,SAAM;AACJ,QAAI,KAAK,UAAU;AACjB,WAAK,QAAQ,UAAU;;EAE3B;EAEA,UAAO;AACL,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,QAAQ,UAAU;;EAE3B;EAEA,cAAc,SAAiB;AAC7B,QAAI,WAAW,MAAM;AACnB,UAAI,YAAY,KAAK,UAAS,GAAI;AAChC,YAAI,SAAS;AACX,eAAK,OAAM;eACN;AACL,eAAK,QAAO;;;eAGP,KAAK,UAAS,GAAI;AAC3B,WAAK,QAAO;WACP;AACL,WAAK,OAAM;;AAGb,WAAO;EACT;EAEA,KAAK,UAAoB,CAAA,GAAE;AACzB,QAAI,CAAC,KAAK,UAAU;AAClB,YAAM,MAAM,KAAK,UAAU,IAAG;AAC9B,UAAI,KAAK;AACP,aAAK,cAAc,KAAK,OAAO;AAC/B,aAAK,UAAU,KAAK,GAAG;AACvB,aAAK,OAAO,QAAQ,KAAK,OAAO;;;AAGpC,WAAO;EACT;EAEA,KAAK,UAAoB,CAAA,GAAE;AACzB,QAAI,CAAC,KAAK,UAAU;AAClB,YAAM,MAAM,KAAK,UAAU,IAAG;AAC9B,UAAI,KAAK;AACP,aAAK,aAAa,KAAK,OAAO;AAC9B,aAAK,cAAc,GAAG;AACtB,aAAK,OAAO,QAAQ,KAAK,OAAO;;;AAGpC,WAAO;EACT;;;;;EAMA,OAAO,UAAoB,CAAA,GAAE;AAC3B,QAAI,CAAC,KAAK,UAAU;AAClB,YAAM,MAAM,KAAK,UAAU,IAAG;AAC9B,UAAI,KAAK;AACP,aAAK,cAAc,KAAK,OAAO;AAC/B,aAAK,YAAY,CAAA;AACjB,aAAK,OAAO,UAAU,KAAK,OAAO;;;AAGtC,WAAO;EACT;EAEA,UAAO;AACL,WAAO,KAAK;EACd;EAEA,oBAAiB;AACf,UAAM,KAAK,KAAK,UAAU;AAC1B,WAAO,KAAK,YAAY;EAC1B;EAEA,cAAW;AACT,WAAO,KAAK,UAAU;EACxB;EAEA,cAAW;AACT,WAAO,KAAK,UAAU;EACxB;EAEA,UAAO;AACL,WAAO,CAAC,KAAK,YAAY,KAAK,UAAU,SAAS;EACnD;EAEA,UAAO;AACL,WAAO,CAAC,KAAK,YAAY,KAAK,UAAU,SAAS;EACnD;EAEA,MAAM,UAAoB,CAAA,GAAE;AAC1B,SAAK,YAAY,CAAA;AACjB,SAAK,YAAY,CAAA;AACjB,SAAK,OAAO,SAAS,MAAM,OAAO;AAClC,WAAO;EACT;;EAIA,IAAI,WAAQ;AACV,WAAO,KAAK,QAAQ,YAAY;EAClC;EAEU,SACR,WACG,WAAuC;AAE1C,SAAK,UAAU,SAAS,QAAQ,GAAG,SAAS;AAC5C,WAAO;EACT;EAEU,iBAAc;AACtB,SAAK,MAAM,GAAG,eAAe,KAAK,kBAAkB,IAAI;AACxD,SAAK,MAAM,GAAG,cAAc,KAAK,mBAAmB,IAAI;AACxD,QAAI,KAAK,QAAQ,YAAY;AAC3B,WAAK,QAAQ,WAAW,QAAQ,CAAC,MAAM,UAAS;AAC9C,aAAK,SAAS,KAAK,IAAI,KAAK,WAAW,KAAK,MAAM,IAAI;AACtD,aAAK,MAAM,GAAG,MAAM,KAAK,SAAS,KAAK,CAAC;MAC1C,CAAC;;AAGH,SAAK,UAAU,GAAG,WAAW,CAAC,SAAS,KAAK,QAAQ,WAAW,IAAI,CAAC;EACtE;EAEU,gBAAa;AACrB,SAAK,MAAM,IAAI,eAAe,KAAK,kBAAkB,IAAI;AACzD,SAAK,MAAM,IAAI,cAAc,KAAK,mBAAmB,IAAI;AACzD,QAAI,KAAK,QAAQ,YAAY;AAC3B,WAAK,QAAQ,WAAW,QAAQ,CAAC,MAAM,UAAS;AAC9C,aAAK,MAAM,IAAI,MAAM,KAAK,SAAS,KAAK,CAAC;MAC3C,CAAC;AACD,WAAK,SAAS,SAAS;;AAEzB,SAAK,UAAU,IAAI,SAAS;EAC9B;EAEU,cAAc,SAA4B;AAClD,WAAO;MACL,OAAO,UAAU,QAAQ,QAAQ;MACjC,MAAM,CAAA;;EAEV;EAEU,cAAc,KAAuB,SAAkB;AAC/D,SAAK,UAAU;AAEf,UAAM,OAAO,MAAM,QAAQ,GAAG,IAAI,KAAK,kBAAkB,GAAG,IAAI,CAAC,GAAG;AACpE,aAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AAC5C,YAAMA,OAAM,KAAK,CAAC;AAClB,YAAM,eAAY,OAAA,OAAA,OAAA,OAAA,CAAA,GACb,OAAO,GACP,eAAU,KAAKA,KAAI,SAAS,KAAK,QAAQ,qBAAqB,CAAA,CAAE,CAAC;AAEtE,WAAK,eAAeA,MAAK,MAAM,YAAY;;AAG7C,SAAK,UAAU;EACjB;EAEU,aAAa,KAAuB,SAAkB;AAC9D,SAAK,UAAU;AAEf,UAAM,OAAO,MAAM,QAAQ,GAAG,IAAI,KAAK,kBAAkB,GAAG,IAAI,CAAC,GAAG;AACpE,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,YAAMA,OAAM,KAAK,CAAC;AAClB,YAAM,eAAY,OAAA,OAAA,OAAA,OAAA,CAAA,GACb,OAAO,GACP,eAAU,KAAKA,KAAI,SAAS,KAAK,QAAQ,oBAAoB,CAAA,CAAE,CAAC;AAErE,WAAK,eAAeA,MAAK,OAAO,YAAY;;AAG9C,SAAK,UAAU;EACjB;EAEU,eACR,KACA,QACA,SAAiB;AAEjB,UAAM,QAAQ,KAAK;AAEnB,UAAM,OAAO,MAAM,QAAQ,IAAI,KAAK,EAAG;AACvC,UAAM,QAAQ,IAAI;AAElB,QACG,KAAK,WAAW,KAAK,KAAK,UAC1B,KAAK,cAAc,KAAK,KAAK,CAAC,QAC/B;AACA,cAAQ,KAAK,OAAO,OAAO;eAE1B,KAAK,WAAW,KAAK,KAAK,CAAC,UAC3B,KAAK,cAAc,KAAK,KAAK,QAC9B;AACA,YAAM,OAAO,IAAI;AACjB,UAAI,KAAK,MAAM;AACb,cAAM,QAAQ,KAAK,OAAO,OAAO;iBACxB,KAAK,MAAM;AACpB,cAAM,QAAQ,KAAK,OAAO,OAAO;;eAE1B,KAAK,cAAc,KAAK,GAAG;AACpC,YAAM,OAAO,IAAI;AACjB,YAAM,MAAM,KAAK;AACjB,UAAI,OAAO,MAAM;AACf,cAAM,QAAQ,SAAS,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG;AAErD,YAAI,KAAK,QAAQ,SAAS;AACxB,gBAAM,mBAAmB,KAAK,qBAC5B,OACA,SAAS,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,CAAC;AAE1C,cAAI,kBAAkB;AAGpB,oBAAQ,QAAQ;;;AAIpB,aAAK,KAAK,KAAK,OAAO,OAAO;;WAE1B;AACL,YAAM,iBAAiB,KAAK,QAAQ;AACpC,UAAI,gBAAgB;AAClB,qBAAY,KAAK,gBAAgB,MAAM,KAAK,QAAQ,OAAO;;;EAGjE;EAEU,WACR,OACA,MAAwB;AAExB,QAAI,KAAK,WAAW,KAAK,UAAU;AACjC;;AAGF,UAAM,YAAY;AAClB,UAAM,UAAU,UAAU,WAAW,CAAA;AACrC,QAAI,QAAQ,QAAQ;AAClB;;AAGF,QACG,KAAK,WAAW,KAAK,KAAK,KAAK,QAAQ,aACvC,KAAK,cAAc,KAAK,KAAK,KAAK,QAAQ,gBAC1C,KAAK,cAAc,KAAK,KAAK,KAAK,QAAQ,cAC3C;AACA;;AAKF,UAAM,SAAS,KAAK,QAAQ;AAC5B,QACE,UAAU,QACV,aAAY,KAAK,QAAQ,MAAM,OAAO,IAAI,MAAM,OAChD;AACA;;AAGF,QAAI,UAAU,iBAAiB;AAE7B,cAAQ,eAAe,UAAU,GAAG;;AAGtC,UAAM,OAAO,UAAU;AACvB,UAAM,gBAAgB,MAAM,QAAQ,IAAI;AACxC,QAAI;AAEJ,QAAI,KAAK,eAAe;AAGtB,YAAM,KAAK,cAAc,KAAK,IAAI,KAAK,gBAAgB,CAAC,CAAC;AAOzD,YAAM,SACH,iBAAiB,CAAC,IAAI,eAAgB,IAAI,KAAK,OAAO,KAAK;AAC9D,YAAM,WAAW,IAAI,UAAU;AAE/B,UAAI,KAAK,kBAAkB,MAAM,UAAU,WAAW;AAGpD,cAAM,QAAQ,KAAK,cAAc,UAC/B,CAACA,UACG,iBAAiBA,KAAI,eAAgBA,KAAI,KAAK,OAAO,KAAK,OAC5DA,KAAI,UAAU,KAAK;AAGvB,YAAI,QAAQ,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,cAAc,KAAK,GAAG;AACpE,gBAAM,KAAK,cAAc,EAAE,OAAO,KAAI,CAAE;eACnC;AACL,gBAAM,KAAK,cAAc,KAAK;AAC9B,eAAK,cAAc,OAAO,OAAO,CAAC;;AAEpC,aAAK,cAAc,KAAK,GAAG;AAC3B,aAAK,iBAAiB,KAAK,cAAc,SAAS;;WAE/C;AACL,YAAM,KAAK,cAAc,EAAE,OAAO,MAAK,CAAE;;AAK3C,QAAI,KAAK,WAAW,KAAK,KAAK,KAAK,cAAc,KAAK,GAAG;AACvD,YAAM,OAAO,IAAI;AACjB,UAAI,QAAQ;AACZ,UAAI,UAAU;AACd,WAAK,KAAK,KAAK;AACf,WAAK,QAAQ,eAAU,UAAU,KAAK,OAAM,CAAE;AAC9C,UAAI,KAAK,OAAM,GAAI;AACjB,aAAK,OAAO;iBACH,KAAK,OAAM,GAAI;AACxB,aAAK,OAAO;;AAGd,aAAO,KAAK,KAAK,KAAK,OAAO;;AAK/B,QAAI,KAAK,cAAc,KAAK,GAAG;AAC7B,YAAM,MAAO,KAA0C;AACvD,YAAM,OAAO,IAAI;AAEjB,UAAI,CAAC,IAAI,SAAS,CAAC,IAAI,OAAO;AAG5B,YAAI,QAAQ;AACZ,YAAI,UAAU;AACd,aAAK,MAAM;AACX,YAAI,KAAK,QAAQ,MAAM;AACrB,eAAK,OAAO,CAAA;;AAEd,aAAK,KAAK,GAAG,IAAI,eAAU,UAAU,KAAK,SAAS,GAAG,CAAC;AAEvD,YAAI,eAAe;AACjB,cAAI,cAAc;eACb;AACL,eAAK,KAAK,KAAK;;;AAInB,UAAI,KAAK,QAAQ,MAAM;AACrB,aAAK,OAAO,CAAA;;AAEd,WAAK,KAAK,GAAG,IAAI,eAAU,UAAU,KAAK,KAAK,GAAG,CAAC;AACnD,aAAO,KAAK,KAAK,KAAK,OAAO;;AAK/B,UAAM,kBAAkB,KAAK,QAAQ;AACrC,QAAI,iBAAiB;AACnB,mBAAY,KAAK,iBAAiB,MAAM,OAAO,MAAM,GAAG;;AAE1D,SAAK,KAAK,KAAK,OAAO;EACxB;;;;;;;;EASU,iBAAiB,SAAiB;AAC1C,QAAI,KAAK,SAAS;AAChB;;AAEF,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc;WACd;AACL,WAAK,gBAAgB,CAAC,KAAK,cAAc,EAAE,OAAO,KAAI,CAAE,CAAC;AACzD,WAAK,aAAa;AAClB,WAAK,iBAAiB;;EAE1B;;;;;EAMU,kBAAkB,SAAiB;AAC3C,QAAI,KAAK,SAAS;AAChB;;AAGF,QAAI,KAAK,iBAAiB,KAAK,cAAc,GAAG;AAC9C,YAAM,OAAO,KAAK,mBAAmB,KAAK,aAAa;AACvD,UAAI,KAAK,SAAS,GAAG;AACnB,aAAK,YAAY,CAAA;AACjB,aAAK,cAAc,IAAI;AACvB,aAAK,oBAAmB;AACxB,aAAK,OAAO,OAAO,MAAM,OAAO;;AAElC,WAAK,gBAAgB;AACrB,WAAK,iBAAiB;AACtB,WAAK,aAAa;eACT,KAAK,iBAAiB,KAAK,aAAa,GAAG;AACpD,WAAK,cAAc;;EAEvB;EAEU,mBAAmB,eAAgC;AAC3D,QAAI,OAAO,cAAc,MAAK;AAC9B,UAAM,SAAS,CAAA;AAEf,WAAO,KAAK,SAAS,GAAG;AACtB,YAAM,MAAM,KAAK,MAAK;AACtB,YAAM,MAAM,IAAI;AAChB,YAAM,KAAK,IAAI,KAAK;AAEpB,UAAI,OAAO,SAAS,MAAM,QAAQ,IAAI,cAAc;AAClD,YAAI,KAAK,WAAW,GAAG,GAAG;AACxB,gBAAM,QAAQ,KAAK,UACjB,CAAC,MAAM,KAAK,cAAc,EAAE,KAAK,KAAK,EAAE,KAAK,OAAO,EAAE;AAGxD,cAAI,SAAS,GAAG;AACd,mBAAO,KAAK,OAAO,CAAC,GAAG,MAAM,QAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;AAC1D;;mBAEO,KAAK,cAAc,GAAG,GAAG;AAClC,gBAAM,QAAQ,KAAK,UACjB,CAAC,MAAM,KAAK,WAAW,EAAE,KAAK,KAAK,EAAE,KAAK,OAAO,EAAE;AAErD,cAAI,SAAS,GAAG;AACd,iBAAK,OAAO,OAAO,CAAC;AACpB;;mBAEO,KAAK,cAAc,GAAG,GAAG;AAClC,gBAAM,OAAO,IAAI;AAEjB,cAAI,eAAU,QAAQ,KAAK,MAAM,KAAK,IAAI,GAAG;AAC3C;;eAEG;;AAIP,eAAO,KAAK,GAAG;;;AAInB,WAAO;EACT;EAEU,OACR,OACA,KACA,SAAiB;AAEjB,UAAM,OAAO,OAAO,OAAO,OAAO,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,GAAG;AACjE,SAAK,KAAK,OAAO,EAAE,MAAM,QAAO,CAAE;AAClC,SAAK,MAAM,QAAQ,WAAW,KAAK,IAAI,EAAE,MAAM,QAAO,CAAE;AACxD,SAAK,KAAK,UAAU,EAAE,MAAM,QAAO,CAAE;AACrC,SAAK,MAAM,QAAQ,kBAAkB,EAAE,MAAM,QAAO,CAAE;EACxD;EAEU,KAAK,KAAsB,SAAiB;AACpD,SAAK,YAAY,CAAA;AACjB,QAAI,IAAI,OAAO;AACb,WAAK,iBAAiB,KAAK,IAAI,KAAK,gBAAgB,CAAC;AACrD,WAAK,KAAK,SAAS,EAAE,KAAK,QAAO,CAAE;WAC9B;AACL,WAAK,cAAc,GAAG;AACtB,WAAK,oBAAmB;AACxB,WAAK,OAAO,OAAO,KAAK,OAAO;;EAEnC;;;;;;;;;;;;;EAcU,sBAAmB;;AAC3B,UAAM,mBAAmB,KAAK,UAAU,KAAK,UAAU,SAAS,CAAC;AACjE,UAAM,0BAA0B,KAAK,UAAU,KAAK,UAAU,SAAS,CAAC;AAIxE,QAAI,CAAC,MAAM,QAAQ,gBAAgB,GAAG;AACpC;;AAEF,UAAM,aAAa,IAAI,IAAI,iBAAiB,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC;AACnE,QACE,WAAW,SAAS,KACpB,CAAC,WAAW,IAAI,oBAAoB,KACpC,CAAC,WAAW,IAAI,sBAAsB,GACtC;AACA;;AAIF,QAAI,CAAC,iBAAiB,MAAM,CAAC,QAAO;AAAA,UAAAC;AAAC,aAAA,IAAI,WAASA,MAAA,IAAI,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAE;IAAE,CAAA,GAAG;AAClE;;AAKF,QACE,CAAC,MAAM,QAAQ,uBAAuB,KACtC,wBAAwB,WAAW,GACnC;AACA;;AAEF,UAAM,sBAAsB,wBAAwB,CAAC;AACrD,QACE,oBAAoB,UAAU,0BAC9B,GAAC,KAAA,oBAAoB,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,KAC9B;AACA;;AAIF,4BAAwB,KAAK,GAAG,gBAAgB;AAChD,SAAK,UAAU,IAAG;EACpB;EAEU,cAAc,KAAqB;AAC3C,QAAI,KAAK,cAAc,GAAG;AACxB,WAAK,UAAU,KAAK,GAAG;AACvB;;AAEF,QAAI,KAAK,UAAU,UAAU,KAAK,WAAW;AAC3C,WAAK,UAAU,MAAK;;AAEtB,SAAK,UAAU,KAAK,GAAG;EACzB;EAEU,qBACR,UACA,UAA6B;AAE7B,QAAI,mBAAmB;AACvB,QACE,aAAa,QACb,aAAa,QACb,OAAO,aAAa,YACpB,OAAO,aAAa,UACpB;AACA,aAAO,KAAK,QAAQ,EAAE,QAAQ,CAAC,QAAO;AAEpC,YAAI,SAAS,GAAG,MAAM,UAAa,SAAS,GAAG,MAAM,QAAW;AAC9D,mBAAS,GAAG,IAAI;AAChB,6BAAmB;mBAEnB,OAAO,SAAS,GAAG,MAAM,YACzB,OAAO,SAAS,GAAG,MAAM,UACzB;AACA,6BAAmB,KAAK,qBACtB,SAAS,GAAG,GACZ,SAAS,GAAG,CAAC;;MAGnB,CAAC;;AAEH,WAAO;EACT;EAGA,UAAO;AACL,SAAK,UAAU,QAAO;AACtB,SAAK,MAAK;AACV,SAAK,cAAa;AAClB,SAAK,IAAG;EACV;;AALA,WAAA;EADC,SAAS,QAAO;;CA2HnB,SAAiBC,UAAO;EAMtB,MAAa,kBAAkB,SAA6B;IAO1D,YAAY,SAA0B;AACpC,YAAK;AACL,WAAK,MAAM,CAAA;AACX,WAAK,UAAU,QAAQ;AACvB,WAAK,gBAAgB,QAAQ,kBAAkB;AAC/C,WAAK,QAAQ,GAAG,OAAO,KAAK,gBAAgB,IAAI;IAClD;IAEU,eAAe,EAAE,KAAI,GAA4B;AACzD,aAAO,MAAM,QAAQ,IAAI,IACrB,KAAK,MAAM,CAAC,QAAQ,KAAK,eAAe,GAAG,CAAC,IAC5C,KAAK,eAAe,IAAI;IAC9B;IAEU,eAAe,KAAoB;AAC3C,UAAI,IAAI,WAAW,IAAI,QAAQ,eAAe,OAAO;AACnD,eAAO;;AAGT,YAAM,YAAa,IAAI,SAAS,KAAK,IAAI,IAAI,KAAK,KAAM,CAAA;AAExD,UAAI,cAA4B;AAEhC,gBAAU,QAAQ,CAAC,WAAU;AAC3B,YAAI,IAAI;AAER,cAAM,SAAS,CAAC,QAAqB;AACnC,gBAAM,KAAK,OAAO,CAAC;AACnB,eAAK;AAEL,cAAI;AACF,gBAAI,IAAI;AACN,iBAAG,KAAK,KAAK,MAAM;mBACd;AACL,4BAAc;AACd;;mBAEKC,MAAK;AACZ,mBAAOA,IAAG;;QAEd;AAEA,eAAO,WAAW;MACpB,CAAC;AAED,UAAI,aAAa;AACf,YAAI,KAAK,eAAe;AACtB,eAAK,QAAQ,OAAM;;AAErB,aAAK,KAAK,WAAW,EAAE,KAAK,YAAW,CAAE;AACzC,eAAO;;AAGT,aAAO;IACT;IAEA,SAAS,WAA8B,WAA+B;AACpE,YAAM,OAAO,MAAM,QAAQ,MAAM,IAAI,SAAS,OAAO,MAAM,KAAK;AAEhE,gBAAU,QAAQ,CAAC,aAAY;AAC7B,YAAI,OAAO,aAAa,YAAY;AAClC,gBAAM,IAAI,MAAM,GAAG,KAAK,KAAK,GAAG,CAAC,+BAA+B;;MAEpE,CAAC;AAED,WAAK,QAAQ,CAAC,UAAS;AACrB,YAAI,KAAK,IAAI,KAAK,KAAK,MAAM;AAC3B,eAAK,IAAI,KAAK,IAAI,CAAA;;AAEpB,aAAK,IAAI,KAAK,EAAE,KAAK,SAAS;MAChC,CAAC;AAED,aAAO;IACT;IAGA,UAAO;AACL,WAAK,QAAQ,IAAI,OAAO,KAAK,gBAAgB,IAAI;IACnD;;AAFA,aAAA;IADC,SAAS,QAAO;;AAlFN,EAAAD,SAAA,YAAS;AA2GxB,GAjHiB,YAAA,UAAO,CAAA,EAAA;AAmHxB,IAAU;CAAV,SAAUE,OAAI;AACZ,WAAgB,WAAW,OAA2B;AACpD,WAAO,UAAU;EACnB;AAFgB,EAAAA,MAAA,aAAU;AAI1B,WAAgB,cAAc,OAA2B;AACvD,WAAO,UAAU;EACnB;AAFgB,EAAAA,MAAA,gBAAa;AAI7B,WAAgB,cAAc,OAA2B;AACvD,WAAO,SAAS,QAAQ,MAAM,WAAW,cAAc;EACzD;AAFgB,EAAAA,MAAA,gBAAa;AAI7B,WAAgB,WAAW,SAAwB;AACjD,UAAM,gBAAuC;MAC3C;MACA;MACA;;AAGF,UAAM,cAAqC,CAAC,eAAe,YAAY;AAEvE,UAAM,aAAa,QAAQ,aACvB,QAAQ,WAAW,OACjB,CAAC,UACC,EACEA,MAAK,cAAc,KAAK,KACxB,cAAc,SAAS,KAAK,KAC5B,YAAY,SAAS,KAAK,EAC3B,IAEL;AAEJ,WAAA,OAAA,OAAA,OAAA,OAAA,EACE,SAAS,KAAI,GACV,OAAO,GAAA,EACV,YACA,kBAAkB,QAAQ,oBAAoB,CAAC,cAAc,GAC7D,mBAAmB,QAAQ,qBAAqB,CAAC,cAAc,EAAC,CAAA;EAEpE;AA3BgB,EAAAA,MAAA,aAAU;AA6B1B,WAAgB,kBAAkB,MAAuB;AACvD,UAAM,UAA6B,CAAA;AACnC,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK,GAAG;AAChD,YAAM,MAAM,KAAK,CAAC;AAClB,UAAI,QAAuB;AAE3B,UAAIA,MAAK,WAAW,IAAI,KAAK,GAAG;AAC9B,cAAM,KAAK,IAAI,KAAK;AACpB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC7B,cAAI,KAAK,CAAC,EAAE,KAAK,OAAO,IAAI;AAC1B,oBAAQ;AACR;;;;AAKN,UAAI,UAAU,MAAM;AAClB,gBAAQ,OAAO,OAAO,GAAG,GAAG;aACvB;AACL,gBAAQ,KAAK,GAAG;;;AAGpB,WAAO;EACT;AAvBgB,EAAAA,MAAA,oBAAiB;AAwBnC,GAlEU,SAAA,OAAI,CAAA,EAAA;", "names": ["cmd", "_a", "History", "err", "<PERSON><PERSON>"]}