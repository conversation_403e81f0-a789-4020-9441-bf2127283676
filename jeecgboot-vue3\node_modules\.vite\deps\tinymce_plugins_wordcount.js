import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/wordcount/plugin.js
var require_plugin = __commonJS({
  "node_modules/.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/wordcount/plugin.js"() {
    (function() {
      "use strict";
      var global$2 = tinymce.util.Tools.resolve("tinymce.PluginManager");
      const eq = (t) => (a) => t === a;
      const isNull = eq(null);
      const identity = (x) => {
        return x;
      };
      const map = (xs, f) => {
        const len = xs.length;
        const r = new Array(len);
        for (let i = 0; i < len; i++) {
          const x = xs[i];
          r[i] = f(x, i);
        }
        return r;
      };
      const punctuationStr = `[~№|!-*+-\\/:;?@\\[-\`{}¡«·»¿;·՚-՟։֊־׀׃׆׳״؉؊،؍؛؞؟٪-٭۔܀-܍߷-߹࠰-࠾࡞।॥॰෴๏๚๛༄-༒༺-༽྅࿐-࿔࿙࿚၊-၏჻፡-፨᐀᙭᙮᚛᚜᛫-᛭᜵᜶។-៖៘-៚᠀-᠊᥄᥅᨞᨟᪠-᪦᪨-᪭᭚-᭠᯼-᯿᰻-᰿᱾᱿᳓‐-‧‰-⁃⁅-⁑⁓-⁞⁽⁾₍₎〈〉❨-❵⟅⟆⟦-⟯⦃-⦘⧘-⧛⧼⧽⳹-⳼⳾⳿⵰⸀-⸮⸰⸱、-〃〈-】〔-〟〰〽゠・꓾꓿꘍-꘏꙳꙾꛲-꛷꡴-꡷꣎꣏꣸-꣺꤮꤯꥟꧁-꧍꧞꧟꩜-꩟꫞꫟꯫﴾﴿︐-︙︰-﹒﹔-﹡﹣﹨﹪﹫！-＃％-＊，-／：；？＠［-］＿｛｝｟-･]`;
      const regExps = {
        aletter: "[A-Za-zªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԧԱ-Ֆՙա-ևא-תװ-׳ؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘऄ-हऽॐक़-ॡॱ-ॷॹ-ॿঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-ళవ-హఽౘౙౠౡಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഅ-ഌഎ-ഐഒ-ഺഽൎൠൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆༀཀ-ཇཉ-ཬྈ-ྌႠ-Ⴥა-ჺჼᄀ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏼᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛰᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰᠠ-ᡷᢀ-ᢨᢪᢰ-ᣵᤀ-ᤜᨀ-ᨖᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᯀ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᳩ-ᳬᳮ-ᳱᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⒶ-ⓩⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⴀ-ⴥⴰ-ⵥⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞⸯ々〻〼ㄅ-ㄭㄱ-ㆎㆠ-ㆺꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚗꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞎꞐꞑꞠ-ꞩꟺ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꨀ-ꨨꩀ-ꩂꩄ-ꩋꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꯀ-ꯢ가-힣ힰ-ퟆퟋ-ퟻﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚﾠ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ]",
        midnumlet: `[-'\\.‘’․﹒＇．]`,
        midletter: "[:··״‧︓﹕：]",
        midnum: "[±+*/,;;։،؍٬߸⁄︐︔﹐﹔，；]",
        numeric: "[0-9٠-٩٫۰-۹߀-߉०-९০-৯੦-੯૦-૯୦-୯௦-௯౦-౯೦-೯൦-൯๐-๙໐-໙༠-༩၀-၉႐-႙០-៩᠐-᠙᥆-᥏᧐-᧙᪀-᪉᪐-᪙᭐-᭙᮰-᮹᱀-᱉᱐-᱙꘠-꘩꣐-꣙꤀-꤉꧐-꧙꩐-꩙꯰-꯹]",
        cr: "\\r",
        lf: "\\n",
        newline: "[\v\f\u2028\u2029]",
        extend: "[̀-ͯ҃-҉֑-ׇֽֿׁׂׅׄؐ-ًؚ-ٰٟۖ-ۜ۟-۪ۤۧۨ-ܑۭܰ-݊ަ-ް߫-߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛ऀ-ःऺ-़ा-ॏ॑-ॗॢॣঁ-ঃ়া-ৄেৈো-্ৗৢৣਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑੰੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣଁ-ଃ଼ା-ୄେୈୋ-୍ୖୗୢୣஂா-ூெ-ைொ-்ௗఁ-ఃా-ౄె-ైొ-్ౕౖౢౣಂಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣംഃാ-ൄെ-ൈൊ-്ൗൢൣංඃ්ා-ුූෘ-ෟෲෳัิ-ฺ็-๎ັິ-ູົຼ່-ໍ༹༘༙༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏႚ-ႝ፝-፟ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳា-៓៝᠋-᠍ᢩᤠ-ᤫᤰ-᤻ᦰ-ᧀᧈᧉᨗ-ᨛᩕ-ᩞ᩠-᩿᩼ᬀ-ᬄ᬴-᭄᭫-᭳ᮀ-ᮂᮡ-᯦᮪-᯳ᰤ-᰷᳐-᳔᳒-᳨᳭ᳲ᷀-ᷦ᷼-᷿‌‍⃐-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꙯-꙲꙼꙽꛰꛱ꠂ꠆ꠋꠣ-ꠧꢀꢁꢴ-꣄꣠-꣱ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀ꨩ-ꨶꩃꩌꩍꩻꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꯣ-ꯪ꯬꯭ﬞ︀-️︠-︦ﾞﾟ]",
        format: "[­؀-؃۝܏឴឵‎‏‪-‮⁠-⁤⁪-⁯\uFEFF￹-￻]",
        katakana: "[〱-〵゛゜゠-ヺー-ヿㇰ-ㇿ㋐-㋾㌀-㍗ｦ-ﾝ]",
        extendnumlet: "[=_‿⁀⁔︳︴﹍-﹏＿∀-⋿<>]",
        punctuation: punctuationStr
      };
      const characterIndices = {
        ALETTER: 0,
        MIDNUMLET: 1,
        MIDLETTER: 2,
        MIDNUM: 3,
        NUMERIC: 4,
        CR: 5,
        LF: 6,
        NEWLINE: 7,
        EXTEND: 8,
        FORMAT: 9,
        KATAKANA: 10,
        EXTENDNUMLET: 11,
        AT: 12,
        OTHER: 13
      };
      const SETS$1 = [
        new RegExp(regExps.aletter),
        new RegExp(regExps.midnumlet),
        new RegExp(regExps.midletter),
        new RegExp(regExps.midnum),
        new RegExp(regExps.numeric),
        new RegExp(regExps.cr),
        new RegExp(regExps.lf),
        new RegExp(regExps.newline),
        new RegExp(regExps.extend),
        new RegExp(regExps.format),
        new RegExp(regExps.katakana),
        new RegExp(regExps.extendnumlet),
        new RegExp("@")
      ];
      const EMPTY_STRING$1 = "";
      const PUNCTUATION$1 = new RegExp("^" + regExps.punctuation + "$");
      const WHITESPACE$1 = /^\s+$/;
      const SETS = SETS$1;
      const OTHER = characterIndices.OTHER;
      const getType = (char) => {
        let type = OTHER;
        const setsLength = SETS.length;
        for (let j = 0; j < setsLength; ++j) {
          const set = SETS[j];
          if (set && set.test(char)) {
            type = j;
            break;
          }
        }
        return type;
      };
      const memoize = (func) => {
        const cache = {};
        return (char) => {
          if (cache[char]) {
            return cache[char];
          } else {
            const result = func(char);
            cache[char] = result;
            return result;
          }
        };
      };
      const classify = (characters) => {
        const memoized = memoize(getType);
        return map(characters, memoized);
      };
      const isWordBoundary = (map2, index) => {
        const type = map2[index];
        const nextType = map2[index + 1];
        if (index < 0 || index > map2.length - 1 && index !== 0) {
          return false;
        }
        if (type === characterIndices.ALETTER && nextType === characterIndices.ALETTER) {
          return false;
        }
        const nextNextType = map2[index + 2];
        if (type === characterIndices.ALETTER && (nextType === characterIndices.MIDLETTER || nextType === characterIndices.MIDNUMLET || nextType === characterIndices.AT) && nextNextType === characterIndices.ALETTER) {
          return false;
        }
        const prevType = map2[index - 1];
        if ((type === characterIndices.MIDLETTER || type === characterIndices.MIDNUMLET || nextType === characterIndices.AT) && nextType === characterIndices.ALETTER && prevType === characterIndices.ALETTER) {
          return false;
        }
        if ((type === characterIndices.NUMERIC || type === characterIndices.ALETTER) && (nextType === characterIndices.NUMERIC || nextType === characterIndices.ALETTER)) {
          return false;
        }
        if ((type === characterIndices.MIDNUM || type === characterIndices.MIDNUMLET) && nextType === characterIndices.NUMERIC && prevType === characterIndices.NUMERIC) {
          return false;
        }
        if (type === characterIndices.NUMERIC && (nextType === characterIndices.MIDNUM || nextType === characterIndices.MIDNUMLET) && nextNextType === characterIndices.NUMERIC) {
          return false;
        }
        if ((type === characterIndices.EXTEND || type === characterIndices.FORMAT) && (nextType === characterIndices.ALETTER || nextType === characterIndices.NUMERIC || nextType === characterIndices.KATAKANA || nextType === characterIndices.EXTEND || nextType === characterIndices.FORMAT) || (nextType === characterIndices.EXTEND || nextType === characterIndices.FORMAT && (nextNextType === characterIndices.ALETTER || nextNextType === characterIndices.NUMERIC || nextNextType === characterIndices.KATAKANA || nextNextType === characterIndices.EXTEND || nextNextType === characterIndices.FORMAT)) && (type === characterIndices.ALETTER || type === characterIndices.NUMERIC || type === characterIndices.KATAKANA || type === characterIndices.EXTEND || type === characterIndices.FORMAT)) {
          return false;
        }
        if (type === characterIndices.CR && nextType === characterIndices.LF) {
          return false;
        }
        if (type === characterIndices.NEWLINE || type === characterIndices.CR || type === characterIndices.LF) {
          return true;
        }
        if (nextType === characterIndices.NEWLINE || nextType === characterIndices.CR || nextType === characterIndices.LF) {
          return true;
        }
        if (type === characterIndices.KATAKANA && nextType === characterIndices.KATAKANA) {
          return false;
        }
        if (nextType === characterIndices.EXTENDNUMLET && (type === characterIndices.ALETTER || type === characterIndices.NUMERIC || type === characterIndices.KATAKANA || type === characterIndices.EXTENDNUMLET)) {
          return false;
        }
        if (type === characterIndices.EXTENDNUMLET && (nextType === characterIndices.ALETTER || nextType === characterIndices.NUMERIC || nextType === characterIndices.KATAKANA)) {
          return false;
        }
        if (type === characterIndices.AT) {
          return false;
        }
        return true;
      };
      const EMPTY_STRING = EMPTY_STRING$1;
      const WHITESPACE = WHITESPACE$1;
      const PUNCTUATION = PUNCTUATION$1;
      const isProtocol = (str) => str === "http" || str === "https";
      const findWordEnd = (characters, startIndex) => {
        let i;
        for (i = startIndex; i < characters.length; i++) {
          if (WHITESPACE.test(characters[i])) {
            break;
          }
        }
        return i;
      };
      const findUrlEnd = (characters, startIndex) => {
        const endIndex = findWordEnd(characters, startIndex + 1);
        const peakedWord = characters.slice(startIndex + 1, endIndex).join(EMPTY_STRING);
        return peakedWord.substr(0, 3) === "://" ? endIndex : startIndex;
      };
      const findWordsWithIndices = (chars, sChars, characterMap, options) => {
        const words = [];
        const indices = [];
        let word = [];
        for (let i = 0; i < characterMap.length; ++i) {
          word.push(chars[i]);
          if (isWordBoundary(characterMap, i)) {
            const ch = sChars[i];
            if ((options.includeWhitespace || !WHITESPACE.test(ch)) && (options.includePunctuation || !PUNCTUATION.test(ch))) {
              const startOfWord = i - word.length + 1;
              const endOfWord = i + 1;
              const str = sChars.slice(startOfWord, endOfWord).join(EMPTY_STRING);
              if (isProtocol(str)) {
                const endOfUrl = findUrlEnd(sChars, i);
                const url = chars.slice(endOfWord, endOfUrl);
                Array.prototype.push.apply(word, url);
                i = endOfUrl;
              }
              words.push(word);
              indices.push({
                start: startOfWord,
                end: endOfWord
              });
            }
            word = [];
          }
        }
        return {
          words,
          indices
        };
      };
      const getDefaultOptions = () => ({
        includeWhitespace: false,
        includePunctuation: false
      });
      const getWordsWithIndices = (chars, extract, options) => {
        options = {
          ...getDefaultOptions(),
          ...options
        };
        const extractedChars = map(chars, extract);
        const characterMap = classify(extractedChars);
        return findWordsWithIndices(chars, extractedChars, characterMap, options);
      };
      const getWords$1 = (chars, extract, options) => getWordsWithIndices(chars, extract, options).words;
      const getWords = getWords$1;
      const removeZwsp$1 = (s) => s.replace(/\uFEFF/g, "");
      var global$1 = tinymce.util.Tools.resolve("tinymce.dom.TreeWalker");
      const getText = (node, schema) => {
        const blockElements = schema.getBlockElements();
        const voidElements = schema.getVoidElements();
        const isNewline = (node2) => blockElements[node2.nodeName] || voidElements[node2.nodeName];
        const textBlocks = [];
        let txt = "";
        const treeWalker = new global$1(node, node);
        let tempNode;
        while (tempNode = treeWalker.next()) {
          if (tempNode.nodeType === 3) {
            txt += removeZwsp$1(tempNode.data);
          } else if (isNewline(tempNode) && txt.length) {
            textBlocks.push(txt);
            txt = "";
          }
        }
        if (txt.length) {
          textBlocks.push(txt);
        }
        return textBlocks;
      };
      const removeZwsp = (text) => text.replace(/\u200B/g, "");
      const strLen = (str) => str.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g, "_").length;
      const countWords = (node, schema) => {
        const text = removeZwsp(getText(node, schema).join("\n"));
        return getWords(text.split(""), identity).length;
      };
      const countCharacters = (node, schema) => {
        const text = getText(node, schema).join("");
        return strLen(text);
      };
      const countCharactersWithoutSpaces = (node, schema) => {
        const text = getText(node, schema).join("").replace(/\s/g, "");
        return strLen(text);
      };
      const createBodyCounter = (editor, count) => () => count(editor.getBody(), editor.schema);
      const createSelectionCounter = (editor, count) => () => count(editor.selection.getRng().cloneContents(), editor.schema);
      const createBodyWordCounter = (editor) => createBodyCounter(editor, countWords);
      const get = (editor) => ({
        body: {
          getWordCount: createBodyWordCounter(editor),
          getCharacterCount: createBodyCounter(editor, countCharacters),
          getCharacterCountWithoutSpaces: createBodyCounter(editor, countCharactersWithoutSpaces)
        },
        selection: {
          getWordCount: createSelectionCounter(editor, countWords),
          getCharacterCount: createSelectionCounter(editor, countCharacters),
          getCharacterCountWithoutSpaces: createSelectionCounter(editor, countCharactersWithoutSpaces)
        },
        getCount: createBodyWordCounter(editor)
      });
      const open = (editor, api) => {
        editor.windowManager.open({
          title: "Word Count",
          body: {
            type: "panel",
            items: [{
              type: "table",
              header: [
                "Count",
                "Document",
                "Selection"
              ],
              cells: [
                [
                  "Words",
                  String(api.body.getWordCount()),
                  String(api.selection.getWordCount())
                ],
                [
                  "Characters (no spaces)",
                  String(api.body.getCharacterCountWithoutSpaces()),
                  String(api.selection.getCharacterCountWithoutSpaces())
                ],
                [
                  "Characters",
                  String(api.body.getCharacterCount()),
                  String(api.selection.getCharacterCount())
                ]
              ]
            }]
          },
          buttons: [{
            type: "cancel",
            name: "close",
            text: "Close",
            primary: true
          }]
        });
      };
      const register$1 = (editor, api) => {
        editor.addCommand("mceWordCount", () => open(editor, api));
      };
      const first = (fn, rate) => {
        let timer = null;
        const cancel = () => {
          if (!isNull(timer)) {
            clearTimeout(timer);
            timer = null;
          }
        };
        const throttle = (...args) => {
          if (isNull(timer)) {
            timer = setTimeout(() => {
              timer = null;
              fn.apply(null, args);
            }, rate);
          }
        };
        return {
          cancel,
          throttle
        };
      };
      var global = tinymce.util.Tools.resolve("tinymce.util.Delay");
      const fireWordCountUpdate = (editor, api) => {
        editor.dispatch("wordCountUpdate", {
          wordCount: {
            words: api.body.getWordCount(),
            characters: api.body.getCharacterCount(),
            charactersWithoutSpaces: api.body.getCharacterCountWithoutSpaces()
          }
        });
      };
      const updateCount = (editor, api) => {
        fireWordCountUpdate(editor, api);
      };
      const setup = (editor, api, delay) => {
        const debouncedUpdate = first(() => updateCount(editor, api), delay);
        editor.on("init", () => {
          updateCount(editor, api);
          global.setEditorTimeout(editor, () => {
            editor.on("SetContent BeforeAddUndo Undo Redo ViewUpdate keyup", debouncedUpdate.throttle);
          }, 0);
          editor.on("remove", debouncedUpdate.cancel);
        });
      };
      const register = (editor) => {
        const onAction = () => editor.execCommand("mceWordCount");
        editor.ui.registry.addButton("wordcount", {
          tooltip: "Word count",
          icon: "character-count",
          onAction
        });
        editor.ui.registry.addMenuItem("wordcount", {
          text: "Word count",
          icon: "character-count",
          onAction
        });
      };
      var Plugin = (delay = 300) => {
        global$2.add("wordcount", (editor) => {
          const api = get(editor);
          register$1(editor, api);
          register(editor);
          setup(editor, api, delay);
          return api;
        });
      };
      Plugin();
    })();
  }
});

// node_modules/.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/wordcount/index.js
require_plugin();
//# sourceMappingURL=tinymce_plugins_wordcount.js.map
