{"version": 3, "sources": ["../../.pnpm/codemirror@5.65.19/node_modules/codemirror/addon/hint/anyword-hint.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var WORD = /[\\w$]+/, RANGE = 500;\n\n  CodeMirror.registerHelper(\"hint\", \"anyword\", function(editor, options) {\n    var word = options && options.word || WORD;\n    var range = options && options.range || RANGE;\n    var cur = editor.getCursor(), curLine = editor.getLine(cur.line);\n    var end = cur.ch, start = end;\n    while (start && word.test(curLine.charAt(start - 1))) --start;\n    var curWord = start != end && curLine.slice(start, end);\n\n    var list = options && options.list || [], seen = {};\n    var re = new RegExp(word.source, \"g\");\n    for (var dir = -1; dir <= 1; dir += 2) {\n      var line = cur.line, endLine = Math.min(Math.max(line + dir * range, editor.firstLine()), editor.lastLine()) + dir;\n      for (; line != endLine; line += dir) {\n        var text = editor.getLine(line), m;\n        while (m = re.exec(text)) {\n          if (line == cur.line && m[0] === curWord) continue;\n          if ((!curWord || m[0].lastIndexOf(curWord, 0) == 0) && !Object.prototype.hasOwnProperty.call(seen, m[0])) {\n            seen[m[0]] = true;\n            list.push(m[0]);\n          }\n        }\n      }\n    }\n    return {list: list, from: CodeMirror.Pos(cur.line, start), to: CodeMirror.Pos(cur.line, end)};\n  });\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,UAAI,OAAO,UAAU,QAAQ;AAE7B,MAAAA,YAAW,eAAe,QAAQ,WAAW,SAAS,QAAQ,SAAS;AACrE,YAAI,OAAO,WAAW,QAAQ,QAAQ;AACtC,YAAI,QAAQ,WAAW,QAAQ,SAAS;AACxC,YAAI,MAAM,OAAO,UAAU,GAAG,UAAU,OAAO,QAAQ,IAAI,IAAI;AAC/D,YAAI,MAAM,IAAI,IAAI,QAAQ;AAC1B,eAAO,SAAS,KAAK,KAAK,QAAQ,OAAO,QAAQ,CAAC,CAAC,EAAG,GAAE;AACxD,YAAI,UAAU,SAAS,OAAO,QAAQ,MAAM,OAAO,GAAG;AAEtD,YAAI,OAAO,WAAW,QAAQ,QAAQ,CAAC,GAAG,OAAO,CAAC;AAClD,YAAI,KAAK,IAAI,OAAO,KAAK,QAAQ,GAAG;AACpC,iBAAS,MAAM,IAAI,OAAO,GAAG,OAAO,GAAG;AACrC,cAAI,OAAO,IAAI,MAAM,UAAU,KAAK,IAAI,KAAK,IAAI,OAAO,MAAM,OAAO,OAAO,UAAU,CAAC,GAAG,OAAO,SAAS,CAAC,IAAI;AAC/G,iBAAO,QAAQ,SAAS,QAAQ,KAAK;AACnC,gBAAI,OAAO,OAAO,QAAQ,IAAI,GAAG;AACjC,mBAAO,IAAI,GAAG,KAAK,IAAI,GAAG;AACxB,kBAAI,QAAQ,IAAI,QAAQ,EAAE,CAAC,MAAM,QAAS;AAC1C,mBAAK,CAAC,WAAW,EAAE,CAAC,EAAE,YAAY,SAAS,CAAC,KAAK,MAAM,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,EAAE,CAAC,CAAC,GAAG;AACxG,qBAAK,EAAE,CAAC,CAAC,IAAI;AACb,qBAAK,KAAK,EAAE,CAAC,CAAC;AAAA,cAChB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO,EAAC,MAAY,MAAMA,YAAW,IAAI,IAAI,MAAM,KAAK,GAAG,IAAIA,YAAW,IAAI,IAAI,MAAM,GAAG,EAAC;AAAA,MAC9F,CAAC;AAAA,IACH,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}