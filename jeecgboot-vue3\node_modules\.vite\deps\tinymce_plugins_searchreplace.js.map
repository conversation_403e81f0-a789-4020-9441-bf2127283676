{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/searchreplace/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/searchreplace/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType$1 = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const isString = isType$1('string');\n    const isArray = isType$1('array');\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isNumber = isSimpleType('number');\n\n    const noop = () => {\n    };\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    const always = constant(true);\n\n    const punctuationStr = `[~\\u2116|!-*+-\\\\/:;?@\\\\[-\\`{}\\u00A1\\u00AB\\u00B7\\u00BB\\u00BF;\\u00B7\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061E\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1361-\\u1368\\u1400\\u166D\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u3008\\u3009\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30\\u2E31\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]`;\n\n    const punctuation$1 = constant(punctuationStr);\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const punctuation = punctuation$1;\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const nativeSlice = Array.prototype.slice;\n    const nativePush = Array.prototype.push;\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const eachr = (xs, f) => {\n      for (let i = xs.length - 1; i >= 0; i--) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const groupBy = (xs, f) => {\n      if (xs.length === 0) {\n        return [];\n      } else {\n        let wasType = f(xs[0]);\n        const r = [];\n        let group = [];\n        for (let i = 0, len = xs.length; i < len; i++) {\n          const x = xs[i];\n          const type = f(x);\n          if (type !== wasType) {\n            r.push(group);\n            group = [];\n          }\n          wasType = type;\n          group.push(x);\n        }\n        if (group.length !== 0) {\n          r.push(group);\n        }\n        return r;\n      }\n    };\n    const foldl = (xs, f, acc) => {\n      each(xs, (x, i) => {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    const flatten = xs => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    const bind = (xs, f) => flatten(map(xs, f));\n    const sort = (xs, comparator) => {\n      const copy = nativeSlice.call(xs, 0);\n      copy.sort(comparator);\n      return copy;\n    };\n\n    const hasOwnProperty = Object.hasOwnProperty;\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const type = element => element.dom.nodeType;\n    const isType = t => element => type(element) === t;\n    const isText$1 = isType(TEXT);\n\n    const rawSet = (dom, key, value) => {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    const set = (element, key, value) => {\n      rawSet(element.dom, key, value);\n    };\n\n    const fromHtml = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        const message = 'HTML does not have a single root node';\n        console.error(message, html);\n        throw new Error(message);\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n      const doc = scope || document;\n      const node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    const fromText = (text, scope) => {\n      const doc = scope || document;\n      const node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    const fromDom = node => {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    const SugarElement = {\n      fromHtml,\n      fromTag,\n      fromText,\n      fromDom,\n      fromPoint\n    };\n\n    const bypassSelector = dom => dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT || dom.childElementCount === 0;\n    const all = (selector, scope) => {\n      const base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? [] : map(base.querySelectorAll(selector), SugarElement.fromDom);\n    };\n\n    const parent = element => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const children = element => map(element.dom.childNodes, SugarElement.fromDom);\n    const spot = (element, offset) => ({\n      element,\n      offset\n    });\n    const leaf = (element, offset) => {\n      const cs = children(element);\n      return cs.length > 0 && offset < cs.length ? spot(cs[offset], 0) : spot(element, offset);\n    };\n\n    const before = (marker, element) => {\n      const parent$1 = parent(marker);\n      parent$1.each(v => {\n        v.dom.insertBefore(element.dom, marker.dom);\n      });\n    };\n    const append = (parent, element) => {\n      parent.dom.appendChild(element.dom);\n    };\n    const wrap = (element, wrapper) => {\n      before(element, wrapper);\n      append(wrapper, element);\n    };\n\n    const NodeValue = (is, name) => {\n      const get = element => {\n        if (!is(element)) {\n          throw new Error('Can only get ' + name + ' value of a ' + name + ' node');\n        }\n        return getOption(element).getOr('');\n      };\n      const getOption = element => is(element) ? Optional.from(element.dom.nodeValue) : Optional.none();\n      const set = (element, value) => {\n        if (!is(element)) {\n          throw new Error('Can only set raw ' + name + ' value of a ' + name + ' node');\n        }\n        element.dom.nodeValue = value;\n      };\n      return {\n        get,\n        getOption,\n        set\n      };\n    };\n\n    const api = NodeValue(isText$1, 'text');\n    const get$1 = element => api.get(element);\n\n    const compareDocumentPosition = (a, b, match) => {\n      return (a.compareDocumentPosition(b) & match) !== 0;\n    };\n    const documentPositionPreceding = (a, b) => {\n      return compareDocumentPosition(a, b, Node.DOCUMENT_POSITION_PRECEDING);\n    };\n\n    const descendants = (scope, selector) => all(selector, scope);\n\n    var global = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker');\n\n    const isSimpleBoundary = (dom, node) => dom.isBlock(node) || has(dom.schema.getVoidElements(), node.nodeName);\n    const isContentEditableFalse = (dom, node) => dom.getContentEditable(node) === 'false';\n    const isContentEditableTrueInCef = (dom, node) => dom.getContentEditable(node) === 'true' && node.parentNode && dom.getContentEditableParent(node.parentNode) === 'false';\n    const isHidden = (dom, node) => !dom.isBlock(node) && has(dom.schema.getWhitespaceElements(), node.nodeName);\n    const isBoundary = (dom, node) => isSimpleBoundary(dom, node) || isContentEditableFalse(dom, node) || isHidden(dom, node) || isContentEditableTrueInCef(dom, node);\n    const isText = node => node.nodeType === 3;\n    const nuSection = () => ({\n      sOffset: 0,\n      fOffset: 0,\n      elements: []\n    });\n    const toLeaf = (node, offset) => leaf(SugarElement.fromDom(node), offset);\n    const walk = (dom, walkerFn, startNode, callbacks, endNode, skipStart = true) => {\n      let next = skipStart ? walkerFn(false) : startNode;\n      while (next) {\n        const isCefNode = isContentEditableFalse(dom, next);\n        if (isCefNode || isHidden(dom, next)) {\n          const stopWalking = isCefNode ? callbacks.cef(next) : callbacks.boundary(next);\n          if (stopWalking) {\n            break;\n          } else {\n            next = walkerFn(true);\n            continue;\n          }\n        } else if (isSimpleBoundary(dom, next)) {\n          if (callbacks.boundary(next)) {\n            break;\n          }\n        } else if (isText(next)) {\n          callbacks.text(next);\n        }\n        if (next === endNode) {\n          break;\n        } else {\n          next = walkerFn(false);\n        }\n      }\n    };\n    const collectTextToBoundary = (dom, section, node, rootNode, forwards) => {\n      var _a;\n      if (isBoundary(dom, node)) {\n        return;\n      }\n      const rootBlock = (_a = dom.getParent(rootNode, dom.isBlock)) !== null && _a !== void 0 ? _a : dom.getRoot();\n      const walker = new global(node, rootBlock);\n      const walkerFn = forwards ? walker.next.bind(walker) : walker.prev.bind(walker);\n      walk(dom, walkerFn, node, {\n        boundary: always,\n        cef: always,\n        text: next => {\n          if (forwards) {\n            section.fOffset += next.length;\n          } else {\n            section.sOffset += next.length;\n          }\n          section.elements.push(SugarElement.fromDom(next));\n        }\n      });\n    };\n    const collect = (dom, rootNode, startNode, endNode, callbacks, skipStart = true) => {\n      const walker = new global(startNode, rootNode);\n      const sections = [];\n      let current = nuSection();\n      collectTextToBoundary(dom, current, startNode, rootNode, false);\n      const finishSection = () => {\n        if (current.elements.length > 0) {\n          sections.push(current);\n          current = nuSection();\n        }\n        return false;\n      };\n      walk(dom, walker.next.bind(walker), startNode, {\n        boundary: finishSection,\n        cef: node => {\n          finishSection();\n          if (callbacks) {\n            sections.push(...callbacks.cef(node));\n          }\n          return false;\n        },\n        text: next => {\n          current.elements.push(SugarElement.fromDom(next));\n          if (callbacks) {\n            callbacks.text(next, current);\n          }\n        }\n      }, endNode, skipStart);\n      if (endNode) {\n        collectTextToBoundary(dom, current, endNode, rootNode, true);\n      }\n      finishSection();\n      return sections;\n    };\n    const collectRangeSections = (dom, rng) => {\n      const start = toLeaf(rng.startContainer, rng.startOffset);\n      const startNode = start.element.dom;\n      const end = toLeaf(rng.endContainer, rng.endOffset);\n      const endNode = end.element.dom;\n      return collect(dom, rng.commonAncestorContainer, startNode, endNode, {\n        text: (node, section) => {\n          if (node === endNode) {\n            section.fOffset += node.length - end.offset;\n          } else if (node === startNode) {\n            section.sOffset += start.offset;\n          }\n        },\n        cef: node => {\n          const sections = bind(descendants(SugarElement.fromDom(node), '*[contenteditable=true]'), e => {\n            const ceTrueNode = e.dom;\n            return collect(dom, ceTrueNode, ceTrueNode);\n          });\n          return sort(sections, (a, b) => documentPositionPreceding(a.elements[0].dom, b.elements[0].dom) ? 1 : -1);\n        }\n      }, false);\n    };\n    const fromRng = (dom, rng) => rng.collapsed ? [] : collectRangeSections(dom, rng);\n    const fromNode = (dom, node) => {\n      const rng = dom.createRng();\n      rng.selectNode(node);\n      return fromRng(dom, rng);\n    };\n    const fromNodes = (dom, nodes) => bind(nodes, node => fromNode(dom, node));\n\n    const find$2 = (text, pattern, start = 0, finish = text.length) => {\n      const regex = pattern.regex;\n      regex.lastIndex = start;\n      const results = [];\n      let match;\n      while (match = regex.exec(text)) {\n        const matchedText = match[pattern.matchIndex];\n        const matchStart = match.index + match[0].indexOf(matchedText);\n        const matchFinish = matchStart + matchedText.length;\n        if (matchFinish > finish) {\n          break;\n        }\n        results.push({\n          start: matchStart,\n          finish: matchFinish\n        });\n        regex.lastIndex = matchFinish;\n      }\n      return results;\n    };\n    const extract = (elements, matches) => {\n      const nodePositions = foldl(elements, (acc, element) => {\n        const content = get$1(element);\n        const start = acc.last;\n        const finish = start + content.length;\n        const positions = bind(matches, (match, matchIdx) => {\n          if (match.start < finish && match.finish > start) {\n            return [{\n                element,\n                start: Math.max(start, match.start) - start,\n                finish: Math.min(finish, match.finish) - start,\n                matchId: matchIdx\n              }];\n          } else {\n            return [];\n          }\n        });\n        return {\n          results: acc.results.concat(positions),\n          last: finish\n        };\n      }, {\n        results: [],\n        last: 0\n      }).results;\n      return groupBy(nodePositions, position => position.matchId);\n    };\n\n    const find$1 = (pattern, sections) => bind(sections, section => {\n      const elements = section.elements;\n      const content = map(elements, get$1).join('');\n      const positions = find$2(content, pattern, section.sOffset, content.length - section.fOffset);\n      return extract(elements, positions);\n    });\n    const mark = (matches, replacementNode) => {\n      eachr(matches, (match, idx) => {\n        eachr(match, pos => {\n          const wrapper = SugarElement.fromDom(replacementNode.cloneNode(false));\n          set(wrapper, 'data-mce-index', idx);\n          const textNode = pos.element.dom;\n          if (textNode.length === pos.finish && pos.start === 0) {\n            wrap(pos.element, wrapper);\n          } else {\n            if (textNode.length !== pos.finish) {\n              textNode.splitText(pos.finish);\n            }\n            const matchNode = textNode.splitText(pos.start);\n            wrap(SugarElement.fromDom(matchNode), wrapper);\n          }\n        });\n      });\n    };\n    const findAndMark = (dom, pattern, node, replacementNode) => {\n      const textSections = fromNode(dom, node);\n      const matches = find$1(pattern, textSections);\n      mark(matches, replacementNode);\n      return matches.length;\n    };\n    const findAndMarkInSelection = (dom, pattern, selection, replacementNode) => {\n      const bookmark = selection.getBookmark();\n      const nodes = dom.select('td[data-mce-selected],th[data-mce-selected]');\n      const textSections = nodes.length > 0 ? fromNodes(dom, nodes) : fromRng(dom, selection.getRng());\n      const matches = find$1(pattern, textSections);\n      mark(matches, replacementNode);\n      selection.moveToBookmark(bookmark);\n      return matches.length;\n    };\n\n    const getElmIndex = elm => {\n      return elm.getAttribute('data-mce-index');\n    };\n    const markAllMatches = (editor, currentSearchState, pattern, inSelection) => {\n      const marker = editor.dom.create('span', { 'data-mce-bogus': 1 });\n      marker.className = 'mce-match-marker';\n      const node = editor.getBody();\n      done(editor, currentSearchState, false);\n      if (inSelection) {\n        return findAndMarkInSelection(editor.dom, pattern, editor.selection, marker);\n      } else {\n        return findAndMark(editor.dom, pattern, node, marker);\n      }\n    };\n    const unwrap = node => {\n      var _a;\n      const parentNode = node.parentNode;\n      if (node.firstChild) {\n        parentNode.insertBefore(node.firstChild, node);\n      }\n      (_a = node.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(node);\n    };\n    const findSpansByIndex = (editor, index) => {\n      const spans = [];\n      const nodes = global$1.toArray(editor.getBody().getElementsByTagName('span'));\n      if (nodes.length) {\n        for (let i = 0; i < nodes.length; i++) {\n          const nodeIndex = getElmIndex(nodes[i]);\n          if (nodeIndex === null || !nodeIndex.length) {\n            continue;\n          }\n          if (nodeIndex === index.toString()) {\n            spans.push(nodes[i]);\n          }\n        }\n      }\n      return spans;\n    };\n    const moveSelection = (editor, currentSearchState, forward) => {\n      const searchState = currentSearchState.get();\n      let testIndex = searchState.index;\n      const dom = editor.dom;\n      if (forward) {\n        if (testIndex + 1 === searchState.count) {\n          testIndex = 0;\n        } else {\n          testIndex++;\n        }\n      } else {\n        if (testIndex - 1 === -1) {\n          testIndex = searchState.count - 1;\n        } else {\n          testIndex--;\n        }\n      }\n      dom.removeClass(findSpansByIndex(editor, searchState.index), 'mce-match-marker-selected');\n      const spans = findSpansByIndex(editor, testIndex);\n      if (spans.length) {\n        dom.addClass(findSpansByIndex(editor, testIndex), 'mce-match-marker-selected');\n        editor.selection.scrollIntoView(spans[0]);\n        return testIndex;\n      }\n      return -1;\n    };\n    const removeNode = (dom, node) => {\n      const parent = node.parentNode;\n      dom.remove(node);\n      if (parent && dom.isEmpty(parent)) {\n        dom.remove(parent);\n      }\n    };\n    const escapeSearchText = (text, wholeWord) => {\n      const escapedText = text.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&').replace(/\\s/g, '[^\\\\S\\\\r\\\\n\\\\uFEFF]');\n      const wordRegex = '(' + escapedText + ')';\n      return wholeWord ? `(?:^|\\\\s|${ punctuation() })` + wordRegex + `(?=$|\\\\s|${ punctuation() })` : wordRegex;\n    };\n    const find = (editor, currentSearchState, text, matchCase, wholeWord, inSelection) => {\n      const selection = editor.selection;\n      const escapedText = escapeSearchText(text, wholeWord);\n      const isForwardSelection = selection.isForward();\n      const pattern = {\n        regex: new RegExp(escapedText, matchCase ? 'g' : 'gi'),\n        matchIndex: 1\n      };\n      const count = markAllMatches(editor, currentSearchState, pattern, inSelection);\n      if (global$2.browser.isSafari()) {\n        selection.setRng(selection.getRng(), isForwardSelection);\n      }\n      if (count) {\n        const newIndex = moveSelection(editor, currentSearchState, true);\n        currentSearchState.set({\n          index: newIndex,\n          count,\n          text,\n          matchCase,\n          wholeWord,\n          inSelection\n        });\n      }\n      return count;\n    };\n    const next = (editor, currentSearchState) => {\n      const index = moveSelection(editor, currentSearchState, true);\n      currentSearchState.set({\n        ...currentSearchState.get(),\n        index\n      });\n    };\n    const prev = (editor, currentSearchState) => {\n      const index = moveSelection(editor, currentSearchState, false);\n      currentSearchState.set({\n        ...currentSearchState.get(),\n        index\n      });\n    };\n    const isMatchSpan = node => {\n      const matchIndex = getElmIndex(node);\n      return matchIndex !== null && matchIndex.length > 0;\n    };\n    const replace = (editor, currentSearchState, text, forward, all) => {\n      const searchState = currentSearchState.get();\n      const currentIndex = searchState.index;\n      let currentMatchIndex, nextIndex = currentIndex;\n      forward = forward !== false;\n      const node = editor.getBody();\n      const nodes = global$1.grep(global$1.toArray(node.getElementsByTagName('span')), isMatchSpan);\n      for (let i = 0; i < nodes.length; i++) {\n        const nodeIndex = getElmIndex(nodes[i]);\n        let matchIndex = currentMatchIndex = parseInt(nodeIndex, 10);\n        if (all || matchIndex === searchState.index) {\n          if (text.length) {\n            nodes[i].innerText = text;\n            unwrap(nodes[i]);\n          } else {\n            removeNode(editor.dom, nodes[i]);\n          }\n          while (nodes[++i]) {\n            matchIndex = parseInt(getElmIndex(nodes[i]), 10);\n            if (matchIndex === currentMatchIndex) {\n              removeNode(editor.dom, nodes[i]);\n            } else {\n              i--;\n              break;\n            }\n          }\n          if (forward) {\n            nextIndex--;\n          }\n        } else if (currentMatchIndex > currentIndex) {\n          nodes[i].setAttribute('data-mce-index', String(currentMatchIndex - 1));\n        }\n      }\n      currentSearchState.set({\n        ...searchState,\n        count: all ? 0 : searchState.count - 1,\n        index: nextIndex\n      });\n      if (forward) {\n        next(editor, currentSearchState);\n      } else {\n        prev(editor, currentSearchState);\n      }\n      return !all && currentSearchState.get().count > 0;\n    };\n    const done = (editor, currentSearchState, keepEditorSelection) => {\n      let startContainer;\n      let endContainer;\n      const searchState = currentSearchState.get();\n      const nodes = global$1.toArray(editor.getBody().getElementsByTagName('span'));\n      for (let i = 0; i < nodes.length; i++) {\n        const nodeIndex = getElmIndex(nodes[i]);\n        if (nodeIndex !== null && nodeIndex.length) {\n          if (nodeIndex === searchState.index.toString()) {\n            if (!startContainer) {\n              startContainer = nodes[i].firstChild;\n            }\n            endContainer = nodes[i].firstChild;\n          }\n          unwrap(nodes[i]);\n        }\n      }\n      currentSearchState.set({\n        ...searchState,\n        index: -1,\n        count: 0,\n        text: ''\n      });\n      if (startContainer && endContainer) {\n        const rng = editor.dom.createRng();\n        rng.setStart(startContainer, 0);\n        rng.setEnd(endContainer, endContainer.data.length);\n        if (keepEditorSelection !== false) {\n          editor.selection.setRng(rng);\n        }\n        return rng;\n      } else {\n        return undefined;\n      }\n    };\n    const hasNext = (editor, currentSearchState) => currentSearchState.get().count > 1;\n    const hasPrev = (editor, currentSearchState) => currentSearchState.get().count > 1;\n\n    const get = (editor, currentState) => {\n      const done$1 = keepEditorSelection => {\n        return done(editor, currentState, keepEditorSelection);\n      };\n      const find$1 = (text, matchCase, wholeWord, inSelection = false) => {\n        return find(editor, currentState, text, matchCase, wholeWord, inSelection);\n      };\n      const next$1 = () => {\n        return next(editor, currentState);\n      };\n      const prev$1 = () => {\n        return prev(editor, currentState);\n      };\n      const replace$1 = (text, forward, all) => {\n        return replace(editor, currentState, text, forward, all);\n      };\n      return {\n        done: done$1,\n        find: find$1,\n        next: next$1,\n        prev: prev$1,\n        replace: replace$1\n      };\n    };\n\n    const singleton = doRevoke => {\n      const subject = Cell(Optional.none());\n      const revoke = () => subject.get().each(doRevoke);\n      const clear = () => {\n        revoke();\n        subject.set(Optional.none());\n      };\n      const isSet = () => subject.get().isSome();\n      const get = () => subject.get();\n      const set = s => {\n        revoke();\n        subject.set(Optional.some(s));\n      };\n      return {\n        clear,\n        isSet,\n        get,\n        set\n      };\n    };\n    const value = () => {\n      const subject = singleton(noop);\n      const on = f => subject.get().each(f);\n      return {\n        ...subject,\n        on\n      };\n    };\n\n    const open = (editor, currentSearchState) => {\n      const dialogApi = value();\n      editor.undoManager.add();\n      const selectedText = global$1.trim(editor.selection.getContent({ format: 'text' }));\n      const updateButtonStates = api => {\n        api.setEnabled('next', hasNext(editor, currentSearchState));\n        api.setEnabled('prev', hasPrev(editor, currentSearchState));\n      };\n      const updateSearchState = api => {\n        const data = api.getData();\n        const current = currentSearchState.get();\n        currentSearchState.set({\n          ...current,\n          matchCase: data.matchcase,\n          wholeWord: data.wholewords,\n          inSelection: data.inselection\n        });\n      };\n      const disableAll = (api, disable) => {\n        const buttons = [\n          'replace',\n          'replaceall',\n          'prev',\n          'next'\n        ];\n        const toggle = name => api.setEnabled(name, !disable);\n        each(buttons, toggle);\n      };\n      const toggleNotFoundAlert = (isVisible, api) => {\n        api.redial(getDialogSpec(isVisible, api.getData()));\n      };\n      const focusButtonIfRequired = (api, name) => {\n        if (global$2.browser.isSafari() && global$2.deviceType.isTouch() && (name === 'find' || name === 'replace' || name === 'replaceall')) {\n          api.focus(name);\n        }\n      };\n      const reset = api => {\n        done(editor, currentSearchState, false);\n        disableAll(api, true);\n        updateButtonStates(api);\n      };\n      const doFind = api => {\n        const data = api.getData();\n        const last = currentSearchState.get();\n        if (!data.findtext.length) {\n          reset(api);\n          return;\n        }\n        if (last.text === data.findtext && last.matchCase === data.matchcase && last.wholeWord === data.wholewords) {\n          next(editor, currentSearchState);\n        } else {\n          const count = find(editor, currentSearchState, data.findtext, data.matchcase, data.wholewords, data.inselection);\n          if (count <= 0) {\n            toggleNotFoundAlert(true, api);\n          }\n          disableAll(api, count === 0);\n        }\n        updateButtonStates(api);\n      };\n      const initialState = currentSearchState.get();\n      const initialData = {\n        findtext: selectedText,\n        replacetext: '',\n        wholewords: initialState.wholeWord,\n        matchcase: initialState.matchCase,\n        inselection: initialState.inSelection\n      };\n      const getPanelItems = error => {\n        const items = [\n          {\n            type: 'bar',\n            items: [\n              {\n                type: 'input',\n                name: 'findtext',\n                placeholder: 'Find',\n                maximized: true,\n                inputMode: 'search'\n              },\n              {\n                type: 'button',\n                name: 'prev',\n                text: 'Previous',\n                icon: 'action-prev',\n                enabled: false,\n                borderless: true\n              },\n              {\n                type: 'button',\n                name: 'next',\n                text: 'Next',\n                icon: 'action-next',\n                enabled: false,\n                borderless: true\n              }\n            ]\n          },\n          {\n            type: 'input',\n            name: 'replacetext',\n            placeholder: 'Replace with',\n            inputMode: 'search'\n          }\n        ];\n        if (error) {\n          items.push({\n            type: 'alertbanner',\n            level: 'error',\n            text: 'Could not find the specified string.',\n            icon: 'warning'\n          });\n        }\n        return items;\n      };\n      const getDialogSpec = (showNoMatchesAlertBanner, initialData) => ({\n        title: 'Find and Replace',\n        size: 'normal',\n        body: {\n          type: 'panel',\n          items: getPanelItems(showNoMatchesAlertBanner)\n        },\n        buttons: [\n          {\n            type: 'menu',\n            name: 'options',\n            icon: 'preferences',\n            tooltip: 'Preferences',\n            align: 'start',\n            items: [\n              {\n                type: 'togglemenuitem',\n                name: 'matchcase',\n                text: 'Match case'\n              },\n              {\n                type: 'togglemenuitem',\n                name: 'wholewords',\n                text: 'Find whole words only'\n              },\n              {\n                type: 'togglemenuitem',\n                name: 'inselection',\n                text: 'Find in selection'\n              }\n            ]\n          },\n          {\n            type: 'custom',\n            name: 'find',\n            text: 'Find',\n            primary: true\n          },\n          {\n            type: 'custom',\n            name: 'replace',\n            text: 'Replace',\n            enabled: false\n          },\n          {\n            type: 'custom',\n            name: 'replaceall',\n            text: 'Replace all',\n            enabled: false\n          }\n        ],\n        initialData,\n        onChange: (api, details) => {\n          if (showNoMatchesAlertBanner) {\n            toggleNotFoundAlert(false, api);\n          }\n          if (details.name === 'findtext' && currentSearchState.get().count > 0) {\n            reset(api);\n          }\n        },\n        onAction: (api, details) => {\n          const data = api.getData();\n          switch (details.name) {\n          case 'find':\n            doFind(api);\n            break;\n          case 'replace':\n            if (!replace(editor, currentSearchState, data.replacetext)) {\n              reset(api);\n            } else {\n              updateButtonStates(api);\n            }\n            break;\n          case 'replaceall':\n            replace(editor, currentSearchState, data.replacetext, true, true);\n            reset(api);\n            break;\n          case 'prev':\n            prev(editor, currentSearchState);\n            updateButtonStates(api);\n            break;\n          case 'next':\n            next(editor, currentSearchState);\n            updateButtonStates(api);\n            break;\n          case 'matchcase':\n          case 'wholewords':\n          case 'inselection':\n            toggleNotFoundAlert(false, api);\n            updateSearchState(api);\n            reset(api);\n            break;\n          }\n          focusButtonIfRequired(api, details.name);\n        },\n        onSubmit: api => {\n          doFind(api);\n          focusButtonIfRequired(api, 'find');\n        },\n        onClose: () => {\n          editor.focus();\n          done(editor, currentSearchState);\n          editor.undoManager.add();\n        }\n      });\n      dialogApi.set(editor.windowManager.open(getDialogSpec(false, initialData), { inline: 'toolbar' }));\n    };\n\n    const register$1 = (editor, currentSearchState) => {\n      editor.addCommand('SearchReplace', () => {\n        open(editor, currentSearchState);\n      });\n    };\n\n    const showDialog = (editor, currentSearchState) => () => {\n      open(editor, currentSearchState);\n    };\n    const register = (editor, currentSearchState) => {\n      editor.ui.registry.addMenuItem('searchreplace', {\n        text: 'Find and replace...',\n        shortcut: 'Meta+F',\n        onAction: showDialog(editor, currentSearchState),\n        icon: 'search'\n      });\n      editor.ui.registry.addButton('searchreplace', {\n        tooltip: 'Find and replace',\n        onAction: showDialog(editor, currentSearchState),\n        icon: 'search'\n      });\n      editor.shortcuts.add('Meta+F', '', showDialog(editor, currentSearchState));\n    };\n\n    var Plugin = () => {\n      global$3.add('searchreplace', editor => {\n        const currentSearchState = Cell({\n          index: -1,\n          count: 0,\n          text: '',\n          matchCase: false,\n          wholeWord: false,\n          inSelection: false\n        });\n        register$1(editor, currentSearchState);\n        register(editor, currentSearchState);\n        return get(editor, currentSearchState);\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"searchreplace\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/searchreplace')\n//   ES2015:\n//     import 'tinymce/plugins/searchreplace'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,YAAM,OAAO,aAAW;AACtB,YAAIA,SAAQ;AACZ,cAAMC,OAAM,MAAM;AAChB,iBAAOD;AAAA,QACT;AACA,cAAME,OAAM,OAAK;AACf,UAAAF,SAAQ;AAAA,QACV;AACA,eAAO;AAAA,UACL,KAAAC;AAAA,UACA,KAAAC;AAAA,QACF;AAAA,MACF;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,YAAI;AACJ,YAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,iBAAO;AAAA,QACT,OAAO;AACL,mBAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,QAC7F;AAAA,MACF;AACA,YAAM,SAAS,OAAK;AAClB,cAAM,IAAI,OAAO;AACjB,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,WAAW,CAAAC,UAAQ,CAAAH,WAAS,OAAOA,MAAK,MAAMG;AACpD,YAAM,eAAe,CAAAA,UAAQ,CAAAH,WAAS,OAAOA,WAAUG;AACvD,YAAM,WAAW,SAAS,QAAQ;AAClC,YAAM,UAAU,SAAS,OAAO;AAChC,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,YAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,YAAM,WAAW,aAAa,QAAQ;AAEtC,YAAM,OAAO,MAAM;AAAA,MACnB;AACA,YAAM,WAAW,CAAAH,WAAS;AACxB,eAAO,MAAM;AACX,iBAAOA;AAAA,QACT;AAAA,MACF;AACA,YAAM,SAAS,SAAS,IAAI;AAE5B,YAAM,iBAAiB;AAEvB,YAAM,gBAAgB,SAAS,cAAc;AAAA,MAE7C,MAAM,SAAS;AAAA,QACb,YAAY,KAAKA,QAAO;AACtB,eAAK,MAAM;AACX,eAAK,QAAQA;AAAA,QACf;AAAA,QACA,OAAO,KAAKA,QAAO;AACjB,iBAAO,IAAI,SAAS,MAAMA,MAAK;AAAA,QACjC;AAAA,QACA,OAAO,OAAO;AACZ,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,KAAK,QAAQ,QAAQ;AACnB,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,QACA,SAAS;AACP,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,SAAS;AACP,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA,IAAI,QAAQ;AACV,cAAI,KAAK,KAAK;AACZ,mBAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,UACzC,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QACzC;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QAC1C;AAAA,QACA,OAAO,WAAW;AAChB,cAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,MAAM,aAAa;AACjB,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,GAAG,aAAa;AACd,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B;AAAA,QACA,WAAW,OAAO;AAChB,iBAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,QACvC;AAAA,QACA,QAAQ,OAAO;AACb,iBAAO,KAAK,MAAM,OAAO,MAAM;AAAA,QACjC;AAAA,QACA,SAAS,SAAS;AAChB,cAAI,CAAC,KAAK,KAAK;AACb,kBAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,UAC9F,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,OAAO,KAAKA,QAAO;AACjB,iBAAO,cAAcA,MAAK,IAAI,SAAS,KAAKA,MAAK,IAAI,SAAS,KAAK;AAAA,QACrE;AAAA,QACA,YAAY;AACV,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,iBAAiB;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AACR,iBAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,YAAM,cAAc;AAEpB,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,aAAa;AAEvD,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE9D,YAAM,cAAc,MAAM,UAAU;AACpC,YAAM,aAAa,MAAM,UAAU;AACnC,YAAM,MAAM,CAAC,IAAI,MAAM;AACrB,cAAM,MAAM,GAAG;AACf,cAAM,IAAI,IAAI,MAAM,GAAG;AACvB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,QACf;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,CAAC,IAAI,MAAM;AACtB,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,IAAI,MAAM;AACvB,iBAAS,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK;AACvC,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,UAAU,CAAC,IAAI,MAAM;AACzB,YAAI,GAAG,WAAW,GAAG;AACnB,iBAAO,CAAC;AAAA,QACV,OAAO;AACL,cAAI,UAAU,EAAE,GAAG,CAAC,CAAC;AACrB,gBAAM,IAAI,CAAC;AACX,cAAI,QAAQ,CAAC;AACb,mBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,kBAAM,IAAI,GAAG,CAAC;AACd,kBAAMG,QAAO,EAAE,CAAC;AAChB,gBAAIA,UAAS,SAAS;AACpB,gBAAE,KAAK,KAAK;AACZ,sBAAQ,CAAC;AAAA,YACX;AACA,sBAAUA;AACV,kBAAM,KAAK,CAAC;AAAA,UACd;AACA,cAAI,MAAM,WAAW,GAAG;AACtB,cAAE,KAAK,KAAK;AAAA,UACd;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,IAAI,GAAG,QAAQ;AAC5B,aAAK,IAAI,CAAC,GAAG,MAAM;AACjB,gBAAM,EAAE,KAAK,GAAG,CAAC;AAAA,QACnB,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,UAAU,QAAM;AACpB,cAAM,IAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B,EAAE;AAAA,UAC7E;AACA,qBAAW,MAAM,GAAG,GAAG,CAAC,CAAC;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,CAAC,IAAI,MAAM,QAAQ,IAAI,IAAI,CAAC,CAAC;AAC1C,YAAM,OAAO,CAAC,IAAI,eAAe;AAC/B,cAAM,OAAO,YAAY,KAAK,IAAI,CAAC;AACnC,aAAK,KAAK,UAAU;AACpB,eAAO;AAAA,MACT;AAEA,YAAM,iBAAiB,OAAO;AAC9B,YAAM,MAAM,CAAC,KAAK,QAAQ,eAAe,KAAK,KAAK,GAAG;AAEtD,aAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAElE,YAAM,WAAW;AACjB,YAAM,oBAAoB;AAC1B,YAAM,UAAU;AAChB,YAAM,OAAO;AAEb,YAAM,OAAO,aAAW,QAAQ,IAAI;AACpC,YAAM,SAAS,OAAK,aAAW,KAAK,OAAO,MAAM;AACjD,YAAM,WAAW,OAAO,IAAI;AAE5B,YAAM,SAAS,CAAC,KAAK,KAAKH,WAAU;AAClC,YAAI,SAASA,MAAK,KAAK,UAAUA,MAAK,KAAK,SAASA,MAAK,GAAG;AAC1D,cAAI,aAAa,KAAKA,SAAQ,EAAE;AAAA,QAClC,OAAO;AACL,kBAAQ,MAAM,uCAAuC,KAAK,aAAaA,QAAO,eAAe,GAAG;AAChG,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QAClD;AAAA,MACF;AACA,YAAM,MAAM,CAAC,SAAS,KAAKA,WAAU;AACnC,eAAO,QAAQ,KAAK,KAAKA,MAAK;AAAA,MAChC;AAEA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,IAAI,cAAc,KAAK;AACnC,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,cAAc,KAAK,IAAI,WAAW,SAAS,GAAG;AACrD,gBAAM,UAAU;AAChB,kBAAQ,MAAM,SAAS,IAAI;AAC3B,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB;AACA,eAAO,QAAQ,IAAI,WAAW,CAAC,CAAC;AAAA,MAClC;AACA,YAAM,UAAU,CAAC,KAAK,UAAU;AAC9B,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,cAAc,GAAG;AAClC,eAAO,QAAQ,IAAI;AAAA,MACrB;AACA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,eAAe,IAAI;AACpC,eAAO,QAAQ,IAAI;AAAA,MACrB;AACA,YAAM,UAAU,UAAQ;AACtB,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AACA,eAAO,EAAE,KAAK,KAAK;AAAA,MACrB;AACA,YAAM,YAAY,CAAC,QAAQ,GAAG,MAAM,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,OAAO;AAChG,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,iBAAiB,SAAO,IAAI,aAAa,WAAW,IAAI,aAAa,YAAY,IAAI,aAAa,qBAAqB,IAAI,sBAAsB;AACvJ,YAAM,MAAM,CAAC,UAAU,UAAU;AAC/B,cAAM,OAAO,UAAU,SAAY,WAAW,MAAM;AACpD,eAAO,eAAe,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,iBAAiB,QAAQ,GAAG,aAAa,OAAO;AAAA,MAC9F;AAEA,YAAM,SAAS,aAAW,SAAS,KAAK,QAAQ,IAAI,UAAU,EAAE,IAAI,aAAa,OAAO;AACxF,YAAM,WAAW,aAAW,IAAI,QAAQ,IAAI,YAAY,aAAa,OAAO;AAC5E,YAAM,OAAO,CAAC,SAAS,YAAY;AAAA,QACjC;AAAA,QACA;AAAA,MACF;AACA,YAAM,OAAO,CAAC,SAAS,WAAW;AAChC,cAAM,KAAK,SAAS,OAAO;AAC3B,eAAO,GAAG,SAAS,KAAK,SAAS,GAAG,SAAS,KAAK,GAAG,MAAM,GAAG,CAAC,IAAI,KAAK,SAAS,MAAM;AAAA,MACzF;AAEA,YAAM,SAAS,CAAC,QAAQ,YAAY;AAClC,cAAM,WAAW,OAAO,MAAM;AAC9B,iBAAS,KAAK,OAAK;AACjB,YAAE,IAAI,aAAa,QAAQ,KAAK,OAAO,GAAG;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,YAAM,SAAS,CAACI,SAAQ,YAAY;AAClC,QAAAA,QAAO,IAAI,YAAY,QAAQ,GAAG;AAAA,MACpC;AACA,YAAM,OAAO,CAAC,SAAS,YAAY;AACjC,eAAO,SAAS,OAAO;AACvB,eAAO,SAAS,OAAO;AAAA,MACzB;AAEA,YAAM,YAAY,CAAC,IAAI,SAAS;AAC9B,cAAMH,OAAM,aAAW;AACrB,cAAI,CAAC,GAAG,OAAO,GAAG;AAChB,kBAAM,IAAI,MAAM,kBAAkB,OAAO,iBAAiB,OAAO,OAAO;AAAA,UAC1E;AACA,iBAAO,UAAU,OAAO,EAAE,MAAM,EAAE;AAAA,QACpC;AACA,cAAM,YAAY,aAAW,GAAG,OAAO,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,IAAI,SAAS,KAAK;AAChG,cAAMC,OAAM,CAAC,SAASF,WAAU;AAC9B,cAAI,CAAC,GAAG,OAAO,GAAG;AAChB,kBAAM,IAAI,MAAM,sBAAsB,OAAO,iBAAiB,OAAO,OAAO;AAAA,UAC9E;AACA,kBAAQ,IAAI,YAAYA;AAAA,QAC1B;AACA,eAAO;AAAA,UACL,KAAAC;AAAA,UACA;AAAA,UACA,KAAAC;AAAA,QACF;AAAA,MACF;AAEA,YAAM,MAAM,UAAU,UAAU,MAAM;AACtC,YAAM,QAAQ,aAAW,IAAI,IAAI,OAAO;AAExC,YAAM,0BAA0B,CAAC,GAAG,GAAG,UAAU;AAC/C,gBAAQ,EAAE,wBAAwB,CAAC,IAAI,WAAW;AAAA,MACpD;AACA,YAAM,4BAA4B,CAAC,GAAG,MAAM;AAC1C,eAAO,wBAAwB,GAAG,GAAG,KAAK,2BAA2B;AAAA,MACvE;AAEA,YAAM,cAAc,CAAC,OAAO,aAAa,IAAI,UAAU,KAAK;AAE5D,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,wBAAwB;AAEhE,YAAM,mBAAmB,CAAC,KAAK,SAAS,IAAI,QAAQ,IAAI,KAAK,IAAI,IAAI,OAAO,gBAAgB,GAAG,KAAK,QAAQ;AAC5G,YAAM,yBAAyB,CAAC,KAAK,SAAS,IAAI,mBAAmB,IAAI,MAAM;AAC/E,YAAM,6BAA6B,CAAC,KAAK,SAAS,IAAI,mBAAmB,IAAI,MAAM,UAAU,KAAK,cAAc,IAAI,yBAAyB,KAAK,UAAU,MAAM;AAClK,YAAM,WAAW,CAAC,KAAK,SAAS,CAAC,IAAI,QAAQ,IAAI,KAAK,IAAI,IAAI,OAAO,sBAAsB,GAAG,KAAK,QAAQ;AAC3G,YAAM,aAAa,CAAC,KAAK,SAAS,iBAAiB,KAAK,IAAI,KAAK,uBAAuB,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,2BAA2B,KAAK,IAAI;AACjK,YAAM,SAAS,UAAQ,KAAK,aAAa;AACzC,YAAM,YAAY,OAAO;AAAA,QACvB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU,CAAC;AAAA,MACb;AACA,YAAM,SAAS,CAAC,MAAM,WAAW,KAAK,aAAa,QAAQ,IAAI,GAAG,MAAM;AACxE,YAAM,OAAO,CAAC,KAAK,UAAU,WAAW,WAAW,SAAS,YAAY,SAAS;AAC/E,YAAIG,QAAO,YAAY,SAAS,KAAK,IAAI;AACzC,eAAOA,OAAM;AACX,gBAAM,YAAY,uBAAuB,KAAKA,KAAI;AAClD,cAAI,aAAa,SAAS,KAAKA,KAAI,GAAG;AACpC,kBAAM,cAAc,YAAY,UAAU,IAAIA,KAAI,IAAI,UAAU,SAASA,KAAI;AAC7E,gBAAI,aAAa;AACf;AAAA,YACF,OAAO;AACL,cAAAA,QAAO,SAAS,IAAI;AACpB;AAAA,YACF;AAAA,UACF,WAAW,iBAAiB,KAAKA,KAAI,GAAG;AACtC,gBAAI,UAAU,SAASA,KAAI,GAAG;AAC5B;AAAA,YACF;AAAA,UACF,WAAW,OAAOA,KAAI,GAAG;AACvB,sBAAU,KAAKA,KAAI;AAAA,UACrB;AACA,cAAIA,UAAS,SAAS;AACpB;AAAA,UACF,OAAO;AACL,YAAAA,QAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AACA,YAAM,wBAAwB,CAAC,KAAK,SAAS,MAAM,UAAU,aAAa;AACxE,YAAI;AACJ,YAAI,WAAW,KAAK,IAAI,GAAG;AACzB;AAAA,QACF;AACA,cAAM,aAAa,KAAK,IAAI,UAAU,UAAU,IAAI,OAAO,OAAO,QAAQ,OAAO,SAAS,KAAK,IAAI,QAAQ;AAC3G,cAAM,SAAS,IAAI,OAAO,MAAM,SAAS;AACzC,cAAM,WAAW,WAAW,OAAO,KAAK,KAAK,MAAM,IAAI,OAAO,KAAK,KAAK,MAAM;AAC9E,aAAK,KAAK,UAAU,MAAM;AAAA,UACxB,UAAU;AAAA,UACV,KAAK;AAAA,UACL,MAAM,CAAAA,UAAQ;AACZ,gBAAI,UAAU;AACZ,sBAAQ,WAAWA,MAAK;AAAA,YAC1B,OAAO;AACL,sBAAQ,WAAWA,MAAK;AAAA,YAC1B;AACA,oBAAQ,SAAS,KAAK,aAAa,QAAQA,KAAI,CAAC;AAAA,UAClD;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,UAAU,CAAC,KAAK,UAAU,WAAW,SAAS,WAAW,YAAY,SAAS;AAClF,cAAM,SAAS,IAAI,OAAO,WAAW,QAAQ;AAC7C,cAAM,WAAW,CAAC;AAClB,YAAI,UAAU,UAAU;AACxB,8BAAsB,KAAK,SAAS,WAAW,UAAU,KAAK;AAC9D,cAAM,gBAAgB,MAAM;AAC1B,cAAI,QAAQ,SAAS,SAAS,GAAG;AAC/B,qBAAS,KAAK,OAAO;AACrB,sBAAU,UAAU;AAAA,UACtB;AACA,iBAAO;AAAA,QACT;AACA,aAAK,KAAK,OAAO,KAAK,KAAK,MAAM,GAAG,WAAW;AAAA,UAC7C,UAAU;AAAA,UACV,KAAK,UAAQ;AACX,0BAAc;AACd,gBAAI,WAAW;AACb,uBAAS,KAAK,GAAG,UAAU,IAAI,IAAI,CAAC;AAAA,YACtC;AACA,mBAAO;AAAA,UACT;AAAA,UACA,MAAM,CAAAA,UAAQ;AACZ,oBAAQ,SAAS,KAAK,aAAa,QAAQA,KAAI,CAAC;AAChD,gBAAI,WAAW;AACb,wBAAU,KAAKA,OAAM,OAAO;AAAA,YAC9B;AAAA,UACF;AAAA,QACF,GAAG,SAAS,SAAS;AACrB,YAAI,SAAS;AACX,gCAAsB,KAAK,SAAS,SAAS,UAAU,IAAI;AAAA,QAC7D;AACA,sBAAc;AACd,eAAO;AAAA,MACT;AACA,YAAM,uBAAuB,CAAC,KAAK,QAAQ;AACzC,cAAM,QAAQ,OAAO,IAAI,gBAAgB,IAAI,WAAW;AACxD,cAAM,YAAY,MAAM,QAAQ;AAChC,cAAM,MAAM,OAAO,IAAI,cAAc,IAAI,SAAS;AAClD,cAAM,UAAU,IAAI,QAAQ;AAC5B,eAAO,QAAQ,KAAK,IAAI,yBAAyB,WAAW,SAAS;AAAA,UACnE,MAAM,CAAC,MAAM,YAAY;AACvB,gBAAI,SAAS,SAAS;AACpB,sBAAQ,WAAW,KAAK,SAAS,IAAI;AAAA,YACvC,WAAW,SAAS,WAAW;AAC7B,sBAAQ,WAAW,MAAM;AAAA,YAC3B;AAAA,UACF;AAAA,UACA,KAAK,UAAQ;AACX,kBAAM,WAAW,KAAK,YAAY,aAAa,QAAQ,IAAI,GAAG,yBAAyB,GAAG,OAAK;AAC7F,oBAAM,aAAa,EAAE;AACrB,qBAAO,QAAQ,KAAK,YAAY,UAAU;AAAA,YAC5C,CAAC;AACD,mBAAO,KAAK,UAAU,CAAC,GAAG,MAAM,0BAA0B,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE,GAAG,IAAI,IAAI,EAAE;AAAA,UAC1G;AAAA,QACF,GAAG,KAAK;AAAA,MACV;AACA,YAAM,UAAU,CAAC,KAAK,QAAQ,IAAI,YAAY,CAAC,IAAI,qBAAqB,KAAK,GAAG;AAChF,YAAM,WAAW,CAAC,KAAK,SAAS;AAC9B,cAAM,MAAM,IAAI,UAAU;AAC1B,YAAI,WAAW,IAAI;AACnB,eAAO,QAAQ,KAAK,GAAG;AAAA,MACzB;AACA,YAAM,YAAY,CAAC,KAAK,UAAU,KAAK,OAAO,UAAQ,SAAS,KAAK,IAAI,CAAC;AAEzE,YAAM,SAAS,CAAC,MAAM,SAAS,QAAQ,GAAG,SAAS,KAAK,WAAW;AACjE,cAAM,QAAQ,QAAQ;AACtB,cAAM,YAAY;AAClB,cAAM,UAAU,CAAC;AACjB,YAAI;AACJ,eAAO,QAAQ,MAAM,KAAK,IAAI,GAAG;AAC/B,gBAAM,cAAc,MAAM,QAAQ,UAAU;AAC5C,gBAAM,aAAa,MAAM,QAAQ,MAAM,CAAC,EAAE,QAAQ,WAAW;AAC7D,gBAAM,cAAc,aAAa,YAAY;AAC7C,cAAI,cAAc,QAAQ;AACxB;AAAA,UACF;AACA,kBAAQ,KAAK;AAAA,YACX,OAAO;AAAA,YACP,QAAQ;AAAA,UACV,CAAC;AACD,gBAAM,YAAY;AAAA,QACpB;AACA,eAAO;AAAA,MACT;AACA,YAAM,UAAU,CAAC,UAAU,YAAY;AACrC,cAAM,gBAAgB,MAAM,UAAU,CAAC,KAAK,YAAY;AACtD,gBAAM,UAAU,MAAM,OAAO;AAC7B,gBAAM,QAAQ,IAAI;AAClB,gBAAM,SAAS,QAAQ,QAAQ;AAC/B,gBAAM,YAAY,KAAK,SAAS,CAAC,OAAO,aAAa;AACnD,gBAAI,MAAM,QAAQ,UAAU,MAAM,SAAS,OAAO;AAChD,qBAAO,CAAC;AAAA,gBACJ;AAAA,gBACA,OAAO,KAAK,IAAI,OAAO,MAAM,KAAK,IAAI;AAAA,gBACtC,QAAQ,KAAK,IAAI,QAAQ,MAAM,MAAM,IAAI;AAAA,gBACzC,SAAS;AAAA,cACX,CAAC;AAAA,YACL,OAAO;AACL,qBAAO,CAAC;AAAA,YACV;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,YACL,SAAS,IAAI,QAAQ,OAAO,SAAS;AAAA,YACrC,MAAM;AAAA,UACR;AAAA,QACF,GAAG;AAAA,UACD,SAAS,CAAC;AAAA,UACV,MAAM;AAAA,QACR,CAAC,EAAE;AACH,eAAO,QAAQ,eAAe,cAAY,SAAS,OAAO;AAAA,MAC5D;AAEA,YAAM,SAAS,CAAC,SAAS,aAAa,KAAK,UAAU,aAAW;AAC9D,cAAM,WAAW,QAAQ;AACzB,cAAM,UAAU,IAAI,UAAU,KAAK,EAAE,KAAK,EAAE;AAC5C,cAAM,YAAY,OAAO,SAAS,SAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ,OAAO;AAC5F,eAAO,QAAQ,UAAU,SAAS;AAAA,MACpC,CAAC;AACD,YAAM,OAAO,CAAC,SAAS,oBAAoB;AACzC,cAAM,SAAS,CAAC,OAAO,QAAQ;AAC7B,gBAAM,OAAO,SAAO;AAClB,kBAAM,UAAU,aAAa,QAAQ,gBAAgB,UAAU,KAAK,CAAC;AACrE,gBAAI,SAAS,kBAAkB,GAAG;AAClC,kBAAM,WAAW,IAAI,QAAQ;AAC7B,gBAAI,SAAS,WAAW,IAAI,UAAU,IAAI,UAAU,GAAG;AACrD,mBAAK,IAAI,SAAS,OAAO;AAAA,YAC3B,OAAO;AACL,kBAAI,SAAS,WAAW,IAAI,QAAQ;AAClC,yBAAS,UAAU,IAAI,MAAM;AAAA,cAC/B;AACA,oBAAM,YAAY,SAAS,UAAU,IAAI,KAAK;AAC9C,mBAAK,aAAa,QAAQ,SAAS,GAAG,OAAO;AAAA,YAC/C;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,cAAc,CAAC,KAAK,SAAS,MAAM,oBAAoB;AAC3D,cAAM,eAAe,SAAS,KAAK,IAAI;AACvC,cAAM,UAAU,OAAO,SAAS,YAAY;AAC5C,aAAK,SAAS,eAAe;AAC7B,eAAO,QAAQ;AAAA,MACjB;AACA,YAAM,yBAAyB,CAAC,KAAK,SAAS,WAAW,oBAAoB;AAC3E,cAAM,WAAW,UAAU,YAAY;AACvC,cAAM,QAAQ,IAAI,OAAO,6CAA6C;AACtE,cAAM,eAAe,MAAM,SAAS,IAAI,UAAU,KAAK,KAAK,IAAI,QAAQ,KAAK,UAAU,OAAO,CAAC;AAC/F,cAAM,UAAU,OAAO,SAAS,YAAY;AAC5C,aAAK,SAAS,eAAe;AAC7B,kBAAU,eAAe,QAAQ;AACjC,eAAO,QAAQ;AAAA,MACjB;AAEA,YAAM,cAAc,SAAO;AACzB,eAAO,IAAI,aAAa,gBAAgB;AAAA,MAC1C;AACA,YAAM,iBAAiB,CAAC,QAAQ,oBAAoB,SAAS,gBAAgB;AAC3E,cAAM,SAAS,OAAO,IAAI,OAAO,QAAQ,EAAE,kBAAkB,EAAE,CAAC;AAChE,eAAO,YAAY;AACnB,cAAM,OAAO,OAAO,QAAQ;AAC5B,aAAK,QAAQ,oBAAoB,KAAK;AACtC,YAAI,aAAa;AACf,iBAAO,uBAAuB,OAAO,KAAK,SAAS,OAAO,WAAW,MAAM;AAAA,QAC7E,OAAO;AACL,iBAAO,YAAY,OAAO,KAAK,SAAS,MAAM,MAAM;AAAA,QACtD;AAAA,MACF;AACA,YAAM,SAAS,UAAQ;AACrB,YAAI;AACJ,cAAM,aAAa,KAAK;AACxB,YAAI,KAAK,YAAY;AACnB,qBAAW,aAAa,KAAK,YAAY,IAAI;AAAA,QAC/C;AACA,SAAC,KAAK,KAAK,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,IAAI;AAAA,MACjF;AACA,YAAM,mBAAmB,CAAC,QAAQ,UAAU;AAC1C,cAAM,QAAQ,CAAC;AACf,cAAM,QAAQ,SAAS,QAAQ,OAAO,QAAQ,EAAE,qBAAqB,MAAM,CAAC;AAC5E,YAAI,MAAM,QAAQ;AAChB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,kBAAM,YAAY,YAAY,MAAM,CAAC,CAAC;AACtC,gBAAI,cAAc,QAAQ,CAAC,UAAU,QAAQ;AAC3C;AAAA,YACF;AACA,gBAAI,cAAc,MAAM,SAAS,GAAG;AAClC,oBAAM,KAAK,MAAM,CAAC,CAAC;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,gBAAgB,CAAC,QAAQ,oBAAoB,YAAY;AAC7D,cAAM,cAAc,mBAAmB,IAAI;AAC3C,YAAI,YAAY,YAAY;AAC5B,cAAM,MAAM,OAAO;AACnB,YAAI,SAAS;AACX,cAAI,YAAY,MAAM,YAAY,OAAO;AACvC,wBAAY;AAAA,UACd,OAAO;AACL;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAI,YAAY,MAAM,IAAI;AACxB,wBAAY,YAAY,QAAQ;AAAA,UAClC,OAAO;AACL;AAAA,UACF;AAAA,QACF;AACA,YAAI,YAAY,iBAAiB,QAAQ,YAAY,KAAK,GAAG,2BAA2B;AACxF,cAAM,QAAQ,iBAAiB,QAAQ,SAAS;AAChD,YAAI,MAAM,QAAQ;AAChB,cAAI,SAAS,iBAAiB,QAAQ,SAAS,GAAG,2BAA2B;AAC7E,iBAAO,UAAU,eAAe,MAAM,CAAC,CAAC;AACxC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,YAAM,aAAa,CAAC,KAAK,SAAS;AAChC,cAAMD,UAAS,KAAK;AACpB,YAAI,OAAO,IAAI;AACf,YAAIA,WAAU,IAAI,QAAQA,OAAM,GAAG;AACjC,cAAI,OAAOA,OAAM;AAAA,QACnB;AAAA,MACF;AACA,YAAM,mBAAmB,CAAC,MAAM,cAAc;AAC5C,cAAM,cAAc,KAAK,QAAQ,uCAAuC,MAAM,EAAE,QAAQ,OAAO,qBAAqB;AACpH,cAAM,YAAY,MAAM,cAAc;AACtC,eAAO,YAAY,YAAa,YAAY,CAAE,MAAM,YAAY,YAAa,YAAY,CAAE,MAAM;AAAA,MACnG;AACA,YAAM,OAAO,CAAC,QAAQ,oBAAoB,MAAM,WAAW,WAAW,gBAAgB;AACpF,cAAM,YAAY,OAAO;AACzB,cAAM,cAAc,iBAAiB,MAAM,SAAS;AACpD,cAAM,qBAAqB,UAAU,UAAU;AAC/C,cAAM,UAAU;AAAA,UACd,OAAO,IAAI,OAAO,aAAa,YAAY,MAAM,IAAI;AAAA,UACrD,YAAY;AAAA,QACd;AACA,cAAM,QAAQ,eAAe,QAAQ,oBAAoB,SAAS,WAAW;AAC7E,YAAI,SAAS,QAAQ,SAAS,GAAG;AAC/B,oBAAU,OAAO,UAAU,OAAO,GAAG,kBAAkB;AAAA,QACzD;AACA,YAAI,OAAO;AACT,gBAAM,WAAW,cAAc,QAAQ,oBAAoB,IAAI;AAC/D,6BAAmB,IAAI;AAAA,YACrB,OAAO;AAAA,YACP;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,CAAC,QAAQ,uBAAuB;AAC3C,cAAM,QAAQ,cAAc,QAAQ,oBAAoB,IAAI;AAC5D,2BAAmB,IAAI;AAAA,UACrB,GAAG,mBAAmB,IAAI;AAAA,UAC1B;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,OAAO,CAAC,QAAQ,uBAAuB;AAC3C,cAAM,QAAQ,cAAc,QAAQ,oBAAoB,KAAK;AAC7D,2BAAmB,IAAI;AAAA,UACrB,GAAG,mBAAmB,IAAI;AAAA,UAC1B;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,cAAc,UAAQ;AAC1B,cAAM,aAAa,YAAY,IAAI;AACnC,eAAO,eAAe,QAAQ,WAAW,SAAS;AAAA,MACpD;AACA,YAAM,UAAU,CAAC,QAAQ,oBAAoB,MAAM,SAASE,SAAQ;AAClE,cAAM,cAAc,mBAAmB,IAAI;AAC3C,cAAM,eAAe,YAAY;AACjC,YAAI,mBAAmB,YAAY;AACnC,kBAAU,YAAY;AACtB,cAAM,OAAO,OAAO,QAAQ;AAC5B,cAAM,QAAQ,SAAS,KAAK,SAAS,QAAQ,KAAK,qBAAqB,MAAM,CAAC,GAAG,WAAW;AAC5F,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAM,YAAY,YAAY,MAAM,CAAC,CAAC;AACtC,cAAI,aAAa,oBAAoB,SAAS,WAAW,EAAE;AAC3D,cAAIA,QAAO,eAAe,YAAY,OAAO;AAC3C,gBAAI,KAAK,QAAQ;AACf,oBAAM,CAAC,EAAE,YAAY;AACrB,qBAAO,MAAM,CAAC,CAAC;AAAA,YACjB,OAAO;AACL,yBAAW,OAAO,KAAK,MAAM,CAAC,CAAC;AAAA,YACjC;AACA,mBAAO,MAAM,EAAE,CAAC,GAAG;AACjB,2BAAa,SAAS,YAAY,MAAM,CAAC,CAAC,GAAG,EAAE;AAC/C,kBAAI,eAAe,mBAAmB;AACpC,2BAAW,OAAO,KAAK,MAAM,CAAC,CAAC;AAAA,cACjC,OAAO;AACL;AACA;AAAA,cACF;AAAA,YACF;AACA,gBAAI,SAAS;AACX;AAAA,YACF;AAAA,UACF,WAAW,oBAAoB,cAAc;AAC3C,kBAAM,CAAC,EAAE,aAAa,kBAAkB,OAAO,oBAAoB,CAAC,CAAC;AAAA,UACvE;AAAA,QACF;AACA,2BAAmB,IAAI;AAAA,UACrB,GAAG;AAAA,UACH,OAAOA,OAAM,IAAI,YAAY,QAAQ;AAAA,UACrC,OAAO;AAAA,QACT,CAAC;AACD,YAAI,SAAS;AACX,eAAK,QAAQ,kBAAkB;AAAA,QACjC,OAAO;AACL,eAAK,QAAQ,kBAAkB;AAAA,QACjC;AACA,eAAO,CAACA,QAAO,mBAAmB,IAAI,EAAE,QAAQ;AAAA,MAClD;AACA,YAAM,OAAO,CAAC,QAAQ,oBAAoB,wBAAwB;AAChE,YAAI;AACJ,YAAI;AACJ,cAAM,cAAc,mBAAmB,IAAI;AAC3C,cAAM,QAAQ,SAAS,QAAQ,OAAO,QAAQ,EAAE,qBAAqB,MAAM,CAAC;AAC5E,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAM,YAAY,YAAY,MAAM,CAAC,CAAC;AACtC,cAAI,cAAc,QAAQ,UAAU,QAAQ;AAC1C,gBAAI,cAAc,YAAY,MAAM,SAAS,GAAG;AAC9C,kBAAI,CAAC,gBAAgB;AACnB,iCAAiB,MAAM,CAAC,EAAE;AAAA,cAC5B;AACA,6BAAe,MAAM,CAAC,EAAE;AAAA,YAC1B;AACA,mBAAO,MAAM,CAAC,CAAC;AAAA,UACjB;AAAA,QACF;AACA,2BAAmB,IAAI;AAAA,UACrB,GAAG;AAAA,UACH,OAAO;AAAA,UACP,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,YAAI,kBAAkB,cAAc;AAClC,gBAAM,MAAM,OAAO,IAAI,UAAU;AACjC,cAAI,SAAS,gBAAgB,CAAC;AAC9B,cAAI,OAAO,cAAc,aAAa,KAAK,MAAM;AACjD,cAAI,wBAAwB,OAAO;AACjC,mBAAO,UAAU,OAAO,GAAG;AAAA,UAC7B;AACA,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,UAAU,CAAC,QAAQ,uBAAuB,mBAAmB,IAAI,EAAE,QAAQ;AACjF,YAAM,UAAU,CAAC,QAAQ,uBAAuB,mBAAmB,IAAI,EAAE,QAAQ;AAEjF,YAAM,MAAM,CAAC,QAAQ,iBAAiB;AACpC,cAAM,SAAS,yBAAuB;AACpC,iBAAO,KAAK,QAAQ,cAAc,mBAAmB;AAAA,QACvD;AACA,cAAMC,UAAS,CAAC,MAAM,WAAW,WAAW,cAAc,UAAU;AAClE,iBAAO,KAAK,QAAQ,cAAc,MAAM,WAAW,WAAW,WAAW;AAAA,QAC3E;AACA,cAAM,SAAS,MAAM;AACnB,iBAAO,KAAK,QAAQ,YAAY;AAAA,QAClC;AACA,cAAM,SAAS,MAAM;AACnB,iBAAO,KAAK,QAAQ,YAAY;AAAA,QAClC;AACA,cAAM,YAAY,CAAC,MAAM,SAASD,SAAQ;AACxC,iBAAO,QAAQ,QAAQ,cAAc,MAAM,SAASA,IAAG;AAAA,QACzD;AACA,eAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAMC;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF;AAEA,YAAM,YAAY,cAAY;AAC5B,cAAM,UAAU,KAAK,SAAS,KAAK,CAAC;AACpC,cAAM,SAAS,MAAM,QAAQ,IAAI,EAAE,KAAK,QAAQ;AAChD,cAAM,QAAQ,MAAM;AAClB,iBAAO;AACP,kBAAQ,IAAI,SAAS,KAAK,CAAC;AAAA,QAC7B;AACA,cAAM,QAAQ,MAAM,QAAQ,IAAI,EAAE,OAAO;AACzC,cAAMN,OAAM,MAAM,QAAQ,IAAI;AAC9B,cAAMC,OAAM,OAAK;AACf,iBAAO;AACP,kBAAQ,IAAI,SAAS,KAAK,CAAC,CAAC;AAAA,QAC9B;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,KAAAD;AAAA,UACA,KAAAC;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ,MAAM;AAClB,cAAM,UAAU,UAAU,IAAI;AAC9B,cAAM,KAAK,OAAK,QAAQ,IAAI,EAAE,KAAK,CAAC;AACpC,eAAO;AAAA,UACL,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAEA,YAAM,OAAO,CAAC,QAAQ,uBAAuB;AAC3C,cAAM,YAAY,MAAM;AACxB,eAAO,YAAY,IAAI;AACvB,cAAM,eAAe,SAAS,KAAK,OAAO,UAAU,WAAW,EAAE,QAAQ,OAAO,CAAC,CAAC;AAClF,cAAM,qBAAqB,CAAAM,SAAO;AAChC,UAAAA,KAAI,WAAW,QAAQ,QAAQ,QAAQ,kBAAkB,CAAC;AAC1D,UAAAA,KAAI,WAAW,QAAQ,QAAQ,QAAQ,kBAAkB,CAAC;AAAA,QAC5D;AACA,cAAM,oBAAoB,CAAAA,SAAO;AAC/B,gBAAM,OAAOA,KAAI,QAAQ;AACzB,gBAAM,UAAU,mBAAmB,IAAI;AACvC,6BAAmB,IAAI;AAAA,YACrB,GAAG;AAAA,YACH,WAAW,KAAK;AAAA,YAChB,WAAW,KAAK;AAAA,YAChB,aAAa,KAAK;AAAA,UACpB,CAAC;AAAA,QACH;AACA,cAAM,aAAa,CAACA,MAAK,YAAY;AACnC,gBAAM,UAAU;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,gBAAM,SAAS,UAAQA,KAAI,WAAW,MAAM,CAAC,OAAO;AACpD,eAAK,SAAS,MAAM;AAAA,QACtB;AACA,cAAM,sBAAsB,CAAC,WAAWA,SAAQ;AAC9C,UAAAA,KAAI,OAAO,cAAc,WAAWA,KAAI,QAAQ,CAAC,CAAC;AAAA,QACpD;AACA,cAAM,wBAAwB,CAACA,MAAK,SAAS;AAC3C,cAAI,SAAS,QAAQ,SAAS,KAAK,SAAS,WAAW,QAAQ,MAAM,SAAS,UAAU,SAAS,aAAa,SAAS,eAAe;AACpI,YAAAA,KAAI,MAAM,IAAI;AAAA,UAChB;AAAA,QACF;AACA,cAAM,QAAQ,CAAAA,SAAO;AACnB,eAAK,QAAQ,oBAAoB,KAAK;AACtC,qBAAWA,MAAK,IAAI;AACpB,6BAAmBA,IAAG;AAAA,QACxB;AACA,cAAM,SAAS,CAAAA,SAAO;AACpB,gBAAM,OAAOA,KAAI,QAAQ;AACzB,gBAAM,OAAO,mBAAmB,IAAI;AACpC,cAAI,CAAC,KAAK,SAAS,QAAQ;AACzB,kBAAMA,IAAG;AACT;AAAA,UACF;AACA,cAAI,KAAK,SAAS,KAAK,YAAY,KAAK,cAAc,KAAK,aAAa,KAAK,cAAc,KAAK,YAAY;AAC1G,iBAAK,QAAQ,kBAAkB;AAAA,UACjC,OAAO;AACL,kBAAM,QAAQ,KAAK,QAAQ,oBAAoB,KAAK,UAAU,KAAK,WAAW,KAAK,YAAY,KAAK,WAAW;AAC/G,gBAAI,SAAS,GAAG;AACd,kCAAoB,MAAMA,IAAG;AAAA,YAC/B;AACA,uBAAWA,MAAK,UAAU,CAAC;AAAA,UAC7B;AACA,6BAAmBA,IAAG;AAAA,QACxB;AACA,cAAM,eAAe,mBAAmB,IAAI;AAC5C,cAAM,cAAc;AAAA,UAClB,UAAU;AAAA,UACV,aAAa;AAAA,UACb,YAAY,aAAa;AAAA,UACzB,WAAW,aAAa;AAAA,UACxB,aAAa,aAAa;AAAA,QAC5B;AACA,cAAM,gBAAgB,WAAS;AAC7B,gBAAM,QAAQ;AAAA,YACZ;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,gBACL;AAAA,kBACE,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,aAAa;AAAA,kBACb,WAAW;AAAA,kBACX,WAAW;AAAA,gBACb;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,SAAS;AAAA,kBACT,YAAY;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,SAAS;AAAA,kBACT,YAAY;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,aAAa;AAAA,cACb,WAAW;AAAA,YACb;AAAA,UACF;AACA,cAAI,OAAO;AACT,kBAAM,KAAK;AAAA,cACT,MAAM;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA,cACN,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AACA,iBAAO;AAAA,QACT;AACA,cAAM,gBAAgB,CAAC,0BAA0BC,kBAAiB;AAAA,UAChE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO,cAAc,wBAAwB;AAAA,UAC/C;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,OAAO;AAAA,cACP,OAAO;AAAA,gBACL;AAAA,kBACE,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,MAAM;AAAA,gBACR;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,MAAM;AAAA,gBACR;AAAA,gBACA;AAAA,kBACE,MAAM;AAAA,kBACN,MAAM;AAAA,kBACN,MAAM;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA,aAAAA;AAAA,UACA,UAAU,CAACD,MAAK,YAAY;AAC1B,gBAAI,0BAA0B;AAC5B,kCAAoB,OAAOA,IAAG;AAAA,YAChC;AACA,gBAAI,QAAQ,SAAS,cAAc,mBAAmB,IAAI,EAAE,QAAQ,GAAG;AACrE,oBAAMA,IAAG;AAAA,YACX;AAAA,UACF;AAAA,UACA,UAAU,CAACA,MAAK,YAAY;AAC1B,kBAAM,OAAOA,KAAI,QAAQ;AACzB,oBAAQ,QAAQ,MAAM;AAAA,cACtB,KAAK;AACH,uBAAOA,IAAG;AACV;AAAA,cACF,KAAK;AACH,oBAAI,CAAC,QAAQ,QAAQ,oBAAoB,KAAK,WAAW,GAAG;AAC1D,wBAAMA,IAAG;AAAA,gBACX,OAAO;AACL,qCAAmBA,IAAG;AAAA,gBACxB;AACA;AAAA,cACF,KAAK;AACH,wBAAQ,QAAQ,oBAAoB,KAAK,aAAa,MAAM,IAAI;AAChE,sBAAMA,IAAG;AACT;AAAA,cACF,KAAK;AACH,qBAAK,QAAQ,kBAAkB;AAC/B,mCAAmBA,IAAG;AACtB;AAAA,cACF,KAAK;AACH,qBAAK,QAAQ,kBAAkB;AAC/B,mCAAmBA,IAAG;AACtB;AAAA,cACF,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AACH,oCAAoB,OAAOA,IAAG;AAC9B,kCAAkBA,IAAG;AACrB,sBAAMA,IAAG;AACT;AAAA,YACF;AACA,kCAAsBA,MAAK,QAAQ,IAAI;AAAA,UACzC;AAAA,UACA,UAAU,CAAAA,SAAO;AACf,mBAAOA,IAAG;AACV,kCAAsBA,MAAK,MAAM;AAAA,UACnC;AAAA,UACA,SAAS,MAAM;AACb,mBAAO,MAAM;AACb,iBAAK,QAAQ,kBAAkB;AAC/B,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,QACF;AACA,kBAAU,IAAI,OAAO,cAAc,KAAK,cAAc,OAAO,WAAW,GAAG,EAAE,QAAQ,UAAU,CAAC,CAAC;AAAA,MACnG;AAEA,YAAM,aAAa,CAAC,QAAQ,uBAAuB;AACjD,eAAO,WAAW,iBAAiB,MAAM;AACvC,eAAK,QAAQ,kBAAkB;AAAA,QACjC,CAAC;AAAA,MACH;AAEA,YAAM,aAAa,CAAC,QAAQ,uBAAuB,MAAM;AACvD,aAAK,QAAQ,kBAAkB;AAAA,MACjC;AACA,YAAM,WAAW,CAAC,QAAQ,uBAAuB;AAC/C,eAAO,GAAG,SAAS,YAAY,iBAAiB;AAAA,UAC9C,MAAM;AAAA,UACN,UAAU;AAAA,UACV,UAAU,WAAW,QAAQ,kBAAkB;AAAA,UAC/C,MAAM;AAAA,QACR,CAAC;AACD,eAAO,GAAG,SAAS,UAAU,iBAAiB;AAAA,UAC5C,SAAS;AAAA,UACT,UAAU,WAAW,QAAQ,kBAAkB;AAAA,UAC/C,MAAM;AAAA,QACR,CAAC;AACD,eAAO,UAAU,IAAI,UAAU,IAAI,WAAW,QAAQ,kBAAkB,CAAC;AAAA,MAC3E;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,iBAAiB,YAAU;AACtC,gBAAM,qBAAqB,KAAK;AAAA,YAC9B,OAAO;AAAA,YACP,OAAO;AAAA,YACP,MAAM;AAAA,YACN,WAAW;AAAA,YACX,WAAW;AAAA,YACX,aAAa;AAAA,UACf,CAAC;AACD,qBAAW,QAAQ,kBAAkB;AACrC,mBAAS,QAAQ,kBAAkB;AACnC,iBAAO,IAAI,QAAQ,kBAAkB;AAAA,QACvC,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;AC9jCH;", "names": ["value", "get", "set", "type", "parent", "next", "all", "find$1", "api", "initialData"]}