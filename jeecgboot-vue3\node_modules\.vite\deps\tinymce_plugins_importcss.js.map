{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/importcss/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/importcss/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const isString = isType('string');\n    const isObject = isType('object');\n    const isArray = isType('array');\n    const isFunction = isSimpleType('function');\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.EditorManager');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const option = name => editor => editor.options.get(name);\n    const register = editor => {\n      const registerOption = editor.options.register;\n      const filterProcessor = value => isString(value) || isFunction(value) || isObject(value);\n      registerOption('importcss_merge_classes', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('importcss_exclusive', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('importcss_selector_converter', { processor: 'function' });\n      registerOption('importcss_selector_filter', { processor: filterProcessor });\n      registerOption('importcss_file_filter', { processor: filterProcessor });\n      registerOption('importcss_groups', { processor: 'object[]' });\n      registerOption('importcss_append', {\n        processor: 'boolean',\n        default: false\n      });\n    };\n    const shouldMergeClasses = option('importcss_merge_classes');\n    const shouldImportExclusive = option('importcss_exclusive');\n    const getSelectorConverter = option('importcss_selector_converter');\n    const getSelectorFilter = option('importcss_selector_filter');\n    const getCssGroups = option('importcss_groups');\n    const shouldAppend = option('importcss_append');\n    const getFileFilter = option('importcss_file_filter');\n    const getSkin = option('skin');\n    const getSkinUrl = option('skin_url');\n\n    const nativePush = Array.prototype.push;\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const flatten = xs => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    const bind = (xs, f) => flatten(map(xs, f));\n\n    const generate = () => {\n      const ungroupedOrder = [];\n      const groupOrder = [];\n      const groups = {};\n      const addItemToGroup = (groupTitle, itemInfo) => {\n        if (groups[groupTitle]) {\n          groups[groupTitle].push(itemInfo);\n        } else {\n          groupOrder.push(groupTitle);\n          groups[groupTitle] = [itemInfo];\n        }\n      };\n      const addItem = itemInfo => {\n        ungroupedOrder.push(itemInfo);\n      };\n      const toFormats = () => {\n        const groupItems = bind(groupOrder, g => {\n          const items = groups[g];\n          return items.length === 0 ? [] : [{\n              title: g,\n              items\n            }];\n        });\n        return groupItems.concat(ungroupedOrder);\n      };\n      return {\n        addItemToGroup,\n        addItem,\n        toFormats\n      };\n    };\n\n    const internalEditorStyle = /^\\.(?:ephox|tiny-pageembed|mce)(?:[.-]+\\w+)+$/;\n    const removeCacheSuffix = url => {\n      const cacheSuffix = global$1.cacheSuffix;\n      if (isString(url)) {\n        url = url.replace('?' + cacheSuffix, '').replace('&' + cacheSuffix, '');\n      }\n      return url;\n    };\n    const isSkinContentCss = (editor, href) => {\n      const skin = getSkin(editor);\n      if (skin) {\n        const skinUrlBase = getSkinUrl(editor);\n        const skinUrl = skinUrlBase ? editor.documentBaseURI.toAbsolute(skinUrlBase) : global$2.baseURL + '/skins/ui/' + skin;\n        const contentSkinUrlPart = global$2.baseURL + '/skins/content/';\n        return href === skinUrl + '/content' + (editor.inline ? '.inline' : '') + '.min.css' || href.indexOf(contentSkinUrlPart) !== -1;\n      }\n      return false;\n    };\n    const compileFilter = filter => {\n      if (isString(filter)) {\n        return value => {\n          return value.indexOf(filter) !== -1;\n        };\n      } else if (filter instanceof RegExp) {\n        return value => {\n          return filter.test(value);\n        };\n      }\n      return filter;\n    };\n    const isCssImportRule = rule => rule.styleSheet;\n    const isCssPageRule = rule => rule.selectorText;\n    const getSelectors = (editor, doc, fileFilter) => {\n      const selectors = [];\n      const contentCSSUrls = {};\n      const append = (styleSheet, imported) => {\n        let href = styleSheet.href;\n        let rules;\n        href = removeCacheSuffix(href);\n        if (!href || fileFilter && !fileFilter(href, imported) || isSkinContentCss(editor, href)) {\n          return;\n        }\n        global.each(styleSheet.imports, styleSheet => {\n          append(styleSheet, true);\n        });\n        try {\n          rules = styleSheet.cssRules || styleSheet.rules;\n        } catch (e) {\n        }\n        global.each(rules, cssRule => {\n          if (isCssImportRule(cssRule)) {\n            append(cssRule.styleSheet, true);\n          } else if (isCssPageRule(cssRule)) {\n            global.each(cssRule.selectorText.split(','), selector => {\n              selectors.push(global.trim(selector));\n            });\n          }\n        });\n      };\n      global.each(editor.contentCSS, url => {\n        contentCSSUrls[url] = true;\n      });\n      if (!fileFilter) {\n        fileFilter = (href, imported) => {\n          return imported || contentCSSUrls[href];\n        };\n      }\n      try {\n        global.each(doc.styleSheets, styleSheet => {\n          append(styleSheet);\n        });\n      } catch (e) {\n      }\n      return selectors;\n    };\n    const defaultConvertSelectorToFormat = (editor, selectorText) => {\n      let format = {};\n      const selector = /^(?:([a-z0-9\\-_]+))?(\\.[a-z0-9_\\-\\.]+)$/i.exec(selectorText);\n      if (!selector) {\n        return;\n      }\n      const elementName = selector[1];\n      const classes = selector[2].substr(1).split('.').join(' ');\n      const inlineSelectorElements = global.makeMap('a,img');\n      if (selector[1]) {\n        format = { title: selectorText };\n        if (editor.schema.getTextBlockElements()[elementName]) {\n          format.block = elementName;\n        } else if (editor.schema.getBlockElements()[elementName] || inlineSelectorElements[elementName.toLowerCase()]) {\n          format.selector = elementName;\n        } else {\n          format.inline = elementName;\n        }\n      } else if (selector[2]) {\n        format = {\n          inline: 'span',\n          title: selectorText.substr(1),\n          classes\n        };\n      }\n      if (shouldMergeClasses(editor)) {\n        format.classes = classes;\n      } else {\n        format.attributes = { class: classes };\n      }\n      return format;\n    };\n    const getGroupsBySelector = (groups, selector) => {\n      return global.grep(groups, group => {\n        return !group.filter || group.filter(selector);\n      });\n    };\n    const compileUserDefinedGroups = groups => {\n      return global.map(groups, group => {\n        return global.extend({}, group, {\n          original: group,\n          selectors: {},\n          filter: compileFilter(group.filter)\n        });\n      });\n    };\n    const isExclusiveMode = (editor, group) => {\n      return group === null || shouldImportExclusive(editor);\n    };\n    const isUniqueSelector = (editor, selector, group, globallyUniqueSelectors) => {\n      return !(isExclusiveMode(editor, group) ? selector in globallyUniqueSelectors : selector in group.selectors);\n    };\n    const markUniqueSelector = (editor, selector, group, globallyUniqueSelectors) => {\n      if (isExclusiveMode(editor, group)) {\n        globallyUniqueSelectors[selector] = true;\n      } else {\n        group.selectors[selector] = true;\n      }\n    };\n    const convertSelectorToFormat = (editor, plugin, selector, group) => {\n      let selectorConverter;\n      const converter = getSelectorConverter(editor);\n      if (group && group.selector_converter) {\n        selectorConverter = group.selector_converter;\n      } else if (converter) {\n        selectorConverter = converter;\n      } else {\n        selectorConverter = () => {\n          return defaultConvertSelectorToFormat(editor, selector);\n        };\n      }\n      return selectorConverter.call(plugin, selector, group);\n    };\n    const setup = editor => {\n      editor.on('init', () => {\n        const model = generate();\n        const globallyUniqueSelectors = {};\n        const selectorFilter = compileFilter(getSelectorFilter(editor));\n        const groups = compileUserDefinedGroups(getCssGroups(editor));\n        const processSelector = (selector, group) => {\n          if (isUniqueSelector(editor, selector, group, globallyUniqueSelectors)) {\n            markUniqueSelector(editor, selector, group, globallyUniqueSelectors);\n            const format = convertSelectorToFormat(editor, editor.plugins.importcss, selector, group);\n            if (format) {\n              const formatName = format.name || global$3.DOM.uniqueId();\n              editor.formatter.register(formatName, format);\n              return {\n                title: format.title,\n                format: formatName\n              };\n            }\n          }\n          return null;\n        };\n        global.each(getSelectors(editor, editor.getDoc(), compileFilter(getFileFilter(editor))), selector => {\n          if (!internalEditorStyle.test(selector)) {\n            if (!selectorFilter || selectorFilter(selector)) {\n              const selectorGroups = getGroupsBySelector(groups, selector);\n              if (selectorGroups.length > 0) {\n                global.each(selectorGroups, group => {\n                  const menuItem = processSelector(selector, group);\n                  if (menuItem) {\n                    model.addItemToGroup(group.title, menuItem);\n                  }\n                });\n              } else {\n                const menuItem = processSelector(selector, null);\n                if (menuItem) {\n                  model.addItem(menuItem);\n                }\n              }\n            }\n          }\n        });\n        const items = model.toFormats();\n        editor.dispatch('addStyleModifications', {\n          items,\n          replace: !shouldAppend(editor)\n        });\n      });\n    };\n\n    const get = editor => {\n      const convertSelectorToFormat = selectorText => {\n        return defaultConvertSelectorToFormat(editor, selectorText);\n      };\n      return { convertSelectorToFormat };\n    };\n\n    var Plugin = () => {\n      global$4.add('importcss', editor => {\n        register(editor);\n        setup(editor);\n        return get(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"importcss\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/importcss')\n//   ES2015:\n//     import 'tinymce/plugins/importcss'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,YAAI;AACJ,YAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,iBAAO;AAAA,QACT,OAAO;AACL,mBAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,QAC7F;AAAA,MACF;AACA,YAAM,SAAS,OAAK;AAClB,cAAM,IAAI,OAAO;AACjB,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,SAAS,UAAQ,WAAS,OAAO,KAAK,MAAM;AAClD,YAAM,eAAe,UAAQ,WAAS,OAAO,UAAU;AACvD,YAAM,WAAW,OAAO,QAAQ;AAChC,YAAM,WAAW,OAAO,QAAQ;AAChC,YAAM,UAAU,OAAO,OAAO;AAC9B,YAAM,aAAa,aAAa,UAAU;AAE1C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,sBAAsB;AAEhE,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,aAAa;AAEvD,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE5D,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,WAAW,YAAU;AACzB,cAAM,iBAAiB,OAAO,QAAQ;AACtC,cAAM,kBAAkB,WAAS,SAAS,KAAK,KAAK,WAAW,KAAK,KAAK,SAAS,KAAK;AACvF,uBAAe,2BAA2B;AAAA,UACxC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,uBAAuB;AAAA,UACpC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,gCAAgC,EAAE,WAAW,WAAW,CAAC;AACxE,uBAAe,6BAA6B,EAAE,WAAW,gBAAgB,CAAC;AAC1E,uBAAe,yBAAyB,EAAE,WAAW,gBAAgB,CAAC;AACtE,uBAAe,oBAAoB,EAAE,WAAW,WAAW,CAAC;AAC5D,uBAAe,oBAAoB;AAAA,UACjC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,qBAAqB,OAAO,yBAAyB;AAC3D,YAAM,wBAAwB,OAAO,qBAAqB;AAC1D,YAAM,uBAAuB,OAAO,8BAA8B;AAClE,YAAM,oBAAoB,OAAO,2BAA2B;AAC5D,YAAM,eAAe,OAAO,kBAAkB;AAC9C,YAAM,eAAe,OAAO,kBAAkB;AAC9C,YAAM,gBAAgB,OAAO,uBAAuB;AACpD,YAAM,UAAU,OAAO,MAAM;AAC7B,YAAM,aAAa,OAAO,UAAU;AAEpC,YAAM,aAAa,MAAM,UAAU;AACnC,YAAM,MAAM,CAAC,IAAI,MAAM;AACrB,cAAM,MAAM,GAAG;AACf,cAAM,IAAI,IAAI,MAAM,GAAG;AACvB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,QACf;AACA,eAAO;AAAA,MACT;AACA,YAAM,UAAU,QAAM;AACpB,cAAM,IAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B,EAAE;AAAA,UAC7E;AACA,qBAAW,MAAM,GAAG,GAAG,CAAC,CAAC;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,CAAC,IAAI,MAAM,QAAQ,IAAI,IAAI,CAAC,CAAC;AAE1C,YAAM,WAAW,MAAM;AACrB,cAAM,iBAAiB,CAAC;AACxB,cAAM,aAAa,CAAC;AACpB,cAAM,SAAS,CAAC;AAChB,cAAM,iBAAiB,CAAC,YAAY,aAAa;AAC/C,cAAI,OAAO,UAAU,GAAG;AACtB,mBAAO,UAAU,EAAE,KAAK,QAAQ;AAAA,UAClC,OAAO;AACL,uBAAW,KAAK,UAAU;AAC1B,mBAAO,UAAU,IAAI,CAAC,QAAQ;AAAA,UAChC;AAAA,QACF;AACA,cAAM,UAAU,cAAY;AAC1B,yBAAe,KAAK,QAAQ;AAAA,QAC9B;AACA,cAAM,YAAY,MAAM;AACtB,gBAAM,aAAa,KAAK,YAAY,OAAK;AACvC,kBAAM,QAAQ,OAAO,CAAC;AACtB,mBAAO,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC;AAAA,cAC9B,OAAO;AAAA,cACP;AAAA,YACF,CAAC;AAAA,UACL,CAAC;AACD,iBAAO,WAAW,OAAO,cAAc;AAAA,QACzC;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,sBAAsB;AAC5B,YAAM,oBAAoB,SAAO;AAC/B,cAAM,cAAc,SAAS;AAC7B,YAAI,SAAS,GAAG,GAAG;AACjB,gBAAM,IAAI,QAAQ,MAAM,aAAa,EAAE,EAAE,QAAQ,MAAM,aAAa,EAAE;AAAA,QACxE;AACA,eAAO;AAAA,MACT;AACA,YAAM,mBAAmB,CAAC,QAAQ,SAAS;AACzC,cAAM,OAAO,QAAQ,MAAM;AAC3B,YAAI,MAAM;AACR,gBAAM,cAAc,WAAW,MAAM;AACrC,gBAAM,UAAU,cAAc,OAAO,gBAAgB,WAAW,WAAW,IAAI,SAAS,UAAU,eAAe;AACjH,gBAAM,qBAAqB,SAAS,UAAU;AAC9C,iBAAO,SAAS,UAAU,cAAc,OAAO,SAAS,YAAY,MAAM,cAAc,KAAK,QAAQ,kBAAkB,MAAM;AAAA,QAC/H;AACA,eAAO;AAAA,MACT;AACA,YAAM,gBAAgB,YAAU;AAC9B,YAAI,SAAS,MAAM,GAAG;AACpB,iBAAO,WAAS;AACd,mBAAO,MAAM,QAAQ,MAAM,MAAM;AAAA,UACnC;AAAA,QACF,WAAW,kBAAkB,QAAQ;AACnC,iBAAO,WAAS;AACd,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,kBAAkB,UAAQ,KAAK;AACrC,YAAM,gBAAgB,UAAQ,KAAK;AACnC,YAAM,eAAe,CAAC,QAAQ,KAAK,eAAe;AAChD,cAAM,YAAY,CAAC;AACnB,cAAM,iBAAiB,CAAC;AACxB,cAAM,SAAS,CAAC,YAAY,aAAa;AACvC,cAAI,OAAO,WAAW;AACtB,cAAI;AACJ,iBAAO,kBAAkB,IAAI;AAC7B,cAAI,CAAC,QAAQ,cAAc,CAAC,WAAW,MAAM,QAAQ,KAAK,iBAAiB,QAAQ,IAAI,GAAG;AACxF;AAAA,UACF;AACA,iBAAO,KAAK,WAAW,SAAS,CAAAA,gBAAc;AAC5C,mBAAOA,aAAY,IAAI;AAAA,UACzB,CAAC;AACD,cAAI;AACF,oBAAQ,WAAW,YAAY,WAAW;AAAA,UAC5C,SAAS,GAAG;AAAA,UACZ;AACA,iBAAO,KAAK,OAAO,aAAW;AAC5B,gBAAI,gBAAgB,OAAO,GAAG;AAC5B,qBAAO,QAAQ,YAAY,IAAI;AAAA,YACjC,WAAW,cAAc,OAAO,GAAG;AACjC,qBAAO,KAAK,QAAQ,aAAa,MAAM,GAAG,GAAG,cAAY;AACvD,0BAAU,KAAK,OAAO,KAAK,QAAQ,CAAC;AAAA,cACtC,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AACA,eAAO,KAAK,OAAO,YAAY,SAAO;AACpC,yBAAe,GAAG,IAAI;AAAA,QACxB,CAAC;AACD,YAAI,CAAC,YAAY;AACf,uBAAa,CAAC,MAAM,aAAa;AAC/B,mBAAO,YAAY,eAAe,IAAI;AAAA,UACxC;AAAA,QACF;AACA,YAAI;AACF,iBAAO,KAAK,IAAI,aAAa,gBAAc;AACzC,mBAAO,UAAU;AAAA,UACnB,CAAC;AAAA,QACH,SAAS,GAAG;AAAA,QACZ;AACA,eAAO;AAAA,MACT;AACA,YAAM,iCAAiC,CAAC,QAAQ,iBAAiB;AAC/D,YAAI,SAAS,CAAC;AACd,cAAM,WAAW,2CAA2C,KAAK,YAAY;AAC7E,YAAI,CAAC,UAAU;AACb;AAAA,QACF;AACA,cAAM,cAAc,SAAS,CAAC;AAC9B,cAAM,UAAU,SAAS,CAAC,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,GAAG;AACzD,cAAM,yBAAyB,OAAO,QAAQ,OAAO;AACrD,YAAI,SAAS,CAAC,GAAG;AACf,mBAAS,EAAE,OAAO,aAAa;AAC/B,cAAI,OAAO,OAAO,qBAAqB,EAAE,WAAW,GAAG;AACrD,mBAAO,QAAQ;AAAA,UACjB,WAAW,OAAO,OAAO,iBAAiB,EAAE,WAAW,KAAK,uBAAuB,YAAY,YAAY,CAAC,GAAG;AAC7G,mBAAO,WAAW;AAAA,UACpB,OAAO;AACL,mBAAO,SAAS;AAAA,UAClB;AAAA,QACF,WAAW,SAAS,CAAC,GAAG;AACtB,mBAAS;AAAA,YACP,QAAQ;AAAA,YACR,OAAO,aAAa,OAAO,CAAC;AAAA,YAC5B;AAAA,UACF;AAAA,QACF;AACA,YAAI,mBAAmB,MAAM,GAAG;AAC9B,iBAAO,UAAU;AAAA,QACnB,OAAO;AACL,iBAAO,aAAa,EAAE,OAAO,QAAQ;AAAA,QACvC;AACA,eAAO;AAAA,MACT;AACA,YAAM,sBAAsB,CAAC,QAAQ,aAAa;AAChD,eAAO,OAAO,KAAK,QAAQ,WAAS;AAClC,iBAAO,CAAC,MAAM,UAAU,MAAM,OAAO,QAAQ;AAAA,QAC/C,CAAC;AAAA,MACH;AACA,YAAM,2BAA2B,YAAU;AACzC,eAAO,OAAO,IAAI,QAAQ,WAAS;AACjC,iBAAO,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,YAC9B,UAAU;AAAA,YACV,WAAW,CAAC;AAAA,YACZ,QAAQ,cAAc,MAAM,MAAM;AAAA,UACpC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,kBAAkB,CAAC,QAAQ,UAAU;AACzC,eAAO,UAAU,QAAQ,sBAAsB,MAAM;AAAA,MACvD;AACA,YAAM,mBAAmB,CAAC,QAAQ,UAAU,OAAO,4BAA4B;AAC7E,eAAO,EAAE,gBAAgB,QAAQ,KAAK,IAAI,YAAY,0BAA0B,YAAY,MAAM;AAAA,MACpG;AACA,YAAM,qBAAqB,CAAC,QAAQ,UAAU,OAAO,4BAA4B;AAC/E,YAAI,gBAAgB,QAAQ,KAAK,GAAG;AAClC,kCAAwB,QAAQ,IAAI;AAAA,QACtC,OAAO;AACL,gBAAM,UAAU,QAAQ,IAAI;AAAA,QAC9B;AAAA,MACF;AACA,YAAM,0BAA0B,CAAC,QAAQ,QAAQ,UAAU,UAAU;AACnE,YAAI;AACJ,cAAM,YAAY,qBAAqB,MAAM;AAC7C,YAAI,SAAS,MAAM,oBAAoB;AACrC,8BAAoB,MAAM;AAAA,QAC5B,WAAW,WAAW;AACpB,8BAAoB;AAAA,QACtB,OAAO;AACL,8BAAoB,MAAM;AACxB,mBAAO,+BAA+B,QAAQ,QAAQ;AAAA,UACxD;AAAA,QACF;AACA,eAAO,kBAAkB,KAAK,QAAQ,UAAU,KAAK;AAAA,MACvD;AACA,YAAM,QAAQ,YAAU;AACtB,eAAO,GAAG,QAAQ,MAAM;AACtB,gBAAM,QAAQ,SAAS;AACvB,gBAAM,0BAA0B,CAAC;AACjC,gBAAM,iBAAiB,cAAc,kBAAkB,MAAM,CAAC;AAC9D,gBAAM,SAAS,yBAAyB,aAAa,MAAM,CAAC;AAC5D,gBAAM,kBAAkB,CAAC,UAAU,UAAU;AAC3C,gBAAI,iBAAiB,QAAQ,UAAU,OAAO,uBAAuB,GAAG;AACtE,iCAAmB,QAAQ,UAAU,OAAO,uBAAuB;AACnE,oBAAM,SAAS,wBAAwB,QAAQ,OAAO,QAAQ,WAAW,UAAU,KAAK;AACxF,kBAAI,QAAQ;AACV,sBAAM,aAAa,OAAO,QAAQ,SAAS,IAAI,SAAS;AACxD,uBAAO,UAAU,SAAS,YAAY,MAAM;AAC5C,uBAAO;AAAA,kBACL,OAAO,OAAO;AAAA,kBACd,QAAQ;AAAA,gBACV;AAAA,cACF;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AACA,iBAAO,KAAK,aAAa,QAAQ,OAAO,OAAO,GAAG,cAAc,cAAc,MAAM,CAAC,CAAC,GAAG,cAAY;AACnG,gBAAI,CAAC,oBAAoB,KAAK,QAAQ,GAAG;AACvC,kBAAI,CAAC,kBAAkB,eAAe,QAAQ,GAAG;AAC/C,sBAAM,iBAAiB,oBAAoB,QAAQ,QAAQ;AAC3D,oBAAI,eAAe,SAAS,GAAG;AAC7B,yBAAO,KAAK,gBAAgB,WAAS;AACnC,0BAAM,WAAW,gBAAgB,UAAU,KAAK;AAChD,wBAAI,UAAU;AACZ,4BAAM,eAAe,MAAM,OAAO,QAAQ;AAAA,oBAC5C;AAAA,kBACF,CAAC;AAAA,gBACH,OAAO;AACL,wBAAM,WAAW,gBAAgB,UAAU,IAAI;AAC/C,sBAAI,UAAU;AACZ,0BAAM,QAAQ,QAAQ;AAAA,kBACxB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AACD,gBAAM,QAAQ,MAAM,UAAU;AAC9B,iBAAO,SAAS,yBAAyB;AAAA,YACvC;AAAA,YACA,SAAS,CAAC,aAAa,MAAM;AAAA,UAC/B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,YAAM,MAAM,YAAU;AACpB,cAAMC,2BAA0B,kBAAgB;AAC9C,iBAAO,+BAA+B,QAAQ,YAAY;AAAA,QAC5D;AACA,eAAO,EAAE,yBAAAA,yBAAwB;AAAA,MACnC;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,aAAa,YAAU;AAClC,mBAAS,MAAM;AACf,gBAAM,MAAM;AACZ,iBAAO,IAAI,MAAM;AAAA,QACnB,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;ACjVH;", "names": ["styleSheet", "convertSelectorToFormat"]}