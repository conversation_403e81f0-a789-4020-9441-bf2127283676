{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/code/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/code/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const setContent = (editor, html) => {\n      editor.focus();\n      editor.undoManager.transact(() => {\n        editor.setContent(html);\n      });\n      editor.selection.setCursorLocation();\n      editor.nodeChanged();\n    };\n    const getContent = editor => {\n      return editor.getContent({ source_view: true });\n    };\n\n    const open = editor => {\n      const editorContent = getContent(editor);\n      editor.windowManager.open({\n        title: 'Source Code',\n        size: 'large',\n        body: {\n          type: 'panel',\n          items: [{\n              type: 'textarea',\n              name: 'code'\n            }]\n        },\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: { code: editorContent },\n        onSubmit: api => {\n          setContent(editor, api.getData().code);\n          api.close();\n        }\n      });\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('mceCodeEditor', () => {\n        open(editor);\n      });\n    };\n\n    const register = editor => {\n      const onAction = () => editor.execCommand('mceCodeEditor');\n      editor.ui.registry.addButton('code', {\n        icon: 'sourcecode',\n        tooltip: 'Source code',\n        onAction\n      });\n      editor.ui.registry.addMenuItem('code', {\n        icon: 'sourcecode',\n        text: 'Source code',\n        onAction\n      });\n    };\n\n    var Plugin = () => {\n      global.add('code', editor => {\n        register$1(editor);\n        register(editor);\n        return {};\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"code\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/code')\n//   ES2015:\n//     import 'tinymce/plugins/code'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAE/D,YAAM,aAAa,CAAC,QAAQ,SAAS;AACnC,eAAO,MAAM;AACb,eAAO,YAAY,SAAS,MAAM;AAChC,iBAAO,WAAW,IAAI;AAAA,QACxB,CAAC;AACD,eAAO,UAAU,kBAAkB;AACnC,eAAO,YAAY;AAAA,MACrB;AACA,YAAM,aAAa,YAAU;AAC3B,eAAO,OAAO,WAAW,EAAE,aAAa,KAAK,CAAC;AAAA,MAChD;AAEA,YAAM,OAAO,YAAU;AACrB,cAAM,gBAAgB,WAAW,MAAM;AACvC,eAAO,cAAc,KAAK;AAAA,UACxB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO,CAAC;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,YACR,CAAC;AAAA,UACL;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA,aAAa,EAAE,MAAM,cAAc;AAAA,UACnC,UAAU,SAAO;AACf,uBAAW,QAAQ,IAAI,QAAQ,EAAE,IAAI;AACrC,gBAAI,MAAM;AAAA,UACZ;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,aAAa,YAAU;AAC3B,eAAO,WAAW,iBAAiB,MAAM;AACvC,eAAK,MAAM;AAAA,QACb,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,YAAU;AACzB,cAAM,WAAW,MAAM,OAAO,YAAY,eAAe;AACzD,eAAO,GAAG,SAAS,UAAU,QAAQ;AAAA,UACnC,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA,QACF,CAAC;AACD,eAAO,GAAG,SAAS,YAAY,QAAQ;AAAA,UACrC,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,eAAO,IAAI,QAAQ,YAAU;AAC3B,qBAAW,MAAM;AACjB,mBAAS,MAAM;AACf,iBAAO,CAAC;AAAA,QACV,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;AC9EH;", "names": []}