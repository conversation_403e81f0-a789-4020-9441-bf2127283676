{"version": 3, "sources": ["../../.pnpm/codemirror@5.65.19/node_modules/codemirror/mode/python/python.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  function wordRegexp(words) {\n    return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n  }\n\n  var wordOperators = wordRegexp([\"and\", \"or\", \"not\", \"is\"]);\n  var commonKeywords = [\"as\", \"assert\", \"break\", \"class\", \"continue\",\n                        \"def\", \"del\", \"elif\", \"else\", \"except\", \"finally\",\n                        \"for\", \"from\", \"global\", \"if\", \"import\",\n                        \"lambda\", \"pass\", \"raise\", \"return\",\n                        \"try\", \"while\", \"with\", \"yield\", \"in\", \"False\", \"True\"];\n  var commonBuiltins = [\"abs\", \"all\", \"any\", \"bin\", \"bool\", \"bytearray\", \"callable\", \"chr\",\n                        \"classmethod\", \"compile\", \"complex\", \"delattr\", \"dict\", \"dir\", \"divmod\",\n                        \"enumerate\", \"eval\", \"filter\", \"float\", \"format\", \"frozenset\",\n                        \"getattr\", \"globals\", \"hasattr\", \"hash\", \"help\", \"hex\", \"id\",\n                        \"input\", \"int\", \"isinstance\", \"issubclass\", \"iter\", \"len\",\n                        \"list\", \"locals\", \"map\", \"max\", \"memoryview\", \"min\", \"next\",\n                        \"object\", \"oct\", \"open\", \"ord\", \"pow\", \"property\", \"range\",\n                        \"repr\", \"reversed\", \"round\", \"set\", \"setattr\", \"slice\",\n                        \"sorted\", \"staticmethod\", \"str\", \"sum\", \"super\", \"tuple\",\n                        \"type\", \"vars\", \"zip\", \"__import__\", \"NotImplemented\",\n                        \"Ellipsis\", \"__debug__\"];\n  CodeMirror.registerHelper(\"hintWords\", \"python\", commonKeywords.concat(commonBuiltins).concat([\"exec\", \"print\"]));\n\n  function top(state) {\n    return state.scopes[state.scopes.length - 1];\n  }\n\n  CodeMirror.defineMode(\"python\", function(conf, parserConf) {\n    var ERRORCLASS = \"error\";\n\n    var delimiters = parserConf.delimiters || parserConf.singleDelimiters || /^[\\(\\)\\[\\]\\{\\}@,:`=;\\.\\\\]/;\n    //               (Backwards-compatibility with old, cumbersome config system)\n    var operators = [parserConf.singleOperators, parserConf.doubleOperators, parserConf.doubleDelimiters, parserConf.tripleDelimiters,\n                     parserConf.operators || /^([-+*/%\\/&|^]=?|[<>=]+|\\/\\/=?|\\*\\*=?|!=|[~!@]|\\.\\.\\.)/]\n    for (var i = 0; i < operators.length; i++) if (!operators[i]) operators.splice(i--, 1)\n\n    var hangingIndent = parserConf.hangingIndent || conf.indentUnit;\n\n    var myKeywords = commonKeywords, myBuiltins = commonBuiltins;\n    if (parserConf.extra_keywords != undefined)\n      myKeywords = myKeywords.concat(parserConf.extra_keywords);\n\n    if (parserConf.extra_builtins != undefined)\n      myBuiltins = myBuiltins.concat(parserConf.extra_builtins);\n\n    var py3 = !(parserConf.version && Number(parserConf.version) < 3)\n    if (py3) {\n      // since http://legacy.python.org/dev/peps/pep-0465/ @ is also an operator\n      var identifiers = parserConf.identifiers|| /^[_A-Za-z\\u00A1-\\uFFFF][_A-Za-z0-9\\u00A1-\\uFFFF]*/;\n      myKeywords = myKeywords.concat([\"nonlocal\", \"None\", \"aiter\", \"anext\", \"async\", \"await\", \"breakpoint\", \"match\", \"case\"]);\n      myBuiltins = myBuiltins.concat([\"ascii\", \"bytes\", \"exec\", \"print\"]);\n      var stringPrefixes = new RegExp(\"^(([rbuf]|(br)|(rb)|(fr)|(rf))?('{3}|\\\"{3}|['\\\"]))\", \"i\");\n    } else {\n      var identifiers = parserConf.identifiers|| /^[_A-Za-z][_A-Za-z0-9]*/;\n      myKeywords = myKeywords.concat([\"exec\", \"print\"]);\n      myBuiltins = myBuiltins.concat([\"apply\", \"basestring\", \"buffer\", \"cmp\", \"coerce\", \"execfile\",\n                                      \"file\", \"intern\", \"long\", \"raw_input\", \"reduce\", \"reload\",\n                                      \"unichr\", \"unicode\", \"xrange\", \"None\"]);\n      var stringPrefixes = new RegExp(\"^(([rubf]|(ur)|(br))?('{3}|\\\"{3}|['\\\"]))\", \"i\");\n    }\n    var keywords = wordRegexp(myKeywords);\n    var builtins = wordRegexp(myBuiltins);\n\n    // tokenizers\n    function tokenBase(stream, state) {\n      var sol = stream.sol() && state.lastToken != \"\\\\\"\n      if (sol) state.indent = stream.indentation()\n      // Handle scope changes\n      if (sol && top(state).type == \"py\") {\n        var scopeOffset = top(state).offset;\n        if (stream.eatSpace()) {\n          var lineOffset = stream.indentation();\n          if (lineOffset > scopeOffset)\n            pushPyScope(state);\n          else if (lineOffset < scopeOffset && dedent(stream, state) && stream.peek() != \"#\")\n            state.errorToken = true;\n          return null;\n        } else {\n          var style = tokenBaseInner(stream, state);\n          if (scopeOffset > 0 && dedent(stream, state))\n            style += \" \" + ERRORCLASS;\n          return style;\n        }\n      }\n      return tokenBaseInner(stream, state);\n    }\n\n    function tokenBaseInner(stream, state, inFormat) {\n      if (stream.eatSpace()) return null;\n\n      // Handle Comments\n      if (!inFormat && stream.match(/^#.*/)) return \"comment\";\n\n      // Handle Number Literals\n      if (stream.match(/^[0-9\\.]/, false)) {\n        var floatLiteral = false;\n        // Floats\n        if (stream.match(/^[\\d_]*\\.\\d+(e[\\+\\-]?\\d+)?/i)) { floatLiteral = true; }\n        if (stream.match(/^[\\d_]+\\.\\d*/)) { floatLiteral = true; }\n        if (stream.match(/^\\.\\d+/)) { floatLiteral = true; }\n        if (floatLiteral) {\n          // Float literals may be \"imaginary\"\n          stream.eat(/J/i);\n          return \"number\";\n        }\n        // Integers\n        var intLiteral = false;\n        // Hex\n        if (stream.match(/^0x[0-9a-f_]+/i)) intLiteral = true;\n        // Binary\n        if (stream.match(/^0b[01_]+/i)) intLiteral = true;\n        // Octal\n        if (stream.match(/^0o[0-7_]+/i)) intLiteral = true;\n        // Decimal\n        if (stream.match(/^[1-9][\\d_]*(e[\\+\\-]?[\\d_]+)?/)) {\n          // Decimal literals may be \"imaginary\"\n          stream.eat(/J/i);\n          // TODO - Can you have imaginary longs?\n          intLiteral = true;\n        }\n        // Zero by itself with no other piece of number.\n        if (stream.match(/^0(?![\\dx])/i)) intLiteral = true;\n        if (intLiteral) {\n          // Integer literals may be \"long\"\n          stream.eat(/L/i);\n          return \"number\";\n        }\n      }\n\n      // Handle Strings\n      if (stream.match(stringPrefixes)) {\n        var isFmtString = stream.current().toLowerCase().indexOf('f') !== -1;\n        if (!isFmtString) {\n          state.tokenize = tokenStringFactory(stream.current(), state.tokenize);\n          return state.tokenize(stream, state);\n        } else {\n          state.tokenize = formatStringFactory(stream.current(), state.tokenize);\n          return state.tokenize(stream, state);\n        }\n      }\n\n      for (var i = 0; i < operators.length; i++)\n        if (stream.match(operators[i])) return \"operator\"\n\n      if (stream.match(delimiters)) return \"punctuation\";\n\n      if (state.lastToken == \".\" && stream.match(identifiers))\n        return \"property\";\n\n      if (stream.match(keywords) || stream.match(wordOperators))\n        return \"keyword\";\n\n      if (stream.match(builtins))\n        return \"builtin\";\n\n      if (stream.match(/^(self|cls)\\b/))\n        return \"variable-2\";\n\n      if (stream.match(identifiers)) {\n        if (state.lastToken == \"def\" || state.lastToken == \"class\")\n          return \"def\";\n        return \"variable\";\n      }\n\n      // Handle non-detected items\n      stream.next();\n      return inFormat ? null :ERRORCLASS;\n    }\n\n    function formatStringFactory(delimiter, tokenOuter) {\n      while (\"rubf\".indexOf(delimiter.charAt(0).toLowerCase()) >= 0)\n        delimiter = delimiter.substr(1);\n\n      var singleline = delimiter.length == 1;\n      var OUTCLASS = \"string\";\n\n      function tokenNestedExpr(depth) {\n        return function(stream, state) {\n          var inner = tokenBaseInner(stream, state, true)\n          if (inner == \"punctuation\") {\n            if (stream.current() == \"{\") {\n              state.tokenize = tokenNestedExpr(depth + 1)\n            } else if (stream.current() == \"}\") {\n              if (depth > 1) state.tokenize = tokenNestedExpr(depth - 1)\n              else state.tokenize = tokenString\n            }\n          }\n          return inner\n        }\n      }\n\n      function tokenString(stream, state) {\n        while (!stream.eol()) {\n          stream.eatWhile(/[^'\"\\{\\}\\\\]/);\n          if (stream.eat(\"\\\\\")) {\n            stream.next();\n            if (singleline && stream.eol())\n              return OUTCLASS;\n          } else if (stream.match(delimiter)) {\n            state.tokenize = tokenOuter;\n            return OUTCLASS;\n          } else if (stream.match('{{')) {\n            // ignore {{ in f-str\n            return OUTCLASS;\n          } else if (stream.match('{', false)) {\n            // switch to nested mode\n            state.tokenize = tokenNestedExpr(0)\n            if (stream.current()) return OUTCLASS;\n            else return state.tokenize(stream, state)\n          } else if (stream.match('}}')) {\n            return OUTCLASS;\n          } else if (stream.match('}')) {\n            // single } in f-string is an error\n            return ERRORCLASS;\n          } else {\n            stream.eat(/['\"]/);\n          }\n        }\n        if (singleline) {\n          if (parserConf.singleLineStringErrors)\n            return ERRORCLASS;\n          else\n            state.tokenize = tokenOuter;\n        }\n        return OUTCLASS;\n      }\n      tokenString.isString = true;\n      return tokenString;\n    }\n\n    function tokenStringFactory(delimiter, tokenOuter) {\n      while (\"rubf\".indexOf(delimiter.charAt(0).toLowerCase()) >= 0)\n        delimiter = delimiter.substr(1);\n\n      var singleline = delimiter.length == 1;\n      var OUTCLASS = \"string\";\n\n      function tokenString(stream, state) {\n        while (!stream.eol()) {\n          stream.eatWhile(/[^'\"\\\\]/);\n          if (stream.eat(\"\\\\\")) {\n            stream.next();\n            if (singleline && stream.eol())\n              return OUTCLASS;\n          } else if (stream.match(delimiter)) {\n            state.tokenize = tokenOuter;\n            return OUTCLASS;\n          } else {\n            stream.eat(/['\"]/);\n          }\n        }\n        if (singleline) {\n          if (parserConf.singleLineStringErrors)\n            return ERRORCLASS;\n          else\n            state.tokenize = tokenOuter;\n        }\n        return OUTCLASS;\n      }\n      tokenString.isString = true;\n      return tokenString;\n    }\n\n    function pushPyScope(state) {\n      while (top(state).type != \"py\") state.scopes.pop()\n      state.scopes.push({offset: top(state).offset + conf.indentUnit,\n                         type: \"py\",\n                         align: null})\n    }\n\n    function pushBracketScope(stream, state, type) {\n      var align = stream.match(/^[\\s\\[\\{\\(]*(?:#|$)/, false) ? null : stream.column() + 1\n      state.scopes.push({offset: state.indent + hangingIndent,\n                         type: type,\n                         align: align})\n    }\n\n    function dedent(stream, state) {\n      var indented = stream.indentation();\n      while (state.scopes.length > 1 && top(state).offset > indented) {\n        if (top(state).type != \"py\") return true;\n        state.scopes.pop();\n      }\n      return top(state).offset != indented;\n    }\n\n    function tokenLexer(stream, state) {\n      if (stream.sol()) {\n        state.beginningOfLine = true;\n        state.dedent = false;\n      }\n\n      var style = state.tokenize(stream, state);\n      var current = stream.current();\n\n      // Handle decorators\n      if (state.beginningOfLine && current == \"@\")\n        return stream.match(identifiers, false) ? \"meta\" : py3 ? \"operator\" : ERRORCLASS;\n\n      if (/\\S/.test(current)) state.beginningOfLine = false;\n\n      if ((style == \"variable\" || style == \"builtin\")\n          && state.lastToken == \"meta\")\n        style = \"meta\";\n\n      // Handle scope changes.\n      if (current == \"pass\" || current == \"return\")\n        state.dedent = true;\n\n      if (current == \"lambda\") state.lambda = true;\n      if (current == \":\" && !state.lambda && top(state).type == \"py\" && stream.match(/^\\s*(?:#|$)/, false))\n        pushPyScope(state);\n\n      if (current.length == 1 && !/string|comment/.test(style)) {\n        var delimiter_index = \"[({\".indexOf(current);\n        if (delimiter_index != -1)\n          pushBracketScope(stream, state, \"])}\".slice(delimiter_index, delimiter_index+1));\n\n        delimiter_index = \"])}\".indexOf(current);\n        if (delimiter_index != -1) {\n          if (top(state).type == current) state.indent = state.scopes.pop().offset - hangingIndent\n          else return ERRORCLASS;\n        }\n      }\n      if (state.dedent && stream.eol() && top(state).type == \"py\" && state.scopes.length > 1)\n        state.scopes.pop();\n\n      return style;\n    }\n\n    var external = {\n      startState: function(basecolumn) {\n        return {\n          tokenize: tokenBase,\n          scopes: [{offset: basecolumn || 0, type: \"py\", align: null}],\n          indent: basecolumn || 0,\n          lastToken: null,\n          lambda: false,\n          dedent: 0\n        };\n      },\n\n      token: function(stream, state) {\n        var addErr = state.errorToken;\n        if (addErr) state.errorToken = false;\n        var style = tokenLexer(stream, state);\n\n        if (style && style != \"comment\")\n          state.lastToken = (style == \"keyword\" || style == \"punctuation\") ? stream.current() : style;\n        if (style == \"punctuation\") style = null;\n\n        if (stream.eol() && state.lambda)\n          state.lambda = false;\n        return addErr ? style + \" \" + ERRORCLASS : style;\n      },\n\n      indent: function(state, textAfter) {\n        if (state.tokenize != tokenBase)\n          return state.tokenize.isString ? CodeMirror.Pass : 0;\n\n        var scope = top(state)\n        var closing = scope.type == textAfter.charAt(0) ||\n            scope.type == \"py\" && !state.dedent && /^(else:|elif |except |finally:)/.test(textAfter)\n        if (scope.align != null)\n          return scope.align - (closing ? 1 : 0)\n        else\n          return scope.offset - (closing ? hangingIndent : 0)\n      },\n\n      electricInput: /^\\s*([\\}\\]\\)]|else:|elif |except |finally:)$/,\n      closeBrackets: {triples: \"'\\\"\"},\n      lineComment: \"#\",\n      fold: \"indent\"\n    };\n    return external;\n  });\n\n  CodeMirror.defineMIME(\"text/x-python\", \"python\");\n\n  var words = function(str) { return str.split(\" \"); };\n\n  CodeMirror.defineMIME(\"text/x-cython\", {\n    name: \"python\",\n    extra_keywords: words(\"by cdef cimport cpdef ctypedef enum except \"+\n                          \"extern gil include nogil property public \"+\n                          \"readonly struct union DEF IF ELIF ELSE\")\n  });\n\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,eAAS,WAAWC,QAAO;AACzB,eAAO,IAAI,OAAO,QAAQA,OAAM,KAAK,KAAK,IAAI,OAAO;AAAA,MACvD;AAEA,UAAI,gBAAgB,WAAW,CAAC,OAAO,MAAM,OAAO,IAAI,CAAC;AACzD,UAAI,iBAAiB;AAAA,QAAC;AAAA,QAAM;AAAA,QAAU;AAAA,QAAS;AAAA,QAAS;AAAA,QAClC;AAAA,QAAO;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAU;AAAA,QACxC;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAM;AAAA,QAC/B;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAS;AAAA,QAC3B;AAAA,QAAO;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAM;AAAA,QAAS;AAAA,MAAM;AAC5E,UAAI,iBAAiB;AAAA,QAAC;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAa;AAAA,QAAY;AAAA,QAC7D;AAAA,QAAe;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAO;AAAA,QAC/D;AAAA,QAAa;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAS;AAAA,QAAU;AAAA,QAClD;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAO;AAAA,QACxD;AAAA,QAAS;AAAA,QAAO;AAAA,QAAc;AAAA,QAAc;AAAA,QAAQ;AAAA,QACpD;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAO;AAAA,QAAO;AAAA,QAAc;AAAA,QAAO;AAAA,QACrD;AAAA,QAAU;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAO;AAAA,QAAO;AAAA,QAAY;AAAA,QACnD;AAAA,QAAQ;AAAA,QAAY;AAAA,QAAS;AAAA,QAAO;AAAA,QAAW;AAAA,QAC/C;AAAA,QAAU;AAAA,QAAgB;AAAA,QAAO;AAAA,QAAO;AAAA,QAAS;AAAA,QACjD;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAO;AAAA,QAAc;AAAA,QACrC;AAAA,QAAY;AAAA,MAAW;AAC7C,MAAAD,YAAW,eAAe,aAAa,UAAU,eAAe,OAAO,cAAc,EAAE,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC;AAEhH,eAAS,IAAI,OAAO;AAClB,eAAO,MAAM,OAAO,MAAM,OAAO,SAAS,CAAC;AAAA,MAC7C;AAEA,MAAAA,YAAW,WAAW,UAAU,SAAS,MAAM,YAAY;AACzD,YAAI,aAAa;AAEjB,YAAI,aAAa,WAAW,cAAc,WAAW,oBAAoB;AAEzE,YAAI,YAAY;AAAA,UAAC,WAAW;AAAA,UAAiB,WAAW;AAAA,UAAiB,WAAW;AAAA,UAAkB,WAAW;AAAA,UAChG,WAAW,aAAa;AAAA,QAAwD;AACjG,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAK,KAAI,CAAC,UAAU,CAAC,EAAG,WAAU,OAAO,KAAK,CAAC;AAErF,YAAI,gBAAgB,WAAW,iBAAiB,KAAK;AAErD,YAAI,aAAa,gBAAgB,aAAa;AAC9C,YAAI,WAAW,kBAAkB;AAC/B,uBAAa,WAAW,OAAO,WAAW,cAAc;AAE1D,YAAI,WAAW,kBAAkB;AAC/B,uBAAa,WAAW,OAAO,WAAW,cAAc;AAE1D,YAAI,MAAM,EAAE,WAAW,WAAW,OAAO,WAAW,OAAO,IAAI;AAC/D,YAAI,KAAK;AAEP,cAAI,cAAc,WAAW,eAAc;AAC3C,uBAAa,WAAW,OAAO,CAAC,YAAY,QAAQ,SAAS,SAAS,SAAS,SAAS,cAAc,SAAS,MAAM,CAAC;AACtH,uBAAa,WAAW,OAAO,CAAC,SAAS,SAAS,QAAQ,OAAO,CAAC;AAClE,cAAI,iBAAiB,IAAI,OAAO,oDAAsD,GAAG;AAAA,QAC3F,OAAO;AACL,cAAI,cAAc,WAAW,eAAc;AAC3C,uBAAa,WAAW,OAAO,CAAC,QAAQ,OAAO,CAAC;AAChD,uBAAa,WAAW,OAAO;AAAA,YAAC;AAAA,YAAS;AAAA,YAAc;AAAA,YAAU;AAAA,YAAO;AAAA,YAAU;AAAA,YAClD;AAAA,YAAQ;AAAA,YAAU;AAAA,YAAQ;AAAA,YAAa;AAAA,YAAU;AAAA,YACjD;AAAA,YAAU;AAAA,YAAW;AAAA,YAAU;AAAA,UAAM,CAAC;AACtE,cAAI,iBAAiB,IAAI,OAAO,0CAA4C,GAAG;AAAA,QACjF;AACA,YAAI,WAAW,WAAW,UAAU;AACpC,YAAI,WAAW,WAAW,UAAU;AAGpC,iBAAS,UAAU,QAAQ,OAAO;AAChC,cAAI,MAAM,OAAO,IAAI,KAAK,MAAM,aAAa;AAC7C,cAAI,IAAK,OAAM,SAAS,OAAO,YAAY;AAE3C,cAAI,OAAO,IAAI,KAAK,EAAE,QAAQ,MAAM;AAClC,gBAAI,cAAc,IAAI,KAAK,EAAE;AAC7B,gBAAI,OAAO,SAAS,GAAG;AACrB,kBAAI,aAAa,OAAO,YAAY;AACpC,kBAAI,aAAa;AACf,4BAAY,KAAK;AAAA,uBACV,aAAa,eAAe,OAAO,QAAQ,KAAK,KAAK,OAAO,KAAK,KAAK;AAC7E,sBAAM,aAAa;AACrB,qBAAO;AAAA,YACT,OAAO;AACL,kBAAI,QAAQ,eAAe,QAAQ,KAAK;AACxC,kBAAI,cAAc,KAAK,OAAO,QAAQ,KAAK;AACzC,yBAAS,MAAM;AACjB,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO,eAAe,QAAQ,KAAK;AAAA,QACrC;AAEA,iBAAS,eAAe,QAAQ,OAAO,UAAU;AAC/C,cAAI,OAAO,SAAS,EAAG,QAAO;AAG9B,cAAI,CAAC,YAAY,OAAO,MAAM,MAAM,EAAG,QAAO;AAG9C,cAAI,OAAO,MAAM,YAAY,KAAK,GAAG;AACnC,gBAAI,eAAe;AAEnB,gBAAI,OAAO,MAAM,6BAA6B,GAAG;AAAE,6BAAe;AAAA,YAAM;AACxE,gBAAI,OAAO,MAAM,cAAc,GAAG;AAAE,6BAAe;AAAA,YAAM;AACzD,gBAAI,OAAO,MAAM,QAAQ,GAAG;AAAE,6BAAe;AAAA,YAAM;AACnD,gBAAI,cAAc;AAEhB,qBAAO,IAAI,IAAI;AACf,qBAAO;AAAA,YACT;AAEA,gBAAI,aAAa;AAEjB,gBAAI,OAAO,MAAM,gBAAgB,EAAG,cAAa;AAEjD,gBAAI,OAAO,MAAM,YAAY,EAAG,cAAa;AAE7C,gBAAI,OAAO,MAAM,aAAa,EAAG,cAAa;AAE9C,gBAAI,OAAO,MAAM,+BAA+B,GAAG;AAEjD,qBAAO,IAAI,IAAI;AAEf,2BAAa;AAAA,YACf;AAEA,gBAAI,OAAO,MAAM,cAAc,EAAG,cAAa;AAC/C,gBAAI,YAAY;AAEd,qBAAO,IAAI,IAAI;AACf,qBAAO;AAAA,YACT;AAAA,UACF;AAGA,cAAI,OAAO,MAAM,cAAc,GAAG;AAChC,gBAAI,cAAc,OAAO,QAAQ,EAAE,YAAY,EAAE,QAAQ,GAAG,MAAM;AAClE,gBAAI,CAAC,aAAa;AAChB,oBAAM,WAAW,mBAAmB,OAAO,QAAQ,GAAG,MAAM,QAAQ;AACpE,qBAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,YACrC,OAAO;AACL,oBAAM,WAAW,oBAAoB,OAAO,QAAQ,GAAG,MAAM,QAAQ;AACrE,qBAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,YACrC;AAAA,UACF;AAEA,mBAASE,KAAI,GAAGA,KAAI,UAAU,QAAQA;AACpC,gBAAI,OAAO,MAAM,UAAUA,EAAC,CAAC,EAAG,QAAO;AAEzC,cAAI,OAAO,MAAM,UAAU,EAAG,QAAO;AAErC,cAAI,MAAM,aAAa,OAAO,OAAO,MAAM,WAAW;AACpD,mBAAO;AAET,cAAI,OAAO,MAAM,QAAQ,KAAK,OAAO,MAAM,aAAa;AACtD,mBAAO;AAET,cAAI,OAAO,MAAM,QAAQ;AACvB,mBAAO;AAET,cAAI,OAAO,MAAM,eAAe;AAC9B,mBAAO;AAET,cAAI,OAAO,MAAM,WAAW,GAAG;AAC7B,gBAAI,MAAM,aAAa,SAAS,MAAM,aAAa;AACjD,qBAAO;AACT,mBAAO;AAAA,UACT;AAGA,iBAAO,KAAK;AACZ,iBAAO,WAAW,OAAM;AAAA,QAC1B;AAEA,iBAAS,oBAAoB,WAAW,YAAY;AAClD,iBAAO,OAAO,QAAQ,UAAU,OAAO,CAAC,EAAE,YAAY,CAAC,KAAK;AAC1D,wBAAY,UAAU,OAAO,CAAC;AAEhC,cAAI,aAAa,UAAU,UAAU;AACrC,cAAI,WAAW;AAEf,mBAAS,gBAAgB,OAAO;AAC9B,mBAAO,SAAS,QAAQ,OAAO;AAC7B,kBAAI,QAAQ,eAAe,QAAQ,OAAO,IAAI;AAC9C,kBAAI,SAAS,eAAe;AAC1B,oBAAI,OAAO,QAAQ,KAAK,KAAK;AAC3B,wBAAM,WAAW,gBAAgB,QAAQ,CAAC;AAAA,gBAC5C,WAAW,OAAO,QAAQ,KAAK,KAAK;AAClC,sBAAI,QAAQ,EAAG,OAAM,WAAW,gBAAgB,QAAQ,CAAC;AAAA,sBACpD,OAAM,WAAW;AAAA,gBACxB;AAAA,cACF;AACA,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,mBAAS,YAAY,QAAQ,OAAO;AAClC,mBAAO,CAAC,OAAO,IAAI,GAAG;AACpB,qBAAO,SAAS,aAAa;AAC7B,kBAAI,OAAO,IAAI,IAAI,GAAG;AACpB,uBAAO,KAAK;AACZ,oBAAI,cAAc,OAAO,IAAI;AAC3B,yBAAO;AAAA,cACX,WAAW,OAAO,MAAM,SAAS,GAAG;AAClC,sBAAM,WAAW;AACjB,uBAAO;AAAA,cACT,WAAW,OAAO,MAAM,IAAI,GAAG;AAE7B,uBAAO;AAAA,cACT,WAAW,OAAO,MAAM,KAAK,KAAK,GAAG;AAEnC,sBAAM,WAAW,gBAAgB,CAAC;AAClC,oBAAI,OAAO,QAAQ,EAAG,QAAO;AAAA,oBACxB,QAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,cAC1C,WAAW,OAAO,MAAM,IAAI,GAAG;AAC7B,uBAAO;AAAA,cACT,WAAW,OAAO,MAAM,GAAG,GAAG;AAE5B,uBAAO;AAAA,cACT,OAAO;AACL,uBAAO,IAAI,MAAM;AAAA,cACnB;AAAA,YACF;AACA,gBAAI,YAAY;AACd,kBAAI,WAAW;AACb,uBAAO;AAAA;AAEP,sBAAM,WAAW;AAAA,YACrB;AACA,mBAAO;AAAA,UACT;AACA,sBAAY,WAAW;AACvB,iBAAO;AAAA,QACT;AAEA,iBAAS,mBAAmB,WAAW,YAAY;AACjD,iBAAO,OAAO,QAAQ,UAAU,OAAO,CAAC,EAAE,YAAY,CAAC,KAAK;AAC1D,wBAAY,UAAU,OAAO,CAAC;AAEhC,cAAI,aAAa,UAAU,UAAU;AACrC,cAAI,WAAW;AAEf,mBAAS,YAAY,QAAQ,OAAO;AAClC,mBAAO,CAAC,OAAO,IAAI,GAAG;AACpB,qBAAO,SAAS,SAAS;AACzB,kBAAI,OAAO,IAAI,IAAI,GAAG;AACpB,uBAAO,KAAK;AACZ,oBAAI,cAAc,OAAO,IAAI;AAC3B,yBAAO;AAAA,cACX,WAAW,OAAO,MAAM,SAAS,GAAG;AAClC,sBAAM,WAAW;AACjB,uBAAO;AAAA,cACT,OAAO;AACL,uBAAO,IAAI,MAAM;AAAA,cACnB;AAAA,YACF;AACA,gBAAI,YAAY;AACd,kBAAI,WAAW;AACb,uBAAO;AAAA;AAEP,sBAAM,WAAW;AAAA,YACrB;AACA,mBAAO;AAAA,UACT;AACA,sBAAY,WAAW;AACvB,iBAAO;AAAA,QACT;AAEA,iBAAS,YAAY,OAAO;AAC1B,iBAAO,IAAI,KAAK,EAAE,QAAQ,KAAM,OAAM,OAAO,IAAI;AACjD,gBAAM,OAAO,KAAK;AAAA,YAAC,QAAQ,IAAI,KAAK,EAAE,SAAS,KAAK;AAAA,YACjC,MAAM;AAAA,YACN,OAAO;AAAA,UAAI,CAAC;AAAA,QACjC;AAEA,iBAAS,iBAAiB,QAAQ,OAAO,MAAM;AAC7C,cAAI,QAAQ,OAAO,MAAM,uBAAuB,KAAK,IAAI,OAAO,OAAO,OAAO,IAAI;AAClF,gBAAM,OAAO,KAAK;AAAA,YAAC,QAAQ,MAAM,SAAS;AAAA,YACvB;AAAA,YACA;AAAA,UAAY,CAAC;AAAA,QAClC;AAEA,iBAAS,OAAO,QAAQ,OAAO;AAC7B,cAAI,WAAW,OAAO,YAAY;AAClC,iBAAO,MAAM,OAAO,SAAS,KAAK,IAAI,KAAK,EAAE,SAAS,UAAU;AAC9D,gBAAI,IAAI,KAAK,EAAE,QAAQ,KAAM,QAAO;AACpC,kBAAM,OAAO,IAAI;AAAA,UACnB;AACA,iBAAO,IAAI,KAAK,EAAE,UAAU;AAAA,QAC9B;AAEA,iBAAS,WAAW,QAAQ,OAAO;AACjC,cAAI,OAAO,IAAI,GAAG;AAChB,kBAAM,kBAAkB;AACxB,kBAAM,SAAS;AAAA,UACjB;AAEA,cAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,cAAI,UAAU,OAAO,QAAQ;AAG7B,cAAI,MAAM,mBAAmB,WAAW;AACtC,mBAAO,OAAO,MAAM,aAAa,KAAK,IAAI,SAAS,MAAM,aAAa;AAExE,cAAI,KAAK,KAAK,OAAO,EAAG,OAAM,kBAAkB;AAEhD,eAAK,SAAS,cAAc,SAAS,cAC9B,MAAM,aAAa;AACxB,oBAAQ;AAGV,cAAI,WAAW,UAAU,WAAW;AAClC,kBAAM,SAAS;AAEjB,cAAI,WAAW,SAAU,OAAM,SAAS;AACxC,cAAI,WAAW,OAAO,CAAC,MAAM,UAAU,IAAI,KAAK,EAAE,QAAQ,QAAQ,OAAO,MAAM,eAAe,KAAK;AACjG,wBAAY,KAAK;AAEnB,cAAI,QAAQ,UAAU,KAAK,CAAC,iBAAiB,KAAK,KAAK,GAAG;AACxD,gBAAI,kBAAkB,MAAM,QAAQ,OAAO;AAC3C,gBAAI,mBAAmB;AACrB,+BAAiB,QAAQ,OAAO,MAAM,MAAM,iBAAiB,kBAAgB,CAAC,CAAC;AAEjF,8BAAkB,MAAM,QAAQ,OAAO;AACvC,gBAAI,mBAAmB,IAAI;AACzB,kBAAI,IAAI,KAAK,EAAE,QAAQ,QAAS,OAAM,SAAS,MAAM,OAAO,IAAI,EAAE,SAAS;AAAA,kBACtE,QAAO;AAAA,YACd;AAAA,UACF;AACA,cAAI,MAAM,UAAU,OAAO,IAAI,KAAK,IAAI,KAAK,EAAE,QAAQ,QAAQ,MAAM,OAAO,SAAS;AACnF,kBAAM,OAAO,IAAI;AAEnB,iBAAO;AAAA,QACT;AAEA,YAAI,WAAW;AAAA,UACb,YAAY,SAAS,YAAY;AAC/B,mBAAO;AAAA,cACL,UAAU;AAAA,cACV,QAAQ,CAAC,EAAC,QAAQ,cAAc,GAAG,MAAM,MAAM,OAAO,KAAI,CAAC;AAAA,cAC3D,QAAQ,cAAc;AAAA,cACtB,WAAW;AAAA,cACX,QAAQ;AAAA,cACR,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,SAAS,MAAM;AACnB,gBAAI,OAAQ,OAAM,aAAa;AAC/B,gBAAI,QAAQ,WAAW,QAAQ,KAAK;AAEpC,gBAAI,SAAS,SAAS;AACpB,oBAAM,YAAa,SAAS,aAAa,SAAS,gBAAiB,OAAO,QAAQ,IAAI;AACxF,gBAAI,SAAS,cAAe,SAAQ;AAEpC,gBAAI,OAAO,IAAI,KAAK,MAAM;AACxB,oBAAM,SAAS;AACjB,mBAAO,SAAS,QAAQ,MAAM,aAAa;AAAA,UAC7C;AAAA,UAEA,QAAQ,SAAS,OAAO,WAAW;AACjC,gBAAI,MAAM,YAAY;AACpB,qBAAO,MAAM,SAAS,WAAWF,YAAW,OAAO;AAErD,gBAAI,QAAQ,IAAI,KAAK;AACrB,gBAAI,UAAU,MAAM,QAAQ,UAAU,OAAO,CAAC,KAC1C,MAAM,QAAQ,QAAQ,CAAC,MAAM,UAAU,kCAAkC,KAAK,SAAS;AAC3F,gBAAI,MAAM,SAAS;AACjB,qBAAO,MAAM,SAAS,UAAU,IAAI;AAAA;AAEpC,qBAAO,MAAM,UAAU,UAAU,gBAAgB;AAAA,UACrD;AAAA,UAEA,eAAe;AAAA,UACf,eAAe,EAAC,SAAS,KAAK;AAAA,UAC9B,aAAa;AAAA,UACb,MAAM;AAAA,QACR;AACA,eAAO;AAAA,MACT,CAAC;AAED,MAAAA,YAAW,WAAW,iBAAiB,QAAQ;AAE/C,UAAI,QAAQ,SAAS,KAAK;AAAE,eAAO,IAAI,MAAM,GAAG;AAAA,MAAG;AAEnD,MAAAA,YAAW,WAAW,iBAAiB;AAAA,QACrC,MAAM;AAAA,QACN,gBAAgB,MAAM,4HAEwC;AAAA,MAChE,CAAC;AAAA,IAEH,CAAC;AAAA;AAAA;", "names": ["CodeMirror", "words", "i"]}