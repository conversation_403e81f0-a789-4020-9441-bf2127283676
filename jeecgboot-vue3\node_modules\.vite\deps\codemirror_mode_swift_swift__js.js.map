{"version": 3, "sources": ["../../.pnpm/codemirror@5.65.19/node_modules/codemirror/mode/swift/swift.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n// Swift mode created by <PERSON> https://github.com/mkaminsky11\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\")\n    mod(require(\"../../lib/codemirror\"))\n  else if (typeof define == \"function\" && define.amd)\n    define([\"../../lib/codemirror\"], mod)\n  else\n    mod(CodeMirror)\n})(function(CodeMirror) {\n  \"use strict\"\n\n  function wordSet(words) {\n    var set = {}\n    for (var i = 0; i < words.length; i++) set[words[i]] = true\n    return set\n  }\n\n  var keywords = wordSet([\"_\",\"var\",\"let\",\"actor\",\"class\",\"enum\",\"extension\",\"import\",\"protocol\",\"struct\",\"func\",\"typealias\",\"associatedtype\",\n                          \"open\",\"public\",\"internal\",\"fileprivate\",\"private\",\"deinit\",\"init\",\"new\",\"override\",\"self\",\"subscript\",\"super\",\n                          \"convenience\",\"dynamic\",\"final\",\"indirect\",\"lazy\",\"required\",\"static\",\"unowned\",\"unowned(safe)\",\"unowned(unsafe)\",\"weak\",\"as\",\"is\",\n                          \"break\",\"case\",\"continue\",\"default\",\"else\",\"fallthrough\",\"for\",\"guard\",\"if\",\"in\",\"repeat\",\"switch\",\"where\",\"while\",\n                          \"defer\",\"return\",\"inout\",\"mutating\",\"nonmutating\",\"isolated\",\"nonisolated\",\"catch\",\"do\",\"rethrows\",\"throw\",\"throws\",\"async\",\"await\",\"try\",\"didSet\",\"get\",\"set\",\"willSet\",\n                          \"assignment\",\"associativity\",\"infix\",\"left\",\"none\",\"operator\",\"postfix\",\"precedence\",\"precedencegroup\",\"prefix\",\"right\",\n                          \"Any\",\"AnyObject\",\"Type\",\"dynamicType\",\"Self\",\"Protocol\",\"__COLUMN__\",\"__FILE__\",\"__FUNCTION__\",\"__LINE__\"])\n  var definingKeywords = wordSet([\"var\",\"let\",\"actor\",\"class\",\"enum\",\"extension\",\"import\",\"protocol\",\"struct\",\"func\",\"typealias\",\"associatedtype\",\"for\"])\n  var atoms = wordSet([\"true\",\"false\",\"nil\",\"self\",\"super\",\"_\"])\n  var types = wordSet([\"Array\",\"Bool\",\"Character\",\"Dictionary\",\"Double\",\"Float\",\"Int\",\"Int8\",\"Int16\",\"Int32\",\"Int64\",\"Never\",\"Optional\",\"Set\",\"String\",\n                       \"UInt8\",\"UInt16\",\"UInt32\",\"UInt64\",\"Void\"])\n  var operators = \"+-/*%=|&<>~^?!\"\n  var punc = \":;,.(){}[]\"\n  var binary = /^\\-?0b[01][01_]*/\n  var octal = /^\\-?0o[0-7][0-7_]*/\n  var hexadecimal = /^\\-?0x[\\dA-Fa-f][\\dA-Fa-f_]*(?:(?:\\.[\\dA-Fa-f][\\dA-Fa-f_]*)?[Pp]\\-?\\d[\\d_]*)?/\n  var decimal = /^\\-?\\d[\\d_]*(?:\\.\\d[\\d_]*)?(?:[Ee]\\-?\\d[\\d_]*)?/\n  var identifier = /^\\$\\d+|(`?)[_A-Za-z][_A-Za-z$0-9]*\\1/\n  var property = /^\\.(?:\\$\\d+|(`?)[_A-Za-z][_A-Za-z$0-9]*\\1)/\n  var instruction = /^\\#[A-Za-z]+/\n  var attribute = /^@(?:\\$\\d+|(`?)[_A-Za-z][_A-Za-z$0-9]*\\1)/\n  //var regexp = /^\\/(?!\\s)(?:\\/\\/)?(?:\\\\.|[^\\/])+\\//\n\n  function tokenBase(stream, state, prev) {\n    if (stream.sol()) state.indented = stream.indentation()\n    if (stream.eatSpace()) return null\n\n    var ch = stream.peek()\n    if (ch == \"/\") {\n      if (stream.match(\"//\")) {\n        stream.skipToEnd()\n        return \"comment\"\n      }\n      if (stream.match(\"/*\")) {\n        state.tokenize.push(tokenComment)\n        return tokenComment(stream, state)\n      }\n    }\n    if (stream.match(instruction)) return \"builtin\"\n    if (stream.match(attribute)) return \"attribute\"\n    if (stream.match(binary)) return \"number\"\n    if (stream.match(octal)) return \"number\"\n    if (stream.match(hexadecimal)) return \"number\"\n    if (stream.match(decimal)) return \"number\"\n    if (stream.match(property)) return \"property\"\n    if (operators.indexOf(ch) > -1) {\n      stream.next()\n      return \"operator\"\n    }\n    if (punc.indexOf(ch) > -1) {\n      stream.next()\n      stream.match(\"..\")\n      return \"punctuation\"\n    }\n    var stringMatch\n    if (stringMatch = stream.match(/(\"\"\"|\"|')/)) {\n      var tokenize = tokenString.bind(null, stringMatch[0])\n      state.tokenize.push(tokenize)\n      return tokenize(stream, state)\n    }\n\n    if (stream.match(identifier)) {\n      var ident = stream.current()\n      if (types.hasOwnProperty(ident)) return \"variable-2\"\n      if (atoms.hasOwnProperty(ident)) return \"atom\"\n      if (keywords.hasOwnProperty(ident)) {\n        if (definingKeywords.hasOwnProperty(ident))\n          state.prev = \"define\"\n        return \"keyword\"\n      }\n      if (prev == \"define\") return \"def\"\n      return \"variable\"\n    }\n\n    stream.next()\n    return null\n  }\n\n  function tokenUntilClosingParen() {\n    var depth = 0\n    return function(stream, state, prev) {\n      var inner = tokenBase(stream, state, prev)\n      if (inner == \"punctuation\") {\n        if (stream.current() == \"(\") ++depth\n        else if (stream.current() == \")\") {\n          if (depth == 0) {\n            stream.backUp(1)\n            state.tokenize.pop()\n            return state.tokenize[state.tokenize.length - 1](stream, state)\n          }\n          else --depth\n        }\n      }\n      return inner\n    }\n  }\n\n  function tokenString(openQuote, stream, state) {\n    var singleLine = openQuote.length == 1\n    var ch, escaped = false\n    while (ch = stream.peek()) {\n      if (escaped) {\n        stream.next()\n        if (ch == \"(\") {\n          state.tokenize.push(tokenUntilClosingParen())\n          return \"string\"\n        }\n        escaped = false\n      } else if (stream.match(openQuote)) {\n        state.tokenize.pop()\n        return \"string\"\n      } else {\n        stream.next()\n        escaped = ch == \"\\\\\"\n      }\n    }\n    if (singleLine) {\n      state.tokenize.pop()\n    }\n    return \"string\"\n  }\n\n  function tokenComment(stream, state) {\n    var ch\n    while (ch = stream.next()) {\n      if (ch === \"/\" && stream.eat(\"*\")) {\n        state.tokenize.push(tokenComment)\n      } else if (ch === \"*\" && stream.eat(\"/\")) {\n        state.tokenize.pop()\n        break\n      }\n    }\n    return \"comment\"\n  }\n\n  function Context(prev, align, indented) {\n    this.prev = prev\n    this.align = align\n    this.indented = indented\n  }\n\n  function pushContext(state, stream) {\n    var align = stream.match(/^\\s*($|\\/[\\/\\*])/, false) ? null : stream.column() + 1\n    state.context = new Context(state.context, align, state.indented)\n  }\n\n  function popContext(state) {\n    if (state.context) {\n      state.indented = state.context.indented\n      state.context = state.context.prev\n    }\n  }\n\n  CodeMirror.defineMode(\"swift\", function(config) {\n    return {\n      startState: function() {\n        return {\n          prev: null,\n          context: null,\n          indented: 0,\n          tokenize: []\n        }\n      },\n\n      token: function(stream, state) {\n        var prev = state.prev\n        state.prev = null\n        var tokenize = state.tokenize[state.tokenize.length - 1] || tokenBase\n        var style = tokenize(stream, state, prev)\n        if (!style || style == \"comment\") state.prev = prev\n        else if (!state.prev) state.prev = style\n\n        if (style == \"punctuation\") {\n          var bracket = /[\\(\\[\\{]|([\\]\\)\\}])/.exec(stream.current())\n          if (bracket) (bracket[1] ? popContext : pushContext)(state, stream)\n        }\n\n        return style\n      },\n\n      indent: function(state, textAfter) {\n        var cx = state.context\n        if (!cx) return 0\n        var closing = /^[\\]\\}\\)]/.test(textAfter)\n        if (cx.align != null) return cx.align - (closing ? 1 : 0)\n        return cx.indented + (closing ? 0 : config.indentUnit)\n      },\n\n      electricInput: /^\\s*[\\)\\}\\]]$/,\n\n      lineComment: \"//\",\n      blockCommentStart: \"/*\",\n      blockCommentEnd: \"*/\",\n      fold: \"brace\",\n      closeBrackets: \"()[]{}''\\\"\\\"``\"\n    }\n  })\n\n  CodeMirror.defineMIME(\"text/x-swift\",\"swift\")\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAKA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,eAAS,QAAQ,OAAO;AACtB,YAAI,MAAM,CAAC;AACX,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,KAAI,MAAM,CAAC,CAAC,IAAI;AACvD,eAAO;AAAA,MACT;AAEA,UAAI,WAAW,QAAQ;AAAA,QAAC;AAAA,QAAI;AAAA,QAAM;AAAA,QAAM;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAO;AAAA,QAAY;AAAA,QAAS;AAAA,QAAW;AAAA,QAAS;AAAA,QAAO;AAAA,QAAY;AAAA,QACnG;AAAA,QAAO;AAAA,QAAS;AAAA,QAAW;AAAA,QAAc;AAAA,QAAU;AAAA,QAAS;AAAA,QAAO;AAAA,QAAM;AAAA,QAAW;AAAA,QAAO;AAAA,QAAY;AAAA,QACvG;AAAA,QAAc;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAO;AAAA,QAAW;AAAA,QAAS;AAAA,QAAU;AAAA,QAAgB;AAAA,QAAkB;AAAA,QAAO;AAAA,QAAK;AAAA,QAC9H;AAAA,QAAQ;AAAA,QAAO;AAAA,QAAW;AAAA,QAAU;AAAA,QAAO;AAAA,QAAc;AAAA,QAAM;AAAA,QAAQ;AAAA,QAAK;AAAA,QAAK;AAAA,QAAS;AAAA,QAAS;AAAA,QAAQ;AAAA,QAC3G;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAc;AAAA,QAAW;AAAA,QAAc;AAAA,QAAQ;AAAA,QAAK;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAM;AAAA,QAAS;AAAA,QAAM;AAAA,QAAM;AAAA,QAC/J;AAAA,QAAa;AAAA,QAAgB;AAAA,QAAQ;AAAA,QAAO;AAAA,QAAO;AAAA,QAAW;AAAA,QAAU;AAAA,QAAa;AAAA,QAAkB;AAAA,QAAS;AAAA,QAChH;AAAA,QAAM;AAAA,QAAY;AAAA,QAAO;AAAA,QAAc;AAAA,QAAO;AAAA,QAAW;AAAA,QAAa;AAAA,QAAW;AAAA,QAAe;AAAA,MAAU,CAAC;AACnI,UAAI,mBAAmB,QAAQ,CAAC,OAAM,OAAM,SAAQ,SAAQ,QAAO,aAAY,UAAS,YAAW,UAAS,QAAO,aAAY,kBAAiB,KAAK,CAAC;AACtJ,UAAI,QAAQ,QAAQ,CAAC,QAAO,SAAQ,OAAM,QAAO,SAAQ,GAAG,CAAC;AAC7D,UAAI,QAAQ,QAAQ;AAAA,QAAC;AAAA,QAAQ;AAAA,QAAO;AAAA,QAAY;AAAA,QAAa;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAM;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAM;AAAA,QACvH;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAS;AAAA,QAAS;AAAA,MAAM,CAAC;AAC/D,UAAI,YAAY;AAChB,UAAI,OAAO;AACX,UAAI,SAAS;AACb,UAAI,QAAQ;AACZ,UAAI,cAAc;AAClB,UAAI,UAAU;AACd,UAAI,aAAa;AACjB,UAAI,WAAW;AACf,UAAI,cAAc;AAClB,UAAI,YAAY;AAGhB,eAAS,UAAU,QAAQ,OAAO,MAAM;AACtC,YAAI,OAAO,IAAI,EAAG,OAAM,WAAW,OAAO,YAAY;AACtD,YAAI,OAAO,SAAS,EAAG,QAAO;AAE9B,YAAI,KAAK,OAAO,KAAK;AACrB,YAAI,MAAM,KAAK;AACb,cAAI,OAAO,MAAM,IAAI,GAAG;AACtB,mBAAO,UAAU;AACjB,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,MAAM,IAAI,GAAG;AACtB,kBAAM,SAAS,KAAK,YAAY;AAChC,mBAAO,aAAa,QAAQ,KAAK;AAAA,UACnC;AAAA,QACF;AACA,YAAI,OAAO,MAAM,WAAW,EAAG,QAAO;AACtC,YAAI,OAAO,MAAM,SAAS,EAAG,QAAO;AACpC,YAAI,OAAO,MAAM,MAAM,EAAG,QAAO;AACjC,YAAI,OAAO,MAAM,KAAK,EAAG,QAAO;AAChC,YAAI,OAAO,MAAM,WAAW,EAAG,QAAO;AACtC,YAAI,OAAO,MAAM,OAAO,EAAG,QAAO;AAClC,YAAI,OAAO,MAAM,QAAQ,EAAG,QAAO;AACnC,YAAI,UAAU,QAAQ,EAAE,IAAI,IAAI;AAC9B,iBAAO,KAAK;AACZ,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,QAAQ,EAAE,IAAI,IAAI;AACzB,iBAAO,KAAK;AACZ,iBAAO,MAAM,IAAI;AACjB,iBAAO;AAAA,QACT;AACA,YAAI;AACJ,YAAI,cAAc,OAAO,MAAM,WAAW,GAAG;AAC3C,cAAI,WAAW,YAAY,KAAK,MAAM,YAAY,CAAC,CAAC;AACpD,gBAAM,SAAS,KAAK,QAAQ;AAC5B,iBAAO,SAAS,QAAQ,KAAK;AAAA,QAC/B;AAEA,YAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,cAAI,QAAQ,OAAO,QAAQ;AAC3B,cAAI,MAAM,eAAe,KAAK,EAAG,QAAO;AACxC,cAAI,MAAM,eAAe,KAAK,EAAG,QAAO;AACxC,cAAI,SAAS,eAAe,KAAK,GAAG;AAClC,gBAAI,iBAAiB,eAAe,KAAK;AACvC,oBAAM,OAAO;AACf,mBAAO;AAAA,UACT;AACA,cAAI,QAAQ,SAAU,QAAO;AAC7B,iBAAO;AAAA,QACT;AAEA,eAAO,KAAK;AACZ,eAAO;AAAA,MACT;AAEA,eAAS,yBAAyB;AAChC,YAAI,QAAQ;AACZ,eAAO,SAAS,QAAQ,OAAO,MAAM;AACnC,cAAI,QAAQ,UAAU,QAAQ,OAAO,IAAI;AACzC,cAAI,SAAS,eAAe;AAC1B,gBAAI,OAAO,QAAQ,KAAK,IAAK,GAAE;AAAA,qBACtB,OAAO,QAAQ,KAAK,KAAK;AAChC,kBAAI,SAAS,GAAG;AACd,uBAAO,OAAO,CAAC;AACf,sBAAM,SAAS,IAAI;AACnB,uBAAO,MAAM,SAAS,MAAM,SAAS,SAAS,CAAC,EAAE,QAAQ,KAAK;AAAA,cAChE,MACK,GAAE;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,YAAY,WAAW,QAAQ,OAAO;AAC7C,YAAI,aAAa,UAAU,UAAU;AACrC,YAAI,IAAI,UAAU;AAClB,eAAO,KAAK,OAAO,KAAK,GAAG;AACzB,cAAI,SAAS;AACX,mBAAO,KAAK;AACZ,gBAAI,MAAM,KAAK;AACb,oBAAM,SAAS,KAAK,uBAAuB,CAAC;AAC5C,qBAAO;AAAA,YACT;AACA,sBAAU;AAAA,UACZ,WAAW,OAAO,MAAM,SAAS,GAAG;AAClC,kBAAM,SAAS,IAAI;AACnB,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,KAAK;AACZ,sBAAU,MAAM;AAAA,UAClB;AAAA,QACF;AACA,YAAI,YAAY;AACd,gBAAM,SAAS,IAAI;AAAA,QACrB;AACA,eAAO;AAAA,MACT;AAEA,eAAS,aAAa,QAAQ,OAAO;AACnC,YAAI;AACJ,eAAO,KAAK,OAAO,KAAK,GAAG;AACzB,cAAI,OAAO,OAAO,OAAO,IAAI,GAAG,GAAG;AACjC,kBAAM,SAAS,KAAK,YAAY;AAAA,UAClC,WAAW,OAAO,OAAO,OAAO,IAAI,GAAG,GAAG;AACxC,kBAAM,SAAS,IAAI;AACnB;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,eAAS,QAAQ,MAAM,OAAO,UAAU;AACtC,aAAK,OAAO;AACZ,aAAK,QAAQ;AACb,aAAK,WAAW;AAAA,MAClB;AAEA,eAAS,YAAY,OAAO,QAAQ;AAClC,YAAI,QAAQ,OAAO,MAAM,oBAAoB,KAAK,IAAI,OAAO,OAAO,OAAO,IAAI;AAC/E,cAAM,UAAU,IAAI,QAAQ,MAAM,SAAS,OAAO,MAAM,QAAQ;AAAA,MAClE;AAEA,eAAS,WAAW,OAAO;AACzB,YAAI,MAAM,SAAS;AACjB,gBAAM,WAAW,MAAM,QAAQ;AAC/B,gBAAM,UAAU,MAAM,QAAQ;AAAA,QAChC;AAAA,MACF;AAEA,MAAAA,YAAW,WAAW,SAAS,SAAS,QAAQ;AAC9C,eAAO;AAAA,UACL,YAAY,WAAW;AACrB,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,SAAS;AAAA,cACT,UAAU;AAAA,cACV,UAAU,CAAC;AAAA,YACb;AAAA,UACF;AAAA,UAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,OAAO,MAAM;AACjB,kBAAM,OAAO;AACb,gBAAI,WAAW,MAAM,SAAS,MAAM,SAAS,SAAS,CAAC,KAAK;AAC5D,gBAAI,QAAQ,SAAS,QAAQ,OAAO,IAAI;AACxC,gBAAI,CAAC,SAAS,SAAS,UAAW,OAAM,OAAO;AAAA,qBACtC,CAAC,MAAM,KAAM,OAAM,OAAO;AAEnC,gBAAI,SAAS,eAAe;AAC1B,kBAAI,UAAU,sBAAsB,KAAK,OAAO,QAAQ,CAAC;AACzD,kBAAI,QAAS,EAAC,QAAQ,CAAC,IAAI,aAAa,aAAa,OAAO,MAAM;AAAA,YACpE;AAEA,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,OAAO,WAAW;AACjC,gBAAI,KAAK,MAAM;AACf,gBAAI,CAAC,GAAI,QAAO;AAChB,gBAAI,UAAU,YAAY,KAAK,SAAS;AACxC,gBAAI,GAAG,SAAS,KAAM,QAAO,GAAG,SAAS,UAAU,IAAI;AACvD,mBAAO,GAAG,YAAY,UAAU,IAAI,OAAO;AAAA,UAC7C;AAAA,UAEA,eAAe;AAAA,UAEf,aAAa;AAAA,UACb,mBAAmB;AAAA,UACnB,iBAAiB;AAAA,UACjB,MAAM;AAAA,UACN,eAAe;AAAA,QACjB;AAAA,MACF,CAAC;AAED,MAAAA,YAAW,WAAW,gBAAe,OAAO;AAAA,IAC9C,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}