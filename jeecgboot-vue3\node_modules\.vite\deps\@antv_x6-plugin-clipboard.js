import {
  Basecoat,
  Config,
  Graph,
  Model,
  array_exports
} from "./chunk-S4VGQJFK.js";
import "./chunk-7SCNZKQU.js";
import "./chunk-KD4KUVZ2.js";
import "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/@antv+x6-plugin-clipboard@2.1.6_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-clipboard/es/clipboard.js
var ClipboardImpl = class {
  constructor() {
    this.cells = [];
  }
  copy(cells, graph, options = {}) {
    this.options = Object.assign({}, options);
    const model = Model.isModel(graph) ? graph : graph.model;
    const cloned = model.cloneSubGraph(cells, options);
    this.cells = array_exports.sortBy(Object.keys(cloned).map((key) => cloned[key]), (cell) => cell.isEdge() ? 2 : 1);
    this.serialize(options);
  }
  cut(cells, graph, options = {}) {
    this.copy(cells, graph, options);
    const model = Graph.isGraph(graph) ? graph.model : graph;
    model.batchUpdate("cut", () => {
      cells.forEach((cell) => cell.remove());
    });
  }
  paste(graph, options = {}) {
    const localOptions = Object.assign(Object.assign({}, this.options), options);
    const { offset, edgeProps, nodeProps } = localOptions;
    let dx = 20;
    let dy = 20;
    if (offset) {
      dx = typeof offset === "number" ? offset : offset.dx;
      dy = typeof offset === "number" ? offset : offset.dy;
    }
    this.deserialize(localOptions);
    const cells = this.cells;
    cells.forEach((cell) => {
      cell.model = null;
      cell.removeProp("zIndex");
      if (dx || dy) {
        cell.translate(dx, dy);
      }
      if (nodeProps && cell.isNode()) {
        cell.prop(nodeProps);
      }
      if (edgeProps && cell.isEdge()) {
        cell.prop(edgeProps);
      }
    });
    const model = Graph.isGraph(graph) ? graph.model : graph;
    model.batchUpdate("paste", () => {
      model.addCells(this.cells);
    });
    this.copy(cells, graph, options);
    return cells;
  }
  serialize(options) {
    if (options.useLocalStorage !== false) {
      Storage.save(this.cells);
    }
  }
  deserialize(options) {
    if (options.useLocalStorage) {
      const cells = Storage.fetch();
      if (cells) {
        this.cells = cells;
      }
    }
  }
  isEmpty(options = {}) {
    if (options.useLocalStorage) {
      this.deserialize(options);
    }
    return this.cells.length <= 0;
  }
  clean() {
    this.options = {};
    this.cells = [];
    Storage.clean();
  }
};
var Storage;
(function(Storage2) {
  const LOCAL_STORAGE_KEY = `${Config.prefixCls}.clipboard.cells`;
  function save(cells) {
    if (window.localStorage) {
      const data = cells.map((cell) => cell.toJSON());
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(data));
    }
  }
  Storage2.save = save;
  function fetch() {
    if (window.localStorage) {
      const raw = localStorage.getItem(LOCAL_STORAGE_KEY);
      const cells = raw ? JSON.parse(raw) : [];
      if (cells) {
        return Model.fromJSON(cells);
      }
    }
  }
  Storage2.fetch = fetch;
  function clean() {
    if (window.localStorage) {
      localStorage.removeItem(LOCAL_STORAGE_KEY);
    }
  }
  Storage2.clean = clean;
})(Storage || (Storage = {}));

// node_modules/.pnpm/@antv+x6-plugin-clipboard@2.1.6_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-clipboard/es/api.js
Graph.prototype.isClipboardEnabled = function() {
  const clipboard = this.getPlugin("clipboard");
  if (clipboard) {
    return clipboard.isEnabled();
  }
  return false;
};
Graph.prototype.enableClipboard = function() {
  const clipboard = this.getPlugin("clipboard");
  if (clipboard) {
    clipboard.enable();
  }
  return this;
};
Graph.prototype.disableClipboard = function() {
  const clipboard = this.getPlugin("clipboard");
  if (clipboard) {
    clipboard.disable();
  }
  return this;
};
Graph.prototype.toggleClipboard = function(enabled) {
  const clipboard = this.getPlugin("clipboard");
  if (clipboard) {
    clipboard.toggleEnabled(enabled);
  }
  return this;
};
Graph.prototype.isClipboardEmpty = function(options) {
  const clipboard = this.getPlugin("clipboard");
  if (clipboard) {
    return clipboard.isEmpty(options);
  }
  return true;
};
Graph.prototype.getCellsInClipboard = function() {
  const clipboard = this.getPlugin("clipboard");
  if (clipboard) {
    return clipboard.getCellsInClipboard();
  }
  return [];
};
Graph.prototype.cleanClipboard = function() {
  const clipboard = this.getPlugin("clipboard");
  if (clipboard) {
    clipboard.clean();
  }
  return this;
};
Graph.prototype.copy = function(cells, options) {
  const clipboard = this.getPlugin("clipboard");
  if (clipboard) {
    clipboard.copy(cells, options);
  }
  return this;
};
Graph.prototype.cut = function(cells, options) {
  const clipboard = this.getPlugin("clipboard");
  if (clipboard) {
    clipboard.cut(cells, options);
  }
  return this;
};
Graph.prototype.paste = function(options, graph) {
  const clipboard = this.getPlugin("clipboard");
  if (clipboard) {
    return clipboard.paste(options, graph);
  }
  return [];
};

// node_modules/.pnpm/@antv+x6-plugin-clipboard@2.1.6_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-clipboard/es/index.js
var __decorate = function(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __rest = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
    t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
      if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
        t[p[i]] = s[p[i]];
    }
  return t;
};
var Clipboard = class extends Basecoat {
  get disabled() {
    return this.options.enabled !== true;
  }
  get cells() {
    return this.clipboardImpl.cells;
  }
  constructor(options = {}) {
    super();
    this.name = "clipboard";
    this.options = Object.assign({ enabled: true }, options);
  }
  init(graph) {
    this.graph = graph;
    this.clipboardImpl = new ClipboardImpl();
    this.clipboardImpl.deserialize(this.options);
  }
  // #region api
  isEnabled() {
    return !this.disabled;
  }
  enable() {
    if (this.disabled) {
      this.options.enabled = true;
    }
  }
  disable() {
    if (!this.disabled) {
      this.options.enabled = false;
    }
  }
  toggleEnabled(enabled) {
    if (enabled != null) {
      if (enabled !== this.isEnabled()) {
        if (enabled) {
          this.enable();
        } else {
          this.disable();
        }
      }
    } else if (this.isEnabled()) {
      this.disable();
    } else {
      this.enable();
    }
    return this;
  }
  isEmpty(options = {}) {
    return this.clipboardImpl.isEmpty(options);
  }
  getCellsInClipboard() {
    return this.cells;
  }
  clean(force) {
    if (!this.disabled || force) {
      this.clipboardImpl.clean();
      this.notify("clipboard:changed", { cells: [] });
    }
    return this;
  }
  copy(cells, options = {}) {
    if (!this.disabled) {
      this.clipboardImpl.copy(cells, this.graph, Object.assign(Object.assign({}, this.commonOptions), options));
      this.notify("clipboard:changed", { cells });
    }
    return this;
  }
  cut(cells, options = {}) {
    if (!this.disabled) {
      this.clipboardImpl.cut(cells, this.graph, Object.assign(Object.assign({}, this.commonOptions), options));
      this.notify("clipboard:changed", { cells });
    }
    return this;
  }
  paste(options = {}, graph = this.graph) {
    if (!this.disabled) {
      return this.clipboardImpl.paste(graph, Object.assign(Object.assign({}, this.commonOptions), options));
    }
    return [];
  }
  // #endregion
  get commonOptions() {
    const _a = this.options, { enabled } = _a, others = __rest(_a, ["enabled"]);
    return others;
  }
  notify(name, args) {
    this.trigger(name, args);
    this.graph.trigger(name, args);
  }
  dispose() {
    this.clean(true);
    this.off();
  }
};
__decorate([
  Basecoat.dispose()
], Clipboard.prototype, "dispose", null);
export {
  Clipboard
};
//# sourceMappingURL=@antv_x6-plugin-clipboard.js.map
