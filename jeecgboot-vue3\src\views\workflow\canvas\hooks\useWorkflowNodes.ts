import { ref } from 'vue';
import { Graph } from '@antv/x6';
import type { PortConfig, LaneNodeConfig } from '../types/workflow';

/**
 * 工作流节点管理 Hook
 */
export function useWorkflowNodes() {
  const laneCounter = ref(2);

  /**
   * 获取连接桩配置
   */
  const getPortsConfig = (): PortConfig => {
    return {
      groups: {
        top: {
          position: 'top',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              fill: '#fff',
            },
          },
        },
        right: {
          position: 'right',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              fill: '#fff',
            },
          },
        },
        bottom: {
          position: 'bottom',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              fill: '#fff',
            },
          },
        },
        left: {
          position: 'left',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              fill: '#fff',
            },
          },
        },
      },
      items: [
        { id: '1', group: 'top' },
        { id: '2', group: 'right' },
        { id: '3', group: 'bottom' },
        { id: '4', group: 'left' },
      ],
    };
  };

  /**
   * 注册所有自定义节点
   */
  const registerAllNodes = () => {
    // 注册泳道节点
    Graph.registerNode(
      'lane',
      {
        inherit: 'rect',
        markup: [
          {
            tagName: 'rect',
            selector: 'body',
          },
          {
            tagName: 'rect',
            selector: 'name-rect',
          },
          {
            tagName: 'text',
            selector: 'name-text',
          },
        ],
        attrs: {
          body: {
            fill: '#FFF',
            stroke: '#5F95FF',
            strokeWidth: 1,
          },
          'name-rect': {
            width: '200px', // 使用相对宽度，自动适应泳道宽度
            height: 30,
            fill: '#5F95FF',
            stroke: '#fff',
            strokeWidth: 1,
            x: -1,
          },
          'name-text': {
            ref: 'name-rect',
            refY: 0.5,
            refX: 0.5,
            textAnchor: 'middle',
            fontWeight: 'bold',
            fill: '#fff',
            fontSize: 12,
          },
        },
      },
      true
    );
    // 注册泳道内矩形节点
    Graph.registerNode(
      'lane-rect',
      {
        inherit: 'rect',
        width: 100,
        height: 60,
        ports: getPortsConfig(),
        attrs: {
          body: {
            strokeWidth: 1,
            stroke: '#5F95FF',
            fill: '#EFF4FF',
          },
          text: {
            fontSize: 12,
            fill: '#262626',
          },
        },
      },
      true
    );
    // 注册多边形节点
    Graph.registerNode(
      'lane-polygon',
      {
        inherit: 'polygon',
        width: 80,
        height: 80,
        ports: getPortsConfig(),
        attrs: {
          body: {
            strokeWidth: 1,
            stroke: '#5F95FF',
            fill: '#EFF4FF',
            refPoints: '0,10 10,0 20,10 10,20',
          },
          text: {
            fontSize: 12,
            fill: '#262626',
          },
        },
      },
      true
    );
  };

  /**
   * 注册所有自定义边
   */
  const registerAllEdges = () => {
    // 注册边
    Graph.registerEdge(
      'lane-edge',
      {
        inherit: 'edge',
        attrs: {
          line: {
            stroke: '#A2B1C3',
            strokeWidth: 2,
          },
        },
        label: {
          attrs: {
            label: {
              fill: '#A2B1C3',
              fontSize: 12,
            },
          },
        },
      },
      true
    );
  };

  /**
   * 创建泳道节点
   */
  const createLaneNode = (graph: Graph, config?: Partial<LaneNodeConfig>) => {
    if (!graph) return null;

    // 获取现有泳道
    const lanes = graph.getCells().filter((cell) => cell.shape === 'lane');

    // 计算新泳道的位置
    let x = 60;
    if (lanes.length > 0) {
      const rightmostLane = lanes.reduce((prev, current) => {
        return prev.getBBox().x + prev.getBBox().width > current.getBBox().x + current.getBBox().width ? prev : current;
      });
      x = rightmostLane.getBBox().x + rightmostLane.getBBox().width;
    }

    // 创建新泳道
    laneCounter.value++;
    const defaultConfig: LaneNodeConfig = {
      id: `lane${laneCounter.value}`,
      shape: 'lane',
      position: {
        x,
        y: 60,
      },
      width: 200,
      height: 500,
      label: `部门${laneCounter.value}`,
      attrs: {
        body: {
          fill: '#FFF',
          stroke: '#5F95FF',
          strokeWidth: 1,
        },
        'name-rect': {
          width: 200, // 与泳道宽度保持一致
          height: 30,
          fill: '#5F95FF',
          stroke: '#fff',
          strokeWidth: 1,
          x: -1,
        },
        'name-text': {
          ref: 'name-rect',
          refY: 0.5,
          refX: 0.5,
          textAnchor: 'middle',
          fontWeight: 'bold',
          fill: '#fff',
          fontSize: 12,
        },
      },
    };

    const finalConfig = { ...defaultConfig, ...config };

    const newLane = graph.createNode(finalConfig);

    // 添加新泳道到图表
    graph.addNode(newLane);

    // 调整视图以适应新泳道
    graph.zoomToFit({ padding: 10, maxScale: 1 });

    return newLane;
  };

  /**
   * 创建带有默认属性的节点
   */
  const createNodeWithDefaults = (config: any) => {
    const defaultData = {
      name: config.label || '未命名节点',
      type: config.nodeType || 'userTask',
      handler: '',
      description: '',
      // 注意：委托人和委托机构属性现在存储在泳道节点中，不在普通节点中
    };

    return {
      ...config,
      data: { ...defaultData, ...(config.data || {}) },
    };
  };

  /**
   * 获取基础节点列表
   */
  const getBaseNodes = (graph: Graph) => {
    return [
      graph.createNode(
        createNodeWithDefaults({
          shape: 'lane-rect',
          label: '开始',
          nodeType: 'start',
          attrs: { body: { rx: 30, ry: 30 } },
        })
      ),
      graph.createNode(
        createNodeWithDefaults({
          shape: 'lane-rect',
          label: '用户任务',
          nodeType: 'userTask',
        })
      ),
      graph.createNode(
        createNodeWithDefaults({
          shape: 'lane-rect',
          label: '结束',
          nodeType: 'end',
          attrs: { body: { rx: 30, ry: 30 } },
        })
      ),
    ];
  };

  /**
   * 获取系统节点列表
   */
  const getSystemNodes = (graph: Graph) => {
    return [];
  };

  /**
   * 获取泳道节点列表
   */
  const getLaneNodes = (graph: Graph) => {
    return [];
  };

  return {
    laneCounter,
    registerAllNodes,
    registerAllEdges,
    createLaneNode,
    getBaseNodes,
    getSystemNodes,
    getLaneNodes,
    getPortsConfig,
  };
}
