{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/visualchars/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/visualchars/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const get$2 = toggleState => {\n      const isEnabled = () => {\n        return toggleState.get();\n      };\n      return { isEnabled };\n    };\n\n    const fireVisualChars = (editor, state) => {\n      return editor.dispatch('VisualChars', { state });\n    };\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType$1 = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const eq = t => a => t === a;\n    const isString = isType$1('string');\n    const isObject = isType$1('object');\n    const isNull = eq(null);\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isNumber = isSimpleType('number');\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each$1 = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const filter = (xs, pred) => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n\n    const keys = Object.keys;\n    const each = (obj, f) => {\n      const props = keys(obj);\n      for (let k = 0, len = props.length; k < len; k++) {\n        const i = props[k];\n        const x = obj[i];\n        f(x, i);\n      }\n    };\n\n    const Global = typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const path = (parts, scope) => {\n      let o = scope !== undefined && scope !== null ? scope : Global;\n      for (let i = 0; i < parts.length && o !== undefined && o !== null; ++i) {\n        o = o[parts[i]];\n      }\n      return o;\n    };\n    const resolve = (p, scope) => {\n      const parts = p.split('.');\n      return path(parts, scope);\n    };\n\n    const unsafe = (name, scope) => {\n      return resolve(name, scope);\n    };\n    const getOrDie = (name, scope) => {\n      const actual = unsafe(name, scope);\n      if (actual === undefined || actual === null) {\n        throw new Error(name + ' not available on this browser');\n      }\n      return actual;\n    };\n\n    const getPrototypeOf = Object.getPrototypeOf;\n    const sandHTMLElement = scope => {\n      return getOrDie('HTMLElement', scope);\n    };\n    const isPrototypeOf = x => {\n      const scope = resolve('ownerDocument.defaultView', x);\n      return isObject(x) && (sandHTMLElement(scope).prototype.isPrototypeOf(x) || /^HTML\\w*Element$/.test(getPrototypeOf(x).constructor.name));\n    };\n\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const type = element => element.dom.nodeType;\n    const value = element => element.dom.nodeValue;\n    const isType = t => element => type(element) === t;\n    const isHTMLElement = element => isElement(element) && isPrototypeOf(element.dom);\n    const isElement = isType(ELEMENT);\n    const isText = isType(TEXT);\n\n    const rawSet = (dom, key, value) => {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    const set = (element, key, value) => {\n      rawSet(element.dom, key, value);\n    };\n    const get$1 = (element, key) => {\n      const v = element.dom.getAttribute(key);\n      return v === null ? undefined : v;\n    };\n    const remove$3 = (element, key) => {\n      element.dom.removeAttribute(key);\n    };\n\n    const read = (element, attr) => {\n      const value = get$1(element, attr);\n      return value === undefined || value === '' ? [] : value.split(' ');\n    };\n    const add$2 = (element, attr, id) => {\n      const old = read(element, attr);\n      const nu = old.concat([id]);\n      set(element, attr, nu.join(' '));\n      return true;\n    };\n    const remove$2 = (element, attr, id) => {\n      const nu = filter(read(element, attr), v => v !== id);\n      if (nu.length > 0) {\n        set(element, attr, nu.join(' '));\n      } else {\n        remove$3(element, attr);\n      }\n      return false;\n    };\n\n    const supports = element => element.dom.classList !== undefined;\n    const get = element => read(element, 'class');\n    const add$1 = (element, clazz) => add$2(element, 'class', clazz);\n    const remove$1 = (element, clazz) => remove$2(element, 'class', clazz);\n\n    const add = (element, clazz) => {\n      if (supports(element)) {\n        element.dom.classList.add(clazz);\n      } else {\n        add$1(element, clazz);\n      }\n    };\n    const cleanClass = element => {\n      const classList = supports(element) ? element.dom.classList : get(element);\n      if (classList.length === 0) {\n        remove$3(element, 'class');\n      }\n    };\n    const remove = (element, clazz) => {\n      if (supports(element)) {\n        const classList = element.dom.classList;\n        classList.remove(clazz);\n      } else {\n        remove$1(element, clazz);\n      }\n      cleanClass(element);\n    };\n\n    const fromHtml = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        const message = 'HTML does not have a single root node';\n        console.error(message, html);\n        throw new Error(message);\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n      const doc = scope || document;\n      const node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    const fromText = (text, scope) => {\n      const doc = scope || document;\n      const node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    const fromDom = node => {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    const SugarElement = {\n      fromHtml,\n      fromTag,\n      fromText,\n      fromDom,\n      fromPoint\n    };\n\n    const charMap = {\n      '\\xA0': 'nbsp',\n      '\\xAD': 'shy'\n    };\n    const charMapToRegExp = (charMap, global) => {\n      let regExp = '';\n      each(charMap, (_value, key) => {\n        regExp += key;\n      });\n      return new RegExp('[' + regExp + ']', global ? 'g' : '');\n    };\n    const charMapToSelector = charMap => {\n      let selector = '';\n      each(charMap, value => {\n        if (selector) {\n          selector += ',';\n        }\n        selector += 'span.mce-' + value;\n      });\n      return selector;\n    };\n    const regExp = charMapToRegExp(charMap);\n    const regExpGlobal = charMapToRegExp(charMap, true);\n    const selector = charMapToSelector(charMap);\n    const nbspClass = 'mce-nbsp';\n\n    const getRaw = element => element.dom.contentEditable;\n\n    const wrapCharWithSpan = value => '<span data-mce-bogus=\"1\" class=\"mce-' + charMap[value] + '\">' + value + '</span>';\n\n    const isWrappedNbsp = node => node.nodeName.toLowerCase() === 'span' && node.classList.contains('mce-nbsp-wrap');\n    const isMatch = n => {\n      const value$1 = value(n);\n      return isText(n) && isString(value$1) && regExp.test(value$1);\n    };\n    const isContentEditableFalse = node => isHTMLElement(node) && getRaw(node) === 'false';\n    const isChildEditable = (node, currentState) => {\n      if (isHTMLElement(node) && !isWrappedNbsp(node.dom)) {\n        const value = getRaw(node);\n        if (value === 'true') {\n          return true;\n        } else if (value === 'false') {\n          return false;\n        }\n      }\n      return currentState;\n    };\n    const filterEditableDescendants = (scope, predicate, editable) => {\n      let result = [];\n      const dom = scope.dom;\n      const children = map(dom.childNodes, SugarElement.fromDom);\n      const isEditable = node => isWrappedNbsp(node.dom) || !isContentEditableFalse(node);\n      each$1(children, x => {\n        if (editable && isEditable(x) && predicate(x)) {\n          result = result.concat([x]);\n        }\n        result = result.concat(filterEditableDescendants(x, predicate, isChildEditable(x, editable)));\n      });\n      return result;\n    };\n    const findParentElm = (elm, rootElm) => {\n      while (elm.parentNode) {\n        if (elm.parentNode === rootElm) {\n          return rootElm;\n        }\n        elm = elm.parentNode;\n      }\n      return undefined;\n    };\n    const replaceWithSpans = text => text.replace(regExpGlobal, wrapCharWithSpan);\n\n    const show = (editor, rootElm) => {\n      const dom = editor.dom;\n      const nodeList = filterEditableDescendants(SugarElement.fromDom(rootElm), isMatch, editor.dom.isEditable(rootElm));\n      each$1(nodeList, n => {\n        var _a;\n        const parent = n.dom.parentNode;\n        if (isWrappedNbsp(parent)) {\n          add(SugarElement.fromDom(parent), nbspClass);\n        } else {\n          const withSpans = replaceWithSpans(dom.encode((_a = value(n)) !== null && _a !== void 0 ? _a : ''));\n          const div = dom.create('div', {}, withSpans);\n          let node;\n          while (node = div.lastChild) {\n            dom.insertAfter(node, n.dom);\n          }\n          editor.dom.remove(n.dom);\n        }\n      });\n    };\n    const hide = (editor, rootElm) => {\n      const nodeList = editor.dom.select(selector, rootElm);\n      each$1(nodeList, node => {\n        if (isWrappedNbsp(node)) {\n          remove(SugarElement.fromDom(node), nbspClass);\n        } else {\n          editor.dom.remove(node, true);\n        }\n      });\n    };\n    const toggle = editor => {\n      const body = editor.getBody();\n      const bookmark = editor.selection.getBookmark();\n      let parentNode = findParentElm(editor.selection.getNode(), body);\n      parentNode = parentNode !== undefined ? parentNode : body;\n      hide(editor, parentNode);\n      show(editor, parentNode);\n      editor.selection.moveToBookmark(bookmark);\n    };\n\n    const applyVisualChars = (editor, toggleState) => {\n      fireVisualChars(editor, toggleState.get());\n      const body = editor.getBody();\n      if (toggleState.get() === true) {\n        show(editor, body);\n      } else {\n        hide(editor, body);\n      }\n    };\n    const toggleVisualChars = (editor, toggleState) => {\n      toggleState.set(!toggleState.get());\n      const bookmark = editor.selection.getBookmark();\n      applyVisualChars(editor, toggleState);\n      editor.selection.moveToBookmark(bookmark);\n    };\n\n    const register$2 = (editor, toggleState) => {\n      editor.addCommand('mceVisualChars', () => {\n        toggleVisualChars(editor, toggleState);\n      });\n    };\n\n    const option = name => editor => editor.options.get(name);\n    const register$1 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('visualchars_default_state', {\n        processor: 'boolean',\n        default: false\n      });\n    };\n    const isEnabledByDefault = option('visualchars_default_state');\n\n    const setup$1 = (editor, toggleState) => {\n      editor.on('init', () => {\n        applyVisualChars(editor, toggleState);\n      });\n    };\n\n    const first = (fn, rate) => {\n      let timer = null;\n      const cancel = () => {\n        if (!isNull(timer)) {\n          clearTimeout(timer);\n          timer = null;\n        }\n      };\n      const throttle = (...args) => {\n        if (isNull(timer)) {\n          timer = setTimeout(() => {\n            timer = null;\n            fn.apply(null, args);\n          }, rate);\n        }\n      };\n      return {\n        cancel,\n        throttle\n      };\n    };\n\n    const setup = (editor, toggleState) => {\n      const debouncedToggle = first(() => {\n        toggle(editor);\n      }, 300);\n      editor.on('keydown', e => {\n        if (toggleState.get() === true) {\n          e.keyCode === 13 ? toggle(editor) : debouncedToggle.throttle();\n        }\n      });\n      editor.on('remove', debouncedToggle.cancel);\n    };\n\n    const toggleActiveState = (editor, enabledStated) => api => {\n      api.setActive(enabledStated.get());\n      const editorEventCallback = e => api.setActive(e.state);\n      editor.on('VisualChars', editorEventCallback);\n      return () => editor.off('VisualChars', editorEventCallback);\n    };\n    const register = (editor, toggleState) => {\n      const onAction = () => editor.execCommand('mceVisualChars');\n      editor.ui.registry.addToggleButton('visualchars', {\n        tooltip: 'Show invisible characters',\n        icon: 'visualchars',\n        onAction,\n        onSetup: toggleActiveState(editor, toggleState)\n      });\n      editor.ui.registry.addToggleMenuItem('visualchars', {\n        text: 'Show invisible characters',\n        icon: 'visualchars',\n        onAction,\n        onSetup: toggleActiveState(editor, toggleState)\n      });\n    };\n\n    var Plugin = () => {\n      global.add('visualchars', editor => {\n        register$1(editor);\n        const toggleState = Cell(isEnabledByDefault(editor));\n        register$2(editor, toggleState);\n        register(editor, toggleState);\n        setup(editor, toggleState);\n        setup$1(editor, toggleState);\n        return get$2(toggleState);\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"visualchars\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/visualchars')\n//   ES2015:\n//     import 'tinymce/plugins/visualchars'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,YAAM,OAAO,aAAW;AACtB,YAAIA,SAAQ;AACZ,cAAMC,OAAM,MAAM;AAChB,iBAAOD;AAAA,QACT;AACA,cAAME,OAAM,OAAK;AACf,UAAAF,SAAQ;AAAA,QACV;AACA,eAAO;AAAA,UACL,KAAAC;AAAA,UACA,KAAAC;AAAA,QACF;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAE/D,YAAM,QAAQ,iBAAe;AAC3B,cAAM,YAAY,MAAM;AACtB,iBAAO,YAAY,IAAI;AAAA,QACzB;AACA,eAAO,EAAE,UAAU;AAAA,MACrB;AAEA,YAAM,kBAAkB,CAAC,QAAQ,UAAU;AACzC,eAAO,OAAO,SAAS,eAAe,EAAE,MAAM,CAAC;AAAA,MACjD;AAEA,YAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,YAAI;AACJ,YAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,iBAAO;AAAA,QACT,OAAO;AACL,mBAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,QAC7F;AAAA,MACF;AACA,YAAM,SAAS,OAAK;AAClB,cAAM,IAAI,OAAO;AACjB,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,WAAW,CAAAC,UAAQ,CAAAH,WAAS,OAAOA,MAAK,MAAMG;AACpD,YAAM,eAAe,CAAAA,UAAQ,CAAAH,WAAS,OAAOA,WAAUG;AACvD,YAAM,KAAK,OAAK,OAAK,MAAM;AAC3B,YAAM,WAAW,SAAS,QAAQ;AAClC,YAAM,WAAW,SAAS,QAAQ;AAClC,YAAM,SAAS,GAAG,IAAI;AACtB,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,YAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,YAAM,WAAW,aAAa,QAAQ;AAAA,MAEtC,MAAM,SAAS;AAAA,QACb,YAAY,KAAKH,QAAO;AACtB,eAAK,MAAM;AACX,eAAK,QAAQA;AAAA,QACf;AAAA,QACA,OAAO,KAAKA,QAAO;AACjB,iBAAO,IAAI,SAAS,MAAMA,MAAK;AAAA,QACjC;AAAA,QACA,OAAO,OAAO;AACZ,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,KAAK,QAAQ,QAAQ;AACnB,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,QACA,SAAS;AACP,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,SAAS;AACP,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA,IAAI,QAAQ;AACV,cAAI,KAAK,KAAK;AACZ,mBAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,UACzC,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QACzC;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QAC1C;AAAA,QACA,OAAO,WAAW;AAChB,cAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,MAAM,aAAa;AACjB,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,GAAG,aAAa;AACd,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B;AAAA,QACA,WAAW,OAAO;AAChB,iBAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,QACvC;AAAA,QACA,QAAQ,OAAO;AACb,iBAAO,KAAK,MAAM,OAAO,MAAM;AAAA,QACjC;AAAA,QACA,SAAS,SAAS;AAChB,cAAI,CAAC,KAAK,KAAK;AACb,kBAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,UAC9F,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,OAAO,KAAKA,QAAO;AACjB,iBAAO,cAAcA,MAAK,IAAI,SAAS,KAAKA,MAAK,IAAI,SAAS,KAAK;AAAA,QACrE;AAAA,QACA,YAAY;AACV,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,iBAAiB;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AACR,iBAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,YAAM,MAAM,CAAC,IAAI,MAAM;AACrB,cAAM,MAAM,GAAG;AACf,cAAM,IAAI,IAAI,MAAM,GAAG;AACvB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,QACf;AACA,eAAO;AAAA,MACT;AACA,YAAM,SAAS,CAAC,IAAI,MAAM;AACxB,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,cAAM,IAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,cAAE,KAAK,CAAC;AAAA,UACV;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,YAAM,OAAO,OAAO;AACpB,YAAM,OAAO,CAAC,KAAK,MAAM;AACvB,cAAM,QAAQ,KAAK,GAAG;AACtB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,gBAAM,IAAI,MAAM,CAAC;AACjB,gBAAM,IAAI,IAAI,CAAC;AACf,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AAEA,YAAM,SAAS,OAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAEjF,YAAM,OAAO,CAAC,OAAO,UAAU;AAC7B,YAAI,IAAI,UAAU,UAAa,UAAU,OAAO,QAAQ;AACxD,iBAAS,IAAI,GAAG,IAAI,MAAM,UAAU,MAAM,UAAa,MAAM,MAAM,EAAE,GAAG;AACtE,cAAI,EAAE,MAAM,CAAC,CAAC;AAAA,QAChB;AACA,eAAO;AAAA,MACT;AACA,YAAM,UAAU,CAAC,GAAG,UAAU;AAC5B,cAAM,QAAQ,EAAE,MAAM,GAAG;AACzB,eAAO,KAAK,OAAO,KAAK;AAAA,MAC1B;AAEA,YAAM,SAAS,CAAC,MAAM,UAAU;AAC9B,eAAO,QAAQ,MAAM,KAAK;AAAA,MAC5B;AACA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,SAAS,OAAO,MAAM,KAAK;AACjC,YAAI,WAAW,UAAa,WAAW,MAAM;AAC3C,gBAAM,IAAI,MAAM,OAAO,gCAAgC;AAAA,QACzD;AACA,eAAO;AAAA,MACT;AAEA,YAAM,iBAAiB,OAAO;AAC9B,YAAM,kBAAkB,WAAS;AAC/B,eAAO,SAAS,eAAe,KAAK;AAAA,MACtC;AACA,YAAM,gBAAgB,OAAK;AACzB,cAAM,QAAQ,QAAQ,6BAA6B,CAAC;AACpD,eAAO,SAAS,CAAC,MAAM,gBAAgB,KAAK,EAAE,UAAU,cAAc,CAAC,KAAK,mBAAmB,KAAK,eAAe,CAAC,EAAE,YAAY,IAAI;AAAA,MACxI;AAEA,YAAM,UAAU;AAChB,YAAM,OAAO;AAEb,YAAM,OAAO,aAAW,QAAQ,IAAI;AACpC,YAAM,QAAQ,aAAW,QAAQ,IAAI;AACrC,YAAM,SAAS,OAAK,aAAW,KAAK,OAAO,MAAM;AACjD,YAAM,gBAAgB,aAAW,UAAU,OAAO,KAAK,cAAc,QAAQ,GAAG;AAChF,YAAM,YAAY,OAAO,OAAO;AAChC,YAAM,SAAS,OAAO,IAAI;AAE1B,YAAM,SAAS,CAAC,KAAK,KAAKA,WAAU;AAClC,YAAI,SAASA,MAAK,KAAK,UAAUA,MAAK,KAAK,SAASA,MAAK,GAAG;AAC1D,cAAI,aAAa,KAAKA,SAAQ,EAAE;AAAA,QAClC,OAAO;AACL,kBAAQ,MAAM,uCAAuC,KAAK,aAAaA,QAAO,eAAe,GAAG;AAChG,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QAClD;AAAA,MACF;AACA,YAAM,MAAM,CAAC,SAAS,KAAKA,WAAU;AACnC,eAAO,QAAQ,KAAK,KAAKA,MAAK;AAAA,MAChC;AACA,YAAM,QAAQ,CAAC,SAAS,QAAQ;AAC9B,cAAM,IAAI,QAAQ,IAAI,aAAa,GAAG;AACtC,eAAO,MAAM,OAAO,SAAY;AAAA,MAClC;AACA,YAAM,WAAW,CAAC,SAAS,QAAQ;AACjC,gBAAQ,IAAI,gBAAgB,GAAG;AAAA,MACjC;AAEA,YAAM,OAAO,CAAC,SAAS,SAAS;AAC9B,cAAMA,SAAQ,MAAM,SAAS,IAAI;AACjC,eAAOA,WAAU,UAAaA,WAAU,KAAK,CAAC,IAAIA,OAAM,MAAM,GAAG;AAAA,MACnE;AACA,YAAM,QAAQ,CAAC,SAAS,MAAM,OAAO;AACnC,cAAM,MAAM,KAAK,SAAS,IAAI;AAC9B,cAAM,KAAK,IAAI,OAAO,CAAC,EAAE,CAAC;AAC1B,YAAI,SAAS,MAAM,GAAG,KAAK,GAAG,CAAC;AAC/B,eAAO;AAAA,MACT;AACA,YAAM,WAAW,CAAC,SAAS,MAAM,OAAO;AACtC,cAAM,KAAK,OAAO,KAAK,SAAS,IAAI,GAAG,OAAK,MAAM,EAAE;AACpD,YAAI,GAAG,SAAS,GAAG;AACjB,cAAI,SAAS,MAAM,GAAG,KAAK,GAAG,CAAC;AAAA,QACjC,OAAO;AACL,mBAAS,SAAS,IAAI;AAAA,QACxB;AACA,eAAO;AAAA,MACT;AAEA,YAAM,WAAW,aAAW,QAAQ,IAAI,cAAc;AACtD,YAAM,MAAM,aAAW,KAAK,SAAS,OAAO;AAC5C,YAAM,QAAQ,CAAC,SAAS,UAAU,MAAM,SAAS,SAAS,KAAK;AAC/D,YAAM,WAAW,CAAC,SAAS,UAAU,SAAS,SAAS,SAAS,KAAK;AAErE,YAAM,MAAM,CAAC,SAAS,UAAU;AAC9B,YAAI,SAAS,OAAO,GAAG;AACrB,kBAAQ,IAAI,UAAU,IAAI,KAAK;AAAA,QACjC,OAAO;AACL,gBAAM,SAAS,KAAK;AAAA,QACtB;AAAA,MACF;AACA,YAAM,aAAa,aAAW;AAC5B,cAAM,YAAY,SAAS,OAAO,IAAI,QAAQ,IAAI,YAAY,IAAI,OAAO;AACzE,YAAI,UAAU,WAAW,GAAG;AAC1B,mBAAS,SAAS,OAAO;AAAA,QAC3B;AAAA,MACF;AACA,YAAM,SAAS,CAAC,SAAS,UAAU;AACjC,YAAI,SAAS,OAAO,GAAG;AACrB,gBAAM,YAAY,QAAQ,IAAI;AAC9B,oBAAU,OAAO,KAAK;AAAA,QACxB,OAAO;AACL,mBAAS,SAAS,KAAK;AAAA,QACzB;AACA,mBAAW,OAAO;AAAA,MACpB;AAEA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,IAAI,cAAc,KAAK;AACnC,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,cAAc,KAAK,IAAI,WAAW,SAAS,GAAG;AACrD,gBAAM,UAAU;AAChB,kBAAQ,MAAM,SAAS,IAAI;AAC3B,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB;AACA,eAAO,QAAQ,IAAI,WAAW,CAAC,CAAC;AAAA,MAClC;AACA,YAAM,UAAU,CAAC,KAAK,UAAU;AAC9B,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,cAAc,GAAG;AAClC,eAAO,QAAQ,IAAI;AAAA,MACrB;AACA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,eAAe,IAAI;AACpC,eAAO,QAAQ,IAAI;AAAA,MACrB;AACA,YAAM,UAAU,UAAQ;AACtB,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AACA,eAAO,EAAE,KAAK,KAAK;AAAA,MACrB;AACA,YAAM,YAAY,CAAC,QAAQ,GAAG,MAAM,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,OAAO;AAChG,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,UAAU;AAAA,QACd,KAAQ;AAAA,QACR,KAAQ;AAAA,MACV;AACA,YAAM,kBAAkB,CAACI,UAASC,YAAW;AAC3C,YAAIC,UAAS;AACb,aAAKF,UAAS,CAAC,QAAQ,QAAQ;AAC7B,UAAAE,WAAU;AAAA,QACZ,CAAC;AACD,eAAO,IAAI,OAAO,MAAMA,UAAS,KAAKD,UAAS,MAAM,EAAE;AAAA,MACzD;AACA,YAAM,oBAAoB,CAAAD,aAAW;AACnC,YAAIG,YAAW;AACf,aAAKH,UAAS,CAAAJ,WAAS;AACrB,cAAIO,WAAU;AACZ,YAAAA,aAAY;AAAA,UACd;AACA,UAAAA,aAAY,cAAcP;AAAA,QAC5B,CAAC;AACD,eAAOO;AAAA,MACT;AACA,YAAM,SAAS,gBAAgB,OAAO;AACtC,YAAM,eAAe,gBAAgB,SAAS,IAAI;AAClD,YAAM,WAAW,kBAAkB,OAAO;AAC1C,YAAM,YAAY;AAElB,YAAM,SAAS,aAAW,QAAQ,IAAI;AAEtC,YAAM,mBAAmB,CAAAP,WAAS,yCAAyC,QAAQA,MAAK,IAAI,OAAOA,SAAQ;AAE3G,YAAM,gBAAgB,UAAQ,KAAK,SAAS,YAAY,MAAM,UAAU,KAAK,UAAU,SAAS,eAAe;AAC/G,YAAM,UAAU,OAAK;AACnB,cAAM,UAAU,MAAM,CAAC;AACvB,eAAO,OAAO,CAAC,KAAK,SAAS,OAAO,KAAK,OAAO,KAAK,OAAO;AAAA,MAC9D;AACA,YAAM,yBAAyB,UAAQ,cAAc,IAAI,KAAK,OAAO,IAAI,MAAM;AAC/E,YAAM,kBAAkB,CAAC,MAAM,iBAAiB;AAC9C,YAAI,cAAc,IAAI,KAAK,CAAC,cAAc,KAAK,GAAG,GAAG;AACnD,gBAAMA,SAAQ,OAAO,IAAI;AACzB,cAAIA,WAAU,QAAQ;AACpB,mBAAO;AAAA,UACT,WAAWA,WAAU,SAAS;AAC5B,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,4BAA4B,CAAC,OAAO,WAAW,aAAa;AAChE,YAAI,SAAS,CAAC;AACd,cAAM,MAAM,MAAM;AAClB,cAAM,WAAW,IAAI,IAAI,YAAY,aAAa,OAAO;AACzD,cAAM,aAAa,UAAQ,cAAc,KAAK,GAAG,KAAK,CAAC,uBAAuB,IAAI;AAClF,eAAO,UAAU,OAAK;AACpB,cAAI,YAAY,WAAW,CAAC,KAAK,UAAU,CAAC,GAAG;AAC7C,qBAAS,OAAO,OAAO,CAAC,CAAC,CAAC;AAAA,UAC5B;AACA,mBAAS,OAAO,OAAO,0BAA0B,GAAG,WAAW,gBAAgB,GAAG,QAAQ,CAAC,CAAC;AAAA,QAC9F,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,gBAAgB,CAAC,KAAK,YAAY;AACtC,eAAO,IAAI,YAAY;AACrB,cAAI,IAAI,eAAe,SAAS;AAC9B,mBAAO;AAAA,UACT;AACA,gBAAM,IAAI;AAAA,QACZ;AACA,eAAO;AAAA,MACT;AACA,YAAM,mBAAmB,UAAQ,KAAK,QAAQ,cAAc,gBAAgB;AAE5E,YAAM,OAAO,CAAC,QAAQ,YAAY;AAChC,cAAM,MAAM,OAAO;AACnB,cAAM,WAAW,0BAA0B,aAAa,QAAQ,OAAO,GAAG,SAAS,OAAO,IAAI,WAAW,OAAO,CAAC;AACjH,eAAO,UAAU,OAAK;AACpB,cAAI;AACJ,gBAAM,SAAS,EAAE,IAAI;AACrB,cAAI,cAAc,MAAM,GAAG;AACzB,gBAAI,aAAa,QAAQ,MAAM,GAAG,SAAS;AAAA,UAC7C,OAAO;AACL,kBAAM,YAAY,iBAAiB,IAAI,QAAQ,KAAK,MAAM,CAAC,OAAO,QAAQ,OAAO,SAAS,KAAK,EAAE,CAAC;AAClG,kBAAM,MAAM,IAAI,OAAO,OAAO,CAAC,GAAG,SAAS;AAC3C,gBAAI;AACJ,mBAAO,OAAO,IAAI,WAAW;AAC3B,kBAAI,YAAY,MAAM,EAAE,GAAG;AAAA,YAC7B;AACA,mBAAO,IAAI,OAAO,EAAE,GAAG;AAAA,UACzB;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,OAAO,CAAC,QAAQ,YAAY;AAChC,cAAM,WAAW,OAAO,IAAI,OAAO,UAAU,OAAO;AACpD,eAAO,UAAU,UAAQ;AACvB,cAAI,cAAc,IAAI,GAAG;AACvB,mBAAO,aAAa,QAAQ,IAAI,GAAG,SAAS;AAAA,UAC9C,OAAO;AACL,mBAAO,IAAI,OAAO,MAAM,IAAI;AAAA,UAC9B;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,SAAS,YAAU;AACvB,cAAM,OAAO,OAAO,QAAQ;AAC5B,cAAM,WAAW,OAAO,UAAU,YAAY;AAC9C,YAAI,aAAa,cAAc,OAAO,UAAU,QAAQ,GAAG,IAAI;AAC/D,qBAAa,eAAe,SAAY,aAAa;AACrD,aAAK,QAAQ,UAAU;AACvB,aAAK,QAAQ,UAAU;AACvB,eAAO,UAAU,eAAe,QAAQ;AAAA,MAC1C;AAEA,YAAM,mBAAmB,CAAC,QAAQ,gBAAgB;AAChD,wBAAgB,QAAQ,YAAY,IAAI,CAAC;AACzC,cAAM,OAAO,OAAO,QAAQ;AAC5B,YAAI,YAAY,IAAI,MAAM,MAAM;AAC9B,eAAK,QAAQ,IAAI;AAAA,QACnB,OAAO;AACL,eAAK,QAAQ,IAAI;AAAA,QACnB;AAAA,MACF;AACA,YAAM,oBAAoB,CAAC,QAAQ,gBAAgB;AACjD,oBAAY,IAAI,CAAC,YAAY,IAAI,CAAC;AAClC,cAAM,WAAW,OAAO,UAAU,YAAY;AAC9C,yBAAiB,QAAQ,WAAW;AACpC,eAAO,UAAU,eAAe,QAAQ;AAAA,MAC1C;AAEA,YAAM,aAAa,CAAC,QAAQ,gBAAgB;AAC1C,eAAO,WAAW,kBAAkB,MAAM;AACxC,4BAAkB,QAAQ,WAAW;AAAA,QACvC,CAAC;AAAA,MACH;AAEA,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,aAAa,YAAU;AAC3B,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,6BAA6B;AAAA,UAC1C,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,qBAAqB,OAAO,2BAA2B;AAE7D,YAAM,UAAU,CAAC,QAAQ,gBAAgB;AACvC,eAAO,GAAG,QAAQ,MAAM;AACtB,2BAAiB,QAAQ,WAAW;AAAA,QACtC,CAAC;AAAA,MACH;AAEA,YAAM,QAAQ,CAAC,IAAI,SAAS;AAC1B,YAAI,QAAQ;AACZ,cAAM,SAAS,MAAM;AACnB,cAAI,CAAC,OAAO,KAAK,GAAG;AAClB,yBAAa,KAAK;AAClB,oBAAQ;AAAA,UACV;AAAA,QACF;AACA,cAAM,WAAW,IAAI,SAAS;AAC5B,cAAI,OAAO,KAAK,GAAG;AACjB,oBAAQ,WAAW,MAAM;AACvB,sBAAQ;AACR,iBAAG,MAAM,MAAM,IAAI;AAAA,YACrB,GAAG,IAAI;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,QAAQ,CAAC,QAAQ,gBAAgB;AACrC,cAAM,kBAAkB,MAAM,MAAM;AAClC,iBAAO,MAAM;AAAA,QACf,GAAG,GAAG;AACN,eAAO,GAAG,WAAW,OAAK;AACxB,cAAI,YAAY,IAAI,MAAM,MAAM;AAC9B,cAAE,YAAY,KAAK,OAAO,MAAM,IAAI,gBAAgB,SAAS;AAAA,UAC/D;AAAA,QACF,CAAC;AACD,eAAO,GAAG,UAAU,gBAAgB,MAAM;AAAA,MAC5C;AAEA,YAAM,oBAAoB,CAAC,QAAQ,kBAAkB,SAAO;AAC1D,YAAI,UAAU,cAAc,IAAI,CAAC;AACjC,cAAM,sBAAsB,OAAK,IAAI,UAAU,EAAE,KAAK;AACtD,eAAO,GAAG,eAAe,mBAAmB;AAC5C,eAAO,MAAM,OAAO,IAAI,eAAe,mBAAmB;AAAA,MAC5D;AACA,YAAM,WAAW,CAAC,QAAQ,gBAAgB;AACxC,cAAM,WAAW,MAAM,OAAO,YAAY,gBAAgB;AAC1D,eAAO,GAAG,SAAS,gBAAgB,eAAe;AAAA,UAChD,SAAS;AAAA,UACT,MAAM;AAAA,UACN;AAAA,UACA,SAAS,kBAAkB,QAAQ,WAAW;AAAA,QAChD,CAAC;AACD,eAAO,GAAG,SAAS,kBAAkB,eAAe;AAAA,UAClD,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,SAAS,kBAAkB,QAAQ,WAAW;AAAA,QAChD,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,eAAO,IAAI,eAAe,YAAU;AAClC,qBAAW,MAAM;AACjB,gBAAM,cAAc,KAAK,mBAAmB,MAAM,CAAC;AACnD,qBAAW,QAAQ,WAAW;AAC9B,mBAAS,QAAQ,WAAW;AAC5B,gBAAM,QAAQ,WAAW;AACzB,kBAAQ,QAAQ,WAAW;AAC3B,iBAAO,MAAM,WAAW;AAAA,QAC1B,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;ACziBH;", "names": ["value", "get", "set", "type", "charMap", "global", "regExp", "selector"]}