{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/insertdatetime/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/insertdatetime/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const option = name => editor => editor.options.get(name);\n    const register$2 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('insertdatetime_dateformat', {\n        processor: 'string',\n        default: editor.translate('%Y-%m-%d')\n      });\n      registerOption('insertdatetime_timeformat', {\n        processor: 'string',\n        default: editor.translate('%H:%M:%S')\n      });\n      registerOption('insertdatetime_formats', {\n        processor: 'string[]',\n        default: [\n          '%H:%M:%S',\n          '%Y-%m-%d',\n          '%I:%M:%S %p',\n          '%D'\n        ]\n      });\n      registerOption('insertdatetime_element', {\n        processor: 'boolean',\n        default: false\n      });\n    };\n    const getDateFormat = option('insertdatetime_dateformat');\n    const getTimeFormat = option('insertdatetime_timeformat');\n    const getFormats = option('insertdatetime_formats');\n    const shouldInsertTimeElement = option('insertdatetime_element');\n    const getDefaultDateTime = editor => {\n      const formats = getFormats(editor);\n      return formats.length > 0 ? formats[0] : getTimeFormat(editor);\n    };\n\n    const daysShort = 'Sun Mon Tue Wed Thu Fri Sat Sun'.split(' ');\n    const daysLong = 'Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday'.split(' ');\n    const monthsShort = 'Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec'.split(' ');\n    const monthsLong = 'January February March April May June July August September October November December'.split(' ');\n    const addZeros = (value, len) => {\n      value = '' + value;\n      if (value.length < len) {\n        for (let i = 0; i < len - value.length; i++) {\n          value = '0' + value;\n        }\n      }\n      return value;\n    };\n    const getDateTime = (editor, fmt, date = new Date()) => {\n      fmt = fmt.replace('%D', '%m/%d/%Y');\n      fmt = fmt.replace('%r', '%I:%M:%S %p');\n      fmt = fmt.replace('%Y', '' + date.getFullYear());\n      fmt = fmt.replace('%y', '' + date.getYear());\n      fmt = fmt.replace('%m', addZeros(date.getMonth() + 1, 2));\n      fmt = fmt.replace('%d', addZeros(date.getDate(), 2));\n      fmt = fmt.replace('%H', '' + addZeros(date.getHours(), 2));\n      fmt = fmt.replace('%M', '' + addZeros(date.getMinutes(), 2));\n      fmt = fmt.replace('%S', '' + addZeros(date.getSeconds(), 2));\n      fmt = fmt.replace('%I', '' + ((date.getHours() + 11) % 12 + 1));\n      fmt = fmt.replace('%p', '' + (date.getHours() < 12 ? 'AM' : 'PM'));\n      fmt = fmt.replace('%B', '' + editor.translate(monthsLong[date.getMonth()]));\n      fmt = fmt.replace('%b', '' + editor.translate(monthsShort[date.getMonth()]));\n      fmt = fmt.replace('%A', '' + editor.translate(daysLong[date.getDay()]));\n      fmt = fmt.replace('%a', '' + editor.translate(daysShort[date.getDay()]));\n      fmt = fmt.replace('%%', '%');\n      return fmt;\n    };\n    const updateElement = (editor, timeElm, computerTime, userTime) => {\n      const newTimeElm = editor.dom.create('time', { datetime: computerTime }, userTime);\n      editor.dom.replace(newTimeElm, timeElm);\n      editor.selection.select(newTimeElm, true);\n      editor.selection.collapse(false);\n    };\n    const insertDateTime = (editor, format) => {\n      if (shouldInsertTimeElement(editor)) {\n        const userTime = getDateTime(editor, format);\n        let computerTime;\n        if (/%[HMSIp]/.test(format)) {\n          computerTime = getDateTime(editor, '%Y-%m-%dT%H:%M');\n        } else {\n          computerTime = getDateTime(editor, '%Y-%m-%d');\n        }\n        const timeElm = editor.dom.getParent(editor.selection.getStart(), 'time');\n        if (timeElm) {\n          updateElement(editor, timeElm, computerTime, userTime);\n        } else {\n          editor.insertContent('<time datetime=\"' + computerTime + '\">' + userTime + '</time>');\n        }\n      } else {\n        editor.insertContent(getDateTime(editor, format));\n      }\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('mceInsertDate', (_ui, value) => {\n        insertDateTime(editor, value !== null && value !== void 0 ? value : getDateFormat(editor));\n      });\n      editor.addCommand('mceInsertTime', (_ui, value) => {\n        insertDateTime(editor, value !== null && value !== void 0 ? value : getTimeFormat(editor));\n      });\n    };\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const onSetupEditable = editor => api => {\n      const nodeChanged = () => {\n        api.setEnabled(editor.selection.isEditable());\n      };\n      editor.on('NodeChange', nodeChanged);\n      nodeChanged();\n      return () => {\n        editor.off('NodeChange', nodeChanged);\n      };\n    };\n    const register = editor => {\n      const formats = getFormats(editor);\n      const defaultFormat = Cell(getDefaultDateTime(editor));\n      const insertDateTime = format => editor.execCommand('mceInsertDate', false, format);\n      editor.ui.registry.addSplitButton('insertdatetime', {\n        icon: 'insert-time',\n        tooltip: 'Insert date/time',\n        select: value => value === defaultFormat.get(),\n        fetch: done => {\n          done(global.map(formats, format => ({\n            type: 'choiceitem',\n            text: getDateTime(editor, format),\n            value: format\n          })));\n        },\n        onAction: _api => {\n          insertDateTime(defaultFormat.get());\n        },\n        onItemAction: (_api, value) => {\n          defaultFormat.set(value);\n          insertDateTime(value);\n        },\n        onSetup: onSetupEditable(editor)\n      });\n      const makeMenuItemHandler = format => () => {\n        defaultFormat.set(format);\n        insertDateTime(format);\n      };\n      editor.ui.registry.addNestedMenuItem('insertdatetime', {\n        icon: 'insert-time',\n        text: 'Date/time',\n        getSubmenuItems: () => global.map(formats, format => ({\n          type: 'menuitem',\n          text: getDateTime(editor, format),\n          onAction: makeMenuItemHandler(format)\n        })),\n        onSetup: onSetupEditable(editor)\n      });\n    };\n\n    var Plugin = () => {\n      global$1.add('insertdatetime', editor => {\n        register$2(editor);\n        register$1(editor);\n        register(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"insertdatetime\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/insertdatetime')\n//   ES2015:\n//     import 'tinymce/plugins/insertdatetime'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,aAAa,YAAU;AAC3B,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,6BAA6B;AAAA,UAC1C,WAAW;AAAA,UACX,SAAS,OAAO,UAAU,UAAU;AAAA,QACtC,CAAC;AACD,uBAAe,6BAA6B;AAAA,UAC1C,WAAW;AAAA,UACX,SAAS,OAAO,UAAU,UAAU;AAAA,QACtC,CAAC;AACD,uBAAe,0BAA0B;AAAA,UACvC,WAAW;AAAA,UACX,SAAS;AAAA,YACP;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AACD,uBAAe,0BAA0B;AAAA,UACvC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB,OAAO,2BAA2B;AACxD,YAAM,gBAAgB,OAAO,2BAA2B;AACxD,YAAM,aAAa,OAAO,wBAAwB;AAClD,YAAM,0BAA0B,OAAO,wBAAwB;AAC/D,YAAM,qBAAqB,YAAU;AACnC,cAAM,UAAU,WAAW,MAAM;AACjC,eAAO,QAAQ,SAAS,IAAI,QAAQ,CAAC,IAAI,cAAc,MAAM;AAAA,MAC/D;AAEA,YAAM,YAAY,kCAAkC,MAAM,GAAG;AAC7D,YAAM,WAAW,kEAAkE,MAAM,GAAG;AAC5F,YAAM,cAAc,kDAAkD,MAAM,GAAG;AAC/E,YAAM,aAAa,wFAAwF,MAAM,GAAG;AACpH,YAAM,WAAW,CAAC,OAAO,QAAQ;AAC/B,gBAAQ,KAAK;AACb,YAAI,MAAM,SAAS,KAAK;AACtB,mBAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,oBAAQ,MAAM;AAAA,UAChB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,cAAc,CAAC,QAAQ,KAAK,OAAO,oBAAI,KAAK,MAAM;AACtD,cAAM,IAAI,QAAQ,MAAM,UAAU;AAClC,cAAM,IAAI,QAAQ,MAAM,aAAa;AACrC,cAAM,IAAI,QAAQ,MAAM,KAAK,KAAK,YAAY,CAAC;AAC/C,cAAM,IAAI,QAAQ,MAAM,KAAK,KAAK,QAAQ,CAAC;AAC3C,cAAM,IAAI,QAAQ,MAAM,SAAS,KAAK,SAAS,IAAI,GAAG,CAAC,CAAC;AACxD,cAAM,IAAI,QAAQ,MAAM,SAAS,KAAK,QAAQ,GAAG,CAAC,CAAC;AACnD,cAAM,IAAI,QAAQ,MAAM,KAAK,SAAS,KAAK,SAAS,GAAG,CAAC,CAAC;AACzD,cAAM,IAAI,QAAQ,MAAM,KAAK,SAAS,KAAK,WAAW,GAAG,CAAC,CAAC;AAC3D,cAAM,IAAI,QAAQ,MAAM,KAAK,SAAS,KAAK,WAAW,GAAG,CAAC,CAAC;AAC3D,cAAM,IAAI,QAAQ,MAAM,OAAO,KAAK,SAAS,IAAI,MAAM,KAAK,EAAE;AAC9D,cAAM,IAAI,QAAQ,MAAY,KAAK,SAAS,IAAI,KAAK,OAAO,IAAK;AACjE,cAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,UAAU,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC;AAC1E,cAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,UAAU,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC;AAC3E,cAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,UAAU,SAAS,KAAK,OAAO,CAAC,CAAC,CAAC;AACtE,cAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,UAAU,UAAU,KAAK,OAAO,CAAC,CAAC,CAAC;AACvE,cAAM,IAAI,QAAQ,MAAM,GAAG;AAC3B,eAAO;AAAA,MACT;AACA,YAAM,gBAAgB,CAAC,QAAQ,SAAS,cAAc,aAAa;AACjE,cAAM,aAAa,OAAO,IAAI,OAAO,QAAQ,EAAE,UAAU,aAAa,GAAG,QAAQ;AACjF,eAAO,IAAI,QAAQ,YAAY,OAAO;AACtC,eAAO,UAAU,OAAO,YAAY,IAAI;AACxC,eAAO,UAAU,SAAS,KAAK;AAAA,MACjC;AACA,YAAM,iBAAiB,CAAC,QAAQ,WAAW;AACzC,YAAI,wBAAwB,MAAM,GAAG;AACnC,gBAAM,WAAW,YAAY,QAAQ,MAAM;AAC3C,cAAI;AACJ,cAAI,WAAW,KAAK,MAAM,GAAG;AAC3B,2BAAe,YAAY,QAAQ,gBAAgB;AAAA,UACrD,OAAO;AACL,2BAAe,YAAY,QAAQ,UAAU;AAAA,UAC/C;AACA,gBAAM,UAAU,OAAO,IAAI,UAAU,OAAO,UAAU,SAAS,GAAG,MAAM;AACxE,cAAI,SAAS;AACX,0BAAc,QAAQ,SAAS,cAAc,QAAQ;AAAA,UACvD,OAAO;AACL,mBAAO,cAAc,qBAAqB,eAAe,OAAO,WAAW,SAAS;AAAA,UACtF;AAAA,QACF,OAAO;AACL,iBAAO,cAAc,YAAY,QAAQ,MAAM,CAAC;AAAA,QAClD;AAAA,MACF;AAEA,YAAM,aAAa,YAAU;AAC3B,eAAO,WAAW,iBAAiB,CAAC,KAAK,UAAU;AACjD,yBAAe,QAAQ,UAAU,QAAQ,UAAU,SAAS,QAAQ,cAAc,MAAM,CAAC;AAAA,QAC3F,CAAC;AACD,eAAO,WAAW,iBAAiB,CAAC,KAAK,UAAU;AACjD,yBAAe,QAAQ,UAAU,QAAQ,UAAU,SAAS,QAAQ,cAAc,MAAM,CAAC;AAAA,QAC3F,CAAC;AAAA,MACH;AAEA,YAAM,OAAO,aAAW;AACtB,YAAI,QAAQ;AACZ,cAAM,MAAM,MAAM;AAChB,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,OAAK;AACf,kBAAQ;AAAA,QACV;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE5D,YAAM,kBAAkB,YAAU,SAAO;AACvC,cAAM,cAAc,MAAM;AACxB,cAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,QAC9C;AACA,eAAO,GAAG,cAAc,WAAW;AACnC,oBAAY;AACZ,eAAO,MAAM;AACX,iBAAO,IAAI,cAAc,WAAW;AAAA,QACtC;AAAA,MACF;AACA,YAAM,WAAW,YAAU;AACzB,cAAM,UAAU,WAAW,MAAM;AACjC,cAAM,gBAAgB,KAAK,mBAAmB,MAAM,CAAC;AACrD,cAAMA,kBAAiB,YAAU,OAAO,YAAY,iBAAiB,OAAO,MAAM;AAClF,eAAO,GAAG,SAAS,eAAe,kBAAkB;AAAA,UAClD,MAAM;AAAA,UACN,SAAS;AAAA,UACT,QAAQ,WAAS,UAAU,cAAc,IAAI;AAAA,UAC7C,OAAO,UAAQ;AACb,iBAAK,OAAO,IAAI,SAAS,aAAW;AAAA,cAClC,MAAM;AAAA,cACN,MAAM,YAAY,QAAQ,MAAM;AAAA,cAChC,OAAO;AAAA,YACT,EAAE,CAAC;AAAA,UACL;AAAA,UACA,UAAU,UAAQ;AAChB,YAAAA,gBAAe,cAAc,IAAI,CAAC;AAAA,UACpC;AAAA,UACA,cAAc,CAAC,MAAM,UAAU;AAC7B,0BAAc,IAAI,KAAK;AACvB,YAAAA,gBAAe,KAAK;AAAA,UACtB;AAAA,UACA,SAAS,gBAAgB,MAAM;AAAA,QACjC,CAAC;AACD,cAAM,sBAAsB,YAAU,MAAM;AAC1C,wBAAc,IAAI,MAAM;AACxB,UAAAA,gBAAe,MAAM;AAAA,QACvB;AACA,eAAO,GAAG,SAAS,kBAAkB,kBAAkB;AAAA,UACrD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,iBAAiB,MAAM,OAAO,IAAI,SAAS,aAAW;AAAA,YACpD,MAAM;AAAA,YACN,MAAM,YAAY,QAAQ,MAAM;AAAA,YAChC,UAAU,oBAAoB,MAAM;AAAA,UACtC,EAAE;AAAA,UACF,SAAS,gBAAgB,MAAM;AAAA,QACjC,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,kBAAkB,YAAU;AACvC,qBAAW,MAAM;AACjB,qBAAW,MAAM;AACjB,mBAAS,MAAM;AAAA,QACjB,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;ACpLH;", "names": ["insertDateTime"]}