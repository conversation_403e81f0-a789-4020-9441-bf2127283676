{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/advlist/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/advlist/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const applyListFormat = (editor, listName, styleValue) => {\n      const cmd = listName === 'UL' ? 'InsertUnorderedList' : 'InsertOrderedList';\n      editor.execCommand(cmd, false, styleValue === false ? null : { 'list-style-type': styleValue });\n    };\n\n    const register$2 = editor => {\n      editor.addCommand('ApplyUnorderedListStyle', (ui, value) => {\n        applyListFormat(editor, 'UL', value['list-style-type']);\n      });\n      editor.addCommand('ApplyOrderedListStyle', (ui, value) => {\n        applyListFormat(editor, 'OL', value['list-style-type']);\n      });\n    };\n\n    const option = name => editor => editor.options.get(name);\n    const register$1 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('advlist_number_styles', {\n        processor: 'string[]',\n        default: 'default,lower-alpha,lower-greek,lower-roman,upper-alpha,upper-roman'.split(',')\n      });\n      registerOption('advlist_bullet_styles', {\n        processor: 'string[]',\n        default: 'default,circle,square'.split(',')\n      });\n    };\n    const getNumberStyles = option('advlist_number_styles');\n    const getBulletStyles = option('advlist_bullet_styles');\n\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const findUntil = (xs, pred, until) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          return Optional.some(x);\n        } else if (until(x, i)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n\n    const isCustomList = list => /\\btox\\-/.test(list.className);\n    const isChildOfBody = (editor, elm) => {\n      return editor.dom.isChildOf(elm, editor.getBody());\n    };\n    const matchNodeNames = regex => node => isNonNullable(node) && regex.test(node.nodeName);\n    const isListNode = matchNodeNames(/^(OL|UL|DL)$/);\n    const isTableCellNode = matchNodeNames(/^(TH|TD)$/);\n    const inList = (editor, parents, nodeName) => findUntil(parents, parent => isListNode(parent) && !isCustomList(parent), isTableCellNode).exists(list => list.nodeName === nodeName && isChildOfBody(editor, list));\n    const getSelectedStyleType = editor => {\n      const listElm = editor.dom.getParent(editor.selection.getNode(), 'ol,ul');\n      const style = editor.dom.getStyle(listElm, 'listStyleType');\n      return Optional.from(style);\n    };\n    const isWithinNonEditable = (editor, element) => element !== null && !editor.dom.isEditable(element);\n    const isWithinNonEditableList = (editor, element) => {\n      const parentList = editor.dom.getParent(element, 'ol,ul,dl');\n      return isWithinNonEditable(editor, parentList) && editor.selection.isEditable();\n    };\n    const setNodeChangeHandler = (editor, nodeChangeHandler) => {\n      const initialNode = editor.selection.getNode();\n      nodeChangeHandler({\n        parents: editor.dom.getParents(initialNode),\n        element: initialNode\n      });\n      editor.on('NodeChange', nodeChangeHandler);\n      return () => editor.off('NodeChange', nodeChangeHandler);\n    };\n\n    const styleValueToText = styleValue => {\n      return styleValue.replace(/\\-/g, ' ').replace(/\\b\\w/g, chr => {\n        return chr.toUpperCase();\n      });\n    };\n    const normalizeStyleValue = styleValue => isNullable(styleValue) || styleValue === 'default' ? '' : styleValue;\n    const makeSetupHandler = (editor, nodeName) => api => {\n      const updateButtonState = (editor, parents) => {\n        const element = editor.selection.getStart(true);\n        api.setActive(inList(editor, parents, nodeName));\n        api.setEnabled(!isWithinNonEditableList(editor, element) && editor.selection.isEditable());\n      };\n      const nodeChangeHandler = e => updateButtonState(editor, e.parents);\n      return setNodeChangeHandler(editor, nodeChangeHandler);\n    };\n    const addSplitButton = (editor, id, tooltip, cmd, nodeName, styles) => {\n      editor.ui.registry.addSplitButton(id, {\n        tooltip,\n        icon: nodeName === 'OL' ? 'ordered-list' : 'unordered-list',\n        presets: 'listpreview',\n        columns: 3,\n        fetch: callback => {\n          const items = global.map(styles, styleValue => {\n            const iconStyle = nodeName === 'OL' ? 'num' : 'bull';\n            const iconName = styleValue === 'disc' || styleValue === 'decimal' ? 'default' : styleValue;\n            const itemValue = normalizeStyleValue(styleValue);\n            const displayText = styleValueToText(styleValue);\n            return {\n              type: 'choiceitem',\n              value: itemValue,\n              icon: 'list-' + iconStyle + '-' + iconName,\n              text: displayText\n            };\n          });\n          callback(items);\n        },\n        onAction: () => editor.execCommand(cmd),\n        onItemAction: (_splitButtonApi, value) => {\n          applyListFormat(editor, nodeName, value);\n        },\n        select: value => {\n          const listStyleType = getSelectedStyleType(editor);\n          return listStyleType.map(listStyle => value === listStyle).getOr(false);\n        },\n        onSetup: makeSetupHandler(editor, nodeName)\n      });\n    };\n    const addButton = (editor, id, tooltip, cmd, nodeName, styleValue) => {\n      editor.ui.registry.addToggleButton(id, {\n        active: false,\n        tooltip,\n        icon: nodeName === 'OL' ? 'ordered-list' : 'unordered-list',\n        onSetup: makeSetupHandler(editor, nodeName),\n        onAction: () => editor.queryCommandState(cmd) || styleValue === '' ? editor.execCommand(cmd) : applyListFormat(editor, nodeName, styleValue)\n      });\n    };\n    const addControl = (editor, id, tooltip, cmd, nodeName, styles) => {\n      if (styles.length > 1) {\n        addSplitButton(editor, id, tooltip, cmd, nodeName, styles);\n      } else {\n        addButton(editor, id, tooltip, cmd, nodeName, normalizeStyleValue(styles[0]));\n      }\n    };\n    const register = editor => {\n      addControl(editor, 'numlist', 'Numbered list', 'InsertOrderedList', 'OL', getNumberStyles(editor));\n      addControl(editor, 'bullist', 'Bullet list', 'InsertUnorderedList', 'UL', getBulletStyles(editor));\n    };\n\n    var Plugin = () => {\n      global$1.add('advlist', editor => {\n        if (editor.hasPlugin('lists')) {\n          register$1(editor);\n          register(editor);\n          register$2(editor);\n        } else {\n          console.error('Please use the Lists plugin together with the Advanced List plugin.');\n        }\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"advlist\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/advlist')\n//   ES2015:\n//     import 'tinymce/plugins/advlist'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,kBAAkB,CAAC,QAAQ,UAAU,eAAe;AACxD,cAAM,MAAM,aAAa,OAAO,wBAAwB;AACxD,eAAO,YAAY,KAAK,OAAO,eAAe,QAAQ,OAAO,EAAE,mBAAmB,WAAW,CAAC;AAAA,MAChG;AAEA,YAAM,aAAa,YAAU;AAC3B,eAAO,WAAW,2BAA2B,CAAC,IAAI,UAAU;AAC1D,0BAAgB,QAAQ,MAAM,MAAM,iBAAiB,CAAC;AAAA,QACxD,CAAC;AACD,eAAO,WAAW,yBAAyB,CAAC,IAAI,UAAU;AACxD,0BAAgB,QAAQ,MAAM,MAAM,iBAAiB,CAAC;AAAA,QACxD,CAAC;AAAA,MACH;AAEA,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,aAAa,YAAU;AAC3B,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,yBAAyB;AAAA,UACtC,WAAW;AAAA,UACX,SAAS,sEAAsE,MAAM,GAAG;AAAA,QAC1F,CAAC;AACD,uBAAe,yBAAyB;AAAA,UACtC,WAAW;AAAA,UACX,SAAS,wBAAwB,MAAM,GAAG;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,YAAM,kBAAkB,OAAO,uBAAuB;AACtD,YAAM,kBAAkB,OAAO,uBAAuB;AAEtD,YAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,YAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AAExC,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAAA,MAE5D,MAAM,SAAS;AAAA,QACb,YAAY,KAAK,OAAO;AACtB,eAAK,MAAM;AACX,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,IAAI,SAAS,MAAM,KAAK;AAAA,QACjC;AAAA,QACA,OAAO,OAAO;AACZ,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,KAAK,QAAQ,QAAQ;AACnB,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,QACA,SAAS;AACP,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,SAAS;AACP,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA,IAAI,QAAQ;AACV,cAAI,KAAK,KAAK;AACZ,mBAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,UACzC,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QACzC;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QAC1C;AAAA,QACA,OAAO,WAAW;AAChB,cAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,MAAM,aAAa;AACjB,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,GAAG,aAAa;AACd,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B;AAAA,QACA,WAAW,OAAO;AAChB,iBAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,QACvC;AAAA,QACA,QAAQ,OAAO;AACb,iBAAO,KAAK,MAAM,OAAO,MAAM;AAAA,QACjC;AAAA,QACA,SAAS,SAAS;AAChB,cAAI,CAAC,KAAK,KAAK;AACb,kBAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,UAC9F,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,cAAc,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,QACrE;AAAA,QACA,YAAY;AACV,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,iBAAiB;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AACR,iBAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,YAAM,YAAY,CAAC,IAAI,MAAM,UAAU;AACrC,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,mBAAO,SAAS,KAAK,CAAC;AAAA,UACxB,WAAW,MAAM,GAAG,CAAC,GAAG;AACtB;AAAA,UACF;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AAEA,YAAM,eAAe,UAAQ,UAAU,KAAK,KAAK,SAAS;AAC1D,YAAM,gBAAgB,CAAC,QAAQ,QAAQ;AACrC,eAAO,OAAO,IAAI,UAAU,KAAK,OAAO,QAAQ,CAAC;AAAA,MACnD;AACA,YAAM,iBAAiB,WAAS,UAAQ,cAAc,IAAI,KAAK,MAAM,KAAK,KAAK,QAAQ;AACvF,YAAM,aAAa,eAAe,cAAc;AAChD,YAAM,kBAAkB,eAAe,WAAW;AAClD,YAAM,SAAS,CAAC,QAAQ,SAAS,aAAa,UAAU,SAAS,YAAU,WAAW,MAAM,KAAK,CAAC,aAAa,MAAM,GAAG,eAAe,EAAE,OAAO,UAAQ,KAAK,aAAa,YAAY,cAAc,QAAQ,IAAI,CAAC;AACjN,YAAM,uBAAuB,YAAU;AACrC,cAAM,UAAU,OAAO,IAAI,UAAU,OAAO,UAAU,QAAQ,GAAG,OAAO;AACxE,cAAM,QAAQ,OAAO,IAAI,SAAS,SAAS,eAAe;AAC1D,eAAO,SAAS,KAAK,KAAK;AAAA,MAC5B;AACA,YAAM,sBAAsB,CAAC,QAAQ,YAAY,YAAY,QAAQ,CAAC,OAAO,IAAI,WAAW,OAAO;AACnG,YAAM,0BAA0B,CAAC,QAAQ,YAAY;AACnD,cAAM,aAAa,OAAO,IAAI,UAAU,SAAS,UAAU;AAC3D,eAAO,oBAAoB,QAAQ,UAAU,KAAK,OAAO,UAAU,WAAW;AAAA,MAChF;AACA,YAAM,uBAAuB,CAAC,QAAQ,sBAAsB;AAC1D,cAAM,cAAc,OAAO,UAAU,QAAQ;AAC7C,0BAAkB;AAAA,UAChB,SAAS,OAAO,IAAI,WAAW,WAAW;AAAA,UAC1C,SAAS;AAAA,QACX,CAAC;AACD,eAAO,GAAG,cAAc,iBAAiB;AACzC,eAAO,MAAM,OAAO,IAAI,cAAc,iBAAiB;AAAA,MACzD;AAEA,YAAM,mBAAmB,gBAAc;AACrC,eAAO,WAAW,QAAQ,OAAO,GAAG,EAAE,QAAQ,SAAS,SAAO;AAC5D,iBAAO,IAAI,YAAY;AAAA,QACzB,CAAC;AAAA,MACH;AACA,YAAM,sBAAsB,gBAAc,WAAW,UAAU,KAAK,eAAe,YAAY,KAAK;AACpG,YAAM,mBAAmB,CAAC,QAAQ,aAAa,SAAO;AACpD,cAAM,oBAAoB,CAACA,SAAQ,YAAY;AAC7C,gBAAM,UAAUA,QAAO,UAAU,SAAS,IAAI;AAC9C,cAAI,UAAU,OAAOA,SAAQ,SAAS,QAAQ,CAAC;AAC/C,cAAI,WAAW,CAAC,wBAAwBA,SAAQ,OAAO,KAAKA,QAAO,UAAU,WAAW,CAAC;AAAA,QAC3F;AACA,cAAM,oBAAoB,OAAK,kBAAkB,QAAQ,EAAE,OAAO;AAClE,eAAO,qBAAqB,QAAQ,iBAAiB;AAAA,MACvD;AACA,YAAM,iBAAiB,CAAC,QAAQ,IAAI,SAAS,KAAK,UAAU,WAAW;AACrE,eAAO,GAAG,SAAS,eAAe,IAAI;AAAA,UACpC;AAAA,UACA,MAAM,aAAa,OAAO,iBAAiB;AAAA,UAC3C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO,cAAY;AACjB,kBAAM,QAAQ,OAAO,IAAI,QAAQ,gBAAc;AAC7C,oBAAM,YAAY,aAAa,OAAO,QAAQ;AAC9C,oBAAM,WAAW,eAAe,UAAU,eAAe,YAAY,YAAY;AACjF,oBAAM,YAAY,oBAAoB,UAAU;AAChD,oBAAM,cAAc,iBAAiB,UAAU;AAC/C,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,gBACP,MAAM,UAAU,YAAY,MAAM;AAAA,gBAClC,MAAM;AAAA,cACR;AAAA,YACF,CAAC;AACD,qBAAS,KAAK;AAAA,UAChB;AAAA,UACA,UAAU,MAAM,OAAO,YAAY,GAAG;AAAA,UACtC,cAAc,CAAC,iBAAiB,UAAU;AACxC,4BAAgB,QAAQ,UAAU,KAAK;AAAA,UACzC;AAAA,UACA,QAAQ,WAAS;AACf,kBAAM,gBAAgB,qBAAqB,MAAM;AACjD,mBAAO,cAAc,IAAI,eAAa,UAAU,SAAS,EAAE,MAAM,KAAK;AAAA,UACxE;AAAA,UACA,SAAS,iBAAiB,QAAQ,QAAQ;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,YAAM,YAAY,CAAC,QAAQ,IAAI,SAAS,KAAK,UAAU,eAAe;AACpE,eAAO,GAAG,SAAS,gBAAgB,IAAI;AAAA,UACrC,QAAQ;AAAA,UACR;AAAA,UACA,MAAM,aAAa,OAAO,iBAAiB;AAAA,UAC3C,SAAS,iBAAiB,QAAQ,QAAQ;AAAA,UAC1C,UAAU,MAAM,OAAO,kBAAkB,GAAG,KAAK,eAAe,KAAK,OAAO,YAAY,GAAG,IAAI,gBAAgB,QAAQ,UAAU,UAAU;AAAA,QAC7I,CAAC;AAAA,MACH;AACA,YAAM,aAAa,CAAC,QAAQ,IAAI,SAAS,KAAK,UAAU,WAAW;AACjE,YAAI,OAAO,SAAS,GAAG;AACrB,yBAAe,QAAQ,IAAI,SAAS,KAAK,UAAU,MAAM;AAAA,QAC3D,OAAO;AACL,oBAAU,QAAQ,IAAI,SAAS,KAAK,UAAU,oBAAoB,OAAO,CAAC,CAAC,CAAC;AAAA,QAC9E;AAAA,MACF;AACA,YAAM,WAAW,YAAU;AACzB,mBAAW,QAAQ,WAAW,iBAAiB,qBAAqB,MAAM,gBAAgB,MAAM,CAAC;AACjG,mBAAW,QAAQ,WAAW,eAAe,uBAAuB,MAAM,gBAAgB,MAAM,CAAC;AAAA,MACnG;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,WAAW,YAAU;AAChC,cAAI,OAAO,UAAU,OAAO,GAAG;AAC7B,uBAAW,MAAM;AACjB,qBAAS,MAAM;AACf,uBAAW,MAAM;AAAA,UACnB,OAAO;AACL,oBAAQ,MAAM,qEAAqE;AAAA,UACrF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;AC5PH;", "names": ["editor"]}