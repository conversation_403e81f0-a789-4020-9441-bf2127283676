{"version": 3, "sources": ["../../.pnpm/@antv+x6-plugin-dnd@2.1.1_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-dnd/src/style/raw.ts", "../../.pnpm/@antv+x6-plugin-dnd@2.1.1_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-dnd/src/index.ts", "../../.pnpm/@antv+x6-plugin-stencil@2.1.5_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-stencil/src/grid.ts", "../../.pnpm/@antv+x6-plugin-stencil@2.1.5_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-stencil/src/style/raw.ts", "../../.pnpm/@antv+x6-plugin-stencil@2.1.5_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-stencil/src/index.ts"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * Auto generated file, do not modify it!\n */\n\nexport const content = `.x6-widget-dnd {\n  position: absolute;\n  top: -10000px;\n  left: -10000px;\n  z-index: 999999;\n  display: none;\n  cursor: move;\n  opacity: 0.7;\n  pointer-events: 'cursor';\n}\n.x6-widget-dnd.dragging {\n  display: inline-block;\n}\n.x6-widget-dnd.dragging * {\n  pointer-events: none !important;\n}\n.x6-widget-dnd .x6-graph {\n  background: transparent;\n  box-shadow: none;\n}\n`\n", "import {\n  Geometry<PERSON>til,\n  Rectangle,\n  Point,\n  FunctionExt,\n  Dom,\n  CssLoader,\n  Cell,\n  Node,\n  View,\n  NodeView,\n  Graph,\n  EventArgs,\n} from '@antv/x6'\nimport { content } from './style/raw'\n\nexport class Dnd extends View implements Graph.Plugin {\n  public name = 'dnd'\n\n  protected sourceNode: Node | null\n  protected draggingNode: Node | null\n  protected draggingView: NodeView | null\n  protected draggingBBox: Rectangle\n  protected geometryBBox: Rectangle\n  protected candidateEmbedView: NodeView | null\n  protected delta: Point | null\n  protected padding: number | null\n  protected snapOffset: Point.PointLike | null\n  protected originOffset: null | { left: number; top: number }\n\n  public options: Dnd.Options\n  public draggingGraph: Graph\n\n  protected get targetScroller() {\n    const target = this.options.target\n    const scroller = target.getPlugin<any>('scroller')\n    return scroller\n  }\n\n  protected get targetGraph() {\n    return this.options.target\n  }\n\n  protected get targetModel() {\n    return this.targetGraph.model\n  }\n\n  protected get snapline() {\n    const target = this.options.target\n    const snapline = target.getPlugin<any>('snapline')\n    return snapline\n  }\n\n  constructor(options: Partial<Dnd.Options> & { target: Graph }) {\n    super()\n    this.options = {\n      ...Dnd.defaults,\n      ...options,\n    } as Dnd.Options\n    this.init()\n  }\n\n  init() {\n    CssLoader.ensure(this.name, content)\n\n    this.container = document.createElement('div')\n    Dom.addClass(this.container, this.prefixClassName('widget-dnd'))\n\n    this.draggingGraph = new Graph({\n      ...this.options.delegateGraphOptions,\n      container: document.createElement('div'),\n      width: 1,\n      height: 1,\n      async: false,\n    })\n\n    Dom.append(this.container, this.draggingGraph.container)\n  }\n\n  start(node: Node, evt: Dom.MouseDownEvent | MouseEvent) {\n    const e = evt as Dom.MouseDownEvent\n\n    e.preventDefault()\n\n    this.targetModel.startBatch('dnd')\n    Dom.addClass(this.container, 'dragging')\n    Dom.appendTo(\n      this.container,\n      this.options.draggingContainer || document.body,\n    )\n\n    this.sourceNode = node\n    this.prepareDragging(node, e.clientX, e.clientY)\n\n    const local = this.updateNodePosition(e.clientX, e.clientY)\n\n    if (this.isSnaplineEnabled()) {\n      this.snapline.captureCursorOffset({\n        e,\n        node,\n        cell: node,\n        view: this.draggingView!,\n        x: local.x,\n        y: local.y,\n      })\n      this.draggingNode!.on('change:position', this.snap, this)\n    }\n\n    this.delegateDocumentEvents(Dnd.documentEvents, e.data)\n  }\n\n  protected isSnaplineEnabled() {\n    return this.snapline && this.snapline.isEnabled()\n  }\n\n  protected prepareDragging(\n    sourceNode: Node,\n    clientX: number,\n    clientY: number,\n  ) {\n    const draggingGraph = this.draggingGraph\n    const draggingModel = draggingGraph.model\n    const draggingNode = this.options.getDragNode(sourceNode, {\n      sourceNode,\n      draggingGraph,\n      targetGraph: this.targetGraph,\n    })\n\n    draggingNode.position(0, 0)\n\n    let padding = 5\n    if (this.isSnaplineEnabled()) {\n      padding += this.snapline.options.tolerance || 0\n    }\n\n    if (this.isSnaplineEnabled() || this.options.scaled) {\n      const scale = this.targetGraph.transform.getScale()\n      draggingGraph.scale(scale.sx, scale.sy)\n      padding *= Math.max(scale.sx, scale.sy)\n    } else {\n      draggingGraph.scale(1, 1)\n    }\n\n    this.clearDragging()\n\n    // if (this.options.animation) {\n    //   this.$container.stop(true, true)\n    // }\n\n    draggingModel.resetCells([draggingNode])\n\n    const delegateView = draggingGraph.findViewByCell(draggingNode) as NodeView\n    delegateView.undelegateEvents()\n    delegateView.cell.off('changed')\n    draggingGraph.fitToContent({\n      padding,\n      allowNewOrigin: 'any',\n      useCellGeometry: false,\n    })\n\n    const bbox = delegateView.getBBox()\n    this.geometryBBox = delegateView.getBBox({ useCellGeometry: true })\n    this.delta = this.geometryBBox.getTopLeft().diff(bbox.getTopLeft())\n    this.draggingNode = draggingNode\n    this.draggingView = delegateView\n    this.draggingBBox = draggingNode.getBBox()\n    this.padding = padding\n    this.originOffset = this.updateGraphPosition(clientX, clientY)\n  }\n\n  protected updateGraphPosition(clientX: number, clientY: number) {\n    const scrollTop =\n      document.body.scrollTop || document.documentElement.scrollTop\n    const scrollLeft =\n      document.body.scrollLeft || document.documentElement.scrollLeft\n    const delta = this.delta!\n    const nodeBBox = this.geometryBBox\n    const padding = this.padding || 5\n    const offset = {\n      left: clientX - delta.x - nodeBBox.width / 2 - padding + scrollLeft,\n      top: clientY - delta.y - nodeBBox.height / 2 - padding + scrollTop,\n    }\n\n    if (this.draggingGraph) {\n      Dom.css(this.container, {\n        left: `${offset.left}px`,\n        top: `${offset.top}px`,\n      })\n    }\n\n    return offset\n  }\n\n  protected updateNodePosition(x: number, y: number) {\n    const local = this.targetGraph.clientToLocal(x, y)\n    const bbox = this.draggingBBox!\n    local.x -= bbox.width / 2\n    local.y -= bbox.height / 2\n    this.draggingNode!.position(local.x, local.y)\n    return local\n  }\n\n  protected snap({\n    cell,\n    current,\n    options,\n  }: Cell.EventArgs['change:position']) {\n    const node = cell as Node\n    if (options.snapped) {\n      const bbox = this.draggingBBox\n      node.position(bbox.x + options.tx, bbox.y + options.ty, { silent: true })\n      this.draggingView!.translate()\n      node.position(current!.x, current!.y, { silent: true })\n\n      this.snapOffset = {\n        x: options.tx,\n        y: options.ty,\n      }\n    } else {\n      this.snapOffset = null\n    }\n  }\n\n  protected onDragging(evt: Dom.MouseMoveEvent) {\n    const draggingView = this.draggingView\n    if (draggingView) {\n      evt.preventDefault()\n      const e = this.normalizeEvent(evt)\n      const clientX = e.clientX\n      const clientY = e.clientY\n\n      this.updateGraphPosition(clientX, clientY)\n      const local = this.updateNodePosition(clientX, clientY)\n      const embeddingMode = this.targetGraph.options.embedding.enabled\n      const isValidArea =\n        (embeddingMode || this.isSnaplineEnabled()) &&\n        this.isInsideValidArea({\n          x: clientX,\n          y: clientY,\n        })\n\n      if (embeddingMode) {\n        draggingView.setEventData(e, {\n          graph: this.targetGraph,\n          candidateEmbedView: this.candidateEmbedView,\n        })\n        const data = draggingView.getEventData<any>(e)\n        if (isValidArea) {\n          draggingView.processEmbedding(e, data)\n        } else {\n          draggingView.clearEmbedding(data)\n        }\n        this.candidateEmbedView = data.candidateEmbedView\n      }\n\n      // update snapline\n      if (this.isSnaplineEnabled()) {\n        if (isValidArea) {\n          this.snapline.snapOnMoving({\n            e,\n            view: draggingView!,\n            x: local.x,\n            y: local.y,\n          } as EventArgs['node:mousemove'])\n        } else {\n          this.snapline.hide()\n        }\n      }\n    }\n  }\n\n  protected onDragEnd(evt: Dom.MouseUpEvent) {\n    const draggingNode = this.draggingNode\n    if (draggingNode) {\n      const e = this.normalizeEvent(evt)\n      const draggingView = this.draggingView\n      const draggingBBox = this.draggingBBox\n      const snapOffset = this.snapOffset\n      let x = draggingBBox.x\n      let y = draggingBBox.y\n\n      if (snapOffset) {\n        x += snapOffset.x\n        y += snapOffset.y\n      }\n\n      draggingNode.position(x, y, { silent: true })\n\n      const ret = this.drop(draggingNode, { x: e.clientX, y: e.clientY })\n      const callback = (node: null | Node) => {\n        if (node) {\n          this.onDropped(draggingNode)\n          if (this.targetGraph.options.embedding.enabled && draggingView) {\n            draggingView.setEventData(e, {\n              cell: node,\n              graph: this.targetGraph,\n              candidateEmbedView: this.candidateEmbedView,\n            })\n            draggingView.finalizeEmbedding(e, draggingView.getEventData<any>(e))\n          }\n        } else {\n          this.onDropInvalid()\n        }\n\n        this.candidateEmbedView = null\n        this.targetModel.stopBatch('dnd')\n      }\n\n      if (FunctionExt.isAsync(ret)) {\n        // stop dragging\n        this.undelegateDocumentEvents()\n        ret.then(callback) // eslint-disable-line\n      } else {\n        callback(ret)\n      }\n    }\n  }\n\n  protected clearDragging() {\n    if (this.draggingNode) {\n      this.sourceNode = null\n      this.draggingNode.remove()\n      this.draggingNode = null\n      this.draggingView = null\n      this.delta = null\n      this.padding = null\n      this.snapOffset = null\n      this.originOffset = null\n      this.undelegateDocumentEvents()\n    }\n  }\n\n  protected onDropped(draggingNode: Node) {\n    if (this.draggingNode === draggingNode) {\n      this.clearDragging()\n      Dom.removeClass(this.container, 'dragging')\n      Dom.remove(this.container)\n    }\n  }\n\n  protected onDropInvalid() {\n    const draggingNode = this.draggingNode\n    if (draggingNode) {\n      this.onDropped(draggingNode)\n      // todo\n      // const anim = this.options.animation\n      // if (anim) {\n      //   const duration = (typeof anim === 'object' && anim.duration) || 150\n      //   const easing = (typeof anim === 'object' && anim.easing) || 'swing'\n\n      //   this.draggingView = null\n\n      //   this.$container.animate(this.originOffset!, duration, easing, () =>\n      //     this.onDropped(draggingNode),\n      //   )\n      // } else {\n      //   this.onDropped(draggingNode)\n      // }\n    }\n  }\n\n  protected isInsideValidArea(p: Point.PointLike) {\n    let targetRect: Rectangle\n    let dndRect: Rectangle | null = null\n    const targetGraph = this.targetGraph\n    const targetScroller = this.targetScroller\n\n    if (this.options.dndContainer) {\n      dndRect = this.getDropArea(this.options.dndContainer)\n    }\n    const isInsideDndRect = dndRect && dndRect.containsPoint(p)\n\n    if (targetScroller) {\n      if (targetScroller.options.autoResize) {\n        targetRect = this.getDropArea(targetScroller.container)\n      } else {\n        const outter = this.getDropArea(targetScroller.container)\n        targetRect = this.getDropArea(targetGraph.container).intersectsWithRect(\n          outter,\n        )!\n      }\n    } else {\n      targetRect = this.getDropArea(targetGraph.container)\n    }\n\n    return !isInsideDndRect && targetRect && targetRect.containsPoint(p)\n  }\n\n  protected getDropArea(elem: Element) {\n    const offset = Dom.offset(elem)!\n    const scrollTop =\n      document.body.scrollTop || document.documentElement.scrollTop\n    const scrollLeft =\n      document.body.scrollLeft || document.documentElement.scrollLeft\n\n    return Rectangle.create({\n      x:\n        offset.left +\n        parseInt(Dom.css(elem, 'border-left-width')!, 10) -\n        scrollLeft,\n      y:\n        offset.top +\n        parseInt(Dom.css(elem, 'border-top-width')!, 10) -\n        scrollTop,\n      width: elem.clientWidth,\n      height: elem.clientHeight,\n    })\n  }\n\n  protected drop(draggingNode: Node, pos: Point.PointLike) {\n    if (this.isInsideValidArea(pos)) {\n      const targetGraph = this.targetGraph\n      const targetModel = targetGraph.model\n      const local = targetGraph.clientToLocal(pos)\n      const sourceNode = this.sourceNode!\n      const droppingNode = this.options.getDropNode(draggingNode, {\n        sourceNode,\n        draggingNode,\n        targetGraph: this.targetGraph,\n        draggingGraph: this.draggingGraph,\n      })\n      const bbox = droppingNode.getBBox()\n      local.x += bbox.x - bbox.width / 2\n      local.y += bbox.y - bbox.height / 2\n      const gridSize = this.snapOffset ? 1 : targetGraph.getGridSize()\n\n      droppingNode.position(\n        GeometryUtil.snapToGrid(local.x, gridSize),\n        GeometryUtil.snapToGrid(local.y, gridSize),\n      )\n\n      droppingNode.removeZIndex()\n\n      const validateNode = this.options.validateNode\n      const ret = validateNode\n        ? validateNode(droppingNode, {\n            sourceNode,\n            draggingNode,\n            droppingNode,\n            targetGraph,\n            draggingGraph: this.draggingGraph,\n          })\n        : true\n\n      if (typeof ret === 'boolean') {\n        if (ret) {\n          targetModel.addCell(droppingNode, { stencil: this.cid })\n          return droppingNode\n        }\n        return null\n      }\n\n      return FunctionExt.toDeferredBoolean(ret).then((valid) => {\n        if (valid) {\n          targetModel.addCell(droppingNode, { stencil: this.cid })\n          return droppingNode\n        }\n        return null\n      })\n    }\n\n    return null\n  }\n\n  protected onRemove() {\n    if (this.draggingGraph) {\n      this.draggingGraph.view.remove()\n      this.draggingGraph.dispose()\n    }\n  }\n\n  @View.dispose()\n  dispose() {\n    this.remove()\n    CssLoader.clean(this.name)\n  }\n}\n\nexport namespace Dnd {\n  export interface Options {\n    target: Graph\n    /**\n     * Should scale the dragging node or not.\n     */\n    scaled?: boolean\n    delegateGraphOptions?: Graph.Options\n    // animation?:\n    //   | boolean\n    //   | {\n    //       duration?: number\n    //       easing?: string\n    //     }\n    draggingContainer?: HTMLElement\n    /**\n     * dnd tool box container.\n     */\n    dndContainer?: HTMLElement\n    getDragNode: (sourceNode: Node, options: GetDragNodeOptions) => Node\n    getDropNode: (draggingNode: Node, options: GetDropNodeOptions) => Node\n    validateNode?: (\n      droppingNode: Node,\n      options: ValidateNodeOptions,\n    ) => boolean | Promise<boolean>\n  }\n\n  export interface GetDragNodeOptions {\n    sourceNode: Node\n    targetGraph: Graph\n    draggingGraph: Graph\n  }\n\n  export interface GetDropNodeOptions extends GetDragNodeOptions {\n    draggingNode: Node\n  }\n\n  export interface ValidateNodeOptions extends GetDropNodeOptions {\n    droppingNode: Node\n  }\n\n  export const defaults: Partial<Options> = {\n    // animation: false,\n    getDragNode: (sourceNode) => sourceNode.clone(),\n    getDropNode: (draggingNode) => draggingNode.clone(),\n  }\n\n  export const documentEvents = {\n    mousemove: 'onDragging',\n    touchmove: 'onDragging',\n    mouseup: 'onDragEnd',\n    touchend: 'onDragEnd',\n    touchcancel: 'onDragEnd',\n  }\n}\n", "import { Node, Model } from '@antv/x6'\n\nexport function grid(cells: Node[] | Model, options: GridLayout.Options = {}) {\n  const model = Model.isModel(cells)\n    ? cells\n    : new Model().resetCells(cells, {\n        sort: false,\n        dryrun: true,\n      })\n\n  const nodes = model.getNodes()\n  const columns = options.columns || 1\n  const rows = Math.ceil(nodes.length / columns)\n  const dx = options.dx || 0\n  const dy = options.dy || 0\n  const centre = options.center !== false\n  const resizeToFit = options.resizeToFit === true\n  const marginX = options.marginX || 0\n  const marginY = options.marginY || 0\n  const columnWidths: number[] = []\n\n  let columnWidth = options.columnWidth\n\n  if (columnWidth === 'compact') {\n    for (let j = 0; j < columns; j += 1) {\n      const items = GridLayout.getNodesInColumn(nodes, j, columns)\n      columnWidths.push(GridLayout.getMaxDim(items, 'width') + dx)\n    }\n  } else {\n    if (columnWidth == null || columnWidth === 'auto') {\n      columnWidth = GridLayout.getMaxDim(nodes, 'width') + dx\n    }\n\n    for (let i = 0; i < columns; i += 1) {\n      columnWidths.push(columnWidth)\n    }\n  }\n\n  const columnLefts = GridLayout.accumulate(columnWidths, marginX)\n\n  const rowHeights: number[] = []\n  let rowHeight = options.rowHeight\n  if (rowHeight === 'compact') {\n    for (let i = 0; i < rows; i += 1) {\n      const items = GridLayout.getNodesInRow(nodes, i, columns)\n      rowHeights.push(GridLayout.getMaxDim(items, 'height') + dy)\n    }\n  } else {\n    if (rowHeight == null || rowHeight === 'auto') {\n      rowHeight = GridLayout.getMaxDim(nodes, 'height') + dy\n    }\n\n    for (let i = 0; i < rows; i += 1) {\n      rowHeights.push(rowHeight)\n    }\n  }\n  const rowTops = GridLayout.accumulate(rowHeights, marginY)\n\n  model.startBatch('layout')\n\n  nodes.forEach((node, index) => {\n    const rowIndex = index % columns\n    const columnIndex = Math.floor(index / columns)\n    const columnWidth = columnWidths[rowIndex]\n    const rowHeight = rowHeights[columnIndex]\n\n    let cx = 0\n    let cy = 0\n    let size = node.getSize()\n\n    if (resizeToFit) {\n      let width = columnWidth - 2 * dx\n      let height = rowHeight - 2 * dy\n      const calcHeight = size.height * (size.width ? width / size.width : 1)\n      const calcWidth = size.width * (size.height ? height / size.height : 1)\n      if (rowHeight < calcHeight) {\n        width = calcWidth\n      } else {\n        height = calcHeight\n      }\n      size = {\n        width,\n        height,\n      }\n      node.setSize(size, options)\n    }\n\n    if (centre) {\n      cx = (columnWidth - size.width) / 2\n      cy = (rowHeight - size.height) / 2\n    }\n\n    node.position(\n      columnLefts[rowIndex] + dx + cx,\n      rowTops[columnIndex] + dy + cy,\n      options,\n    )\n  })\n\n  model.stopBatch('layout')\n}\n\nnamespace GridLayout {\n  export interface Options extends Node.SetPositionOptions {\n    columns?: number\n    columnWidth?: number | 'auto' | 'compact'\n    rowHeight?: number | 'auto' | 'compact'\n    dx?: number\n    dy?: number\n    marginX?: number\n    marginY?: number\n    /**\n     * Positions the elements in the center of a grid cell.\n     *\n     * Default: true\n     */\n    center?: boolean\n    /**\n     * Resizes the elements to fit a grid cell, preserving the aspect ratio.\n     *\n     * Default: false\n     */\n    resizeToFit?: boolean\n  }\n\n  export function getMaxDim(nodes: Node[], name: 'width' | 'height') {\n    return nodes.reduce(\n      (memo, node) => Math.max(node?.getSize()[name], memo),\n      0,\n    )\n  }\n\n  export function getNodesInRow(\n    nodes: Node[],\n    rowIndex: number,\n    columnCount: number,\n  ) {\n    const res: Node[] = []\n    for (let i = columnCount * rowIndex, ii = i + columnCount; i < ii; i += 1) {\n      if (nodes[i]) res.push(nodes[i])\n    }\n    return res\n  }\n\n  export function getNodesInColumn(\n    nodes: Node[],\n    columnIndex: number,\n    columnCount: number,\n  ) {\n    const res: Node[] = []\n    for (let i = columnIndex, ii = nodes.length; i < ii; i += columnCount) {\n      if (nodes[i]) res.push(nodes[i])\n    }\n    return res\n  }\n\n  export function accumulate(items: number[], start: number) {\n    return items.reduce(\n      (memo, item, i) => {\n        memo.push(memo[i] + item)\n        return memo\n      },\n      [start || 0],\n    )\n  }\n}\n", "/* eslint-disable */\n\n/**\n * Auto generated file, do not modify it!\n */\n\nexport const content = `.x6-widget-dnd {\n  position: absolute;\n  top: -10000px;\n  left: -10000px;\n  z-index: 999999;\n  display: none;\n  cursor: move;\n  opacity: 0.7;\n  pointer-events: 'cursor';\n}\n.x6-widget-dnd.dragging {\n  display: inline-block;\n}\n.x6-widget-dnd.dragging * {\n  pointer-events: none !important;\n}\n.x6-widget-dnd .x6-graph {\n  background: transparent;\n  box-shadow: none;\n}\n.x6-widget-stencil {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n.x6-widget-stencil::after {\n  position: absolute;\n  top: 0;\n  display: block;\n  width: 100%;\n  height: 20px;\n  padding: 8px 0;\n  line-height: 20px;\n  text-align: center;\n  opacity: 0;\n  transition: top 0.1s linear, opacity 0.1s linear;\n  content: ' ';\n  pointer-events: none;\n}\n.x6-widget-stencil-content {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  height: auto;\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n.x6-widget-stencil .x6-node [magnet]:not([magnet='passive']) {\n  pointer-events: none;\n}\n.x6-widget-stencil-group {\n  padding: 0;\n  padding-bottom: 8px;\n  overflow: hidden;\n  user-select: none;\n}\n.x6-widget-stencil-group.collapsed {\n  height: auto;\n  padding-bottom: 0;\n}\n.x6-widget-stencil-group-title {\n  position: relative;\n  margin-top: 0;\n  margin-bottom: 0;\n  padding: 4px;\n  cursor: pointer;\n}\n.x6-widget-stencil-title,\n.x6-widget-stencil-group > .x6-widget-stencil-group-title {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  user-select: none;\n}\n.x6-widget-stencil .unmatched {\n  opacity: 0.3;\n}\n.x6-widget-stencil .x6-node.unmatched {\n  display: none;\n}\n.x6-widget-stencil-group.unmatched {\n  display: none;\n}\n.x6-widget-stencil-search-text {\n  position: relative;\n  z-index: 1;\n  box-sizing: border-box;\n  width: 100%;\n  height: 30px;\n  max-height: 30px;\n  line-height: 30px;\n  outline: 0;\n}\n.x6-widget-stencil.not-found::after {\n  opacity: 1;\n  content: attr(data-not-found-text);\n}\n.x6-widget-stencil.not-found.searchable::after {\n  top: 30px;\n}\n.x6-widget-stencil.not-found.searchable.collapsable::after {\n  top: 50px;\n}\n.x6-widget-stencil {\n  color: #333;\n  background: #f5f5f5;\n}\n.x6-widget-stencil-content {\n  position: absolute;\n}\n.x6-widget-stencil.collapsable > .x6-widget-stencil-content {\n  top: 32px;\n}\n.x6-widget-stencil.searchable > .x6-widget-stencil-content {\n  top: 80px;\n}\n.x6-widget-stencil.not-found::after {\n  position: absolute;\n}\n.x6-widget-stencil.not-found.searchable.collapsable::after {\n  top: 80px;\n}\n.x6-widget-stencil.not-found.searchable::after {\n  top: 60px;\n}\n.x6-widget-stencil-group {\n  height: auto;\n  margin-bottom: 1px;\n  padding: 0;\n  transition: none;\n}\n.x6-widget-stencil-group .x6-graph {\n  background: transparent;\n  box-shadow: none;\n}\n.x6-widget-stencil-group.collapsed {\n  height: auto;\n  max-height: 31px;\n}\n.x6-widget-stencil-title,\n.x6-widget-stencil-group > .x6-widget-stencil-group-title {\n  position: relative;\n  left: 0;\n  box-sizing: border-box;\n  width: 100%;\n  height: 32px;\n  padding: 0 5px 0 8px;\n  color: #666;\n  font-weight: 700;\n  font-size: 12px;\n  line-height: 32px;\n  cursor: default;\n  transition: all 0.3;\n}\n.x6-widget-stencil-title:hover,\n.x6-widget-stencil-group > .x6-widget-stencil-group-title:hover {\n  color: #444;\n}\n.x6-widget-stencil-title {\n  background: #e9e9e9;\n}\n.x6-widget-stencil-group > .x6-widget-stencil-group-title {\n  background: #ededed;\n}\n.x6-widget-stencil.collapsable > .x6-widget-stencil-title,\n.x6-widget-stencil-group.collapsable > .x6-widget-stencil-group-title {\n  padding-left: 32px;\n  cursor: pointer;\n}\n.x6-widget-stencil.collapsable > .x6-widget-stencil-title::before,\n.x6-widget-stencil-group.collapsable > .x6-widget-stencil-group-title::before {\n  position: absolute;\n  top: 6px;\n  left: 8px;\n  display: block;\n  width: 18px;\n  height: 18px;\n  margin: 0;\n  padding: 0;\n  background-color: transparent;\n  background-repeat: no-repeat;\n  background-position: 0 0;\n  border: none;\n  content: ' ';\n}\n.x6-widget-stencil.collapsable > .x6-widget-stencil-title::before,\n.x6-widget-stencil-group.collapsable > .x6-widget-stencil-group-title::before {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0iIzAwMCIgZmlsbC1ydWxlPSJub256ZXJvIj48cGF0aCBkPSJNOS4zNzUuNUM0LjY4Ny41Ljg3NSA0LjMxMy44NzUgOWMwIDQuNjg4IDMuODEyIDguNSA4LjUgOC41IDQuNjg3IDAgOC41LTMuODEyIDguNS04LjUgMC00LjY4Ny0zLjgxMy04LjUtOC41LTguNXptMCAxNS44ODZDNS4zMDMgMTYuMzg2IDEuOTkgMTMuMDcyIDEuOTkgOXMzLjMxMi03LjM4NSA3LjM4NS03LjM4NVMxNi43NiA0LjkyOCAxNi43NiA5YzAgNC4wNzItMy4zMTMgNy4zODYtNy4zODUgNy4zODZ6Ii8+PHBhdGggZD0iTTEyLjc1MyA4LjQ0M0g1Ljk5N2EuNTU4LjU1OCAwIDAwMCAxLjExNmg2Ljc1NmEuNTU4LjU1OCAwIDAwMC0xLjExNnoiLz48L2c+PC9zdmc+');\n  opacity: 0.4;\n  transition: all 0.3s;\n}\n.x6-widget-stencil.collapsable > .x6-widget-stencil-title:hover::before,\n.x6-widget-stencil-group.collapsable > .x6-widget-stencil-group-title:hover::before {\n  opacity: 0.6;\n}\n.x6-widget-stencil.collapsable.collapsed > .x6-widget-stencil-title::before,\n.x6-widget-stencil-group.collapsable.collapsed > .x6-widget-stencil-group-title::before {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0iIzAwMCIgZmlsbC1ydWxlPSJub256ZXJvIj48cGF0aCBkPSJNOS4zNzUuNUM0LjY4Ny41Ljg3NSA0LjMxMy44NzUgOWMwIDQuNjg4IDMuODEyIDguNSA4LjUgOC41IDQuNjg3IDAgOC41LTMuODEyIDguNS04LjUgMC00LjY4Ny0zLjgxMy04LjUtOC41LTguNXptMCAxNS44ODZDNS4zMDMgMTYuMzg2IDEuOTkgMTMuMDcyIDEuOTkgOXMzLjMxMi03LjM4NSA3LjM4NS03LjM4NVMxNi43NiA0LjkyOCAxNi43NiA5YzAgNC4wNzItMy4zMTMgNy4zODYtNy4zODUgNy4zODZ6Ii8+PHBhdGggZD0iTTEyLjc1MyA4LjQ0M0g1Ljk5N2EuNTU4LjU1OCAwIDAwMCAxLjExNmg2Ljc1NmEuNTU4LjU1OCAwIDAwMC0xLjExNnoiLz48cGF0aCBkPSJNOC44MTcgNS42MjN2Ni43NTZhLjU1OC41NTggMCAwMDEuMTE2IDBWNS42MjNhLjU1OC41NTggMCAxMC0xLjExNiAweiIvPjwvZz48L3N2Zz4=');\n  opacity: 0.4;\n}\n.x6-widget-stencil.collapsable.collapsed > .x6-widget-stencil-title:hover::before,\n.x6-widget-stencil-group.collapsable.collapsed > .x6-widget-stencil-group-title:hover::before {\n  opacity: 0.6;\n}\n.x6-widget-stencil input[type='search'] {\n  appearance: textfield;\n}\n.x6-widget-stencil input[type='search']::-webkit-search-cancel-button,\n.x6-widget-stencil input[type='search']::-webkit-search-decoration {\n  appearance: none;\n}\n.x6-widget-stencil-search-text {\n  display: block;\n  width: 90%;\n  margin: 8px 5%;\n  padding-left: 8px;\n  color: #333;\n  background: #fff;\n  border: 1px solid #e9e9e9;\n  border-radius: 12px;\n  outline: 0;\n}\n.x6-widget-stencil-search-text:focus {\n  outline: 0;\n}\n.x6-widget-stencil::after {\n  color: #808080;\n  font-weight: 600;\n  font-size: 12px;\n  background: 0 0;\n}\n`\n", "import {\n  Dom,\n  FunctionExt,\n  CssLoader,\n  Cell,\n  Node,\n  Model,\n  View,\n  Graph,\n  EventArgs,\n} from '@antv/x6'\nimport { Dnd } from '@antv/x6-plugin-dnd'\nimport { grid } from './grid'\nimport { content } from './style/raw'\n\nexport class Stencil extends View implements Graph.Plugin {\n  public name = 'stencil'\n  public options: Stencil.Options\n  public dnd: Dnd\n  protected graphs: { [groupName: string]: Graph }\n  protected groups: { [groupName: string]: HTMLElement }\n  protected content: HTMLDivElement\n\n  protected get targetScroller() {\n    const target = this.options.target\n    const scroller = target.getPlugin<any>('scroller')\n    return scroller\n  }\n\n  protected get targetGraph() {\n    return this.options.target\n  }\n\n  protected get targetModel() {\n    return this.targetGraph.model\n  }\n\n  constructor(options: Partial<Stencil.Options> = {}) {\n    super()\n    CssLoader.ensure(this.name, content)\n    this.graphs = {}\n    this.groups = {}\n    this.options = {\n      ...Stencil.defaultOptions,\n      ...options,\n    } as Stencil.Options\n    this.init()\n  }\n\n  init() {\n    this.dnd = new Dnd(this.options)\n    this.onSearch = FunctionExt.debounce(this.onSearch, 200)\n\n    this.initContainer()\n    this.initSearch()\n    this.initContent()\n    this.initGroups()\n    this.setTitle()\n    this.startListening()\n  }\n\n  // #region api\n\n  load(groups: { [groupName: string]: (Node | Node.Metadata)[] }): this\n  load(nodes: (Node | Node.Metadata)[], groupName?: string): this\n  load(\n    data:\n      | { [groupName: string]: (Node | Node.Metadata)[] }\n      | (Node | Node.Metadata)[],\n    groupName?: string,\n  ) {\n    if (Array.isArray(data)) {\n      this.loadGroup(data, groupName)\n    } else if (this.options.groups) {\n      Object.keys(this.options.groups).forEach((groupName) => {\n        if (data[groupName]) {\n          this.loadGroup(data[groupName], groupName)\n        }\n      })\n    }\n    return this\n  }\n\n  unload(groups: { [groupName: string]: (Node | Node.Metadata)[] }): this\n  unload(nodes: (Node | Node.Metadata)[], groupName?: string): this\n  unload(\n    data:\n      | { [groupName: string]: (Node | Node.Metadata)[] }\n      | (Node | Node.Metadata)[],\n    groupName?: string,\n  ) {\n    if (Array.isArray(data)) {\n      this.loadGroup(data, groupName, true)\n    } else if (this.options.groups) {\n      Object.keys(this.options.groups).forEach((groupName) => {\n        if (data[groupName]) {\n          this.loadGroup(data[groupName], groupName, true)\n        }\n      })\n    }\n    return this\n  }\n\n  toggleGroup(groupName: string) {\n    if (this.isGroupCollapsed(groupName)) {\n      this.expandGroup(groupName)\n    } else {\n      this.collapseGroup(groupName)\n    }\n    return this\n  }\n\n  collapseGroup(groupName: string) {\n    if (this.isGroupCollapsable(groupName)) {\n      const group = this.groups[groupName]\n      if (group && !this.isGroupCollapsed(groupName)) {\n        this.trigger('group:collapse', { name: groupName })\n        Dom.addClass(group, 'collapsed')\n      }\n    }\n    return this\n  }\n\n  expandGroup(groupName: string) {\n    if (this.isGroupCollapsable(groupName)) {\n      const group = this.groups[groupName]\n      if (group && this.isGroupCollapsed(groupName)) {\n        this.trigger('group:expand', { name: groupName })\n        Dom.removeClass(group, 'collapsed')\n      }\n    }\n    return this\n  }\n\n  isGroupCollapsable(groupName: string) {\n    const group = this.groups[groupName]\n    return Dom.hasClass(group, 'collapsable')\n  }\n\n  isGroupCollapsed(groupName: string) {\n    const group = this.groups[groupName]\n    return group && Dom.hasClass(group, 'collapsed')\n  }\n\n  collapseGroups() {\n    Object.keys(this.groups).forEach((groupName) =>\n      this.collapseGroup(groupName),\n    )\n    return this\n  }\n\n  expandGroups() {\n    Object.keys(this.groups).forEach((groupName) => this.expandGroup(groupName))\n    return this\n  }\n\n  resizeGroup(groupName: string, size: { width: number; height: number }) {\n    const graph = this.graphs[groupName]\n    if (graph) {\n      graph.resize(size.width, size.height)\n    }\n    return this\n  }\n\n  addGroup(group: Stencil.Group | Stencil.Group[]) {\n    const groups = Array.isArray(group) ? group : [group]\n    if (this.options.groups) {\n      this.options.groups.push(...groups)\n    } else {\n      this.options.groups = groups\n    }\n    groups.forEach((group) => this.initGroup(group))\n  }\n\n  removeGroup(groupName: string | string[]) {\n    const groupNames = Array.isArray(groupName) ? groupName : [groupName]\n    if (this.options.groups) {\n      this.options.groups = this.options.groups.filter(\n        (group) => !groupNames.includes(group.name),\n      )\n      groupNames.forEach((groupName) => {\n        const graph = this.graphs[groupName]\n        this.unregisterGraphEvents(graph)\n        graph.dispose()\n        delete this.graphs[groupName]\n\n        const elem = this.groups[groupName]\n        Dom.remove(elem)\n        delete this.groups[groupName]\n      })\n    }\n  }\n\n  // #endregion\n\n  protected initContainer() {\n    this.container = document.createElement('div')\n    Dom.addClass(this.container, this.prefixClassName(ClassNames.base))\n    Dom.attr(\n      this.container,\n      'data-not-found-text',\n      this.options.notFoundText || 'No matches found',\n    )\n  }\n\n  protected initContent() {\n    this.content = document.createElement('div')\n    Dom.addClass(this.content, this.prefixClassName(ClassNames.content))\n    Dom.appendTo(this.content, this.container)\n  }\n\n  protected initSearch() {\n    if (this.options.search) {\n      Dom.addClass(this.container, 'searchable')\n      Dom.append(this.container, this.renderSearch())\n    }\n  }\n\n  protected initGroup(group: Stencil.Group) {\n    const globalGraphOptions = this.options.stencilGraphOptions || {}\n    const groupElem = document.createElement('div')\n    Dom.addClass(groupElem, this.prefixClassName(ClassNames.group))\n    Dom.attr(groupElem, 'data-name', group.name)\n\n    if (\n      (group.collapsable == null && this.options.collapsable) ||\n      group.collapsable !== false\n    ) {\n      Dom.addClass(groupElem, 'collapsable')\n    }\n\n    Dom.toggleClass(groupElem, 'collapsed', group.collapsed === true)\n\n    const title = document.createElement('h3')\n    Dom.addClass(title, this.prefixClassName(ClassNames.groupTitle))\n    title.innerHTML = group.title || group.name\n\n    const content = document.createElement('div')\n    Dom.addClass(content, this.prefixClassName(ClassNames.groupContent))\n\n    const graphOptionsInGroup = group.graphOptions\n    const graph = new Graph({\n      ...globalGraphOptions,\n      ...graphOptionsInGroup,\n      container: document.createElement('div'),\n      model: globalGraphOptions.model || new Model(),\n      width: group.graphWidth || this.options.stencilGraphWidth,\n      height: group.graphHeight || this.options.stencilGraphHeight,\n      interacting: false,\n      preventDefaultBlankAction: false,\n    })\n\n    this.registerGraphEvents(graph)\n\n    Dom.append(content, graph.container)\n    Dom.append(groupElem, [title, content])\n    Dom.appendTo(groupElem, this.content)\n\n    this.groups[group.name] = groupElem\n    this.graphs[group.name] = graph\n  }\n\n  protected initGroups() {\n    this.clearGroups()\n    this.setCollapsableState()\n\n    if (this.options.groups && this.options.groups.length) {\n      this.options.groups.forEach((group) => {\n        this.initGroup(group)\n      })\n    } else {\n      const globalGraphOptions = this.options.stencilGraphOptions || {}\n      const graph = new Graph({\n        ...globalGraphOptions,\n        container: document.createElement('div'),\n        model: globalGraphOptions.model || new Model(),\n        width: this.options.stencilGraphWidth,\n        height: this.options.stencilGraphHeight,\n        interacting: false,\n        preventDefaultBlankAction: false,\n      })\n      Dom.append(this.content, graph.container)\n      this.graphs[Private.defaultGroupName] = graph\n    }\n  }\n\n  protected setCollapsableState() {\n    this.options.collapsable =\n      this.options.collapsable &&\n      this.options.groups &&\n      this.options.groups.some((group) => group.collapsable !== false)\n\n    if (this.options.collapsable) {\n      Dom.addClass(this.container, 'collapsable')\n      const collapsed =\n        this.options.groups &&\n        this.options.groups.every(\n          (group) => group.collapsed || group.collapsable === false,\n        )\n      if (collapsed) {\n        Dom.addClass(this.container, 'collapsed')\n      } else {\n        Dom.removeClass(this.container, 'collapsed')\n      }\n    } else {\n      Dom.removeClass(this.container, 'collapsable')\n    }\n  }\n\n  protected setTitle() {\n    const title = document.createElement('div')\n    Dom.addClass(title, this.prefixClassName(ClassNames.title))\n    title.innerHTML = this.options.title\n    Dom.appendTo(title, this.container)\n  }\n\n  protected renderSearch() {\n    const elem = document.createElement('div')\n    Dom.addClass(elem, this.prefixClassName(ClassNames.search))\n    const input = document.createElement('input')\n    Dom.attr(input, {\n      type: 'search',\n      placeholder: this.options.placeholder || 'Search',\n    })\n    Dom.addClass(input, this.prefixClassName(ClassNames.searchText))\n    Dom.append(elem, input)\n\n    return elem\n  }\n\n  protected startListening() {\n    const title = this.prefixClassName(ClassNames.title)\n    const searchText = this.prefixClassName(ClassNames.searchText)\n    const groupTitle = this.prefixClassName(ClassNames.groupTitle)\n\n    this.delegateEvents({\n      [`click .${title}`]: 'onTitleClick',\n      [`touchstart .${title}`]: 'onTitleClick',\n      [`click .${groupTitle}`]: 'onGroupTitleClick',\n      [`touchstart .${groupTitle}`]: 'onGroupTitleClick',\n      [`input .${searchText}`]: 'onSearch',\n      [`focusin .${searchText}`]: 'onSearchFocusIn',\n      [`focusout .${searchText}`]: 'onSearchFocusOut',\n    })\n  }\n\n  protected stopListening() {\n    this.undelegateEvents()\n  }\n\n  protected registerGraphEvents(graph: Graph) {\n    graph.on('cell:mousedown', this.onDragStart, this)\n  }\n\n  protected unregisterGraphEvents(graph: Graph) {\n    graph.off('cell:mousedown', this.onDragStart, this)\n  }\n\n  protected loadGroup(\n    cells: (Node | Node.Metadata)[],\n    groupName?: string,\n    reverse?: boolean,\n  ) {\n    const model = this.getModel(groupName)\n    if (model) {\n      const nodes = cells.map((cell) =>\n        Node.isNode(cell) ? cell : Node.create(cell),\n      )\n      if (reverse === true) {\n        model.removeCells(nodes)\n      } else {\n        model.resetCells(nodes)\n      }\n    }\n\n    const group = this.getGroup(groupName)\n    let height = this.options.stencilGraphHeight\n    if (group && group.graphHeight != null) {\n      height = group.graphHeight\n    }\n\n    const layout = (group && group.layout) || this.options.layout\n    if (layout && model) {\n      FunctionExt.call(layout, this, model, group)\n    }\n\n    if (!height) {\n      const graph = this.getGraph(groupName)\n      graph.fitToContent({\n        minWidth: graph.options.width,\n        gridHeight: 1,\n        padding:\n          (group && group.graphPadding) ||\n          this.options.stencilGraphPadding ||\n          10,\n      })\n    }\n\n    return this\n  }\n\n  protected onDragStart(args: EventArgs['node:mousedown']) {\n    const { e, node } = args\n    const group = this.getGroupByNode(node)\n    if (group && group.nodeMovable === false) {\n      return\n    }\n    this.dnd.start(node, e)\n  }\n\n  protected filter(keyword: string, filter?: Stencil.Filter) {\n    const found = Object.keys(this.graphs).reduce((memo, groupName) => {\n      const graph = this.graphs[groupName]\n      const name = groupName === Private.defaultGroupName ? null : groupName\n      const items = graph.model.getNodes().filter((cell) => {\n        let matched = false\n        if (typeof filter === 'function') {\n          matched = FunctionExt.call(filter, this, cell, keyword, name, this)\n        } else if (typeof filter === 'boolean') {\n          matched = filter\n        } else {\n          matched = this.isCellMatched(\n            cell,\n            keyword,\n            filter,\n            keyword.toLowerCase() !== keyword,\n          )\n        }\n\n        const view = graph.renderer.findViewByCell(cell)\n        if (view) {\n          Dom.toggleClass(view.container, 'unmatched', !matched)\n        }\n\n        return matched\n      })\n\n      const found = items.length > 0\n      const options = this.options\n\n      const model = new Model()\n      model.resetCells(items)\n\n      if (options.layout) {\n        FunctionExt.call(options.layout, this, model, this.getGroup(groupName))\n      }\n\n      if (this.groups[groupName]) {\n        Dom.toggleClass(this.groups[groupName], 'unmatched', !found)\n      }\n\n      graph.fitToContent({\n        gridWidth: 1,\n        gridHeight: 1,\n        padding: options.stencilGraphPadding || 10,\n      })\n\n      return memo || found\n    }, false)\n\n    Dom.toggleClass(this.container, 'not-found', !found)\n  }\n\n  protected isCellMatched(\n    cell: Cell,\n    keyword: string,\n    filters: Stencil.Filters | undefined,\n    ignoreCase: boolean,\n  ) {\n    if (keyword && filters) {\n      return Object.keys(filters).some((shape) => {\n        if (shape === '*' || cell.shape === shape) {\n          const filter = filters[shape]\n          if (typeof filter === 'boolean') {\n            return filter\n          }\n\n          const paths = Array.isArray(filter) ? filter : [filter]\n          return paths.some((path) => {\n            let val = cell.getPropByPath<string>(path)\n            if (val != null) {\n              val = `${val}`\n              if (!ignoreCase) {\n                val = val.toLowerCase()\n              }\n              return val.indexOf(keyword) >= 0\n            }\n            return false\n          })\n        }\n\n        return false\n      })\n    }\n\n    return true\n  }\n\n  protected onSearch(evt: Dom.EventObject) {\n    this.filter(evt.target.value as string, this.options.search)\n  }\n\n  protected onSearchFocusIn() {\n    Dom.addClass(this.container, 'is-focused')\n  }\n\n  protected onSearchFocusOut() {\n    Dom.removeClass(this.container, 'is-focused')\n  }\n\n  protected onTitleClick() {\n    if (this.options.collapsable) {\n      Dom.toggleClass(this.container, 'collapsed')\n      if (Dom.hasClass(this.container, 'collapsed')) {\n        this.collapseGroups()\n      } else {\n        this.expandGroups()\n      }\n    }\n  }\n\n  protected onGroupTitleClick(evt: Dom.EventObject) {\n    const group = evt.target.closest(\n      `.${this.prefixClassName(ClassNames.group)}`,\n    )\n    if (group) {\n      this.toggleGroup(Dom.attr(group, 'data-name') || '')\n    }\n\n    const allCollapsed = Object.keys(this.groups).every((name) => {\n      const group = this.getGroup(name)\n      const groupElem = this.groups[name]\n      return (\n        (group && group.collapsable === false) ||\n        Dom.hasClass(groupElem, 'collapsed')\n      )\n    })\n\n    Dom.toggleClass(this.container, 'collapsed', allCollapsed)\n  }\n\n  protected getModel(groupName?: string) {\n    const graph = this.getGraph(groupName)\n    return graph ? graph.model : null\n  }\n\n  protected getGraph(groupName?: string) {\n    return this.graphs[groupName || Private.defaultGroupName]\n  }\n\n  protected getGroup(groupName?: string) {\n    const groups = this.options.groups\n    if (groupName != null && groups && groups.length) {\n      return groups.find((group) => group.name === groupName)\n    }\n    return null\n  }\n\n  protected getGroupByNode(node: Node) {\n    const groups = this.options.groups\n    if (groups) {\n      return groups.find((group) => {\n        const model = this.getModel(group.name)\n        if (model) {\n          return model.has(node.id)\n        }\n        return false\n      })\n    }\n    return null\n  }\n\n  protected clearGroups() {\n    Object.keys(this.graphs).forEach((groupName) => {\n      const graph = this.graphs[groupName]\n      this.unregisterGraphEvents(graph)\n      graph.dispose()\n    })\n    Object.keys(this.groups).forEach((groupName) => {\n      const elem = this.groups[groupName]\n      Dom.remove(elem)\n    })\n    this.graphs = {}\n    this.groups = {}\n  }\n\n  protected onRemove() {\n    this.clearGroups()\n    this.dnd.remove()\n    this.stopListening()\n    this.undelegateDocumentEvents()\n  }\n\n  @View.dispose()\n  dispose() {\n    this.remove()\n    CssLoader.clean(this.name)\n  }\n}\n\nexport namespace Stencil {\n  export interface Options extends Dnd.Options {\n    title: string\n    groups?: Group[]\n    search?: Filter\n    placeholder?: string\n    notFoundText?: string\n    collapsable?: boolean\n    stencilGraphWidth: number\n    stencilGraphHeight: number\n    stencilGraphOptions?: Graph.Options\n    stencilGraphPadding?: number\n    layout?: (this: Stencil, model: Model, group?: Group | null) => any\n    layoutOptions?: any\n  }\n\n  export type Filter = Filters | FilterFn | boolean\n  export type Filters = { [shape: string]: string | string[] | boolean }\n  export type FilterFn = (\n    this: Stencil,\n    cell: Node,\n    keyword: string,\n    groupName: string | null,\n    stencil: Stencil,\n  ) => boolean\n\n  export interface Group {\n    name: string\n    title?: string\n    collapsed?: boolean\n    collapsable?: boolean\n    nodeMovable?: boolean\n\n    graphWidth?: number\n    graphHeight?: number\n    graphPadding?: number\n    graphOptions?: Graph.Options\n    layout?: (this: Stencil, model: Model, group?: Group | null) => any\n    layoutOptions?: any\n  }\n\n  export const defaultOptions: Partial<Options> = {\n    stencilGraphWidth: 200,\n    stencilGraphHeight: 800,\n    title: 'Stencil',\n    collapsable: false,\n    placeholder: 'Search',\n    notFoundText: 'No matches found',\n\n    layout(model, group) {\n      const options = {\n        columnWidth: (this.options.stencilGraphWidth as number) / 2 - 10,\n        columns: 2,\n        rowHeight: 80,\n        resizeToFit: false,\n        dx: 10,\n        dy: 10,\n      }\n\n      grid(model, {\n        ...options,\n        ...this.options.layoutOptions,\n        ...(group ? group.layoutOptions : {}),\n      })\n    },\n    ...Dnd.defaults,\n  }\n}\n\nnamespace ClassNames {\n  export const base = 'widget-stencil'\n  export const title = `${base}-title`\n  export const search = `${base}-search`\n  export const searchText = `${search}-text`\n  export const content = `${base}-content`\n  export const group = `${base}-group`\n  export const groupTitle = `${group}-title`\n  export const groupContent = `${group}-content`\n}\n\nnamespace Private {\n  export const defaultGroupName = '__default__'\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAMO,IAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACUjB,IAAO,MAAP,MAAO,aAAY,KAAI;EAiB3B,IAAc,iBAAc;AAC1B,UAAM,SAAS,KAAK,QAAQ;AAC5B,UAAM,WAAW,OAAO,UAAe,UAAU;AACjD,WAAO;EACT;EAEA,IAAc,cAAW;AACvB,WAAO,KAAK,QAAQ;EACtB;EAEA,IAAc,cAAW;AACvB,WAAO,KAAK,YAAY;EAC1B;EAEA,IAAc,WAAQ;AACpB,UAAM,SAAS,KAAK,QAAQ;AAC5B,UAAM,WAAW,OAAO,UAAe,UAAU;AACjD,WAAO;EACT;EAEA,YAAY,SAAiD;AAC3D,UAAK;AArCA,SAAA,OAAO;AAsCZ,SAAK,UAAU,OAAA,OAAA,OAAA,OAAA,CAAA,GACV,KAAI,QAAQ,GACZ,OAAO;AAEZ,SAAK,KAAI;EACX;EAEA,OAAI;AACF,mBAAU,OAAO,KAAK,MAAM,OAAO;AAEnC,SAAK,YAAY,SAAS,cAAc,KAAK;AAC7C,IAAAA,cAAI,SAAS,KAAK,WAAW,KAAK,gBAAgB,YAAY,CAAC;AAE/D,SAAK,gBAAgB,IAAI,MAAK,OAAA,OAAA,OAAA,OAAA,CAAA,GACzB,KAAK,QAAQ,oBAAoB,GAAA,EACpC,WAAW,SAAS,cAAc,KAAK,GACvC,OAAO,GACP,QAAQ,GACR,OAAO,MAAK,CAAA,CAAA;AAGd,IAAAA,cAAI,OAAO,KAAK,WAAW,KAAK,cAAc,SAAS;EACzD;EAEA,MAAM,MAAY,KAAoC;AACpD,UAAM,IAAI;AAEV,MAAE,eAAc;AAEhB,SAAK,YAAY,WAAW,KAAK;AACjC,IAAAA,cAAI,SAAS,KAAK,WAAW,UAAU;AACvC,IAAAA,cAAI,SACF,KAAK,WACL,KAAK,QAAQ,qBAAqB,SAAS,IAAI;AAGjD,SAAK,aAAa;AAClB,SAAK,gBAAgB,MAAM,EAAE,SAAS,EAAE,OAAO;AAE/C,UAAM,QAAQ,KAAK,mBAAmB,EAAE,SAAS,EAAE,OAAO;AAE1D,QAAI,KAAK,kBAAiB,GAAI;AAC5B,WAAK,SAAS,oBAAoB;QAChC;QACA;QACA,MAAM;QACN,MAAM,KAAK;QACX,GAAG,MAAM;QACT,GAAG,MAAM;OACV;AACD,WAAK,aAAc,GAAG,mBAAmB,KAAK,MAAM,IAAI;;AAG1D,SAAK,uBAAuB,KAAI,gBAAgB,EAAE,IAAI;EACxD;EAEU,oBAAiB;AACzB,WAAO,KAAK,YAAY,KAAK,SAAS,UAAS;EACjD;EAEU,gBACR,YACA,SACA,SAAe;AAEf,UAAM,gBAAgB,KAAK;AAC3B,UAAM,gBAAgB,cAAc;AACpC,UAAM,eAAe,KAAK,QAAQ,YAAY,YAAY;MACxD;MACA;MACA,aAAa,KAAK;KACnB;AAED,iBAAa,SAAS,GAAG,CAAC;AAE1B,QAAI,UAAU;AACd,QAAI,KAAK,kBAAiB,GAAI;AAC5B,iBAAW,KAAK,SAAS,QAAQ,aAAa;;AAGhD,QAAI,KAAK,kBAAiB,KAAM,KAAK,QAAQ,QAAQ;AACnD,YAAM,QAAQ,KAAK,YAAY,UAAU,SAAQ;AACjD,oBAAc,MAAM,MAAM,IAAI,MAAM,EAAE;AACtC,iBAAW,KAAK,IAAI,MAAM,IAAI,MAAM,EAAE;WACjC;AACL,oBAAc,MAAM,GAAG,CAAC;;AAG1B,SAAK,cAAa;AAMlB,kBAAc,WAAW,CAAC,YAAY,CAAC;AAEvC,UAAM,eAAe,cAAc,eAAe,YAAY;AAC9D,iBAAa,iBAAgB;AAC7B,iBAAa,KAAK,IAAI,SAAS;AAC/B,kBAAc,aAAa;MACzB;MACA,gBAAgB;MAChB,iBAAiB;KAClB;AAED,UAAM,OAAO,aAAa,QAAO;AACjC,SAAK,eAAe,aAAa,QAAQ,EAAE,iBAAiB,KAAI,CAAE;AAClE,SAAK,QAAQ,KAAK,aAAa,WAAU,EAAG,KAAK,KAAK,WAAU,CAAE;AAClE,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,eAAe,aAAa,QAAO;AACxC,SAAK,UAAU;AACf,SAAK,eAAe,KAAK,oBAAoB,SAAS,OAAO;EAC/D;EAEU,oBAAoB,SAAiB,SAAe;AAC5D,UAAM,YACJ,SAAS,KAAK,aAAa,SAAS,gBAAgB;AACtD,UAAM,aACJ,SAAS,KAAK,cAAc,SAAS,gBAAgB;AACvD,UAAM,QAAQ,KAAK;AACnB,UAAM,WAAW,KAAK;AACtB,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,SAAS;MACb,MAAM,UAAU,MAAM,IAAI,SAAS,QAAQ,IAAI,UAAU;MACzD,KAAK,UAAU,MAAM,IAAI,SAAS,SAAS,IAAI,UAAU;;AAG3D,QAAI,KAAK,eAAe;AACtB,MAAAA,cAAI,IAAI,KAAK,WAAW;QACtB,MAAM,GAAG,OAAO,IAAI;QACpB,KAAK,GAAG,OAAO,GAAG;OACnB;;AAGH,WAAO;EACT;EAEU,mBAAmB,GAAW,GAAS;AAC/C,UAAM,QAAQ,KAAK,YAAY,cAAc,GAAG,CAAC;AACjD,UAAM,OAAO,KAAK;AAClB,UAAM,KAAK,KAAK,QAAQ;AACxB,UAAM,KAAK,KAAK,SAAS;AACzB,SAAK,aAAc,SAAS,MAAM,GAAG,MAAM,CAAC;AAC5C,WAAO;EACT;EAEU,KAAK,EACb,MACA,SACA,QAAO,GAC2B;AAClC,UAAM,OAAO;AACb,QAAI,QAAQ,SAAS;AACnB,YAAM,OAAO,KAAK;AAClB,WAAK,SAAS,KAAK,IAAI,QAAQ,IAAI,KAAK,IAAI,QAAQ,IAAI,EAAE,QAAQ,KAAI,CAAE;AACxE,WAAK,aAAc,UAAS;AAC5B,WAAK,SAAS,QAAS,GAAG,QAAS,GAAG,EAAE,QAAQ,KAAI,CAAE;AAEtD,WAAK,aAAa;QAChB,GAAG,QAAQ;QACX,GAAG,QAAQ;;WAER;AACL,WAAK,aAAa;;EAEtB;EAEU,WAAW,KAAuB;AAC1C,UAAM,eAAe,KAAK;AAC1B,QAAI,cAAc;AAChB,UAAI,eAAc;AAClB,YAAM,IAAI,KAAK,eAAe,GAAG;AACjC,YAAM,UAAU,EAAE;AAClB,YAAM,UAAU,EAAE;AAElB,WAAK,oBAAoB,SAAS,OAAO;AACzC,YAAM,QAAQ,KAAK,mBAAmB,SAAS,OAAO;AACtD,YAAM,gBAAgB,KAAK,YAAY,QAAQ,UAAU;AACzD,YAAM,eACH,iBAAiB,KAAK,kBAAiB,MACxC,KAAK,kBAAkB;QACrB,GAAG;QACH,GAAG;OACJ;AAEH,UAAI,eAAe;AACjB,qBAAa,aAAa,GAAG;UAC3B,OAAO,KAAK;UACZ,oBAAoB,KAAK;SAC1B;AACD,cAAM,OAAO,aAAa,aAAkB,CAAC;AAC7C,YAAI,aAAa;AACf,uBAAa,iBAAiB,GAAG,IAAI;eAChC;AACL,uBAAa,eAAe,IAAI;;AAElC,aAAK,qBAAqB,KAAK;;AAIjC,UAAI,KAAK,kBAAiB,GAAI;AAC5B,YAAI,aAAa;AACf,eAAK,SAAS,aAAa;YACzB;YACA,MAAM;YACN,GAAG,MAAM;YACT,GAAG,MAAM;WACqB;eAC3B;AACL,eAAK,SAAS,KAAI;;;;EAI1B;EAEU,UAAU,KAAqB;AACvC,UAAM,eAAe,KAAK;AAC1B,QAAI,cAAc;AAChB,YAAM,IAAI,KAAK,eAAe,GAAG;AACjC,YAAM,eAAe,KAAK;AAC1B,YAAM,eAAe,KAAK;AAC1B,YAAM,aAAa,KAAK;AACxB,UAAI,IAAI,aAAa;AACrB,UAAI,IAAI,aAAa;AAErB,UAAI,YAAY;AACd,aAAK,WAAW;AAChB,aAAK,WAAW;;AAGlB,mBAAa,SAAS,GAAG,GAAG,EAAE,QAAQ,KAAI,CAAE;AAE5C,YAAM,MAAM,KAAK,KAAK,cAAc,EAAE,GAAG,EAAE,SAAS,GAAG,EAAE,QAAO,CAAE;AAClE,YAAM,WAAW,CAAC,SAAqB;AACrC,YAAI,MAAM;AACR,eAAK,UAAU,YAAY;AAC3B,cAAI,KAAK,YAAY,QAAQ,UAAU,WAAW,cAAc;AAC9D,yBAAa,aAAa,GAAG;cAC3B,MAAM;cACN,OAAO,KAAK;cACZ,oBAAoB,KAAK;aAC1B;AACD,yBAAa,kBAAkB,GAAG,aAAa,aAAkB,CAAC,CAAC;;eAEhE;AACL,eAAK,cAAa;;AAGpB,aAAK,qBAAqB;AAC1B,aAAK,YAAY,UAAU,KAAK;MAClC;AAEA,UAAI,aAAY,QAAQ,GAAG,GAAG;AAE5B,aAAK,yBAAwB;AAC7B,YAAI,KAAK,QAAQ;aACZ;AACL,iBAAS,GAAG;;;EAGlB;EAEU,gBAAa;AACrB,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa;AAClB,WAAK,aAAa,OAAM;AACxB,WAAK,eAAe;AACpB,WAAK,eAAe;AACpB,WAAK,QAAQ;AACb,WAAK,UAAU;AACf,WAAK,aAAa;AAClB,WAAK,eAAe;AACpB,WAAK,yBAAwB;;EAEjC;EAEU,UAAU,cAAkB;AACpC,QAAI,KAAK,iBAAiB,cAAc;AACtC,WAAK,cAAa;AAClB,MAAAA,cAAI,YAAY,KAAK,WAAW,UAAU;AAC1C,MAAAA,cAAI,OAAO,KAAK,SAAS;;EAE7B;EAEU,gBAAa;AACrB,UAAM,eAAe,KAAK;AAC1B,QAAI,cAAc;AAChB,WAAK,UAAU,YAAY;;EAgB/B;EAEU,kBAAkB,GAAkB;AAC5C,QAAI;AACJ,QAAI,UAA4B;AAChC,UAAM,cAAc,KAAK;AACzB,UAAM,iBAAiB,KAAK;AAE5B,QAAI,KAAK,QAAQ,cAAc;AAC7B,gBAAU,KAAK,YAAY,KAAK,QAAQ,YAAY;;AAEtD,UAAM,kBAAkB,WAAW,QAAQ,cAAc,CAAC;AAE1D,QAAI,gBAAgB;AAClB,UAAI,eAAe,QAAQ,YAAY;AACrC,qBAAa,KAAK,YAAY,eAAe,SAAS;aACjD;AACL,cAAM,SAAS,KAAK,YAAY,eAAe,SAAS;AACxD,qBAAa,KAAK,YAAY,YAAY,SAAS,EAAE,mBACnD,MAAM;;WAGL;AACL,mBAAa,KAAK,YAAY,YAAY,SAAS;;AAGrD,WAAO,CAAC,mBAAmB,cAAc,WAAW,cAAc,CAAC;EACrE;EAEU,YAAY,MAAa;AACjC,UAAM,SAASA,cAAI,OAAO,IAAI;AAC9B,UAAM,YACJ,SAAS,KAAK,aAAa,SAAS,gBAAgB;AACtD,UAAM,aACJ,SAAS,KAAK,cAAc,SAAS,gBAAgB;AAEvD,WAAO,UAAU,OAAO;MACtB,GACE,OAAO,OACP,SAASA,cAAI,IAAI,MAAM,mBAAmB,GAAI,EAAE,IAChD;MACF,GACE,OAAO,MACP,SAASA,cAAI,IAAI,MAAM,kBAAkB,GAAI,EAAE,IAC/C;MACF,OAAO,KAAK;MACZ,QAAQ,KAAK;KACd;EACH;EAEU,KAAK,cAAoB,KAAoB;AACrD,QAAI,KAAK,kBAAkB,GAAG,GAAG;AAC/B,YAAM,cAAc,KAAK;AACzB,YAAM,cAAc,YAAY;AAChC,YAAM,QAAQ,YAAY,cAAc,GAAG;AAC3C,YAAM,aAAa,KAAK;AACxB,YAAM,eAAe,KAAK,QAAQ,YAAY,cAAc;QAC1D;QACA;QACA,aAAa,KAAK;QAClB,eAAe,KAAK;OACrB;AACD,YAAM,OAAO,aAAa,QAAO;AACjC,YAAM,KAAK,KAAK,IAAI,KAAK,QAAQ;AACjC,YAAM,KAAK,KAAK,IAAI,KAAK,SAAS;AAClC,YAAM,WAAW,KAAK,aAAa,IAAI,YAAY,YAAW;AAE9D,mBAAa,SACX,aAAa,WAAW,MAAM,GAAG,QAAQ,GACzC,aAAa,WAAW,MAAM,GAAG,QAAQ,CAAC;AAG5C,mBAAa,aAAY;AAEzB,YAAM,eAAe,KAAK,QAAQ;AAClC,YAAM,MAAM,eACR,aAAa,cAAc;QACzB;QACA;QACA;QACA;QACA,eAAe,KAAK;OACrB,IACD;AAEJ,UAAI,OAAO,QAAQ,WAAW;AAC5B,YAAI,KAAK;AACP,sBAAY,QAAQ,cAAc,EAAE,SAAS,KAAK,IAAG,CAAE;AACvD,iBAAO;;AAET,eAAO;;AAGT,aAAO,aAAY,kBAAkB,GAAG,EAAE,KAAK,CAAC,UAAS;AACvD,YAAI,OAAO;AACT,sBAAY,QAAQ,cAAc,EAAE,SAAS,KAAK,IAAG,CAAE;AACvD,iBAAO;;AAET,eAAO;MACT,CAAC;;AAGH,WAAO;EACT;EAEU,WAAQ;AAChB,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,KAAK,OAAM;AAC9B,WAAK,cAAc,QAAO;;EAE9B;EAGA,UAAO;AACL,SAAK,OAAM;AACX,mBAAU,MAAM,KAAK,IAAI;EAC3B;;AAHA,WAAA;EADC,KAAK,QAAO;;CAOf,SAAiBC,MAAG;AAyCL,EAAAA,KAAA,WAA6B;;IAExC,aAAa,CAAC,eAAe,WAAW,MAAK;IAC7C,aAAa,CAAC,iBAAiB,aAAa,MAAK;;AAGtC,EAAAA,KAAA,iBAAiB;IAC5B,WAAW;IACX,WAAW;IACX,SAAS;IACT,UAAU;IACV,aAAa;;AAEjB,GAtDiB,QAAA,MAAG,CAAA,EAAA;;;AC5dd,SAAU,KAAK,OAAuB,UAA8B,CAAA,GAAE;AAC1E,QAAM,QAAQ,MAAM,QAAQ,KAAK,IAC7B,QACA,IAAI,MAAK,EAAG,WAAW,OAAO;IAC5B,MAAM;IACN,QAAQ;GACT;AAEL,QAAM,QAAQ,MAAM,SAAQ;AAC5B,QAAM,UAAU,QAAQ,WAAW;AACnC,QAAM,OAAO,KAAK,KAAK,MAAM,SAAS,OAAO;AAC7C,QAAM,KAAK,QAAQ,MAAM;AACzB,QAAM,KAAK,QAAQ,MAAM;AACzB,QAAM,SAAS,QAAQ,WAAW;AAClC,QAAM,cAAc,QAAQ,gBAAgB;AAC5C,QAAM,UAAU,QAAQ,WAAW;AACnC,QAAM,UAAU,QAAQ,WAAW;AACnC,QAAM,eAAyB,CAAA;AAE/B,MAAI,cAAc,QAAQ;AAE1B,MAAI,gBAAgB,WAAW;AAC7B,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,YAAM,QAAQ,WAAW,iBAAiB,OAAO,GAAG,OAAO;AAC3D,mBAAa,KAAK,WAAW,UAAU,OAAO,OAAO,IAAI,EAAE;;SAExD;AACL,QAAI,eAAe,QAAQ,gBAAgB,QAAQ;AACjD,oBAAc,WAAW,UAAU,OAAO,OAAO,IAAI;;AAGvD,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,mBAAa,KAAK,WAAW;;;AAIjC,QAAM,cAAc,WAAW,WAAW,cAAc,OAAO;AAE/D,QAAM,aAAuB,CAAA;AAC7B,MAAI,YAAY,QAAQ;AACxB,MAAI,cAAc,WAAW;AAC3B,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG;AAChC,YAAM,QAAQ,WAAW,cAAc,OAAO,GAAG,OAAO;AACxD,iBAAW,KAAK,WAAW,UAAU,OAAO,QAAQ,IAAI,EAAE;;SAEvD;AACL,QAAI,aAAa,QAAQ,cAAc,QAAQ;AAC7C,kBAAY,WAAW,UAAU,OAAO,QAAQ,IAAI;;AAGtD,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG;AAChC,iBAAW,KAAK,SAAS;;;AAG7B,QAAM,UAAU,WAAW,WAAW,YAAY,OAAO;AAEzD,QAAM,WAAW,QAAQ;AAEzB,QAAM,QAAQ,CAAC,MAAM,UAAS;AAC5B,UAAM,WAAW,QAAQ;AACzB,UAAM,cAAc,KAAK,MAAM,QAAQ,OAAO;AAC9C,UAAMC,eAAc,aAAa,QAAQ;AACzC,UAAMC,aAAY,WAAW,WAAW;AAExC,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,OAAO,KAAK,QAAO;AAEvB,QAAI,aAAa;AACf,UAAI,QAAQD,eAAc,IAAI;AAC9B,UAAI,SAASC,aAAY,IAAI;AAC7B,YAAM,aAAa,KAAK,UAAU,KAAK,QAAQ,QAAQ,KAAK,QAAQ;AACpE,YAAM,YAAY,KAAK,SAAS,KAAK,SAAS,SAAS,KAAK,SAAS;AACrE,UAAIA,aAAY,YAAY;AAC1B,gBAAQ;aACH;AACL,iBAAS;;AAEX,aAAO;QACL;QACA;;AAEF,WAAK,QAAQ,MAAM,OAAO;;AAG5B,QAAI,QAAQ;AACV,YAAMD,eAAc,KAAK,SAAS;AAClC,YAAMC,aAAY,KAAK,UAAU;;AAGnC,SAAK,SACH,YAAY,QAAQ,IAAI,KAAK,IAC7B,QAAQ,WAAW,IAAI,KAAK,IAC5B,OAAO;EAEX,CAAC;AAED,QAAM,UAAU,QAAQ;AAC1B;AAEA,IAAU;CAAV,SAAUC,aAAU;AAuBlB,WAAgB,UAAU,OAAe,MAAwB;AAC/D,WAAO,MAAM,OACX,CAAC,MAAM,SAAS,KAAK,IAAI,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,QAAO,EAAG,IAAI,GAAG,IAAI,GACpD,CAAC;EAEL;AALgB,EAAAA,YAAA,YAAS;AAOzB,WAAgB,cACd,OACA,UACA,aAAmB;AAEnB,UAAM,MAAc,CAAA;AACpB,aAAS,IAAI,cAAc,UAAU,KAAK,IAAI,aAAa,IAAI,IAAI,KAAK,GAAG;AACzE,UAAI,MAAM,CAAC;AAAG,YAAI,KAAK,MAAM,CAAC,CAAC;;AAEjC,WAAO;EACT;AAVgB,EAAAA,YAAA,gBAAa;AAY7B,WAAgB,iBACd,OACA,aACA,aAAmB;AAEnB,UAAM,MAAc,CAAA;AACpB,aAAS,IAAI,aAAa,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK,aAAa;AACrE,UAAI,MAAM,CAAC;AAAG,YAAI,KAAK,MAAM,CAAC,CAAC;;AAEjC,WAAO;EACT;AAVgB,EAAAA,YAAA,mBAAgB;AAYhC,WAAgB,WAAW,OAAiB,OAAa;AACvD,WAAO,MAAM,OACX,CAAC,MAAM,MAAM,MAAK;AAChB,WAAK,KAAK,KAAK,CAAC,IAAI,IAAI;AACxB,aAAO;IACT,GACA,CAAC,SAAS,CAAC,CAAC;EAEhB;AARgB,EAAAA,YAAA,aAAU;AAS5B,GA/DU,eAAA,aAAU,CAAA,EAAA;;;AChGb,IAAMC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSjB,IAAO,UAAP,MAAO,iBAAgB,KAAI;EAQ/B,IAAc,iBAAc;AAC1B,UAAM,SAAS,KAAK,QAAQ;AAC5B,UAAM,WAAW,OAAO,UAAe,UAAU;AACjD,WAAO;EACT;EAEA,IAAc,cAAW;AACvB,WAAO,KAAK,QAAQ;EACtB;EAEA,IAAc,cAAW;AACvB,WAAO,KAAK,YAAY;EAC1B;EAEA,YAAY,UAAoC,CAAA,GAAE;AAChD,UAAK;AAtBA,SAAA,OAAO;AAuBZ,mBAAU,OAAO,KAAK,MAAMC,QAAO;AACnC,SAAK,SAAS,CAAA;AACd,SAAK,SAAS,CAAA;AACd,SAAK,UAAU,OAAA,OAAA,OAAA,OAAA,CAAA,GACV,SAAQ,cAAc,GACtB,OAAO;AAEZ,SAAK,KAAI;EACX;EAEA,OAAI;AACF,SAAK,MAAM,IAAI,IAAI,KAAK,OAAO;AAC/B,SAAK,WAAW,aAAY,SAAS,KAAK,UAAU,GAAG;AAEvD,SAAK,cAAa;AAClB,SAAK,WAAU;AACf,SAAK,YAAW;AAChB,SAAK,WAAU;AACf,SAAK,SAAQ;AACb,SAAK,eAAc;EACrB;EAMA,KACE,MAGA,WAAkB;AAElB,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAK,UAAU,MAAM,SAAS;eACrB,KAAK,QAAQ,QAAQ;AAC9B,aAAO,KAAK,KAAK,QAAQ,MAAM,EAAE,QAAQ,CAACC,eAAa;AACrD,YAAI,KAAKA,UAAS,GAAG;AACnB,eAAK,UAAU,KAAKA,UAAS,GAAGA,UAAS;;MAE7C,CAAC;;AAEH,WAAO;EACT;EAIA,OACE,MAGA,WAAkB;AAElB,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAK,UAAU,MAAM,WAAW,IAAI;eAC3B,KAAK,QAAQ,QAAQ;AAC9B,aAAO,KAAK,KAAK,QAAQ,MAAM,EAAE,QAAQ,CAACA,eAAa;AACrD,YAAI,KAAKA,UAAS,GAAG;AACnB,eAAK,UAAU,KAAKA,UAAS,GAAGA,YAAW,IAAI;;MAEnD,CAAC;;AAEH,WAAO;EACT;EAEA,YAAY,WAAiB;AAC3B,QAAI,KAAK,iBAAiB,SAAS,GAAG;AACpC,WAAK,YAAY,SAAS;WACrB;AACL,WAAK,cAAc,SAAS;;AAE9B,WAAO;EACT;EAEA,cAAc,WAAiB;AAC7B,QAAI,KAAK,mBAAmB,SAAS,GAAG;AACtC,YAAM,QAAQ,KAAK,OAAO,SAAS;AACnC,UAAI,SAAS,CAAC,KAAK,iBAAiB,SAAS,GAAG;AAC9C,aAAK,QAAQ,kBAAkB,EAAE,MAAM,UAAS,CAAE;AAClD,QAAAC,cAAI,SAAS,OAAO,WAAW;;;AAGnC,WAAO;EACT;EAEA,YAAY,WAAiB;AAC3B,QAAI,KAAK,mBAAmB,SAAS,GAAG;AACtC,YAAM,QAAQ,KAAK,OAAO,SAAS;AACnC,UAAI,SAAS,KAAK,iBAAiB,SAAS,GAAG;AAC7C,aAAK,QAAQ,gBAAgB,EAAE,MAAM,UAAS,CAAE;AAChD,QAAAA,cAAI,YAAY,OAAO,WAAW;;;AAGtC,WAAO;EACT;EAEA,mBAAmB,WAAiB;AAClC,UAAM,QAAQ,KAAK,OAAO,SAAS;AACnC,WAAOA,cAAI,SAAS,OAAO,aAAa;EAC1C;EAEA,iBAAiB,WAAiB;AAChC,UAAM,QAAQ,KAAK,OAAO,SAAS;AACnC,WAAO,SAASA,cAAI,SAAS,OAAO,WAAW;EACjD;EAEA,iBAAc;AACZ,WAAO,KAAK,KAAK,MAAM,EAAE,QAAQ,CAAC,cAChC,KAAK,cAAc,SAAS,CAAC;AAE/B,WAAO;EACT;EAEA,eAAY;AACV,WAAO,KAAK,KAAK,MAAM,EAAE,QAAQ,CAAC,cAAc,KAAK,YAAY,SAAS,CAAC;AAC3E,WAAO;EACT;EAEA,YAAY,WAAmB,MAAuC;AACpE,UAAM,QAAQ,KAAK,OAAO,SAAS;AACnC,QAAI,OAAO;AACT,YAAM,OAAO,KAAK,OAAO,KAAK,MAAM;;AAEtC,WAAO;EACT;EAEA,SAAS,OAAsC;AAC7C,UAAM,SAAS,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AACpD,QAAI,KAAK,QAAQ,QAAQ;AACvB,WAAK,QAAQ,OAAO,KAAK,GAAG,MAAM;WAC7B;AACL,WAAK,QAAQ,SAAS;;AAExB,WAAO,QAAQ,CAACC,WAAU,KAAK,UAAUA,MAAK,CAAC;EACjD;EAEA,YAAY,WAA4B;AACtC,UAAM,aAAa,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;AACpE,QAAI,KAAK,QAAQ,QAAQ;AACvB,WAAK,QAAQ,SAAS,KAAK,QAAQ,OAAO,OACxC,CAAC,UAAU,CAAC,WAAW,SAAS,MAAM,IAAI,CAAC;AAE7C,iBAAW,QAAQ,CAACF,eAAa;AAC/B,cAAM,QAAQ,KAAK,OAAOA,UAAS;AACnC,aAAK,sBAAsB,KAAK;AAChC,cAAM,QAAO;AACb,eAAO,KAAK,OAAOA,UAAS;AAE5B,cAAM,OAAO,KAAK,OAAOA,UAAS;AAClC,QAAAC,cAAI,OAAO,IAAI;AACf,eAAO,KAAK,OAAOD,UAAS;MAC9B,CAAC;;EAEL;;EAIU,gBAAa;AACrB,SAAK,YAAY,SAAS,cAAc,KAAK;AAC7C,IAAAC,cAAI,SAAS,KAAK,WAAW,KAAK,gBAAgB,WAAW,IAAI,CAAC;AAClE,IAAAA,cAAI,KACF,KAAK,WACL,uBACA,KAAK,QAAQ,gBAAgB,kBAAkB;EAEnD;EAEU,cAAW;AACnB,SAAK,UAAU,SAAS,cAAc,KAAK;AAC3C,IAAAA,cAAI,SAAS,KAAK,SAAS,KAAK,gBAAgB,WAAW,OAAO,CAAC;AACnE,IAAAA,cAAI,SAAS,KAAK,SAAS,KAAK,SAAS;EAC3C;EAEU,aAAU;AAClB,QAAI,KAAK,QAAQ,QAAQ;AACvB,MAAAA,cAAI,SAAS,KAAK,WAAW,YAAY;AACzC,MAAAA,cAAI,OAAO,KAAK,WAAW,KAAK,aAAY,CAAE;;EAElD;EAEU,UAAU,OAAoB;AACtC,UAAM,qBAAqB,KAAK,QAAQ,uBAAuB,CAAA;AAC/D,UAAM,YAAY,SAAS,cAAc,KAAK;AAC9C,IAAAA,cAAI,SAAS,WAAW,KAAK,gBAAgB,WAAW,KAAK,CAAC;AAC9D,IAAAA,cAAI,KAAK,WAAW,aAAa,MAAM,IAAI;AAE3C,QACG,MAAM,eAAe,QAAQ,KAAK,QAAQ,eAC3C,MAAM,gBAAgB,OACtB;AACA,MAAAA,cAAI,SAAS,WAAW,aAAa;;AAGvC,IAAAA,cAAI,YAAY,WAAW,aAAa,MAAM,cAAc,IAAI;AAEhE,UAAM,QAAQ,SAAS,cAAc,IAAI;AACzC,IAAAA,cAAI,SAAS,OAAO,KAAK,gBAAgB,WAAW,UAAU,CAAC;AAC/D,UAAM,YAAY,MAAM,SAAS,MAAM;AAEvC,UAAMF,WAAU,SAAS,cAAc,KAAK;AAC5C,IAAAE,cAAI,SAASF,UAAS,KAAK,gBAAgB,WAAW,YAAY,CAAC;AAEnE,UAAM,sBAAsB,MAAM;AAClC,UAAM,QAAQ,IAAI,MAAK,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAClB,kBAAkB,GAClB,mBAAmB,GAAA,EACtB,WAAW,SAAS,cAAc,KAAK,GACvC,OAAO,mBAAmB,SAAS,IAAI,MAAK,GAC5C,OAAO,MAAM,cAAc,KAAK,QAAQ,mBACxC,QAAQ,MAAM,eAAe,KAAK,QAAQ,oBAC1C,aAAa,OACb,2BAA2B,MAAK,CAAA,CAAA;AAGlC,SAAK,oBAAoB,KAAK;AAE9B,IAAAE,cAAI,OAAOF,UAAS,MAAM,SAAS;AACnC,IAAAE,cAAI,OAAO,WAAW,CAAC,OAAOF,QAAO,CAAC;AACtC,IAAAE,cAAI,SAAS,WAAW,KAAK,OAAO;AAEpC,SAAK,OAAO,MAAM,IAAI,IAAI;AAC1B,SAAK,OAAO,MAAM,IAAI,IAAI;EAC5B;EAEU,aAAU;AAClB,SAAK,YAAW;AAChB,SAAK,oBAAmB;AAExB,QAAI,KAAK,QAAQ,UAAU,KAAK,QAAQ,OAAO,QAAQ;AACrD,WAAK,QAAQ,OAAO,QAAQ,CAAC,UAAS;AACpC,aAAK,UAAU,KAAK;MACtB,CAAC;WACI;AACL,YAAM,qBAAqB,KAAK,QAAQ,uBAAuB,CAAA;AAC/D,YAAM,QAAQ,IAAI,MAAK,OAAA,OAAA,OAAA,OAAA,CAAA,GAClB,kBAAkB,GAAA,EACrB,WAAW,SAAS,cAAc,KAAK,GACvC,OAAO,mBAAmB,SAAS,IAAI,MAAK,GAC5C,OAAO,KAAK,QAAQ,mBACpB,QAAQ,KAAK,QAAQ,oBACrB,aAAa,OACb,2BAA2B,MAAK,CAAA,CAAA;AAElC,MAAAA,cAAI,OAAO,KAAK,SAAS,MAAM,SAAS;AACxC,WAAK,OAAO,QAAQ,gBAAgB,IAAI;;EAE5C;EAEU,sBAAmB;AAC3B,SAAK,QAAQ,cACX,KAAK,QAAQ,eACb,KAAK,QAAQ,UACb,KAAK,QAAQ,OAAO,KAAK,CAAC,UAAU,MAAM,gBAAgB,KAAK;AAEjE,QAAI,KAAK,QAAQ,aAAa;AAC5B,MAAAA,cAAI,SAAS,KAAK,WAAW,aAAa;AAC1C,YAAM,YACJ,KAAK,QAAQ,UACb,KAAK,QAAQ,OAAO,MAClB,CAAC,UAAU,MAAM,aAAa,MAAM,gBAAgB,KAAK;AAE7D,UAAI,WAAW;AACb,QAAAA,cAAI,SAAS,KAAK,WAAW,WAAW;aACnC;AACL,QAAAA,cAAI,YAAY,KAAK,WAAW,WAAW;;WAExC;AACL,MAAAA,cAAI,YAAY,KAAK,WAAW,aAAa;;EAEjD;EAEU,WAAQ;AAChB,UAAM,QAAQ,SAAS,cAAc,KAAK;AAC1C,IAAAA,cAAI,SAAS,OAAO,KAAK,gBAAgB,WAAW,KAAK,CAAC;AAC1D,UAAM,YAAY,KAAK,QAAQ;AAC/B,IAAAA,cAAI,SAAS,OAAO,KAAK,SAAS;EACpC;EAEU,eAAY;AACpB,UAAM,OAAO,SAAS,cAAc,KAAK;AACzC,IAAAA,cAAI,SAAS,MAAM,KAAK,gBAAgB,WAAW,MAAM,CAAC;AAC1D,UAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,IAAAA,cAAI,KAAK,OAAO;MACd,MAAM;MACN,aAAa,KAAK,QAAQ,eAAe;KAC1C;AACD,IAAAA,cAAI,SAAS,OAAO,KAAK,gBAAgB,WAAW,UAAU,CAAC;AAC/D,IAAAA,cAAI,OAAO,MAAM,KAAK;AAEtB,WAAO;EACT;EAEU,iBAAc;AACtB,UAAM,QAAQ,KAAK,gBAAgB,WAAW,KAAK;AACnD,UAAM,aAAa,KAAK,gBAAgB,WAAW,UAAU;AAC7D,UAAM,aAAa,KAAK,gBAAgB,WAAW,UAAU;AAE7D,SAAK,eAAe;MAClB,CAAC,UAAU,KAAK,EAAE,GAAG;MACrB,CAAC,eAAe,KAAK,EAAE,GAAG;MAC1B,CAAC,UAAU,UAAU,EAAE,GAAG;MAC1B,CAAC,eAAe,UAAU,EAAE,GAAG;MAC/B,CAAC,UAAU,UAAU,EAAE,GAAG;MAC1B,CAAC,YAAY,UAAU,EAAE,GAAG;MAC5B,CAAC,aAAa,UAAU,EAAE,GAAG;KAC9B;EACH;EAEU,gBAAa;AACrB,SAAK,iBAAgB;EACvB;EAEU,oBAAoB,OAAY;AACxC,UAAM,GAAG,kBAAkB,KAAK,aAAa,IAAI;EACnD;EAEU,sBAAsB,OAAY;AAC1C,UAAM,IAAI,kBAAkB,KAAK,aAAa,IAAI;EACpD;EAEU,UACR,OACA,WACA,SAAiB;AAEjB,UAAM,QAAQ,KAAK,SAAS,SAAS;AACrC,QAAI,OAAO;AACT,YAAM,QAAQ,MAAM,IAAI,CAAC,SACvB,KAAK,OAAO,IAAI,IAAI,OAAO,KAAK,OAAO,IAAI,CAAC;AAE9C,UAAI,YAAY,MAAM;AACpB,cAAM,YAAY,KAAK;aAClB;AACL,cAAM,WAAW,KAAK;;;AAI1B,UAAM,QAAQ,KAAK,SAAS,SAAS;AACrC,QAAI,SAAS,KAAK,QAAQ;AAC1B,QAAI,SAAS,MAAM,eAAe,MAAM;AACtC,eAAS,MAAM;;AAGjB,UAAM,SAAU,SAAS,MAAM,UAAW,KAAK,QAAQ;AACvD,QAAI,UAAU,OAAO;AACnB,mBAAY,KAAK,QAAQ,MAAM,OAAO,KAAK;;AAG7C,QAAI,CAAC,QAAQ;AACX,YAAM,QAAQ,KAAK,SAAS,SAAS;AACrC,YAAM,aAAa;QACjB,UAAU,MAAM,QAAQ;QACxB,YAAY;QACZ,SACG,SAAS,MAAM,gBAChB,KAAK,QAAQ,uBACb;OACH;;AAGH,WAAO;EACT;EAEU,YAAY,MAAiC;AACrD,UAAM,EAAE,GAAG,KAAI,IAAK;AACpB,UAAM,QAAQ,KAAK,eAAe,IAAI;AACtC,QAAI,SAAS,MAAM,gBAAgB,OAAO;AACxC;;AAEF,SAAK,IAAI,MAAM,MAAM,CAAC;EACxB;EAEU,OAAO,SAAiB,QAAuB;AACvD,UAAM,QAAQ,OAAO,KAAK,KAAK,MAAM,EAAE,OAAO,CAAC,MAAM,cAAa;AAChE,YAAM,QAAQ,KAAK,OAAO,SAAS;AACnC,YAAM,OAAO,cAAc,QAAQ,mBAAmB,OAAO;AAC7D,YAAM,QAAQ,MAAM,MAAM,SAAQ,EAAG,OAAO,CAAC,SAAQ;AACnD,YAAI,UAAU;AACd,YAAI,OAAO,WAAW,YAAY;AAChC,oBAAU,aAAY,KAAK,QAAQ,MAAM,MAAM,SAAS,MAAM,IAAI;mBACzD,OAAO,WAAW,WAAW;AACtC,oBAAU;eACL;AACL,oBAAU,KAAK,cACb,MACA,SACA,QACA,QAAQ,YAAW,MAAO,OAAO;;AAIrC,cAAM,OAAO,MAAM,SAAS,eAAe,IAAI;AAC/C,YAAI,MAAM;AACR,UAAAA,cAAI,YAAY,KAAK,WAAW,aAAa,CAAC,OAAO;;AAGvD,eAAO;MACT,CAAC;AAED,YAAME,SAAQ,MAAM,SAAS;AAC7B,YAAM,UAAU,KAAK;AAErB,YAAM,QAAQ,IAAI,MAAK;AACvB,YAAM,WAAW,KAAK;AAEtB,UAAI,QAAQ,QAAQ;AAClB,qBAAY,KAAK,QAAQ,QAAQ,MAAM,OAAO,KAAK,SAAS,SAAS,CAAC;;AAGxE,UAAI,KAAK,OAAO,SAAS,GAAG;AAC1B,QAAAF,cAAI,YAAY,KAAK,OAAO,SAAS,GAAG,aAAa,CAACE,MAAK;;AAG7D,YAAM,aAAa;QACjB,WAAW;QACX,YAAY;QACZ,SAAS,QAAQ,uBAAuB;OACzC;AAED,aAAO,QAAQA;IACjB,GAAG,KAAK;AAER,IAAAF,cAAI,YAAY,KAAK,WAAW,aAAa,CAAC,KAAK;EACrD;EAEU,cACR,MACA,SACA,SACA,YAAmB;AAEnB,QAAI,WAAW,SAAS;AACtB,aAAO,OAAO,KAAK,OAAO,EAAE,KAAK,CAAC,UAAS;AACzC,YAAI,UAAU,OAAO,KAAK,UAAU,OAAO;AACzC,gBAAM,SAAS,QAAQ,KAAK;AAC5B,cAAI,OAAO,WAAW,WAAW;AAC/B,mBAAO;;AAGT,gBAAM,QAAQ,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACtD,iBAAO,MAAM,KAAK,CAAC,SAAQ;AACzB,gBAAI,MAAM,KAAK,cAAsB,IAAI;AACzC,gBAAI,OAAO,MAAM;AACf,oBAAM,GAAG,GAAG;AACZ,kBAAI,CAAC,YAAY;AACf,sBAAM,IAAI,YAAW;;AAEvB,qBAAO,IAAI,QAAQ,OAAO,KAAK;;AAEjC,mBAAO;UACT,CAAC;;AAGH,eAAO;MACT,CAAC;;AAGH,WAAO;EACT;EAEU,SAAS,KAAoB;AACrC,SAAK,OAAO,IAAI,OAAO,OAAiB,KAAK,QAAQ,MAAM;EAC7D;EAEU,kBAAe;AACvB,IAAAA,cAAI,SAAS,KAAK,WAAW,YAAY;EAC3C;EAEU,mBAAgB;AACxB,IAAAA,cAAI,YAAY,KAAK,WAAW,YAAY;EAC9C;EAEU,eAAY;AACpB,QAAI,KAAK,QAAQ,aAAa;AAC5B,MAAAA,cAAI,YAAY,KAAK,WAAW,WAAW;AAC3C,UAAIA,cAAI,SAAS,KAAK,WAAW,WAAW,GAAG;AAC7C,aAAK,eAAc;aACd;AACL,aAAK,aAAY;;;EAGvB;EAEU,kBAAkB,KAAoB;AAC9C,UAAM,QAAQ,IAAI,OAAO,QACvB,IAAI,KAAK,gBAAgB,WAAW,KAAK,CAAC,EAAE;AAE9C,QAAI,OAAO;AACT,WAAK,YAAYA,cAAI,KAAK,OAAO,WAAW,KAAK,EAAE;;AAGrD,UAAM,eAAe,OAAO,KAAK,KAAK,MAAM,EAAE,MAAM,CAAC,SAAQ;AAC3D,YAAMC,SAAQ,KAAK,SAAS,IAAI;AAChC,YAAM,YAAY,KAAK,OAAO,IAAI;AAClC,aACGA,UAASA,OAAM,gBAAgB,SAChCD,cAAI,SAAS,WAAW,WAAW;IAEvC,CAAC;AAED,IAAAA,cAAI,YAAY,KAAK,WAAW,aAAa,YAAY;EAC3D;EAEU,SAAS,WAAkB;AACnC,UAAM,QAAQ,KAAK,SAAS,SAAS;AACrC,WAAO,QAAQ,MAAM,QAAQ;EAC/B;EAEU,SAAS,WAAkB;AACnC,WAAO,KAAK,OAAO,aAAa,QAAQ,gBAAgB;EAC1D;EAEU,SAAS,WAAkB;AACnC,UAAM,SAAS,KAAK,QAAQ;AAC5B,QAAI,aAAa,QAAQ,UAAU,OAAO,QAAQ;AAChD,aAAO,OAAO,KAAK,CAAC,UAAU,MAAM,SAAS,SAAS;;AAExD,WAAO;EACT;EAEU,eAAe,MAAU;AACjC,UAAM,SAAS,KAAK,QAAQ;AAC5B,QAAI,QAAQ;AACV,aAAO,OAAO,KAAK,CAAC,UAAS;AAC3B,cAAM,QAAQ,KAAK,SAAS,MAAM,IAAI;AACtC,YAAI,OAAO;AACT,iBAAO,MAAM,IAAI,KAAK,EAAE;;AAE1B,eAAO;MACT,CAAC;;AAEH,WAAO;EACT;EAEU,cAAW;AACnB,WAAO,KAAK,KAAK,MAAM,EAAE,QAAQ,CAAC,cAAa;AAC7C,YAAM,QAAQ,KAAK,OAAO,SAAS;AACnC,WAAK,sBAAsB,KAAK;AAChC,YAAM,QAAO;IACf,CAAC;AACD,WAAO,KAAK,KAAK,MAAM,EAAE,QAAQ,CAAC,cAAa;AAC7C,YAAM,OAAO,KAAK,OAAO,SAAS;AAClC,MAAAA,cAAI,OAAO,IAAI;IACjB,CAAC;AACD,SAAK,SAAS,CAAA;AACd,SAAK,SAAS,CAAA;EAChB;EAEU,WAAQ;AAChB,SAAK,YAAW;AAChB,SAAK,IAAI,OAAM;AACf,SAAK,cAAa;AAClB,SAAK,yBAAwB;EAC/B;EAGA,UAAO;AACL,SAAK,OAAM;AACX,mBAAU,MAAM,KAAK,IAAI;EAC3B;;AAHAG,YAAA;EADC,KAAK,QAAO;;CAOf,SAAiBC,UAAO;AAyCT,EAAAA,SAAA,iBAAc,OAAA,OAAA,EACzB,mBAAmB,KACnB,oBAAoB,KACpB,OAAO,WACP,aAAa,OACb,aAAa,UACb,cAAc,oBAEd,OAAO,OAAO,OAAK;AACjB,UAAM,UAAU;MACd,aAAc,KAAK,QAAQ,oBAA+B,IAAI;MAC9D,SAAS;MACT,WAAW;MACX,aAAa;MACb,IAAI;MACJ,IAAI;;AAGN,SAAK,OAAK,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACL,OAAO,GACP,KAAK,QAAQ,aAAa,GACzB,QAAQ,MAAM,gBAAgB,CAAA,CAAG,CAAA;EAEzC,EAAC,GACE,IAAI,QAAQ;AAEnB,GAnEiB,YAAA,UAAO,CAAA,EAAA;AAqExB,IAAU;CAAV,SAAUC,aAAU;AACL,EAAAA,YAAA,OAAO;AACP,EAAAA,YAAA,QAAQ,GAAGA,YAAA,IAAI;AACf,EAAAA,YAAA,SAAS,GAAGA,YAAA,IAAI;AAChB,EAAAA,YAAA,aAAa,GAAGA,YAAA,MAAM;AACtB,EAAAA,YAAA,UAAU,GAAGA,YAAA,IAAI;AACjB,EAAAA,YAAA,QAAQ,GAAGA,YAAA,IAAI;AACf,EAAAA,YAAA,aAAa,GAAGA,YAAA,KAAK;AACrB,EAAAA,YAAA,eAAe,GAAGA,YAAA,KAAK;AACtC,GATU,eAAA,aAAU,CAAA,EAAA;AAWpB,IAAU;CAAV,SAAUC,UAAO;AACF,EAAAA,SAAA,mBAAmB;AAClC,GAFU,YAAA,UAAO,CAAA,EAAA;", "names": ["main_exports", "Dnd", "columnWidth", "rowHeight", "GridLayout", "content", "content", "groupName", "main_exports", "group", "found", "__decorate", "Stencil", "ClassNames", "Private"]}