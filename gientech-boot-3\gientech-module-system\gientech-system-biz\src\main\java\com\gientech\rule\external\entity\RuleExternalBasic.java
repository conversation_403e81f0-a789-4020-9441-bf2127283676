package com.gientech.rule.external.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 内外规管理模块-外规基本信息表
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-07
 */
@Data
@TableName("rule_external_basic")
@Schema(description = "内外规管理模块-外规基本信息表")
public class RuleExternalBasic implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;

    /**
     * 发文字号
     */
    @Excel(name = "发文字号", width = 15)
    @Schema(description = "发文字号")
    private String basicIssuedNumber;

    /**
     * 法规名称
     */
    @Excel(name = "法规名称", width = 15)
    @Schema(description = "法规名称")
    private String basicRegulatoryName;

    /**
     * 法规内容
     */
    @Excel(name = "法规内容", width = 15)
    @Schema(description = "法规内容")
    private String content;

    /**
     * 发文机构
     */
    @Excel(name = "发文机构", width = 15)
    @Schema(description = "发文机构")
    private String basicIssuedAgency;

    /**
     * 时效性
     */
    @Excel(name = "时效性", width = 15, dicCode = "rule_external_basic_timeliness")
    @Dict(dicCode = "rule_external_basic_timeliness")
    @Schema(description = "时效性")
    private String basicTimeliness;

    /**
     * 发布日期
     */
    @Excel(name = "发布日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "发布日期")
    private Date basicIssuedDate;

    /**
     * 实施日期
     */
    @Excel(name = "实施日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "实施日期")
    private Date basicImplementDate;

    /**
     * 收文日期
     */
    @Excel(name = "收文日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "收文日期")
    private Date basicReceiptDate;

    /**
     * 效力范围
     */
    @Excel(name = "效力范围", width = 15, dicCode = "rule_external_basic_validity_scope")
    @Dict(dicCode = "rule_external_basic_validity_scope")
    @Schema(description = "效力范围")
    private String validityScope;

    /**
     * 效力级别
     */
    @Excel(name = "效力级别", width = 15, dicCode = "rule_external_basic_validity_level")
    @Dict(dicCode = "rule_external_basic_validity_level")
    @Schema(description = "效力级别")
    private String validityLevel;

    /**
     * 所属领域
     */
    @Excel(name = "所属领域", width = 15)
    @Schema(description = "所属领域")
    private String domain;

    /**
     * 外规来源
     */
    @Excel(name = "外规来源", width = 15, dicCode = "rule_external_basic_source")
    @Dict(dicCode = "rule_external_basic_source")
    @Schema(description = "外规来源")
    private String source;

    /**
     * 发文渠道
     */
    @Excel(name = "发文渠道", width = 15, dicCode = "rule_external_basic_issued_channel")
    @Dict(dicCode = "rule_external_basic_issued_channel")
    @Schema(description = "发文渠道")
    private String issuedChannel;

    /**
     * 内化情况
     */
    @Excel(name = "内化情况", width = 15, dicCode = "rule_external_basic_internal_situation")
    @Dict(dicCode = "rule_external_basic_internal_situation")
    @Schema(description = "内化情况")
    private String internalSituation;

    /**
     * 内化比例
     */
    @Excel(name = "内化比例", width = 15)
    @Schema(description = "内化比例")
    private BigDecimal internalizeRatio;

    /**
     * 未内化条款
     */
    @Excel(name = "未内化条款", width = 15)
    @Schema(description = "未内化条款")
    private String haventInternalizeTerm;

    /**
     * 解读文件路径
     */
    @Excel(name = "解读文件路径", width = 15)
    @Schema(description = "解读文件路径")
    private String interpretFilePath;

    /**
     * 解读文件说明
     */
    @Excel(name = "解读文件说明", width = 15)
    @Schema(description = "解读文件说明")
    private String interpretFileDescription;

    /**
     * 外规文件路径
     */
    @Excel(name = "外规文件路径", width = 15)
    @Schema(description = "外规文件路径")
    private String basicFilePath;

    /**
     * 流程状态
     */
    @Excel(name = "流程状态", width = 15, dicCode = "rule_assess_pilot_process_status")
    @Dict(dicCode = "rule_assess_pilot_process_status")
    @Schema(description = "流程状态")
    private java.lang.String processStatus;

    /**
     * 是否关注
     */
    @Schema(description = "是否关注")
    @TableField(exist = false)
    private Boolean isFollow;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    @TableLogic
    private Integer delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;

    /**
     * 所属部门
     */
    @Schema(description = "所属部门")
    private String sysOrgCode;
}