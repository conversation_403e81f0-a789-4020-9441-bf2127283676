{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/anchor/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/anchor/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.RangeUtils');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const option = name => editor => editor.options.get(name);\n    const register$2 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('allow_html_in_named_anchor', {\n        processor: 'boolean',\n        default: false\n      });\n    };\n    const allowHtmlInNamedAnchor = option('allow_html_in_named_anchor');\n\n    const namedAnchorSelector = 'a:not([href])';\n    const isEmptyString = str => !str;\n    const getIdFromAnchor = elm => {\n      const id = elm.getAttribute('id') || elm.getAttribute('name');\n      return id || '';\n    };\n    const isAnchor = elm => elm.nodeName.toLowerCase() === 'a';\n    const isNamedAnchor = elm => isAnchor(elm) && !elm.getAttribute('href') && getIdFromAnchor(elm) !== '';\n    const isEmptyNamedAnchor = elm => isNamedAnchor(elm) && !elm.firstChild;\n\n    const removeEmptyNamedAnchorsInSelection = editor => {\n      const dom = editor.dom;\n      global$1(dom).walk(editor.selection.getRng(), nodes => {\n        global.each(nodes, node => {\n          if (isEmptyNamedAnchor(node)) {\n            dom.remove(node, false);\n          }\n        });\n      });\n    };\n    const isValidId = id => /^[A-Za-z][A-Za-z0-9\\-:._]*$/.test(id);\n    const getNamedAnchor = editor => editor.dom.getParent(editor.selection.getStart(), namedAnchorSelector);\n    const getId = editor => {\n      const anchor = getNamedAnchor(editor);\n      if (anchor) {\n        return getIdFromAnchor(anchor);\n      } else {\n        return '';\n      }\n    };\n    const createAnchor = (editor, id) => {\n      editor.undoManager.transact(() => {\n        if (!allowHtmlInNamedAnchor(editor)) {\n          editor.selection.collapse(true);\n        }\n        if (editor.selection.isCollapsed()) {\n          editor.insertContent(editor.dom.createHTML('a', { id }));\n        } else {\n          removeEmptyNamedAnchorsInSelection(editor);\n          editor.formatter.remove('namedAnchor', undefined, undefined, true);\n          editor.formatter.apply('namedAnchor', { value: id });\n          editor.addVisual();\n        }\n      });\n    };\n    const updateAnchor = (editor, id, anchorElement) => {\n      anchorElement.removeAttribute('name');\n      anchorElement.id = id;\n      editor.addVisual();\n      editor.undoManager.add();\n    };\n    const insert = (editor, id) => {\n      const anchor = getNamedAnchor(editor);\n      if (anchor) {\n        updateAnchor(editor, id, anchor);\n      } else {\n        createAnchor(editor, id);\n      }\n      editor.focus();\n    };\n\n    const insertAnchor = (editor, newId) => {\n      if (!isValidId(newId)) {\n        editor.windowManager.alert('ID should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.');\n        return false;\n      } else {\n        insert(editor, newId);\n        return true;\n      }\n    };\n    const open = editor => {\n      const currentId = getId(editor);\n      editor.windowManager.open({\n        title: 'Anchor',\n        size: 'normal',\n        body: {\n          type: 'panel',\n          items: [{\n              name: 'id',\n              type: 'input',\n              label: 'ID',\n              placeholder: 'example'\n            }]\n        },\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: { id: currentId },\n        onSubmit: api => {\n          if (insertAnchor(editor, api.getData().id)) {\n            api.close();\n          }\n        }\n      });\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('mceAnchor', () => {\n        open(editor);\n      });\n    };\n\n    const isNamedAnchorNode = node => isEmptyString(node.attr('href')) && !isEmptyString(node.attr('id') || node.attr('name'));\n    const isEmptyNamedAnchorNode = node => isNamedAnchorNode(node) && !node.firstChild;\n    const setContentEditable = state => nodes => {\n      for (let i = 0; i < nodes.length; i++) {\n        const node = nodes[i];\n        if (isEmptyNamedAnchorNode(node)) {\n          node.attr('contenteditable', state);\n        }\n      }\n    };\n    const setup = editor => {\n      editor.on('PreInit', () => {\n        editor.parser.addNodeFilter('a', setContentEditable('false'));\n        editor.serializer.addNodeFilter('a', setContentEditable(null));\n      });\n    };\n\n    const registerFormats = editor => {\n      editor.formatter.register('namedAnchor', {\n        inline: 'a',\n        selector: namedAnchorSelector,\n        remove: 'all',\n        split: true,\n        deep: true,\n        attributes: { id: '%value' },\n        onmatch: (node, _fmt, _itemName) => {\n          return isNamedAnchor(node);\n        }\n      });\n    };\n\n    const onSetupEditable = editor => api => {\n      const nodeChanged = () => {\n        api.setEnabled(editor.selection.isEditable());\n      };\n      editor.on('NodeChange', nodeChanged);\n      nodeChanged();\n      return () => {\n        editor.off('NodeChange', nodeChanged);\n      };\n    };\n    const register = editor => {\n      const onAction = () => editor.execCommand('mceAnchor');\n      editor.ui.registry.addToggleButton('anchor', {\n        icon: 'bookmark',\n        tooltip: 'Anchor',\n        onAction,\n        onSetup: buttonApi => {\n          const unbindSelectorChanged = editor.selection.selectorChangedWithUnbind('a:not([href])', buttonApi.setActive).unbind;\n          const unbindEditableChanged = onSetupEditable(editor)(buttonApi);\n          return () => {\n            unbindSelectorChanged();\n            unbindEditableChanged();\n          };\n        }\n      });\n      editor.ui.registry.addMenuItem('anchor', {\n        icon: 'bookmark',\n        text: 'Anchor...',\n        onAction,\n        onSetup: onSetupEditable(editor)\n      });\n    };\n\n    var Plugin = () => {\n      global$2.add('anchor', editor => {\n        register$2(editor);\n        setup(editor);\n        register$1(editor);\n        register(editor);\n        editor.on('PreInit', () => {\n          registerFormats(editor);\n        });\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"anchor\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/anchor')\n//   ES2015:\n//     import 'tinymce/plugins/anchor'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,wBAAwB;AAElE,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE5D,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,aAAa,YAAU;AAC3B,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,8BAA8B;AAAA,UAC3C,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,yBAAyB,OAAO,4BAA4B;AAElE,YAAM,sBAAsB;AAC5B,YAAM,gBAAgB,SAAO,CAAC;AAC9B,YAAM,kBAAkB,SAAO;AAC7B,cAAM,KAAK,IAAI,aAAa,IAAI,KAAK,IAAI,aAAa,MAAM;AAC5D,eAAO,MAAM;AAAA,MACf;AACA,YAAM,WAAW,SAAO,IAAI,SAAS,YAAY,MAAM;AACvD,YAAM,gBAAgB,SAAO,SAAS,GAAG,KAAK,CAAC,IAAI,aAAa,MAAM,KAAK,gBAAgB,GAAG,MAAM;AACpG,YAAM,qBAAqB,SAAO,cAAc,GAAG,KAAK,CAAC,IAAI;AAE7D,YAAM,qCAAqC,YAAU;AACnD,cAAM,MAAM,OAAO;AACnB,iBAAS,GAAG,EAAE,KAAK,OAAO,UAAU,OAAO,GAAG,WAAS;AACrD,iBAAO,KAAK,OAAO,UAAQ;AACzB,gBAAI,mBAAmB,IAAI,GAAG;AAC5B,kBAAI,OAAO,MAAM,KAAK;AAAA,YACxB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,YAAY,QAAM,8BAA8B,KAAK,EAAE;AAC7D,YAAM,iBAAiB,YAAU,OAAO,IAAI,UAAU,OAAO,UAAU,SAAS,GAAG,mBAAmB;AACtG,YAAM,QAAQ,YAAU;AACtB,cAAM,SAAS,eAAe,MAAM;AACpC,YAAI,QAAQ;AACV,iBAAO,gBAAgB,MAAM;AAAA,QAC/B,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,eAAe,CAAC,QAAQ,OAAO;AACnC,eAAO,YAAY,SAAS,MAAM;AAChC,cAAI,CAAC,uBAAuB,MAAM,GAAG;AACnC,mBAAO,UAAU,SAAS,IAAI;AAAA,UAChC;AACA,cAAI,OAAO,UAAU,YAAY,GAAG;AAClC,mBAAO,cAAc,OAAO,IAAI,WAAW,KAAK,EAAE,GAAG,CAAC,CAAC;AAAA,UACzD,OAAO;AACL,+CAAmC,MAAM;AACzC,mBAAO,UAAU,OAAO,eAAe,QAAW,QAAW,IAAI;AACjE,mBAAO,UAAU,MAAM,eAAe,EAAE,OAAO,GAAG,CAAC;AACnD,mBAAO,UAAU;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,eAAe,CAAC,QAAQ,IAAI,kBAAkB;AAClD,sBAAc,gBAAgB,MAAM;AACpC,sBAAc,KAAK;AACnB,eAAO,UAAU;AACjB,eAAO,YAAY,IAAI;AAAA,MACzB;AACA,YAAM,SAAS,CAAC,QAAQ,OAAO;AAC7B,cAAM,SAAS,eAAe,MAAM;AACpC,YAAI,QAAQ;AACV,uBAAa,QAAQ,IAAI,MAAM;AAAA,QACjC,OAAO;AACL,uBAAa,QAAQ,EAAE;AAAA,QACzB;AACA,eAAO,MAAM;AAAA,MACf;AAEA,YAAM,eAAe,CAAC,QAAQ,UAAU;AACtC,YAAI,CAAC,UAAU,KAAK,GAAG;AACrB,iBAAO,cAAc,MAAM,wGAAwG;AACnI,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,QAAQ,KAAK;AACpB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,OAAO,YAAU;AACrB,cAAM,YAAY,MAAM,MAAM;AAC9B,eAAO,cAAc,KAAK;AAAA,UACxB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO,CAAC;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,aAAa;AAAA,YACf,CAAC;AAAA,UACL;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA,aAAa,EAAE,IAAI,UAAU;AAAA,UAC7B,UAAU,SAAO;AACf,gBAAI,aAAa,QAAQ,IAAI,QAAQ,EAAE,EAAE,GAAG;AAC1C,kBAAI,MAAM;AAAA,YACZ;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,aAAa,YAAU;AAC3B,eAAO,WAAW,aAAa,MAAM;AACnC,eAAK,MAAM;AAAA,QACb,CAAC;AAAA,MACH;AAEA,YAAM,oBAAoB,UAAQ,cAAc,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,CAAC;AACzH,YAAM,yBAAyB,UAAQ,kBAAkB,IAAI,KAAK,CAAC,KAAK;AACxE,YAAM,qBAAqB,WAAS,WAAS;AAC3C,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAM,OAAO,MAAM,CAAC;AACpB,cAAI,uBAAuB,IAAI,GAAG;AAChC,iBAAK,KAAK,mBAAmB,KAAK;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ,YAAU;AACtB,eAAO,GAAG,WAAW,MAAM;AACzB,iBAAO,OAAO,cAAc,KAAK,mBAAmB,OAAO,CAAC;AAC5D,iBAAO,WAAW,cAAc,KAAK,mBAAmB,IAAI,CAAC;AAAA,QAC/D,CAAC;AAAA,MACH;AAEA,YAAM,kBAAkB,YAAU;AAChC,eAAO,UAAU,SAAS,eAAe;AAAA,UACvC,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,MAAM;AAAA,UACN,YAAY,EAAE,IAAI,SAAS;AAAA,UAC3B,SAAS,CAAC,MAAM,MAAM,cAAc;AAClC,mBAAO,cAAc,IAAI;AAAA,UAC3B;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,kBAAkB,YAAU,SAAO;AACvC,cAAM,cAAc,MAAM;AACxB,cAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,QAC9C;AACA,eAAO,GAAG,cAAc,WAAW;AACnC,oBAAY;AACZ,eAAO,MAAM;AACX,iBAAO,IAAI,cAAc,WAAW;AAAA,QACtC;AAAA,MACF;AACA,YAAM,WAAW,YAAU;AACzB,cAAM,WAAW,MAAM,OAAO,YAAY,WAAW;AACrD,eAAO,GAAG,SAAS,gBAAgB,UAAU;AAAA,UAC3C,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA,UACA,SAAS,eAAa;AACpB,kBAAM,wBAAwB,OAAO,UAAU,0BAA0B,iBAAiB,UAAU,SAAS,EAAE;AAC/G,kBAAM,wBAAwB,gBAAgB,MAAM,EAAE,SAAS;AAC/D,mBAAO,MAAM;AACX,oCAAsB;AACtB,oCAAsB;AAAA,YACxB;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAO,GAAG,SAAS,YAAY,UAAU;AAAA,UACvC,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,SAAS,gBAAgB,MAAM;AAAA,QACjC,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,UAAU,YAAU;AAC/B,qBAAW,MAAM;AACjB,gBAAM,MAAM;AACZ,qBAAW,MAAM;AACjB,mBAAS,MAAM;AACf,iBAAO,GAAG,WAAW,MAAM;AACzB,4BAAgB,MAAM;AAAA,UACxB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;AC/MH;", "names": []}