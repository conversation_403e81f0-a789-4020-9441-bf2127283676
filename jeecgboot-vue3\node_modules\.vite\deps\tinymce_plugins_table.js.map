{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/table/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/table/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType$1 = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const eq$1 = t => a => t === a;\n    const isString = isType$1('string');\n    const isArray = isType$1('array');\n    const isBoolean = isSimpleType('boolean');\n    const isUndefined = eq$1(undefined);\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n\n    const noop = () => {\n    };\n    const compose1 = (fbc, fab) => a => fbc(fab(a));\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    const identity = x => {\n      return x;\n    };\n    const tripleEquals = (a, b) => {\n      return a === b;\n    };\n    function curry(fn, ...initialArgs) {\n      return (...restArgs) => {\n        const all = initialArgs.concat(restArgs);\n        return fn.apply(null, all);\n      };\n    }\n    const call = f => {\n      f();\n    };\n    const never = constant(false);\n    const always = constant(true);\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const keys = Object.keys;\n    const hasOwnProperty = Object.hasOwnProperty;\n    const each$1 = (obj, f) => {\n      const props = keys(obj);\n      for (let k = 0, len = props.length; k < len; k++) {\n        const i = props[k];\n        const x = obj[i];\n        f(x, i);\n      }\n    };\n    const objAcc = r => (x, i) => {\n      r[i] = x;\n    };\n    const internalFilter = (obj, pred, onTrue, onFalse) => {\n      each$1(obj, (x, i) => {\n        (pred(x, i) ? onTrue : onFalse)(x, i);\n      });\n    };\n    const filter$1 = (obj, pred) => {\n      const t = {};\n      internalFilter(obj, pred, objAcc(t), noop);\n      return t;\n    };\n    const mapToArray = (obj, f) => {\n      const r = [];\n      each$1(obj, (value, name) => {\n        r.push(f(value, name));\n      });\n      return r;\n    };\n    const values = obj => {\n      return mapToArray(obj, identity);\n    };\n    const size = obj => {\n      return keys(obj).length;\n    };\n    const get$4 = (obj, key) => {\n      return has(obj, key) ? Optional.from(obj[key]) : Optional.none();\n    };\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n    const hasNonNullableKey = (obj, key) => has(obj, key) && obj[key] !== undefined && obj[key] !== null;\n    const isEmpty$1 = r => {\n      for (const x in r) {\n        if (hasOwnProperty.call(r, x)) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    const nativeIndexOf = Array.prototype.indexOf;\n    const nativePush = Array.prototype.push;\n    const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);\n    const contains = (xs, x) => rawIndexOf(xs, x) > -1;\n    const exists = (xs, pred) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          return true;\n        }\n      }\n      return false;\n    };\n    const range = (num, f) => {\n      const r = [];\n      for (let i = 0; i < num; i++) {\n        r.push(f(i));\n      }\n      return r;\n    };\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const eachr = (xs, f) => {\n      for (let i = xs.length - 1; i >= 0; i--) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const partition = (xs, pred) => {\n      const pass = [];\n      const fail = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        const arr = pred(x, i) ? pass : fail;\n        arr.push(x);\n      }\n      return {\n        pass,\n        fail\n      };\n    };\n    const filter = (xs, pred) => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n    const foldr = (xs, f, acc) => {\n      eachr(xs, (x, i) => {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    const foldl = (xs, f, acc) => {\n      each(xs, (x, i) => {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    const findUntil = (xs, pred, until) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          return Optional.some(x);\n        } else if (until(x, i)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    const find = (xs, pred) => {\n      return findUntil(xs, pred, never);\n    };\n    const flatten$1 = xs => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    const bind = (xs, f) => flatten$1(map(xs, f));\n    const forall = (xs, pred) => {\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        const x = xs[i];\n        if (pred(x, i) !== true) {\n          return false;\n        }\n      }\n      return true;\n    };\n    const mapToObject = (xs, f) => {\n      const r = {};\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        r[String(x)] = f(x, i);\n      }\n      return r;\n    };\n    const get$3 = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    const head = xs => get$3(xs, 0);\n    const last = xs => get$3(xs, xs.length - 1);\n    const findMap = (arr, f) => {\n      for (let i = 0; i < arr.length; i++) {\n        const r = f(arr[i], i);\n        if (r.isSome()) {\n          return r;\n        }\n      }\n      return Optional.none();\n    };\n\n    const COMMENT = 8;\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const fromHtml = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        const message = 'HTML does not have a single root node';\n        console.error(message, html);\n        throw new Error(message);\n      }\n      return fromDom$1(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n      const doc = scope || document;\n      const node = doc.createElement(tag);\n      return fromDom$1(node);\n    };\n    const fromText = (text, scope) => {\n      const doc = scope || document;\n      const node = doc.createTextNode(text);\n      return fromDom$1(node);\n    };\n    const fromDom$1 = node => {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom$1);\n    const SugarElement = {\n      fromHtml,\n      fromTag,\n      fromText,\n      fromDom: fromDom$1,\n      fromPoint\n    };\n\n    const is$2 = (element, selector) => {\n      const dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        const elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n    const bypassSelector = dom => dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT || dom.childElementCount === 0;\n    const all$1 = (selector, scope) => {\n      const base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? [] : map(base.querySelectorAll(selector), SugarElement.fromDom);\n    };\n    const one = (selector, scope) => {\n      const base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? Optional.none() : Optional.from(base.querySelector(selector)).map(SugarElement.fromDom);\n    };\n\n    const eq = (e1, e2) => e1.dom === e2.dom;\n    const is$1 = is$2;\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const name = element => {\n      const r = element.dom.nodeName;\n      return r.toLowerCase();\n    };\n    const type = element => element.dom.nodeType;\n    const isType = t => element => type(element) === t;\n    const isComment = element => type(element) === COMMENT || name(element) === '#comment';\n    const isElement = isType(ELEMENT);\n    const isText = isType(TEXT);\n    const isDocument = isType(DOCUMENT);\n    const isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n    const isTag = tag => e => isElement(e) && name(e) === tag;\n\n    const owner = element => SugarElement.fromDom(element.dom.ownerDocument);\n    const documentOrOwner = dos => isDocument(dos) ? dos : owner(dos);\n    const parent = element => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const parents = (element, isRoot) => {\n      const stop = isFunction(isRoot) ? isRoot : never;\n      let dom = element.dom;\n      const ret = [];\n      while (dom.parentNode !== null && dom.parentNode !== undefined) {\n        const rawParent = dom.parentNode;\n        const p = SugarElement.fromDom(rawParent);\n        ret.push(p);\n        if (stop(p) === true) {\n          break;\n        } else {\n          dom = rawParent;\n        }\n      }\n      return ret;\n    };\n    const prevSibling = element => Optional.from(element.dom.previousSibling).map(SugarElement.fromDom);\n    const nextSibling = element => Optional.from(element.dom.nextSibling).map(SugarElement.fromDom);\n    const children$3 = element => map(element.dom.childNodes, SugarElement.fromDom);\n    const child$3 = (element, index) => {\n      const cs = element.dom.childNodes;\n      return Optional.from(cs[index]).map(SugarElement.fromDom);\n    };\n    const firstChild = element => child$3(element, 0);\n\n    const isShadowRoot = dos => isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    const supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);\n    const getRootNode = supported ? e => SugarElement.fromDom(e.dom.getRootNode()) : documentOrOwner;\n    const getShadowRoot = e => {\n      const r = getRootNode(e);\n      return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    const getShadowHost = e => SugarElement.fromDom(e.dom.host);\n\n    const inBody = element => {\n      const dom = isText(element) ? element.dom.parentNode : element.dom;\n      if (dom === undefined || dom === null || dom.ownerDocument === null) {\n        return false;\n      }\n      const doc = dom.ownerDocument;\n      return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));\n    };\n\n    var ClosestOrAncestor = (is, ancestor, scope, a, isRoot) => {\n      if (is(scope, a)) {\n        return Optional.some(scope);\n      } else if (isFunction(isRoot) && isRoot(scope)) {\n        return Optional.none();\n      } else {\n        return ancestor(scope, a, isRoot);\n      }\n    };\n\n    const ancestor$1 = (scope, predicate, isRoot) => {\n      let element = scope.dom;\n      const stop = isFunction(isRoot) ? isRoot : never;\n      while (element.parentNode) {\n        element = element.parentNode;\n        const el = SugarElement.fromDom(element);\n        if (predicate(el)) {\n          return Optional.some(el);\n        } else if (stop(el)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    const closest$2 = (scope, predicate, isRoot) => {\n      const is = (s, test) => test(s);\n      return ClosestOrAncestor(is, ancestor$1, scope, predicate, isRoot);\n    };\n    const child$2 = (scope, predicate) => {\n      const pred = node => predicate(SugarElement.fromDom(node));\n      const result = find(scope.dom.childNodes, pred);\n      return result.map(SugarElement.fromDom);\n    };\n\n    const ancestor = (scope, selector, isRoot) => ancestor$1(scope, e => is$2(e, selector), isRoot);\n    const child$1 = (scope, selector) => child$2(scope, e => is$2(e, selector));\n    const descendant = (scope, selector) => one(selector, scope);\n    const closest$1 = (scope, selector, isRoot) => {\n      const is = (element, selector) => is$2(element, selector);\n      return ClosestOrAncestor(is, ancestor, scope, selector, isRoot);\n    };\n\n    const closest = target => closest$1(target, '[contenteditable]');\n    const isEditable = (element, assumeEditable = false) => {\n      if (inBody(element)) {\n        return element.dom.isContentEditable;\n      } else {\n        return closest(element).fold(constant(assumeEditable), editable => getRaw$1(editable) === 'true');\n      }\n    };\n    const getRaw$1 = element => element.dom.contentEditable;\n\n    const getNodeName = elm => elm.nodeName.toLowerCase();\n    const getBody = editor => SugarElement.fromDom(editor.getBody());\n    const getIsRoot = editor => element => eq(element, getBody(editor));\n    const removePxSuffix = size => size ? size.replace(/px$/, '') : '';\n    const addPxSuffix = size => /^\\d+(\\.\\d+)?$/.test(size) ? size + 'px' : size;\n    const getSelectionStart = editor => SugarElement.fromDom(editor.selection.getStart());\n    const getSelectionEnd = editor => SugarElement.fromDom(editor.selection.getEnd());\n    const isInEditableContext = cell => closest$2(cell, isTag('table')).forall(isEditable);\n\n    const children$2 = (scope, predicate) => filter(children$3(scope), predicate);\n    const descendants$1 = (scope, predicate) => {\n      let result = [];\n      each(children$3(scope), x => {\n        if (predicate(x)) {\n          result = result.concat([x]);\n        }\n        result = result.concat(descendants$1(x, predicate));\n      });\n      return result;\n    };\n\n    const children$1 = (scope, selector) => children$2(scope, e => is$2(e, selector));\n    const descendants = (scope, selector) => all$1(selector, scope);\n\n    const rawSet = (dom, key, value) => {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    const set$2 = (element, key, value) => {\n      rawSet(element.dom, key, value);\n    };\n    const setAll = (element, attrs) => {\n      const dom = element.dom;\n      each$1(attrs, (v, k) => {\n        rawSet(dom, k, v);\n      });\n    };\n    const get$2 = (element, key) => {\n      const v = element.dom.getAttribute(key);\n      return v === null ? undefined : v;\n    };\n    const getOpt = (element, key) => Optional.from(get$2(element, key));\n    const remove$2 = (element, key) => {\n      element.dom.removeAttribute(key);\n    };\n    const clone = element => foldl(element.dom.attributes, (acc, attr) => {\n      acc[attr.name] = attr.value;\n      return acc;\n    }, {});\n\n    const is = (lhs, rhs, comparator = tripleEquals) => lhs.exists(left => comparator(left, rhs));\n    const cat = arr => {\n      const r = [];\n      const push = x => {\n        r.push(x);\n      };\n      for (let i = 0; i < arr.length; i++) {\n        arr[i].each(push);\n      }\n      return r;\n    };\n    const lift2 = (oa, ob, f) => oa.isSome() && ob.isSome() ? Optional.some(f(oa.getOrDie(), ob.getOrDie())) : Optional.none();\n    const flatten = oot => oot.bind(identity);\n    const someIf = (b, a) => b ? Optional.some(a) : Optional.none();\n\n    const removeFromStart = (str, numChars) => {\n      return str.substring(numChars);\n    };\n\n    const checkRange = (str, substr, start) => substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    const removeLeading = (str, prefix) => {\n      return startsWith(str, prefix) ? removeFromStart(str, prefix.length) : str;\n    };\n    const startsWith = (str, prefix) => {\n      return checkRange(str, prefix, 0);\n    };\n    const blank = r => s => s.replace(r, '');\n    const trim = blank(/^\\s+|\\s+$/g);\n    const isNotEmpty = s => s.length > 0;\n    const isEmpty = s => !isNotEmpty(s);\n    const toInt = (value, radix = 10) => {\n      const num = parseInt(value, radix);\n      return isNaN(num) ? Optional.none() : Optional.some(num);\n    };\n    const toFloat = value => {\n      const num = parseFloat(value);\n      return isNaN(num) ? Optional.none() : Optional.some(num);\n    };\n\n    const isSupported = dom => dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n\n    const internalSet = (dom, property, value) => {\n      if (!isString(value)) {\n        console.error('Invalid call to CSS.set. Property ', property, ':: Value ', value, ':: Element ', dom);\n        throw new Error('CSS value must be a string: ' + value);\n      }\n      if (isSupported(dom)) {\n        dom.style.setProperty(property, value);\n      }\n    };\n    const internalRemove = (dom, property) => {\n      if (isSupported(dom)) {\n        dom.style.removeProperty(property);\n      }\n    };\n    const set$1 = (element, property, value) => {\n      const dom = element.dom;\n      internalSet(dom, property, value);\n    };\n    const get$1 = (element, property) => {\n      const dom = element.dom;\n      const styles = window.getComputedStyle(dom);\n      const r = styles.getPropertyValue(property);\n      return r === '' && !inBody(element) ? getUnsafeProperty(dom, property) : r;\n    };\n    const getUnsafeProperty = (dom, property) => isSupported(dom) ? dom.style.getPropertyValue(property) : '';\n    const getRaw = (element, property) => {\n      const dom = element.dom;\n      const raw = getUnsafeProperty(dom, property);\n      return Optional.from(raw).filter(r => r.length > 0);\n    };\n    const remove$1 = (element, property) => {\n      const dom = element.dom;\n      internalRemove(dom, property);\n      if (is(getOpt(element, 'style').map(trim), '')) {\n        remove$2(element, 'style');\n      }\n    };\n\n    const getAttrValue = (cell, name, fallback = 0) => getOpt(cell, name).map(value => parseInt(value, 10)).getOr(fallback);\n\n    const firstLayer = (scope, selector) => {\n      return filterFirstLayer(scope, selector, always);\n    };\n    const filterFirstLayer = (scope, selector, predicate) => {\n      return bind(children$3(scope), x => {\n        if (is$2(x, selector)) {\n          return predicate(x) ? [x] : [];\n        } else {\n          return filterFirstLayer(x, selector, predicate);\n        }\n      });\n    };\n\n    const validSectionList = [\n      'tfoot',\n      'thead',\n      'tbody',\n      'colgroup'\n    ];\n    const isValidSection = parentName => contains(validSectionList, parentName);\n    const grid = (rows, columns) => ({\n      rows,\n      columns\n    });\n    const detail = (element, rowspan, colspan) => ({\n      element,\n      rowspan,\n      colspan\n    });\n    const extended = (element, rowspan, colspan, row, column, isLocked) => ({\n      element,\n      rowspan,\n      colspan,\n      row,\n      column,\n      isLocked\n    });\n    const rowdetail = (element, cells, section) => ({\n      element,\n      cells,\n      section\n    });\n    const bounds = (startRow, startCol, finishRow, finishCol) => ({\n      startRow,\n      startCol,\n      finishRow,\n      finishCol\n    });\n    const columnext = (element, colspan, column) => ({\n      element,\n      colspan,\n      column\n    });\n    const colgroup = (element, columns) => ({\n      element,\n      columns\n    });\n\n    const lookup = (tags, element, isRoot = never) => {\n      if (isRoot(element)) {\n        return Optional.none();\n      }\n      if (contains(tags, name(element))) {\n        return Optional.some(element);\n      }\n      const isRootOrUpperTable = elm => is$2(elm, 'table') || isRoot(elm);\n      return ancestor(element, tags.join(','), isRootOrUpperTable);\n    };\n    const cell = (element, isRoot) => lookup([\n      'td',\n      'th'\n    ], element, isRoot);\n    const cells = ancestor => firstLayer(ancestor, 'th,td');\n    const columns = ancestor => {\n      if (is$2(ancestor, 'colgroup')) {\n        return children$1(ancestor, 'col');\n      } else {\n        return bind(columnGroups(ancestor), columnGroup => children$1(columnGroup, 'col'));\n      }\n    };\n    const table = (element, isRoot) => closest$1(element, 'table', isRoot);\n    const rows = ancestor => firstLayer(ancestor, 'tr');\n    const columnGroups = ancestor => table(ancestor).fold(constant([]), table => children$1(table, 'colgroup'));\n\n    const fromRowsOrColGroups = (elems, getSection) => map(elems, row => {\n      if (name(row) === 'colgroup') {\n        const cells = map(columns(row), column => {\n          const colspan = getAttrValue(column, 'span', 1);\n          return detail(column, 1, colspan);\n        });\n        return rowdetail(row, cells, 'colgroup');\n      } else {\n        const cells$1 = map(cells(row), cell => {\n          const rowspan = getAttrValue(cell, 'rowspan', 1);\n          const colspan = getAttrValue(cell, 'colspan', 1);\n          return detail(cell, rowspan, colspan);\n        });\n        return rowdetail(row, cells$1, getSection(row));\n      }\n    });\n    const getParentSection = group => parent(group).map(parent => {\n      const parentName = name(parent);\n      return isValidSection(parentName) ? parentName : 'tbody';\n    }).getOr('tbody');\n    const fromTable$1 = table => {\n      const rows$1 = rows(table);\n      const columnGroups$1 = columnGroups(table);\n      const elems = [\n        ...columnGroups$1,\n        ...rows$1\n      ];\n      return fromRowsOrColGroups(elems, getParentSection);\n    };\n\n    const LOCKED_COL_ATTR = 'data-snooker-locked-cols';\n    const getLockedColumnsFromTable = table => getOpt(table, LOCKED_COL_ATTR).bind(lockedColStr => Optional.from(lockedColStr.match(/\\d+/g))).map(lockedCols => mapToObject(lockedCols, always));\n\n    const key = (row, column) => {\n      return row + ',' + column;\n    };\n    const getAt = (warehouse, row, column) => Optional.from(warehouse.access[key(row, column)]);\n    const findItem = (warehouse, item, comparator) => {\n      const filtered = filterItems(warehouse, detail => {\n        return comparator(item, detail.element);\n      });\n      return filtered.length > 0 ? Optional.some(filtered[0]) : Optional.none();\n    };\n    const filterItems = (warehouse, predicate) => {\n      const all = bind(warehouse.all, r => {\n        return r.cells;\n      });\n      return filter(all, predicate);\n    };\n    const generateColumns = rowData => {\n      const columnsGroup = {};\n      let index = 0;\n      each(rowData.cells, column => {\n        const colspan = column.colspan;\n        range(colspan, columnIndex => {\n          const colIndex = index + columnIndex;\n          columnsGroup[colIndex] = columnext(column.element, colspan, colIndex);\n        });\n        index += colspan;\n      });\n      return columnsGroup;\n    };\n    const generate$1 = list => {\n      const access = {};\n      const cells = [];\n      const tableOpt = head(list).map(rowData => rowData.element).bind(table);\n      const lockedColumns = tableOpt.bind(getLockedColumnsFromTable).getOr({});\n      let maxRows = 0;\n      let maxColumns = 0;\n      let rowCount = 0;\n      const {\n        pass: colgroupRows,\n        fail: rows\n      } = partition(list, rowData => rowData.section === 'colgroup');\n      each(rows, rowData => {\n        const currentRow = [];\n        each(rowData.cells, rowCell => {\n          let start = 0;\n          while (access[key(rowCount, start)] !== undefined) {\n            start++;\n          }\n          const isLocked = hasNonNullableKey(lockedColumns, start.toString());\n          const current = extended(rowCell.element, rowCell.rowspan, rowCell.colspan, rowCount, start, isLocked);\n          for (let occupiedColumnPosition = 0; occupiedColumnPosition < rowCell.colspan; occupiedColumnPosition++) {\n            for (let occupiedRowPosition = 0; occupiedRowPosition < rowCell.rowspan; occupiedRowPosition++) {\n              const rowPosition = rowCount + occupiedRowPosition;\n              const columnPosition = start + occupiedColumnPosition;\n              const newpos = key(rowPosition, columnPosition);\n              access[newpos] = current;\n              maxColumns = Math.max(maxColumns, columnPosition + 1);\n            }\n          }\n          currentRow.push(current);\n        });\n        maxRows++;\n        cells.push(rowdetail(rowData.element, currentRow, rowData.section));\n        rowCount++;\n      });\n      const {columns, colgroups} = last(colgroupRows).map(rowData => {\n        const columns = generateColumns(rowData);\n        const colgroup$1 = colgroup(rowData.element, values(columns));\n        return {\n          colgroups: [colgroup$1],\n          columns\n        };\n      }).getOrThunk(() => ({\n        colgroups: [],\n        columns: {}\n      }));\n      const grid$1 = grid(maxRows, maxColumns);\n      return {\n        grid: grid$1,\n        access,\n        all: cells,\n        columns,\n        colgroups\n      };\n    };\n    const fromTable = table => {\n      const list = fromTable$1(table);\n      return generate$1(list);\n    };\n    const justCells = warehouse => bind(warehouse.all, w => w.cells);\n    const justColumns = warehouse => values(warehouse.columns);\n    const hasColumns = warehouse => keys(warehouse.columns).length > 0;\n    const getColumnAt = (warehouse, columnIndex) => Optional.from(warehouse.columns[columnIndex]);\n    const Warehouse = {\n      fromTable,\n      generate: generate$1,\n      getAt,\n      findItem,\n      filterItems,\n      justCells,\n      justColumns,\n      hasColumns,\n      getColumnAt\n    };\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const getTDTHOverallStyle = (dom, elm, name) => {\n      const cells = dom.select('td,th', elm);\n      let firstChildStyle;\n      for (let i = 0; i < cells.length; i++) {\n        const currentStyle = dom.getStyle(cells[i], name);\n        if (isUndefined(firstChildStyle)) {\n          firstChildStyle = currentStyle;\n        }\n        if (firstChildStyle !== currentStyle) {\n          return '';\n        }\n      }\n      return firstChildStyle;\n    };\n    const setAlign = (editor, elm, name) => {\n      global$2.each('left center right'.split(' '), align => {\n        if (align !== name) {\n          editor.formatter.remove('align' + align, {}, elm);\n        }\n      });\n      if (name) {\n        editor.formatter.apply('align' + name, {}, elm);\n      }\n    };\n    const setVAlign = (editor, elm, name) => {\n      global$2.each('top middle bottom'.split(' '), align => {\n        if (align !== name) {\n          editor.formatter.remove('valign' + align, {}, elm);\n        }\n      });\n      if (name) {\n        editor.formatter.apply('valign' + name, {}, elm);\n      }\n    };\n\n    const fireTableModified = (editor, table, data) => {\n      editor.dispatch('TableModified', {\n        ...data,\n        table\n      });\n    };\n\n    const toNumber = (px, fallback) => toFloat(px).getOr(fallback);\n    const getProp = (element, name, fallback) => toNumber(get$1(element, name), fallback);\n    const calcContentBoxSize = (element, size, upper, lower) => {\n      const paddingUpper = getProp(element, `padding-${ upper }`, 0);\n      const paddingLower = getProp(element, `padding-${ lower }`, 0);\n      const borderUpper = getProp(element, `border-${ upper }-width`, 0);\n      const borderLower = getProp(element, `border-${ lower }-width`, 0);\n      return size - paddingUpper - paddingLower - borderUpper - borderLower;\n    };\n    const getCalculatedWidth = (element, boxSizing) => {\n      const dom = element.dom;\n      const width = dom.getBoundingClientRect().width || dom.offsetWidth;\n      return boxSizing === 'border-box' ? width : calcContentBoxSize(element, width, 'left', 'right');\n    };\n    const getInnerWidth = element => getCalculatedWidth(element, 'content-box');\n\n    const getInner = getInnerWidth;\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    const defaultTableToolbar = 'tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol';\n    const defaultCellBorderWidths = range(5, i => {\n      const size = `${ i + 1 }px`;\n      return {\n        title: size,\n        value: size\n      };\n    });\n    const defaultCellBorderStyles = map([\n      'Solid',\n      'Dotted',\n      'Dashed',\n      'Double',\n      'Groove',\n      'Ridge',\n      'Inset',\n      'Outset',\n      'None',\n      'Hidden'\n    ], type => {\n      return {\n        title: type,\n        value: type.toLowerCase()\n      };\n    });\n    const defaultWidth = '100%';\n    const getPixelForcedWidth = editor => {\n      var _a;\n      const dom = editor.dom;\n      const parentBlock = (_a = dom.getParent(editor.selection.getStart(), dom.isBlock)) !== null && _a !== void 0 ? _a : editor.getBody();\n      return getInner(SugarElement.fromDom(parentBlock)) + 'px';\n    };\n    const determineDefaultStyles = (editor, defaultStyles) => {\n      if (isResponsiveForced(editor) || !shouldStyleWithCss(editor)) {\n        return defaultStyles;\n      } else if (isPixelsForced(editor)) {\n        return {\n          ...defaultStyles,\n          width: getPixelForcedWidth(editor)\n        };\n      } else {\n        return {\n          ...defaultStyles,\n          width: defaultWidth\n        };\n      }\n    };\n    const determineDefaultAttributes = (editor, defaultAttributes) => {\n      if (isResponsiveForced(editor) || shouldStyleWithCss(editor)) {\n        return defaultAttributes;\n      } else if (isPixelsForced(editor)) {\n        return {\n          ...defaultAttributes,\n          width: getPixelForcedWidth(editor)\n        };\n      } else {\n        return {\n          ...defaultAttributes,\n          width: defaultWidth\n        };\n      }\n    };\n    const option = name => editor => editor.options.get(name);\n    const register = editor => {\n      const registerOption = editor.options.register;\n      registerOption('table_border_widths', {\n        processor: 'object[]',\n        default: defaultCellBorderWidths\n      });\n      registerOption('table_border_styles', {\n        processor: 'object[]',\n        default: defaultCellBorderStyles\n      });\n      registerOption('table_cell_advtab', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('table_row_advtab', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('table_advtab', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('table_appearance_options', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('table_grid', {\n        processor: 'boolean',\n        default: !global$1.deviceType.isTouch()\n      });\n      registerOption('table_cell_class_list', {\n        processor: 'object[]',\n        default: []\n      });\n      registerOption('table_row_class_list', {\n        processor: 'object[]',\n        default: []\n      });\n      registerOption('table_class_list', {\n        processor: 'object[]',\n        default: []\n      });\n      registerOption('table_toolbar', {\n        processor: 'string',\n        default: defaultTableToolbar\n      });\n      registerOption('table_background_color_map', {\n        processor: 'object[]',\n        default: []\n      });\n      registerOption('table_border_color_map', {\n        processor: 'object[]',\n        default: []\n      });\n    };\n    const getTableSizingMode = option('table_sizing_mode');\n    const getTableBorderWidths = option('table_border_widths');\n    const getTableBorderStyles = option('table_border_styles');\n    const hasAdvancedCellTab = option('table_cell_advtab');\n    const hasAdvancedRowTab = option('table_row_advtab');\n    const hasAdvancedTableTab = option('table_advtab');\n    const hasAppearanceOptions = option('table_appearance_options');\n    const hasTableGrid = option('table_grid');\n    const shouldStyleWithCss = option('table_style_by_css');\n    const getCellClassList = option('table_cell_class_list');\n    const getRowClassList = option('table_row_class_list');\n    const getTableClassList = option('table_class_list');\n    const getToolbar = option('table_toolbar');\n    const getTableBackgroundColorMap = option('table_background_color_map');\n    const getTableBorderColorMap = option('table_border_color_map');\n    const isPixelsForced = editor => getTableSizingMode(editor) === 'fixed';\n    const isResponsiveForced = editor => getTableSizingMode(editor) === 'responsive';\n    const getDefaultStyles = editor => {\n      const options = editor.options;\n      const defaultStyles = options.get('table_default_styles');\n      return options.isSet('table_default_styles') ? defaultStyles : determineDefaultStyles(editor, defaultStyles);\n    };\n    const getDefaultAttributes = editor => {\n      const options = editor.options;\n      const defaultAttributes = options.get('table_default_attributes');\n      return options.isSet('table_default_attributes') ? defaultAttributes : determineDefaultAttributes(editor, defaultAttributes);\n    };\n\n    const isWithin = (bounds, detail) => {\n      return detail.column >= bounds.startCol && detail.column + detail.colspan - 1 <= bounds.finishCol && detail.row >= bounds.startRow && detail.row + detail.rowspan - 1 <= bounds.finishRow;\n    };\n    const isRectangular = (warehouse, bounds) => {\n      let isRect = true;\n      const detailIsWithin = curry(isWithin, bounds);\n      for (let i = bounds.startRow; i <= bounds.finishRow; i++) {\n        for (let j = bounds.startCol; j <= bounds.finishCol; j++) {\n          isRect = isRect && Warehouse.getAt(warehouse, i, j).exists(detailIsWithin);\n        }\n      }\n      return isRect ? Optional.some(bounds) : Optional.none();\n    };\n\n    const getBounds = (detailA, detailB) => {\n      return bounds(Math.min(detailA.row, detailB.row), Math.min(detailA.column, detailB.column), Math.max(detailA.row + detailA.rowspan - 1, detailB.row + detailB.rowspan - 1), Math.max(detailA.column + detailA.colspan - 1, detailB.column + detailB.colspan - 1));\n    };\n    const getAnyBox = (warehouse, startCell, finishCell) => {\n      const startCoords = Warehouse.findItem(warehouse, startCell, eq);\n      const finishCoords = Warehouse.findItem(warehouse, finishCell, eq);\n      return startCoords.bind(sc => {\n        return finishCoords.map(fc => {\n          return getBounds(sc, fc);\n        });\n      });\n    };\n    const getBox$1 = (warehouse, startCell, finishCell) => {\n      return getAnyBox(warehouse, startCell, finishCell).bind(bounds => {\n        return isRectangular(warehouse, bounds);\n      });\n    };\n\n    const getBox = (table, first, last) => {\n      const warehouse = getWarehouse(table);\n      return getBox$1(warehouse, first, last);\n    };\n    const getWarehouse = Warehouse.fromTable;\n\n    const before = (marker, element) => {\n      const parent$1 = parent(marker);\n      parent$1.each(v => {\n        v.dom.insertBefore(element.dom, marker.dom);\n      });\n    };\n    const after$1 = (marker, element) => {\n      const sibling = nextSibling(marker);\n      sibling.fold(() => {\n        const parent$1 = parent(marker);\n        parent$1.each(v => {\n          append$1(v, element);\n        });\n      }, v => {\n        before(v, element);\n      });\n    };\n    const prepend = (parent, element) => {\n      const firstChild$1 = firstChild(parent);\n      firstChild$1.fold(() => {\n        append$1(parent, element);\n      }, v => {\n        parent.dom.insertBefore(element.dom, v.dom);\n      });\n    };\n    const append$1 = (parent, element) => {\n      parent.dom.appendChild(element.dom);\n    };\n    const wrap = (element, wrapper) => {\n      before(element, wrapper);\n      append$1(wrapper, element);\n    };\n\n    const after = (marker, elements) => {\n      each(elements, (x, i) => {\n        const e = i === 0 ? marker : elements[i - 1];\n        after$1(e, x);\n      });\n    };\n    const append = (parent, elements) => {\n      each(elements, x => {\n        append$1(parent, x);\n      });\n    };\n\n    const remove = element => {\n      const dom = element.dom;\n      if (dom.parentNode !== null) {\n        dom.parentNode.removeChild(dom);\n      }\n    };\n    const unwrap = wrapper => {\n      const children = children$3(wrapper);\n      if (children.length > 0) {\n        after(wrapper, children);\n      }\n      remove(wrapper);\n    };\n\n    const NodeValue = (is, name) => {\n      const get = element => {\n        if (!is(element)) {\n          throw new Error('Can only get ' + name + ' value of a ' + name + ' node');\n        }\n        return getOption(element).getOr('');\n      };\n      const getOption = element => is(element) ? Optional.from(element.dom.nodeValue) : Optional.none();\n      const set = (element, value) => {\n        if (!is(element)) {\n          throw new Error('Can only set raw ' + name + ' value of a ' + name + ' node');\n        }\n        element.dom.nodeValue = value;\n      };\n      return {\n        get,\n        getOption,\n        set\n      };\n    };\n\n    const api = NodeValue(isText, 'text');\n    const get = element => api.get(element);\n    const set = (element, value) => api.set(element, value);\n\n    var TagBoundaries = [\n      'body',\n      'p',\n      'div',\n      'article',\n      'aside',\n      'figcaption',\n      'figure',\n      'footer',\n      'header',\n      'nav',\n      'section',\n      'ol',\n      'ul',\n      'li',\n      'table',\n      'thead',\n      'tbody',\n      'tfoot',\n      'caption',\n      'tr',\n      'td',\n      'th',\n      'h1',\n      'h2',\n      'h3',\n      'h4',\n      'h5',\n      'h6',\n      'blockquote',\n      'pre',\n      'address'\n    ];\n\n    var DomUniverse = () => {\n      const clone$1 = element => {\n        return SugarElement.fromDom(element.dom.cloneNode(false));\n      };\n      const document = element => documentOrOwner(element).dom;\n      const isBoundary = element => {\n        if (!isElement(element)) {\n          return false;\n        }\n        if (name(element) === 'body') {\n          return true;\n        }\n        return contains(TagBoundaries, name(element));\n      };\n      const isEmptyTag = element => {\n        if (!isElement(element)) {\n          return false;\n        }\n        return contains([\n          'br',\n          'img',\n          'hr',\n          'input'\n        ], name(element));\n      };\n      const isNonEditable = element => isElement(element) && get$2(element, 'contenteditable') === 'false';\n      const comparePosition = (element, other) => {\n        return element.dom.compareDocumentPosition(other.dom);\n      };\n      const copyAttributesTo = (source, destination) => {\n        const as = clone(source);\n        setAll(destination, as);\n      };\n      const isSpecial = element => {\n        const tag = name(element);\n        return contains([\n          'script',\n          'noscript',\n          'iframe',\n          'noframes',\n          'noembed',\n          'title',\n          'style',\n          'textarea',\n          'xmp'\n        ], tag);\n      };\n      const getLanguage = element => isElement(element) ? getOpt(element, 'lang') : Optional.none();\n      return {\n        up: constant({\n          selector: ancestor,\n          closest: closest$1,\n          predicate: ancestor$1,\n          all: parents\n        }),\n        down: constant({\n          selector: descendants,\n          predicate: descendants$1\n        }),\n        styles: constant({\n          get: get$1,\n          getRaw: getRaw,\n          set: set$1,\n          remove: remove$1\n        }),\n        attrs: constant({\n          get: get$2,\n          set: set$2,\n          remove: remove$2,\n          copyTo: copyAttributesTo\n        }),\n        insert: constant({\n          before: before,\n          after: after$1,\n          afterAll: after,\n          append: append$1,\n          appendAll: append,\n          prepend: prepend,\n          wrap: wrap\n        }),\n        remove: constant({\n          unwrap: unwrap,\n          remove: remove\n        }),\n        create: constant({\n          nu: SugarElement.fromTag,\n          clone: clone$1,\n          text: SugarElement.fromText\n        }),\n        query: constant({\n          comparePosition,\n          prevSibling: prevSibling,\n          nextSibling: nextSibling\n        }),\n        property: constant({\n          children: children$3,\n          name: name,\n          parent: parent,\n          document,\n          isText: isText,\n          isComment: isComment,\n          isElement: isElement,\n          isSpecial,\n          getLanguage,\n          getText: get,\n          setText: set,\n          isBoundary,\n          isEmptyTag,\n          isNonEditable\n        }),\n        eq: eq,\n        is: is$1\n      };\n    };\n\n    const all = (universe, look, elements, f) => {\n      const head = elements[0];\n      const tail = elements.slice(1);\n      return f(universe, look, head, tail);\n    };\n    const oneAll = (universe, look, elements) => {\n      return elements.length > 0 ? all(universe, look, elements, unsafeOne) : Optional.none();\n    };\n    const unsafeOne = (universe, look, head, tail) => {\n      const start = look(universe, head);\n      return foldr(tail, (b, a) => {\n        const current = look(universe, a);\n        return commonElement(universe, b, current);\n      }, start);\n    };\n    const commonElement = (universe, start, end) => {\n      return start.bind(s => {\n        return end.filter(curry(universe.eq, s));\n      });\n    };\n\n    const sharedOne$1 = oneAll;\n\n    const universe = DomUniverse();\n    const sharedOne = (look, elements) => {\n      return sharedOne$1(universe, (_universe, element) => {\n        return look(element);\n      }, elements);\n    };\n\n    const lookupTable = container => {\n      return ancestor(container, 'table');\n    };\n    const retrieve$1 = (container, selector) => {\n      const sels = descendants(container, selector);\n      return sels.length > 0 ? Optional.some(sels) : Optional.none();\n    };\n    const getEdges = (container, firstSelectedSelector, lastSelectedSelector) => {\n      return descendant(container, firstSelectedSelector).bind(first => {\n        return descendant(container, lastSelectedSelector).bind(last => {\n          return sharedOne(lookupTable, [\n            first,\n            last\n          ]).map(table => {\n            return {\n              first,\n              last,\n              table\n            };\n          });\n        });\n      });\n    };\n\n    const retrieve = (container, selector) => {\n      return retrieve$1(container, selector);\n    };\n    const retrieveBox = (container, firstSelectedSelector, lastSelectedSelector) => {\n      return getEdges(container, firstSelectedSelector, lastSelectedSelector).bind(edges => {\n        const isRoot = ancestor => {\n          return eq(container, ancestor);\n        };\n        const sectionSelector = 'thead,tfoot,tbody,table';\n        const firstAncestor = ancestor(edges.first, sectionSelector, isRoot);\n        const lastAncestor = ancestor(edges.last, sectionSelector, isRoot);\n        return firstAncestor.bind(fA => {\n          return lastAncestor.bind(lA => {\n            return eq(fA, lA) ? getBox(edges.table, edges.first, edges.last) : Optional.none();\n          });\n        });\n      });\n    };\n\n    const fromDom = nodes => map(nodes, SugarElement.fromDom);\n\n    const strSelected = 'data-mce-selected';\n    const strSelectedSelector = 'td[' + strSelected + '],th[' + strSelected + ']';\n    const strFirstSelected = 'data-mce-first-selected';\n    const strFirstSelectedSelector = 'td[' + strFirstSelected + '],th[' + strFirstSelected + ']';\n    const strLastSelected = 'data-mce-last-selected';\n    const strLastSelectedSelector = 'td[' + strLastSelected + '],th[' + strLastSelected + ']';\n    const ephemera = {\n      selected: strSelected,\n      selectedSelector: strSelectedSelector,\n      firstSelected: strFirstSelected,\n      firstSelectedSelector: strFirstSelectedSelector,\n      lastSelected: strLastSelected,\n      lastSelectedSelector: strLastSelectedSelector\n    };\n\n    const getSelectionCellFallback = element => table(element).bind(table => retrieve(table, ephemera.firstSelectedSelector)).fold(constant(element), cells => cells[0]);\n    const getSelectionFromSelector = selector => (initCell, isRoot) => {\n      const cellName = name(initCell);\n      const cell = cellName === 'col' || cellName === 'colgroup' ? getSelectionCellFallback(initCell) : initCell;\n      return closest$1(cell, selector, isRoot);\n    };\n    const getSelectionCellOrCaption = getSelectionFromSelector('th,td,caption');\n    const getSelectionCell = getSelectionFromSelector('th,td');\n    const getCellsFromSelection = editor => fromDom(editor.model.table.getSelectedCells());\n    const getRowsFromSelection = (selected, selector) => {\n      const cellOpt = getSelectionCell(selected);\n      const rowsOpt = cellOpt.bind(cell => table(cell)).map(table => rows(table));\n      return lift2(cellOpt, rowsOpt, (cell, rows) => filter(rows, row => exists(fromDom(row.dom.cells), rowCell => get$2(rowCell, selector) === '1' || eq(rowCell, cell)))).getOr([]);\n    };\n\n    const verticalAlignValues = [\n      {\n        text: 'None',\n        value: ''\n      },\n      {\n        text: 'Top',\n        value: 'top'\n      },\n      {\n        text: 'Middle',\n        value: 'middle'\n      },\n      {\n        text: 'Bottom',\n        value: 'bottom'\n      }\n    ];\n\n    const hexColour = value => ({ value: normalizeHex(value) });\n    const shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\n    const longformRegex = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i;\n    const isHexString = hex => shorthandRegex.test(hex) || longformRegex.test(hex);\n    const normalizeHex = hex => removeLeading(hex, '#').toUpperCase();\n    const fromString$1 = hex => isHexString(hex) ? Optional.some({ value: normalizeHex(hex) }) : Optional.none();\n    const toHex = component => {\n      const hex = component.toString(16);\n      return (hex.length === 1 ? '0' + hex : hex).toUpperCase();\n    };\n    const fromRgba = rgbaColour => {\n      const value = toHex(rgbaColour.red) + toHex(rgbaColour.green) + toHex(rgbaColour.blue);\n      return hexColour(value);\n    };\n\n    const rgbRegex = /^\\s*rgb\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)\\s*$/i;\n    const rgbaRegex = /^\\s*rgba\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d?(?:\\.\\d+)?)\\s*\\)\\s*$/i;\n    const rgbaColour = (red, green, blue, alpha) => ({\n      red,\n      green,\n      blue,\n      alpha\n    });\n    const fromStringValues = (red, green, blue, alpha) => {\n      const r = parseInt(red, 10);\n      const g = parseInt(green, 10);\n      const b = parseInt(blue, 10);\n      const a = parseFloat(alpha);\n      return rgbaColour(r, g, b, a);\n    };\n    const fromString = rgbaString => {\n      if (rgbaString === 'transparent') {\n        return Optional.some(rgbaColour(0, 0, 0, 0));\n      }\n      const rgbMatch = rgbRegex.exec(rgbaString);\n      if (rgbMatch !== null) {\n        return Optional.some(fromStringValues(rgbMatch[1], rgbMatch[2], rgbMatch[3], '1'));\n      }\n      const rgbaMatch = rgbaRegex.exec(rgbaString);\n      if (rgbaMatch !== null) {\n        return Optional.some(fromStringValues(rgbaMatch[1], rgbaMatch[2], rgbaMatch[3], rgbaMatch[4]));\n      }\n      return Optional.none();\n    };\n\n    const anyToHex = color => fromString$1(color).orThunk(() => fromString(color).map(fromRgba)).getOrThunk(() => {\n      const canvas = document.createElement('canvas');\n      canvas.height = 1;\n      canvas.width = 1;\n      const canvasContext = canvas.getContext('2d');\n      canvasContext.clearRect(0, 0, canvas.width, canvas.height);\n      canvasContext.fillStyle = '#FFFFFF';\n      canvasContext.fillStyle = color;\n      canvasContext.fillRect(0, 0, 1, 1);\n      const rgba = canvasContext.getImageData(0, 0, 1, 1).data;\n      const r = rgba[0];\n      const g = rgba[1];\n      const b = rgba[2];\n      const a = rgba[3];\n      return fromRgba(rgbaColour(r, g, b, a));\n    });\n    const rgbaToHexString = color => fromString(color).map(fromRgba).map(h => '#' + h.value).getOr(color);\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    const singleton = doRevoke => {\n      const subject = Cell(Optional.none());\n      const revoke = () => subject.get().each(doRevoke);\n      const clear = () => {\n        revoke();\n        subject.set(Optional.none());\n      };\n      const isSet = () => subject.get().isSome();\n      const get = () => subject.get();\n      const set = s => {\n        revoke();\n        subject.set(Optional.some(s));\n      };\n      return {\n        clear,\n        isSet,\n        get,\n        set\n      };\n    };\n    const unbindable = () => singleton(s => s.unbind());\n\n    const onSetupToggle = (editor, formatName, formatValue) => {\n      return api => {\n        const boundCallback = unbindable();\n        const isNone = isEmpty(formatValue);\n        const init = () => {\n          const selectedCells = getCellsFromSelection(editor);\n          const checkNode = cell => editor.formatter.match(formatName, { value: formatValue }, cell.dom, isNone);\n          if (isNone) {\n            api.setActive(!exists(selectedCells, checkNode));\n            boundCallback.set(editor.formatter.formatChanged(formatName, match => api.setActive(!match), true));\n          } else {\n            api.setActive(forall(selectedCells, checkNode));\n            boundCallback.set(editor.formatter.formatChanged(formatName, api.setActive, false, { value: formatValue }));\n          }\n        };\n        editor.initialized ? init() : editor.on('init', init);\n        return boundCallback.clear;\n      };\n    };\n    const isListGroup = item => hasNonNullableKey(item, 'menu');\n    const buildListItems = items => map(items, item => {\n      const text = item.text || item.title || '';\n      if (isListGroup(item)) {\n        return {\n          text,\n          items: buildListItems(item.menu)\n        };\n      } else {\n        return {\n          text,\n          value: item.value\n        };\n      }\n    });\n    const buildMenuItems = (editor, items, format, onAction) => map(items, item => {\n      const text = item.text || item.title;\n      if (isListGroup(item)) {\n        return {\n          type: 'nestedmenuitem',\n          text,\n          getSubmenuItems: () => buildMenuItems(editor, item.menu, format, onAction)\n        };\n      } else {\n        return {\n          text,\n          type: 'togglemenuitem',\n          onAction: () => onAction(item.value),\n          onSetup: onSetupToggle(editor, format, item.value)\n        };\n      }\n    });\n    const applyTableCellStyle = (editor, style) => value => {\n      editor.execCommand('mceTableApplyCellStyle', false, { [style]: value });\n    };\n    const filterNoneItem = list => bind(list, item => {\n      if (isListGroup(item)) {\n        return [{\n            ...item,\n            menu: filterNoneItem(item.menu)\n          }];\n      } else {\n        return isNotEmpty(item.value) ? [item] : [];\n      }\n    });\n    const generateMenuItemsCallback = (editor, items, format, onAction) => callback => callback(buildMenuItems(editor, items, format, onAction));\n    const buildColorMenu = (editor, colorList, style) => {\n      const colorMap = map(colorList, entry => ({\n        text: entry.title,\n        value: '#' + anyToHex(entry.value).value,\n        type: 'choiceitem'\n      }));\n      return [{\n          type: 'fancymenuitem',\n          fancytype: 'colorswatch',\n          initData: {\n            colors: colorMap.length > 0 ? colorMap : undefined,\n            allowCustomColors: false\n          },\n          onAction: data => {\n            const value = data.value === 'remove' ? '' : data.value;\n            editor.execCommand('mceTableApplyCellStyle', false, { [style]: value });\n          }\n        }];\n    };\n    const changeRowHeader = editor => () => {\n      const currentType = editor.queryCommandValue('mceTableRowType');\n      const newType = currentType === 'header' ? 'body' : 'header';\n      editor.execCommand('mceTableRowType', false, { type: newType });\n    };\n    const changeColumnHeader = editor => () => {\n      const currentType = editor.queryCommandValue('mceTableColType');\n      const newType = currentType === 'th' ? 'td' : 'th';\n      editor.execCommand('mceTableColType', false, { type: newType });\n    };\n\n    const getClassList$1 = editor => {\n      const classes = buildListItems(getCellClassList(editor));\n      if (classes.length > 0) {\n        return Optional.some({\n          name: 'class',\n          type: 'listbox',\n          label: 'Class',\n          items: classes\n        });\n      }\n      return Optional.none();\n    };\n    const children = [\n      {\n        name: 'width',\n        type: 'input',\n        label: 'Width'\n      },\n      {\n        name: 'height',\n        type: 'input',\n        label: 'Height'\n      },\n      {\n        name: 'celltype',\n        type: 'listbox',\n        label: 'Cell type',\n        items: [\n          {\n            text: 'Cell',\n            value: 'td'\n          },\n          {\n            text: 'Header cell',\n            value: 'th'\n          }\n        ]\n      },\n      {\n        name: 'scope',\n        type: 'listbox',\n        label: 'Scope',\n        items: [\n          {\n            text: 'None',\n            value: ''\n          },\n          {\n            text: 'Row',\n            value: 'row'\n          },\n          {\n            text: 'Column',\n            value: 'col'\n          },\n          {\n            text: 'Row group',\n            value: 'rowgroup'\n          },\n          {\n            text: 'Column group',\n            value: 'colgroup'\n          }\n        ]\n      },\n      {\n        name: 'halign',\n        type: 'listbox',\n        label: 'Horizontal align',\n        items: [\n          {\n            text: 'None',\n            value: ''\n          },\n          {\n            text: 'Left',\n            value: 'left'\n          },\n          {\n            text: 'Center',\n            value: 'center'\n          },\n          {\n            text: 'Right',\n            value: 'right'\n          }\n        ]\n      },\n      {\n        name: 'valign',\n        type: 'listbox',\n        label: 'Vertical align',\n        items: verticalAlignValues\n      }\n    ];\n    const getItems$2 = editor => children.concat(getClassList$1(editor).toArray());\n\n    const getAdvancedTab = (editor, dialogName) => {\n      const emptyBorderStyle = [{\n          text: 'Select...',\n          value: ''\n        }];\n      const advTabItems = [\n        {\n          name: 'borderstyle',\n          type: 'listbox',\n          label: 'Border style',\n          items: emptyBorderStyle.concat(buildListItems(getTableBorderStyles(editor)))\n        },\n        {\n          name: 'bordercolor',\n          type: 'colorinput',\n          label: 'Border color'\n        },\n        {\n          name: 'backgroundcolor',\n          type: 'colorinput',\n          label: 'Background color'\n        }\n      ];\n      const borderWidth = {\n        name: 'borderwidth',\n        type: 'input',\n        label: 'Border width'\n      };\n      const items = dialogName === 'cell' ? [borderWidth].concat(advTabItems) : advTabItems;\n      return {\n        title: 'Advanced',\n        name: 'advanced',\n        items\n      };\n    };\n\n    const normal = (editor, element) => {\n      const dom = editor.dom;\n      const setAttrib = (attr, value) => {\n        dom.setAttrib(element, attr, value);\n      };\n      const setStyle = (prop, value) => {\n        dom.setStyle(element, prop, value);\n      };\n      const setFormat = (formatName, value) => {\n        if (value === '') {\n          editor.formatter.remove(formatName, { value: null }, element, true);\n        } else {\n          editor.formatter.apply(formatName, { value }, element);\n        }\n      };\n      return {\n        setAttrib,\n        setStyle,\n        setFormat\n      };\n    };\n    const DomModifier = { normal };\n\n    const isHeaderCell = isTag('th');\n    const getRowHeaderType = (isHeaderRow, isHeaderCells) => {\n      if (isHeaderRow && isHeaderCells) {\n        return 'sectionCells';\n      } else if (isHeaderRow) {\n        return 'section';\n      } else {\n        return 'cells';\n      }\n    };\n    const getRowType$1 = row => {\n      const isHeaderRow = row.section === 'thead';\n      const isHeaderCells = is(findCommonCellType(row.cells), 'th');\n      if (row.section === 'tfoot') {\n        return { type: 'footer' };\n      } else if (isHeaderRow || isHeaderCells) {\n        return {\n          type: 'header',\n          subType: getRowHeaderType(isHeaderRow, isHeaderCells)\n        };\n      } else {\n        return { type: 'body' };\n      }\n    };\n    const findCommonCellType = cells => {\n      const headerCells = filter(cells, cell => isHeaderCell(cell.element));\n      if (headerCells.length === 0) {\n        return Optional.some('td');\n      } else if (headerCells.length === cells.length) {\n        return Optional.some('th');\n      } else {\n        return Optional.none();\n      }\n    };\n    const findCommonRowType = rows => {\n      const rowTypes = map(rows, row => getRowType$1(row).type);\n      const hasHeader = contains(rowTypes, 'header');\n      const hasFooter = contains(rowTypes, 'footer');\n      if (!hasHeader && !hasFooter) {\n        return Optional.some('body');\n      } else {\n        const hasBody = contains(rowTypes, 'body');\n        if (hasHeader && !hasBody && !hasFooter) {\n          return Optional.some('header');\n        } else if (!hasHeader && !hasBody && hasFooter) {\n          return Optional.some('footer');\n        } else {\n          return Optional.none();\n        }\n      }\n    };\n\n    const cached = f => {\n      let called = false;\n      let r;\n      return (...args) => {\n        if (!called) {\n          called = true;\n          r = f.apply(null, args);\n        }\n        return r;\n      };\n    };\n\n    const findInWarehouse = (warehouse, element) => findMap(warehouse.all, r => find(r.cells, e => eq(element, e.element)));\n    const extractCells = (warehouse, target, predicate) => {\n      const details = map(target.selection, cell$1 => {\n        return cell(cell$1).bind(lc => findInWarehouse(warehouse, lc)).filter(predicate);\n      });\n      const cells = cat(details);\n      return someIf(cells.length > 0, cells);\n    };\n    const onMergable = (_warehouse, target) => target.mergable;\n    const onUnmergable = (_warehouse, target) => target.unmergable;\n    const onCells = (warehouse, target) => extractCells(warehouse, target, always);\n    const isUnlockedTableCell = (warehouse, cell) => findInWarehouse(warehouse, cell).exists(detail => !detail.isLocked);\n    const allUnlocked = (warehouse, cells) => forall(cells, cell => isUnlockedTableCell(warehouse, cell));\n    const onUnlockedMergable = (warehouse, target) => onMergable(warehouse, target).filter(mergeable => allUnlocked(warehouse, mergeable.cells));\n    const onUnlockedUnmergable = (warehouse, target) => onUnmergable(warehouse, target).filter(cells => allUnlocked(warehouse, cells));\n\n    const generate = cases => {\n      if (!isArray(cases)) {\n        throw new Error('cases must be an array');\n      }\n      if (cases.length === 0) {\n        throw new Error('there must be at least one case');\n      }\n      const constructors = [];\n      const adt = {};\n      each(cases, (acase, count) => {\n        const keys$1 = keys(acase);\n        if (keys$1.length !== 1) {\n          throw new Error('one and only one name per case');\n        }\n        const key = keys$1[0];\n        const value = acase[key];\n        if (adt[key] !== undefined) {\n          throw new Error('duplicate key detected:' + key);\n        } else if (key === 'cata') {\n          throw new Error('cannot have a case named cata (sorry)');\n        } else if (!isArray(value)) {\n          throw new Error('case arguments must be an array');\n        }\n        constructors.push(key);\n        adt[key] = (...args) => {\n          const argLength = args.length;\n          if (argLength !== value.length) {\n            throw new Error('Wrong number of arguments to case ' + key + '. Expected ' + value.length + ' (' + value + '), got ' + argLength);\n          }\n          const match = branches => {\n            const branchKeys = keys(branches);\n            if (constructors.length !== branchKeys.length) {\n              throw new Error('Wrong number of arguments to match. Expected: ' + constructors.join(',') + '\\nActual: ' + branchKeys.join(','));\n            }\n            const allReqd = forall(constructors, reqKey => {\n              return contains(branchKeys, reqKey);\n            });\n            if (!allReqd) {\n              throw new Error('Not all branches were specified when using match. Specified: ' + branchKeys.join(', ') + '\\nRequired: ' + constructors.join(', '));\n            }\n            return branches[key].apply(null, args);\n          };\n          return {\n            fold: (...foldArgs) => {\n              if (foldArgs.length !== cases.length) {\n                throw new Error('Wrong number of arguments to fold. Expected ' + cases.length + ', got ' + foldArgs.length);\n              }\n              const target = foldArgs[count];\n              return target.apply(null, args);\n            },\n            match,\n            log: label => {\n              console.log(label, {\n                constructors,\n                constructor: key,\n                params: args\n              });\n            }\n          };\n        };\n      });\n      return adt;\n    };\n    const Adt = { generate };\n\n    const adt = Adt.generate([\n      { none: [] },\n      { only: ['index'] },\n      {\n        left: [\n          'index',\n          'next'\n        ]\n      },\n      {\n        middle: [\n          'prev',\n          'index',\n          'next'\n        ]\n      },\n      {\n        right: [\n          'prev',\n          'index'\n        ]\n      }\n    ]);\n    ({ ...adt });\n\n    const opGetRowsType = (table, target) => {\n      const house = Warehouse.fromTable(table);\n      const details = onCells(house, target);\n      return details.bind(selectedCells => {\n        const lastSelectedCell = selectedCells[selectedCells.length - 1];\n        const minRowRange = selectedCells[0].row;\n        const maxRowRange = lastSelectedCell.row + lastSelectedCell.rowspan;\n        const selectedRows = house.all.slice(minRowRange, maxRowRange);\n        return findCommonRowType(selectedRows);\n      }).getOr('');\n    };\n    const getRowsType = opGetRowsType;\n\n    const rgbToHex = value => startsWith(value, 'rgb') ? rgbaToHexString(value) : value;\n    const extractAdvancedStyles = elm => {\n      const element = SugarElement.fromDom(elm);\n      return {\n        borderwidth: getRaw(element, 'border-width').getOr(''),\n        borderstyle: getRaw(element, 'border-style').getOr(''),\n        bordercolor: getRaw(element, 'border-color').map(rgbToHex).getOr(''),\n        backgroundcolor: getRaw(element, 'background-color').map(rgbToHex).getOr('')\n      };\n    };\n    const getSharedValues = data => {\n      const baseData = data[0];\n      const comparisonData = data.slice(1);\n      each(comparisonData, items => {\n        each(keys(baseData), key => {\n          each$1(items, (itemValue, itemKey) => {\n            const comparisonValue = baseData[key];\n            if (comparisonValue !== '' && key === itemKey) {\n              if (comparisonValue !== itemValue) {\n                baseData[key] = '';\n              }\n            }\n          });\n        });\n      });\n      return baseData;\n    };\n    const getAlignment = (formats, formatName, editor, elm) => find(formats, name => !isUndefined(editor.formatter.matchNode(elm, formatName + name))).getOr('');\n    const getHAlignment = curry(getAlignment, [\n      'left',\n      'center',\n      'right'\n    ], 'align');\n    const getVAlignment = curry(getAlignment, [\n      'top',\n      'middle',\n      'bottom'\n    ], 'valign');\n    const extractDataFromSettings = (editor, hasAdvTableTab) => {\n      const style = getDefaultStyles(editor);\n      const attrs = getDefaultAttributes(editor);\n      const extractAdvancedStyleData = () => ({\n        borderstyle: get$4(style, 'border-style').getOr(''),\n        bordercolor: rgbToHex(get$4(style, 'border-color').getOr('')),\n        backgroundcolor: rgbToHex(get$4(style, 'background-color').getOr(''))\n      });\n      const defaultData = {\n        height: '',\n        width: '100%',\n        cellspacing: '',\n        cellpadding: '',\n        caption: false,\n        class: '',\n        align: '',\n        border: ''\n      };\n      const getBorder = () => {\n        const borderWidth = style['border-width'];\n        if (shouldStyleWithCss(editor) && borderWidth) {\n          return { border: borderWidth };\n        }\n        return get$4(attrs, 'border').fold(() => ({}), border => ({ border }));\n      };\n      const advStyle = hasAdvTableTab ? extractAdvancedStyleData() : {};\n      const getCellPaddingCellSpacing = () => {\n        const spacing = get$4(style, 'border-spacing').or(get$4(attrs, 'cellspacing')).fold(() => ({}), cellspacing => ({ cellspacing }));\n        const padding = get$4(style, 'border-padding').or(get$4(attrs, 'cellpadding')).fold(() => ({}), cellpadding => ({ cellpadding }));\n        return {\n          ...spacing,\n          ...padding\n        };\n      };\n      const data = {\n        ...defaultData,\n        ...style,\n        ...attrs,\n        ...advStyle,\n        ...getBorder(),\n        ...getCellPaddingCellSpacing()\n      };\n      return data;\n    };\n    const getRowType = elm => table(SugarElement.fromDom(elm)).map(table => {\n      const target = { selection: fromDom(elm.cells) };\n      return getRowsType(table, target);\n    }).getOr('');\n    const extractDataFromTableElement = (editor, elm, hasAdvTableTab) => {\n      const getBorder = (dom, elm) => {\n        const optBorderWidth = getRaw(SugarElement.fromDom(elm), 'border-width');\n        if (shouldStyleWithCss(editor) && optBorderWidth.isSome()) {\n          return optBorderWidth.getOr('');\n        }\n        return dom.getAttrib(elm, 'border') || getTDTHOverallStyle(editor.dom, elm, 'border-width') || getTDTHOverallStyle(editor.dom, elm, 'border') || '';\n      };\n      const dom = editor.dom;\n      const cellspacing = shouldStyleWithCss(editor) ? dom.getStyle(elm, 'border-spacing') || dom.getAttrib(elm, 'cellspacing') : dom.getAttrib(elm, 'cellspacing') || dom.getStyle(elm, 'border-spacing');\n      const cellpadding = shouldStyleWithCss(editor) ? getTDTHOverallStyle(dom, elm, 'padding') || dom.getAttrib(elm, 'cellpadding') : dom.getAttrib(elm, 'cellpadding') || getTDTHOverallStyle(dom, elm, 'padding');\n      return {\n        width: dom.getStyle(elm, 'width') || dom.getAttrib(elm, 'width'),\n        height: dom.getStyle(elm, 'height') || dom.getAttrib(elm, 'height'),\n        cellspacing: cellspacing !== null && cellspacing !== void 0 ? cellspacing : '',\n        cellpadding: cellpadding !== null && cellpadding !== void 0 ? cellpadding : '',\n        border: getBorder(dom, elm),\n        caption: !!dom.select('caption', elm)[0],\n        class: dom.getAttrib(elm, 'class', ''),\n        align: getHAlignment(editor, elm),\n        ...hasAdvTableTab ? extractAdvancedStyles(elm) : {}\n      };\n    };\n    const extractDataFromRowElement = (editor, elm, hasAdvancedRowTab) => {\n      const dom = editor.dom;\n      return {\n        height: dom.getStyle(elm, 'height') || dom.getAttrib(elm, 'height'),\n        class: dom.getAttrib(elm, 'class', ''),\n        type: getRowType(elm),\n        align: getHAlignment(editor, elm),\n        ...hasAdvancedRowTab ? extractAdvancedStyles(elm) : {}\n      };\n    };\n    const extractDataFromCellElement = (editor, cell, hasAdvancedCellTab, column) => {\n      const dom = editor.dom;\n      const colElm = column.getOr(cell);\n      const getStyle = (element, style) => dom.getStyle(element, style) || dom.getAttrib(element, style);\n      return {\n        width: getStyle(colElm, 'width'),\n        height: getStyle(cell, 'height'),\n        scope: dom.getAttrib(cell, 'scope'),\n        celltype: getNodeName(cell),\n        class: dom.getAttrib(cell, 'class', ''),\n        halign: getHAlignment(editor, cell),\n        valign: getVAlignment(editor, cell),\n        ...hasAdvancedCellTab ? extractAdvancedStyles(cell) : {}\n      };\n    };\n\n    const getSelectedCells = (table, cells) => {\n      const warehouse = Warehouse.fromTable(table);\n      const allCells = Warehouse.justCells(warehouse);\n      const filtered = filter(allCells, cellA => exists(cells, cellB => eq(cellA.element, cellB)));\n      return map(filtered, cell => ({\n        element: cell.element.dom,\n        column: Warehouse.getColumnAt(warehouse, cell.column).map(col => col.element.dom)\n      }));\n    };\n    const updateSimpleProps$1 = (modifier, colModifier, data, shouldUpdate) => {\n      if (shouldUpdate('scope')) {\n        modifier.setAttrib('scope', data.scope);\n      }\n      if (shouldUpdate('class')) {\n        modifier.setAttrib('class', data.class);\n      }\n      if (shouldUpdate('height')) {\n        modifier.setStyle('height', addPxSuffix(data.height));\n      }\n      if (shouldUpdate('width')) {\n        colModifier.setStyle('width', addPxSuffix(data.width));\n      }\n    };\n    const updateAdvancedProps$1 = (modifier, data, shouldUpdate) => {\n      if (shouldUpdate('backgroundcolor')) {\n        modifier.setFormat('tablecellbackgroundcolor', data.backgroundcolor);\n      }\n      if (shouldUpdate('bordercolor')) {\n        modifier.setFormat('tablecellbordercolor', data.bordercolor);\n      }\n      if (shouldUpdate('borderstyle')) {\n        modifier.setFormat('tablecellborderstyle', data.borderstyle);\n      }\n      if (shouldUpdate('borderwidth')) {\n        modifier.setFormat('tablecellborderwidth', addPxSuffix(data.borderwidth));\n      }\n    };\n    const applyStyleData$1 = (editor, cells, data, wasChanged) => {\n      const isSingleCell = cells.length === 1;\n      each(cells, item => {\n        const cellElm = item.element;\n        const shouldOverrideCurrentValue = isSingleCell ? always : wasChanged;\n        const modifier = DomModifier.normal(editor, cellElm);\n        const colModifier = item.column.map(col => DomModifier.normal(editor, col)).getOr(modifier);\n        updateSimpleProps$1(modifier, colModifier, data, shouldOverrideCurrentValue);\n        if (hasAdvancedCellTab(editor)) {\n          updateAdvancedProps$1(modifier, data, shouldOverrideCurrentValue);\n        }\n        if (wasChanged('halign')) {\n          setAlign(editor, cellElm, data.halign);\n        }\n        if (wasChanged('valign')) {\n          setVAlign(editor, cellElm, data.valign);\n        }\n      });\n    };\n    const applyStructureData$1 = (editor, data) => {\n      editor.execCommand('mceTableCellType', false, {\n        type: data.celltype,\n        no_events: true\n      });\n    };\n    const applyCellData = (editor, cells, oldData, data) => {\n      const modifiedData = filter$1(data, (value, key) => oldData[key] !== value);\n      if (size(modifiedData) > 0 && cells.length >= 1) {\n        table(cells[0]).each(table => {\n          const selectedCells = getSelectedCells(table, cells);\n          const styleModified = size(filter$1(modifiedData, (_value, key) => key !== 'scope' && key !== 'celltype')) > 0;\n          const structureModified = has(modifiedData, 'celltype');\n          if (styleModified || has(modifiedData, 'scope')) {\n            applyStyleData$1(editor, selectedCells, data, curry(has, modifiedData));\n          }\n          if (structureModified) {\n            applyStructureData$1(editor, data);\n          }\n          fireTableModified(editor, table.dom, {\n            structure: structureModified,\n            style: styleModified\n          });\n        });\n      }\n    };\n    const onSubmitCellForm = (editor, cells, oldData, api) => {\n      const data = api.getData();\n      api.close();\n      editor.undoManager.transact(() => {\n        applyCellData(editor, cells, oldData, data);\n        editor.focus();\n      });\n    };\n    const getData$1 = (editor, cells) => {\n      const cellsData = table(cells[0]).map(table => map(getSelectedCells(table, cells), item => extractDataFromCellElement(editor, item.element, hasAdvancedCellTab(editor), item.column)));\n      return getSharedValues(cellsData.getOrDie());\n    };\n    const open$2 = editor => {\n      const cells = getCellsFromSelection(editor);\n      if (cells.length === 0) {\n        return;\n      }\n      const data = getData$1(editor, cells);\n      const dialogTabPanel = {\n        type: 'tabpanel',\n        tabs: [\n          {\n            title: 'General',\n            name: 'general',\n            items: getItems$2(editor)\n          },\n          getAdvancedTab(editor, 'cell')\n        ]\n      };\n      const dialogPanel = {\n        type: 'panel',\n        items: [{\n            type: 'grid',\n            columns: 2,\n            items: getItems$2(editor)\n          }]\n      };\n      editor.windowManager.open({\n        title: 'Cell Properties',\n        size: 'normal',\n        body: hasAdvancedCellTab(editor) ? dialogTabPanel : dialogPanel,\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: data,\n        onSubmit: curry(onSubmitCellForm, editor, cells, data)\n      });\n    };\n\n    const getClassList = editor => {\n      const classes = buildListItems(getRowClassList(editor));\n      if (classes.length > 0) {\n        return Optional.some({\n          name: 'class',\n          type: 'listbox',\n          label: 'Class',\n          items: classes\n        });\n      }\n      return Optional.none();\n    };\n    const formChildren = [\n      {\n        type: 'listbox',\n        name: 'type',\n        label: 'Row type',\n        items: [\n          {\n            text: 'Header',\n            value: 'header'\n          },\n          {\n            text: 'Body',\n            value: 'body'\n          },\n          {\n            text: 'Footer',\n            value: 'footer'\n          }\n        ]\n      },\n      {\n        type: 'listbox',\n        name: 'align',\n        label: 'Alignment',\n        items: [\n          {\n            text: 'None',\n            value: ''\n          },\n          {\n            text: 'Left',\n            value: 'left'\n          },\n          {\n            text: 'Center',\n            value: 'center'\n          },\n          {\n            text: 'Right',\n            value: 'right'\n          }\n        ]\n      },\n      {\n        label: 'Height',\n        name: 'height',\n        type: 'input'\n      }\n    ];\n    const getItems$1 = editor => formChildren.concat(getClassList(editor).toArray());\n\n    const updateSimpleProps = (modifier, data, shouldUpdate) => {\n      if (shouldUpdate('class')) {\n        modifier.setAttrib('class', data.class);\n      }\n      if (shouldUpdate('height')) {\n        modifier.setStyle('height', addPxSuffix(data.height));\n      }\n    };\n    const updateAdvancedProps = (modifier, data, shouldUpdate) => {\n      if (shouldUpdate('backgroundcolor')) {\n        modifier.setStyle('background-color', data.backgroundcolor);\n      }\n      if (shouldUpdate('bordercolor')) {\n        modifier.setStyle('border-color', data.bordercolor);\n      }\n      if (shouldUpdate('borderstyle')) {\n        modifier.setStyle('border-style', data.borderstyle);\n      }\n    };\n    const applyStyleData = (editor, rows, data, wasChanged) => {\n      const isSingleRow = rows.length === 1;\n      const shouldOverrideCurrentValue = isSingleRow ? always : wasChanged;\n      each(rows, rowElm => {\n        const modifier = DomModifier.normal(editor, rowElm);\n        updateSimpleProps(modifier, data, shouldOverrideCurrentValue);\n        if (hasAdvancedRowTab(editor)) {\n          updateAdvancedProps(modifier, data, shouldOverrideCurrentValue);\n        }\n        if (wasChanged('align')) {\n          setAlign(editor, rowElm, data.align);\n        }\n      });\n    };\n    const applyStructureData = (editor, data) => {\n      editor.execCommand('mceTableRowType', false, {\n        type: data.type,\n        no_events: true\n      });\n    };\n    const applyRowData = (editor, rows, oldData, data) => {\n      const modifiedData = filter$1(data, (value, key) => oldData[key] !== value);\n      if (size(modifiedData) > 0) {\n        const typeModified = has(modifiedData, 'type');\n        const styleModified = typeModified ? size(modifiedData) > 1 : true;\n        if (styleModified) {\n          applyStyleData(editor, rows, data, curry(has, modifiedData));\n        }\n        if (typeModified) {\n          applyStructureData(editor, data);\n        }\n        table(SugarElement.fromDom(rows[0])).each(table => fireTableModified(editor, table.dom, {\n          structure: typeModified,\n          style: styleModified\n        }));\n      }\n    };\n    const onSubmitRowForm = (editor, rows, oldData, api) => {\n      const data = api.getData();\n      api.close();\n      editor.undoManager.transact(() => {\n        applyRowData(editor, rows, oldData, data);\n        editor.focus();\n      });\n    };\n    const open$1 = editor => {\n      const rows = getRowsFromSelection(getSelectionStart(editor), ephemera.selected);\n      if (rows.length === 0) {\n        return;\n      }\n      const rowsData = map(rows, rowElm => extractDataFromRowElement(editor, rowElm.dom, hasAdvancedRowTab(editor)));\n      const data = getSharedValues(rowsData);\n      const dialogTabPanel = {\n        type: 'tabpanel',\n        tabs: [\n          {\n            title: 'General',\n            name: 'general',\n            items: getItems$1(editor)\n          },\n          getAdvancedTab(editor, 'row')\n        ]\n      };\n      const dialogPanel = {\n        type: 'panel',\n        items: [{\n            type: 'grid',\n            columns: 2,\n            items: getItems$1(editor)\n          }]\n      };\n      editor.windowManager.open({\n        title: 'Row Properties',\n        size: 'normal',\n        body: hasAdvancedRowTab(editor) ? dialogTabPanel : dialogPanel,\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: data,\n        onSubmit: curry(onSubmitRowForm, editor, map(rows, r => r.dom), data)\n      });\n    };\n\n    const getItems = (editor, classes, insertNewTable) => {\n      const rowColCountItems = !insertNewTable ? [] : [\n        {\n          type: 'input',\n          name: 'cols',\n          label: 'Cols',\n          inputMode: 'numeric'\n        },\n        {\n          type: 'input',\n          name: 'rows',\n          label: 'Rows',\n          inputMode: 'numeric'\n        }\n      ];\n      const alwaysItems = [\n        {\n          type: 'input',\n          name: 'width',\n          label: 'Width'\n        },\n        {\n          type: 'input',\n          name: 'height',\n          label: 'Height'\n        }\n      ];\n      const appearanceItems = hasAppearanceOptions(editor) ? [\n        {\n          type: 'input',\n          name: 'cellspacing',\n          label: 'Cell spacing',\n          inputMode: 'numeric'\n        },\n        {\n          type: 'input',\n          name: 'cellpadding',\n          label: 'Cell padding',\n          inputMode: 'numeric'\n        },\n        {\n          type: 'input',\n          name: 'border',\n          label: 'Border width'\n        },\n        {\n          type: 'label',\n          label: 'Caption',\n          items: [{\n              type: 'checkbox',\n              name: 'caption',\n              label: 'Show caption'\n            }]\n        }\n      ] : [];\n      const alignmentItem = [{\n          type: 'listbox',\n          name: 'align',\n          label: 'Alignment',\n          items: [\n            {\n              text: 'None',\n              value: ''\n            },\n            {\n              text: 'Left',\n              value: 'left'\n            },\n            {\n              text: 'Center',\n              value: 'center'\n            },\n            {\n              text: 'Right',\n              value: 'right'\n            }\n          ]\n        }];\n      const classListItem = classes.length > 0 ? [{\n          type: 'listbox',\n          name: 'class',\n          label: 'Class',\n          items: classes\n        }] : [];\n      return rowColCountItems.concat(alwaysItems).concat(appearanceItems).concat(alignmentItem).concat(classListItem);\n    };\n\n    const styleTDTH = (dom, elm, name, value) => {\n      if (elm.tagName === 'TD' || elm.tagName === 'TH') {\n        if (isString(name) && isNonNullable(value)) {\n          dom.setStyle(elm, name, value);\n        } else {\n          dom.setStyles(elm, name);\n        }\n      } else {\n        if (elm.children) {\n          for (let i = 0; i < elm.children.length; i++) {\n            styleTDTH(dom, elm.children[i], name, value);\n          }\n        }\n      }\n    };\n    const applyDataToElement = (editor, tableElm, data, shouldApplyOnCell) => {\n      const dom = editor.dom;\n      const attrs = {};\n      const styles = {};\n      const shouldStyleWithCss$1 = shouldStyleWithCss(editor);\n      const hasAdvancedTableTab$1 = hasAdvancedTableTab(editor);\n      if (!isUndefined(data.class)) {\n        attrs.class = data.class;\n      }\n      styles.height = addPxSuffix(data.height);\n      if (shouldStyleWithCss$1) {\n        styles.width = addPxSuffix(data.width);\n      } else if (dom.getAttrib(tableElm, 'width')) {\n        attrs.width = removePxSuffix(data.width);\n      }\n      if (shouldStyleWithCss$1) {\n        styles['border-width'] = addPxSuffix(data.border);\n        styles['border-spacing'] = addPxSuffix(data.cellspacing);\n      } else {\n        attrs.border = data.border;\n        attrs.cellpadding = data.cellpadding;\n        attrs.cellspacing = data.cellspacing;\n      }\n      if (shouldStyleWithCss$1 && tableElm.children) {\n        const cellStyles = {};\n        if (shouldApplyOnCell.border) {\n          cellStyles['border-width'] = addPxSuffix(data.border);\n        }\n        if (shouldApplyOnCell.cellpadding) {\n          cellStyles.padding = addPxSuffix(data.cellpadding);\n        }\n        if (hasAdvancedTableTab$1 && shouldApplyOnCell.bordercolor) {\n          cellStyles['border-color'] = data.bordercolor;\n        }\n        if (!isEmpty$1(cellStyles)) {\n          for (let i = 0; i < tableElm.children.length; i++) {\n            styleTDTH(dom, tableElm.children[i], cellStyles);\n          }\n        }\n      }\n      if (hasAdvancedTableTab$1) {\n        const advData = data;\n        styles['background-color'] = advData.backgroundcolor;\n        styles['border-color'] = advData.bordercolor;\n        styles['border-style'] = advData.borderstyle;\n      }\n      attrs.style = dom.serializeStyle({\n        ...getDefaultStyles(editor),\n        ...styles\n      });\n      dom.setAttribs(tableElm, {\n        ...getDefaultAttributes(editor),\n        ...attrs\n      });\n    };\n    const onSubmitTableForm = (editor, tableElm, oldData, api) => {\n      const dom = editor.dom;\n      const data = api.getData();\n      const modifiedData = filter$1(data, (value, key) => oldData[key] !== value);\n      api.close();\n      if (data.class === '') {\n        delete data.class;\n      }\n      editor.undoManager.transact(() => {\n        if (!tableElm) {\n          const cols = toInt(data.cols).getOr(1);\n          const rows = toInt(data.rows).getOr(1);\n          editor.execCommand('mceInsertTable', false, {\n            rows,\n            columns: cols\n          });\n          tableElm = getSelectionCell(getSelectionStart(editor), getIsRoot(editor)).bind(cell => table(cell, getIsRoot(editor))).map(table => table.dom).getOrDie();\n        }\n        if (size(modifiedData) > 0) {\n          const applicableCellProperties = {\n            border: has(modifiedData, 'border'),\n            bordercolor: has(modifiedData, 'bordercolor'),\n            cellpadding: has(modifiedData, 'cellpadding')\n          };\n          applyDataToElement(editor, tableElm, data, applicableCellProperties);\n          const captionElm = dom.select('caption', tableElm)[0];\n          if (captionElm && !data.caption || !captionElm && data.caption) {\n            editor.execCommand('mceTableToggleCaption');\n          }\n          setAlign(editor, tableElm, data.align);\n        }\n        editor.focus();\n        editor.addVisual();\n        if (size(modifiedData) > 0) {\n          const captionModified = has(modifiedData, 'caption');\n          const styleModified = captionModified ? size(modifiedData) > 1 : true;\n          fireTableModified(editor, tableElm, {\n            structure: captionModified,\n            style: styleModified\n          });\n        }\n      });\n    };\n    const open = (editor, insertNewTable) => {\n      const dom = editor.dom;\n      let tableElm;\n      let data = extractDataFromSettings(editor, hasAdvancedTableTab(editor));\n      if (insertNewTable) {\n        data.cols = '1';\n        data.rows = '1';\n        if (hasAdvancedTableTab(editor)) {\n          data.borderstyle = '';\n          data.bordercolor = '';\n          data.backgroundcolor = '';\n        }\n      } else {\n        tableElm = dom.getParent(editor.selection.getStart(), 'table', editor.getBody());\n        if (tableElm) {\n          data = extractDataFromTableElement(editor, tableElm, hasAdvancedTableTab(editor));\n        } else {\n          if (hasAdvancedTableTab(editor)) {\n            data.borderstyle = '';\n            data.bordercolor = '';\n            data.backgroundcolor = '';\n          }\n        }\n      }\n      const classes = buildListItems(getTableClassList(editor));\n      if (classes.length > 0) {\n        if (data.class) {\n          data.class = data.class.replace(/\\s*mce\\-item\\-table\\s*/g, '');\n        }\n      }\n      const generalPanel = {\n        type: 'grid',\n        columns: 2,\n        items: getItems(editor, classes, insertNewTable)\n      };\n      const nonAdvancedForm = () => ({\n        type: 'panel',\n        items: [generalPanel]\n      });\n      const advancedForm = () => ({\n        type: 'tabpanel',\n        tabs: [\n          {\n            title: 'General',\n            name: 'general',\n            items: [generalPanel]\n          },\n          getAdvancedTab(editor, 'table')\n        ]\n      });\n      const dialogBody = hasAdvancedTableTab(editor) ? advancedForm() : nonAdvancedForm();\n      editor.windowManager.open({\n        title: 'Table Properties',\n        size: 'normal',\n        body: dialogBody,\n        onSubmit: curry(onSubmitTableForm, editor, tableElm, data),\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: data\n      });\n    };\n\n    const registerCommands = editor => {\n      const runAction = f => {\n        if (isInEditableContext(getSelectionStart(editor))) {\n          f();\n        }\n      };\n      each$1({\n        mceTableProps: curry(open, editor, false),\n        mceTableRowProps: curry(open$1, editor),\n        mceTableCellProps: curry(open$2, editor),\n        mceInsertTableDialog: curry(open, editor, true)\n      }, (func, name) => editor.addCommand(name, () => runAction(func)));\n    };\n\n    const child = (scope, selector) => child$1(scope, selector).isSome();\n\n    const selection = identity;\n    const unmergable = selectedCells => {\n      const hasSpan = (elem, type) => getOpt(elem, type).exists(span => parseInt(span, 10) > 1);\n      const hasRowOrColSpan = elem => hasSpan(elem, 'rowspan') || hasSpan(elem, 'colspan');\n      return selectedCells.length > 0 && forall(selectedCells, hasRowOrColSpan) ? Optional.some(selectedCells) : Optional.none();\n    };\n    const mergable = (table, selectedCells, ephemera) => {\n      if (selectedCells.length <= 1) {\n        return Optional.none();\n      } else {\n        return retrieveBox(table, ephemera.firstSelectedSelector, ephemera.lastSelectedSelector).map(bounds => ({\n          bounds,\n          cells: selectedCells\n        }));\n      }\n    };\n\n    const noMenu = cell => ({\n      element: cell,\n      mergable: Optional.none(),\n      unmergable: Optional.none(),\n      selection: [cell]\n    });\n    const forMenu = (selectedCells, table, cell) => ({\n      element: cell,\n      mergable: mergable(table, selectedCells, ephemera),\n      unmergable: unmergable(selectedCells),\n      selection: selection(selectedCells)\n    });\n\n    const getSelectionTargets = editor => {\n      const targets = Cell(Optional.none());\n      const changeHandlers = Cell([]);\n      let selectionDetails = Optional.none();\n      const isCaption = isTag('caption');\n      const isDisabledForSelection = key => selectionDetails.forall(details => !details[key]);\n      const getStart = () => getSelectionCellOrCaption(getSelectionStart(editor), getIsRoot(editor));\n      const getEnd = () => getSelectionCellOrCaption(getSelectionEnd(editor), getIsRoot(editor));\n      const findTargets = () => getStart().bind(startCellOrCaption => flatten(lift2(table(startCellOrCaption), getEnd().bind(table), (startTable, endTable) => {\n        if (eq(startTable, endTable)) {\n          if (isCaption(startCellOrCaption)) {\n            return Optional.some(noMenu(startCellOrCaption));\n          } else {\n            return Optional.some(forMenu(getCellsFromSelection(editor), startTable, startCellOrCaption));\n          }\n        }\n        return Optional.none();\n      })));\n      const getExtractedDetails = targets => {\n        const tableOpt = table(targets.element);\n        return tableOpt.map(table => {\n          const warehouse = Warehouse.fromTable(table);\n          const selectedCells = onCells(warehouse, targets).getOr([]);\n          const locked = foldl(selectedCells, (acc, cell) => {\n            if (cell.isLocked) {\n              acc.onAny = true;\n              if (cell.column === 0) {\n                acc.onFirst = true;\n              } else if (cell.column + cell.colspan >= warehouse.grid.columns) {\n                acc.onLast = true;\n              }\n            }\n            return acc;\n          }, {\n            onAny: false,\n            onFirst: false,\n            onLast: false\n          });\n          return {\n            mergeable: onUnlockedMergable(warehouse, targets).isSome(),\n            unmergeable: onUnlockedUnmergable(warehouse, targets).isSome(),\n            locked\n          };\n        });\n      };\n      const resetTargets = () => {\n        targets.set(cached(findTargets)());\n        selectionDetails = targets.get().bind(getExtractedDetails);\n        each(changeHandlers.get(), call);\n      };\n      const setupHandler = handler => {\n        handler();\n        changeHandlers.set(changeHandlers.get().concat([handler]));\n        return () => {\n          changeHandlers.set(filter(changeHandlers.get(), h => h !== handler));\n        };\n      };\n      const onSetup = (api, isDisabled) => setupHandler(() => targets.get().fold(() => {\n        api.setEnabled(false);\n      }, targets => {\n        api.setEnabled(!isDisabled(targets) && editor.selection.isEditable());\n      }));\n      const onSetupWithToggle = (api, isDisabled, isActive) => setupHandler(() => targets.get().fold(() => {\n        api.setEnabled(false);\n        api.setActive(false);\n      }, targets => {\n        api.setEnabled(!isDisabled(targets) && editor.selection.isEditable());\n        api.setActive(isActive(targets));\n      }));\n      const isDisabledFromLocked = lockedDisable => selectionDetails.exists(details => details.locked[lockedDisable]);\n      const onSetupTable = api => onSetup(api, _ => false);\n      const onSetupCellOrRow = api => onSetup(api, targets => isCaption(targets.element));\n      const onSetupColumn = lockedDisable => api => onSetup(api, targets => isCaption(targets.element) || isDisabledFromLocked(lockedDisable));\n      const onSetupPasteable = getClipboardData => api => onSetup(api, targets => isCaption(targets.element) || getClipboardData().isNone());\n      const onSetupPasteableColumn = (getClipboardData, lockedDisable) => api => onSetup(api, targets => isCaption(targets.element) || getClipboardData().isNone() || isDisabledFromLocked(lockedDisable));\n      const onSetupMergeable = api => onSetup(api, _targets => isDisabledForSelection('mergeable'));\n      const onSetupUnmergeable = api => onSetup(api, _targets => isDisabledForSelection('unmergeable'));\n      const onSetupTableWithCaption = api => {\n        return onSetupWithToggle(api, never, targets => {\n          const tableOpt = table(targets.element, getIsRoot(editor));\n          return tableOpt.exists(table => child(table, 'caption'));\n        });\n      };\n      const onSetupTableHeaders = (command, headerType) => api => {\n        return onSetupWithToggle(api, targets => isCaption(targets.element), () => editor.queryCommandValue(command) === headerType);\n      };\n      const onSetupTableRowHeaders = onSetupTableHeaders('mceTableRowType', 'header');\n      const onSetupTableColumnHeaders = onSetupTableHeaders('mceTableColType', 'th');\n      editor.on('NodeChange ExecCommand TableSelectorChange', resetTargets);\n      return {\n        onSetupTable,\n        onSetupCellOrRow,\n        onSetupColumn,\n        onSetupPasteable,\n        onSetupPasteableColumn,\n        onSetupMergeable,\n        onSetupUnmergeable,\n        resetTargets,\n        onSetupTableWithCaption,\n        onSetupTableRowHeaders,\n        onSetupTableColumnHeaders,\n        targets: targets.get\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.FakeClipboard');\n\n    const tableTypeBase = 'x-tinymce/dom-table-';\n    const tableTypeRow = tableTypeBase + 'rows';\n    const tableTypeColumn = tableTypeBase + 'columns';\n    const getData = type => {\n      var _a;\n      const items = (_a = global.read()) !== null && _a !== void 0 ? _a : [];\n      return findMap(items, item => Optional.from(item.getType(type)));\n    };\n    const getRows = () => getData(tableTypeRow);\n    const getColumns = () => getData(tableTypeColumn);\n\n    const onSetupEditable$1 = editor => api => {\n      const nodeChanged = () => {\n        api.setEnabled(editor.selection.isEditable());\n      };\n      editor.on('NodeChange', nodeChanged);\n      nodeChanged();\n      return () => {\n        editor.off('NodeChange', nodeChanged);\n      };\n    };\n    const addButtons = (editor, selectionTargets) => {\n      editor.ui.registry.addMenuButton('table', {\n        tooltip: 'Table',\n        icon: 'table',\n        onSetup: onSetupEditable$1(editor),\n        fetch: callback => callback('inserttable | cell row column | advtablesort | tableprops deletetable')\n      });\n      const cmd = command => () => editor.execCommand(command);\n      const addButtonIfRegistered = (name, spec) => {\n        if (editor.queryCommandSupported(spec.command)) {\n          editor.ui.registry.addButton(name, {\n            ...spec,\n            onAction: isFunction(spec.onAction) ? spec.onAction : cmd(spec.command)\n          });\n        }\n      };\n      const addToggleButtonIfRegistered = (name, spec) => {\n        if (editor.queryCommandSupported(spec.command)) {\n          editor.ui.registry.addToggleButton(name, {\n            ...spec,\n            onAction: isFunction(spec.onAction) ? spec.onAction : cmd(spec.command)\n          });\n        }\n      };\n      addButtonIfRegistered('tableprops', {\n        tooltip: 'Table properties',\n        command: 'mceTableProps',\n        icon: 'table',\n        onSetup: selectionTargets.onSetupTable\n      });\n      addButtonIfRegistered('tabledelete', {\n        tooltip: 'Delete table',\n        command: 'mceTableDelete',\n        icon: 'table-delete-table',\n        onSetup: selectionTargets.onSetupTable\n      });\n      addButtonIfRegistered('tablecellprops', {\n        tooltip: 'Cell properties',\n        command: 'mceTableCellProps',\n        icon: 'table-cell-properties',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tablemergecells', {\n        tooltip: 'Merge cells',\n        command: 'mceTableMergeCells',\n        icon: 'table-merge-cells',\n        onSetup: selectionTargets.onSetupMergeable\n      });\n      addButtonIfRegistered('tablesplitcells', {\n        tooltip: 'Split cell',\n        command: 'mceTableSplitCells',\n        icon: 'table-split-cells',\n        onSetup: selectionTargets.onSetupUnmergeable\n      });\n      addButtonIfRegistered('tableinsertrowbefore', {\n        tooltip: 'Insert row before',\n        command: 'mceTableInsertRowBefore',\n        icon: 'table-insert-row-above',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tableinsertrowafter', {\n        tooltip: 'Insert row after',\n        command: 'mceTableInsertRowAfter',\n        icon: 'table-insert-row-after',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tabledeleterow', {\n        tooltip: 'Delete row',\n        command: 'mceTableDeleteRow',\n        icon: 'table-delete-row',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tablerowprops', {\n        tooltip: 'Row properties',\n        command: 'mceTableRowProps',\n        icon: 'table-row-properties',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tableinsertcolbefore', {\n        tooltip: 'Insert column before',\n        command: 'mceTableInsertColBefore',\n        icon: 'table-insert-column-before',\n        onSetup: selectionTargets.onSetupColumn('onFirst')\n      });\n      addButtonIfRegistered('tableinsertcolafter', {\n        tooltip: 'Insert column after',\n        command: 'mceTableInsertColAfter',\n        icon: 'table-insert-column-after',\n        onSetup: selectionTargets.onSetupColumn('onLast')\n      });\n      addButtonIfRegistered('tabledeletecol', {\n        tooltip: 'Delete column',\n        command: 'mceTableDeleteCol',\n        icon: 'table-delete-column',\n        onSetup: selectionTargets.onSetupColumn('onAny')\n      });\n      addButtonIfRegistered('tablecutrow', {\n        tooltip: 'Cut row',\n        command: 'mceTableCutRow',\n        icon: 'cut-row',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tablecopyrow', {\n        tooltip: 'Copy row',\n        command: 'mceTableCopyRow',\n        icon: 'duplicate-row',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tablepasterowbefore', {\n        tooltip: 'Paste row before',\n        command: 'mceTablePasteRowBefore',\n        icon: 'paste-row-before',\n        onSetup: selectionTargets.onSetupPasteable(getRows)\n      });\n      addButtonIfRegistered('tablepasterowafter', {\n        tooltip: 'Paste row after',\n        command: 'mceTablePasteRowAfter',\n        icon: 'paste-row-after',\n        onSetup: selectionTargets.onSetupPasteable(getRows)\n      });\n      addButtonIfRegistered('tablecutcol', {\n        tooltip: 'Cut column',\n        command: 'mceTableCutCol',\n        icon: 'cut-column',\n        onSetup: selectionTargets.onSetupColumn('onAny')\n      });\n      addButtonIfRegistered('tablecopycol', {\n        tooltip: 'Copy column',\n        command: 'mceTableCopyCol',\n        icon: 'duplicate-column',\n        onSetup: selectionTargets.onSetupColumn('onAny')\n      });\n      addButtonIfRegistered('tablepastecolbefore', {\n        tooltip: 'Paste column before',\n        command: 'mceTablePasteColBefore',\n        icon: 'paste-column-before',\n        onSetup: selectionTargets.onSetupPasteableColumn(getColumns, 'onFirst')\n      });\n      addButtonIfRegistered('tablepastecolafter', {\n        tooltip: 'Paste column after',\n        command: 'mceTablePasteColAfter',\n        icon: 'paste-column-after',\n        onSetup: selectionTargets.onSetupPasteableColumn(getColumns, 'onLast')\n      });\n      addButtonIfRegistered('tableinsertdialog', {\n        tooltip: 'Insert table',\n        command: 'mceInsertTableDialog',\n        icon: 'table',\n        onSetup: onSetupEditable$1(editor)\n      });\n      const tableClassList = filterNoneItem(getTableClassList(editor));\n      if (tableClassList.length !== 0 && editor.queryCommandSupported('mceTableToggleClass')) {\n        editor.ui.registry.addMenuButton('tableclass', {\n          icon: 'table-classes',\n          tooltip: 'Table styles',\n          fetch: generateMenuItemsCallback(editor, tableClassList, 'tableclass', value => editor.execCommand('mceTableToggleClass', false, value)),\n          onSetup: selectionTargets.onSetupTable\n        });\n      }\n      const tableCellClassList = filterNoneItem(getCellClassList(editor));\n      if (tableCellClassList.length !== 0 && editor.queryCommandSupported('mceTableCellToggleClass')) {\n        editor.ui.registry.addMenuButton('tablecellclass', {\n          icon: 'table-cell-classes',\n          tooltip: 'Cell styles',\n          fetch: generateMenuItemsCallback(editor, tableCellClassList, 'tablecellclass', value => editor.execCommand('mceTableCellToggleClass', false, value)),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n      }\n      if (editor.queryCommandSupported('mceTableApplyCellStyle')) {\n        editor.ui.registry.addMenuButton('tablecellvalign', {\n          icon: 'vertical-align',\n          tooltip: 'Vertical align',\n          fetch: generateMenuItemsCallback(editor, verticalAlignValues, 'tablecellverticalalign', applyTableCellStyle(editor, 'vertical-align')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addMenuButton('tablecellborderwidth', {\n          icon: 'border-width',\n          tooltip: 'Border width',\n          fetch: generateMenuItemsCallback(editor, getTableBorderWidths(editor), 'tablecellborderwidth', applyTableCellStyle(editor, 'border-width')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addMenuButton('tablecellborderstyle', {\n          icon: 'border-style',\n          tooltip: 'Border style',\n          fetch: generateMenuItemsCallback(editor, getTableBorderStyles(editor), 'tablecellborderstyle', applyTableCellStyle(editor, 'border-style')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addMenuButton('tablecellbackgroundcolor', {\n          icon: 'cell-background-color',\n          tooltip: 'Background color',\n          fetch: callback => callback(buildColorMenu(editor, getTableBackgroundColorMap(editor), 'background-color')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addMenuButton('tablecellbordercolor', {\n          icon: 'cell-border-color',\n          tooltip: 'Border color',\n          fetch: callback => callback(buildColorMenu(editor, getTableBorderColorMap(editor), 'border-color')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n      }\n      addToggleButtonIfRegistered('tablecaption', {\n        tooltip: 'Table caption',\n        icon: 'table-caption',\n        command: 'mceTableToggleCaption',\n        onSetup: selectionTargets.onSetupTableWithCaption\n      });\n      addToggleButtonIfRegistered('tablerowheader', {\n        tooltip: 'Row header',\n        icon: 'table-top-header',\n        command: 'mceTableRowType',\n        onAction: changeRowHeader(editor),\n        onSetup: selectionTargets.onSetupTableRowHeaders\n      });\n      addToggleButtonIfRegistered('tablecolheader', {\n        tooltip: 'Column header',\n        icon: 'table-left-header',\n        command: 'mceTableColType',\n        onAction: changeColumnHeader(editor),\n        onSetup: selectionTargets.onSetupTableColumnHeaders\n      });\n    };\n    const addToolbars = editor => {\n      const isEditableTable = table => editor.dom.is(table, 'table') && editor.getBody().contains(table) && editor.dom.isEditable(table.parentNode);\n      const toolbar = getToolbar(editor);\n      if (toolbar.length > 0) {\n        editor.ui.registry.addContextToolbar('table', {\n          predicate: isEditableTable,\n          items: toolbar,\n          scope: 'node',\n          position: 'node'\n        });\n      }\n    };\n\n    const onSetupEditable = editor => api => {\n      const nodeChanged = () => {\n        api.setEnabled(editor.selection.isEditable());\n      };\n      editor.on('NodeChange', nodeChanged);\n      nodeChanged();\n      return () => {\n        editor.off('NodeChange', nodeChanged);\n      };\n    };\n    const addMenuItems = (editor, selectionTargets) => {\n      const cmd = command => () => editor.execCommand(command);\n      const addMenuIfRegistered = (name, spec) => {\n        if (editor.queryCommandSupported(spec.command)) {\n          editor.ui.registry.addMenuItem(name, {\n            ...spec,\n            onAction: isFunction(spec.onAction) ? spec.onAction : cmd(spec.command)\n          });\n          return true;\n        } else {\n          return false;\n        }\n      };\n      const addToggleMenuIfRegistered = (name, spec) => {\n        if (editor.queryCommandSupported(spec.command)) {\n          editor.ui.registry.addToggleMenuItem(name, {\n            ...spec,\n            onAction: isFunction(spec.onAction) ? spec.onAction : cmd(spec.command)\n          });\n        }\n      };\n      const insertTableAction = data => {\n        editor.execCommand('mceInsertTable', false, {\n          rows: data.numRows,\n          columns: data.numColumns\n        });\n      };\n      const hasRowMenuItems = [\n        addMenuIfRegistered('tableinsertrowbefore', {\n          text: 'Insert row before',\n          icon: 'table-insert-row-above',\n          command: 'mceTableInsertRowBefore',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tableinsertrowafter', {\n          text: 'Insert row after',\n          icon: 'table-insert-row-after',\n          command: 'mceTableInsertRowAfter',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tabledeleterow', {\n          text: 'Delete row',\n          icon: 'table-delete-row',\n          command: 'mceTableDeleteRow',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tablerowprops', {\n          text: 'Row properties',\n          icon: 'table-row-properties',\n          command: 'mceTableRowProps',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tablecutrow', {\n          text: 'Cut row',\n          icon: 'cut-row',\n          command: 'mceTableCutRow',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tablecopyrow', {\n          text: 'Copy row',\n          icon: 'duplicate-row',\n          command: 'mceTableCopyRow',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tablepasterowbefore', {\n          text: 'Paste row before',\n          icon: 'paste-row-before',\n          command: 'mceTablePasteRowBefore',\n          onSetup: selectionTargets.onSetupPasteable(getRows)\n        }),\n        addMenuIfRegistered('tablepasterowafter', {\n          text: 'Paste row after',\n          icon: 'paste-row-after',\n          command: 'mceTablePasteRowAfter',\n          onSetup: selectionTargets.onSetupPasteable(getRows)\n        })\n      ];\n      const hasColumnMenuItems = [\n        addMenuIfRegistered('tableinsertcolumnbefore', {\n          text: 'Insert column before',\n          icon: 'table-insert-column-before',\n          command: 'mceTableInsertColBefore',\n          onSetup: selectionTargets.onSetupColumn('onFirst')\n        }),\n        addMenuIfRegistered('tableinsertcolumnafter', {\n          text: 'Insert column after',\n          icon: 'table-insert-column-after',\n          command: 'mceTableInsertColAfter',\n          onSetup: selectionTargets.onSetupColumn('onLast')\n        }),\n        addMenuIfRegistered('tabledeletecolumn', {\n          text: 'Delete column',\n          icon: 'table-delete-column',\n          command: 'mceTableDeleteCol',\n          onSetup: selectionTargets.onSetupColumn('onAny')\n        }),\n        addMenuIfRegistered('tablecutcolumn', {\n          text: 'Cut column',\n          icon: 'cut-column',\n          command: 'mceTableCutCol',\n          onSetup: selectionTargets.onSetupColumn('onAny')\n        }),\n        addMenuIfRegistered('tablecopycolumn', {\n          text: 'Copy column',\n          icon: 'duplicate-column',\n          command: 'mceTableCopyCol',\n          onSetup: selectionTargets.onSetupColumn('onAny')\n        }),\n        addMenuIfRegistered('tablepastecolumnbefore', {\n          text: 'Paste column before',\n          icon: 'paste-column-before',\n          command: 'mceTablePasteColBefore',\n          onSetup: selectionTargets.onSetupPasteableColumn(getColumns, 'onFirst')\n        }),\n        addMenuIfRegistered('tablepastecolumnafter', {\n          text: 'Paste column after',\n          icon: 'paste-column-after',\n          command: 'mceTablePasteColAfter',\n          onSetup: selectionTargets.onSetupPasteableColumn(getColumns, 'onLast')\n        })\n      ];\n      const hasCellMenuItems = [\n        addMenuIfRegistered('tablecellprops', {\n          text: 'Cell properties',\n          icon: 'table-cell-properties',\n          command: 'mceTableCellProps',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tablemergecells', {\n          text: 'Merge cells',\n          icon: 'table-merge-cells',\n          command: 'mceTableMergeCells',\n          onSetup: selectionTargets.onSetupMergeable\n        }),\n        addMenuIfRegistered('tablesplitcells', {\n          text: 'Split cell',\n          icon: 'table-split-cells',\n          command: 'mceTableSplitCells',\n          onSetup: selectionTargets.onSetupUnmergeable\n        })\n      ];\n      if (!hasTableGrid(editor)) {\n        editor.ui.registry.addMenuItem('inserttable', {\n          text: 'Table',\n          icon: 'table',\n          onAction: cmd('mceInsertTableDialog'),\n          onSetup: onSetupEditable(editor)\n        });\n      } else {\n        editor.ui.registry.addNestedMenuItem('inserttable', {\n          text: 'Table',\n          icon: 'table',\n          getSubmenuItems: () => [{\n              type: 'fancymenuitem',\n              fancytype: 'inserttable',\n              onAction: insertTableAction\n            }],\n          onSetup: onSetupEditable(editor)\n        });\n      }\n      editor.ui.registry.addMenuItem('inserttabledialog', {\n        text: 'Insert table',\n        icon: 'table',\n        onAction: cmd('mceInsertTableDialog'),\n        onSetup: onSetupEditable(editor)\n      });\n      addMenuIfRegistered('tableprops', {\n        text: 'Table properties',\n        onSetup: selectionTargets.onSetupTable,\n        command: 'mceTableProps'\n      });\n      addMenuIfRegistered('deletetable', {\n        text: 'Delete table',\n        icon: 'table-delete-table',\n        onSetup: selectionTargets.onSetupTable,\n        command: 'mceTableDelete'\n      });\n      if (contains(hasRowMenuItems, true)) {\n        editor.ui.registry.addNestedMenuItem('row', {\n          type: 'nestedmenuitem',\n          text: 'Row',\n          getSubmenuItems: constant('tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter')\n        });\n      }\n      if (contains(hasColumnMenuItems, true)) {\n        editor.ui.registry.addNestedMenuItem('column', {\n          type: 'nestedmenuitem',\n          text: 'Column',\n          getSubmenuItems: constant('tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn | tablecutcolumn tablecopycolumn tablepastecolumnbefore tablepastecolumnafter')\n        });\n      }\n      if (contains(hasCellMenuItems, true)) {\n        editor.ui.registry.addNestedMenuItem('cell', {\n          type: 'nestedmenuitem',\n          text: 'Cell',\n          getSubmenuItems: constant('tablecellprops tablemergecells tablesplitcells')\n        });\n      }\n      editor.ui.registry.addContextMenu('table', {\n        update: () => {\n          selectionTargets.resetTargets();\n          return selectionTargets.targets().fold(constant(''), targets => {\n            if (name(targets.element) === 'caption') {\n              return 'tableprops deletetable';\n            } else {\n              return 'cell row column | advtablesort | tableprops deletetable';\n            }\n          });\n        }\n      });\n      const tableClassList = filterNoneItem(getTableClassList(editor));\n      if (tableClassList.length !== 0 && editor.queryCommandSupported('mceTableToggleClass')) {\n        editor.ui.registry.addNestedMenuItem('tableclass', {\n          icon: 'table-classes',\n          text: 'Table styles',\n          getSubmenuItems: () => buildMenuItems(editor, tableClassList, 'tableclass', value => editor.execCommand('mceTableToggleClass', false, value)),\n          onSetup: selectionTargets.onSetupTable\n        });\n      }\n      const tableCellClassList = filterNoneItem(getCellClassList(editor));\n      if (tableCellClassList.length !== 0 && editor.queryCommandSupported('mceTableCellToggleClass')) {\n        editor.ui.registry.addNestedMenuItem('tablecellclass', {\n          icon: 'table-cell-classes',\n          text: 'Cell styles',\n          getSubmenuItems: () => buildMenuItems(editor, tableCellClassList, 'tablecellclass', value => editor.execCommand('mceTableCellToggleClass', false, value)),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n      }\n      if (editor.queryCommandSupported('mceTableApplyCellStyle')) {\n        editor.ui.registry.addNestedMenuItem('tablecellvalign', {\n          icon: 'vertical-align',\n          text: 'Vertical align',\n          getSubmenuItems: () => buildMenuItems(editor, verticalAlignValues, 'tablecellverticalalign', applyTableCellStyle(editor, 'vertical-align')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addNestedMenuItem('tablecellborderwidth', {\n          icon: 'border-width',\n          text: 'Border width',\n          getSubmenuItems: () => buildMenuItems(editor, getTableBorderWidths(editor), 'tablecellborderwidth', applyTableCellStyle(editor, 'border-width')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addNestedMenuItem('tablecellborderstyle', {\n          icon: 'border-style',\n          text: 'Border style',\n          getSubmenuItems: () => buildMenuItems(editor, getTableBorderStyles(editor), 'tablecellborderstyle', applyTableCellStyle(editor, 'border-style')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addNestedMenuItem('tablecellbackgroundcolor', {\n          icon: 'cell-background-color',\n          text: 'Background color',\n          getSubmenuItems: () => buildColorMenu(editor, getTableBackgroundColorMap(editor), 'background-color'),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addNestedMenuItem('tablecellbordercolor', {\n          icon: 'cell-border-color',\n          text: 'Border color',\n          getSubmenuItems: () => buildColorMenu(editor, getTableBorderColorMap(editor), 'border-color'),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n      }\n      addToggleMenuIfRegistered('tablecaption', {\n        icon: 'table-caption',\n        text: 'Table caption',\n        command: 'mceTableToggleCaption',\n        onSetup: selectionTargets.onSetupTableWithCaption\n      });\n      addToggleMenuIfRegistered('tablerowheader', {\n        text: 'Row header',\n        icon: 'table-top-header',\n        command: 'mceTableRowType',\n        onAction: changeRowHeader(editor),\n        onSetup: selectionTargets.onSetupTableRowHeaders\n      });\n      addToggleMenuIfRegistered('tablecolheader', {\n        text: 'Column header',\n        icon: 'table-left-header',\n        command: 'mceTableColType',\n        onAction: changeColumnHeader(editor),\n        onSetup: selectionTargets.onSetupTableRowHeaders\n      });\n    };\n\n    const Plugin = editor => {\n      const selectionTargets = getSelectionTargets(editor);\n      register(editor);\n      registerCommands(editor);\n      addMenuItems(editor, selectionTargets);\n      addButtons(editor, selectionTargets);\n      addToolbars(editor);\n    };\n    var Plugin$1 = () => {\n      global$3.add('table', Plugin);\n    };\n\n    Plugin$1();\n\n})();\n", "// Exports the \"table\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/table')\n//   ES2015:\n//     import 'tinymce/plugins/table'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,YAAI;AACJ,YAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,iBAAO;AAAA,QACT,OAAO;AACL,mBAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,QAC7F;AAAA,MACF;AACA,YAAM,SAAS,OAAK;AAClB,cAAM,IAAI,OAAO;AACjB,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,WAAW,CAAAA,UAAQ,WAAS,OAAO,KAAK,MAAMA;AACpD,YAAM,eAAe,CAAAA,UAAQ,WAAS,OAAO,UAAUA;AACvD,YAAM,OAAO,OAAK,OAAK,MAAM;AAC7B,YAAM,WAAW,SAAS,QAAQ;AAClC,YAAM,UAAU,SAAS,OAAO;AAChC,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,cAAc,KAAK,MAAS;AAClC,YAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,YAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,YAAM,aAAa,aAAa,UAAU;AAC1C,YAAM,WAAW,aAAa,QAAQ;AAEtC,YAAM,OAAO,MAAM;AAAA,MACnB;AACA,YAAM,WAAW,CAAC,KAAK,QAAQ,OAAK,IAAI,IAAI,CAAC,CAAC;AAC9C,YAAM,WAAW,WAAS;AACxB,eAAO,MAAM;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,WAAW,OAAK;AACpB,eAAO;AAAA,MACT;AACA,YAAM,eAAe,CAAC,GAAG,MAAM;AAC7B,eAAO,MAAM;AAAA,MACf;AACA,eAAS,MAAM,OAAO,aAAa;AACjC,eAAO,IAAI,aAAa;AACtB,gBAAMC,OAAM,YAAY,OAAO,QAAQ;AACvC,iBAAO,GAAG,MAAM,MAAMA,IAAG;AAAA,QAC3B;AAAA,MACF;AACA,YAAM,OAAO,OAAK;AAChB,UAAE;AAAA,MACJ;AACA,YAAM,QAAQ,SAAS,KAAK;AAC5B,YAAM,SAAS,SAAS,IAAI;AAAA,MAE5B,MAAM,SAAS;AAAA,QACb,YAAY,KAAK,OAAO;AACtB,eAAK,MAAM;AACX,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,IAAI,SAAS,MAAM,KAAK;AAAA,QACjC;AAAA,QACA,OAAO,OAAO;AACZ,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,KAAK,QAAQ,QAAQ;AACnB,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,QACA,SAAS;AACP,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,SAAS;AACP,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA,IAAI,QAAQ;AACV,cAAI,KAAK,KAAK;AACZ,mBAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,UACzC,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QACzC;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QAC1C;AAAA,QACA,OAAO,WAAW;AAChB,cAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,MAAM,aAAa;AACjB,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,GAAG,aAAa;AACd,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B;AAAA,QACA,WAAW,OAAO;AAChB,iBAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,QACvC;AAAA,QACA,QAAQ,OAAO;AACb,iBAAO,KAAK,MAAM,OAAO,MAAM;AAAA,QACjC;AAAA,QACA,SAAS,SAAS;AAChB,cAAI,CAAC,KAAK,KAAK;AACb,kBAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,UAC9F,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,cAAc,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,QACrE;AAAA,QACA,YAAY;AACV,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,iBAAiB;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AACR,iBAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,YAAM,OAAO,OAAO;AACpB,YAAM,iBAAiB,OAAO;AAC9B,YAAM,SAAS,CAAC,KAAK,MAAM;AACzB,cAAM,QAAQ,KAAK,GAAG;AACtB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,gBAAM,IAAI,MAAM,CAAC;AACjB,gBAAM,IAAI,IAAI,CAAC;AACf,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,SAAS,OAAK,CAAC,GAAG,MAAM;AAC5B,UAAE,CAAC,IAAI;AAAA,MACT;AACA,YAAM,iBAAiB,CAAC,KAAK,MAAM,QAAQ,YAAY;AACrD,eAAO,KAAK,CAAC,GAAG,MAAM;AACpB,WAAC,KAAK,GAAG,CAAC,IAAI,SAAS,SAAS,GAAG,CAAC;AAAA,QACtC,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAAC,KAAK,SAAS;AAC9B,cAAM,IAAI,CAAC;AACX,uBAAe,KAAK,MAAM,OAAO,CAAC,GAAG,IAAI;AACzC,eAAO;AAAA,MACT;AACA,YAAM,aAAa,CAAC,KAAK,MAAM;AAC7B,cAAM,IAAI,CAAC;AACX,eAAO,KAAK,CAAC,OAAOC,UAAS;AAC3B,YAAE,KAAK,EAAE,OAAOA,KAAI,CAAC;AAAA,QACvB,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,SAAS,SAAO;AACpB,eAAO,WAAW,KAAK,QAAQ;AAAA,MACjC;AACA,YAAM,OAAO,SAAO;AAClB,eAAO,KAAK,GAAG,EAAE;AAAA,MACnB;AACA,YAAM,QAAQ,CAAC,KAAKC,SAAQ;AAC1B,eAAO,IAAI,KAAKA,IAAG,IAAI,SAAS,KAAK,IAAIA,IAAG,CAAC,IAAI,SAAS,KAAK;AAAA,MACjE;AACA,YAAM,MAAM,CAAC,KAAKA,SAAQ,eAAe,KAAK,KAAKA,IAAG;AACtD,YAAM,oBAAoB,CAAC,KAAKA,SAAQ,IAAI,KAAKA,IAAG,KAAK,IAAIA,IAAG,MAAM,UAAa,IAAIA,IAAG,MAAM;AAChG,YAAM,YAAY,OAAK;AACrB,mBAAW,KAAK,GAAG;AACjB,cAAI,eAAe,KAAK,GAAG,CAAC,GAAG;AAC7B,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,YAAM,gBAAgB,MAAM,UAAU;AACtC,YAAM,aAAa,MAAM,UAAU;AACnC,YAAM,aAAa,CAAC,IAAI,MAAM,cAAc,KAAK,IAAI,CAAC;AACtD,YAAM,WAAW,CAAC,IAAI,MAAM,WAAW,IAAI,CAAC,IAAI;AAChD,YAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,CAAC,KAAK,MAAM;AACxB,cAAM,IAAI,CAAC;AACX,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAE,KAAK,EAAE,CAAC,CAAC;AAAA,QACb;AACA,eAAO;AAAA,MACT;AACA,YAAM,MAAM,CAAC,IAAI,MAAM;AACrB,cAAM,MAAM,GAAG;AACf,cAAM,IAAI,IAAI,MAAM,GAAG;AACvB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,QACf;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,CAAC,IAAI,MAAM;AACtB,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,IAAI,MAAM;AACvB,iBAAS,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK;AACvC,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,YAAY,CAAC,IAAI,SAAS;AAC9B,cAAM,OAAO,CAAC;AACd,cAAM,OAAO,CAAC;AACd,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,gBAAM,MAAM,KAAK,GAAG,CAAC,IAAI,OAAO;AAChC,cAAI,KAAK,CAAC;AAAA,QACZ;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,cAAM,IAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,cAAE,KAAK,CAAC;AAAA,UACV;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,CAAC,IAAI,GAAG,QAAQ;AAC5B,cAAM,IAAI,CAAC,GAAG,MAAM;AAClB,gBAAM,EAAE,KAAK,GAAG,CAAC;AAAA,QACnB,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,CAAC,IAAI,GAAG,QAAQ;AAC5B,aAAK,IAAI,CAAC,GAAG,MAAM;AACjB,gBAAM,EAAE,KAAK,GAAG,CAAC;AAAA,QACnB,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,YAAY,CAAC,IAAI,MAAM,UAAU;AACrC,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,mBAAO,SAAS,KAAK,CAAC;AAAA,UACxB,WAAW,MAAM,GAAG,CAAC,GAAG;AACtB;AAAA,UACF;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,OAAO,CAAC,IAAI,SAAS;AACzB,eAAO,UAAU,IAAI,MAAM,KAAK;AAAA,MAClC;AACA,YAAM,YAAY,QAAM;AACtB,cAAM,IAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B,EAAE;AAAA,UAC7E;AACA,qBAAW,MAAM,GAAG,GAAG,CAAC,CAAC;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,CAAC,IAAI,MAAM,UAAU,IAAI,IAAI,CAAC,CAAC;AAC5C,YAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,cAAI,KAAK,GAAG,CAAC,MAAM,MAAM;AACvB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,cAAc,CAAC,IAAI,MAAM;AAC7B,cAAM,IAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,CAAC,IAAI,MAAM,KAAK,KAAK,IAAI,GAAG,SAAS,SAAS,KAAK,GAAG,CAAC,CAAC,IAAI,SAAS,KAAK;AACxF,YAAM,OAAO,QAAM,MAAM,IAAI,CAAC;AAC9B,YAAM,OAAO,QAAM,MAAM,IAAI,GAAG,SAAS,CAAC;AAC1C,YAAM,UAAU,CAAC,KAAK,MAAM;AAC1B,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,gBAAM,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC;AACrB,cAAI,EAAE,OAAO,GAAG;AACd,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AAEA,YAAM,UAAU;AAChB,YAAM,WAAW;AACjB,YAAM,oBAAoB;AAC1B,YAAM,UAAU;AAChB,YAAM,OAAO;AAEb,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,IAAI,cAAc,KAAK;AACnC,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,cAAc,KAAK,IAAI,WAAW,SAAS,GAAG;AACrD,gBAAM,UAAU;AAChB,kBAAQ,MAAM,SAAS,IAAI;AAC3B,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB;AACA,eAAO,UAAU,IAAI,WAAW,CAAC,CAAC;AAAA,MACpC;AACA,YAAM,UAAU,CAAC,KAAK,UAAU;AAC9B,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,cAAc,GAAG;AAClC,eAAO,UAAU,IAAI;AAAA,MACvB;AACA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,eAAe,IAAI;AACpC,eAAO,UAAU,IAAI;AAAA,MACvB;AACA,YAAM,YAAY,UAAQ;AACxB,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AACA,eAAO,EAAE,KAAK,KAAK;AAAA,MACrB;AACA,YAAM,YAAY,CAAC,QAAQ,GAAG,MAAM,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,SAAS;AAClG,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT;AAAA,MACF;AAEA,YAAM,OAAO,CAAC,SAAS,aAAa;AAClC,cAAM,MAAM,QAAQ;AACpB,YAAI,IAAI,aAAa,SAAS;AAC5B,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM,OAAO;AACb,cAAI,KAAK,YAAY,QAAW;AAC9B,mBAAO,KAAK,QAAQ,QAAQ;AAAA,UAC9B,WAAW,KAAK,sBAAsB,QAAW;AAC/C,mBAAO,KAAK,kBAAkB,QAAQ;AAAA,UACxC,WAAW,KAAK,0BAA0B,QAAW;AACnD,mBAAO,KAAK,sBAAsB,QAAQ;AAAA,UAC5C,WAAW,KAAK,uBAAuB,QAAW;AAChD,mBAAO,KAAK,mBAAmB,QAAQ;AAAA,UACzC,OAAO;AACL,kBAAM,IAAI,MAAM,gCAAgC;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AACA,YAAM,iBAAiB,SAAO,IAAI,aAAa,WAAW,IAAI,aAAa,YAAY,IAAI,aAAa,qBAAqB,IAAI,sBAAsB;AACvJ,YAAM,QAAQ,CAAC,UAAU,UAAU;AACjC,cAAM,OAAO,UAAU,SAAY,WAAW,MAAM;AACpD,eAAO,eAAe,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,iBAAiB,QAAQ,GAAG,aAAa,OAAO;AAAA,MAC9F;AACA,YAAM,MAAM,CAAC,UAAU,UAAU;AAC/B,cAAM,OAAO,UAAU,SAAY,WAAW,MAAM;AACpD,eAAO,eAAe,IAAI,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,KAAK,cAAc,QAAQ,CAAC,EAAE,IAAI,aAAa,OAAO;AAAA,MACtH;AAEA,YAAM,KAAK,CAAC,IAAI,OAAO,GAAG,QAAQ,GAAG;AACrC,YAAM,OAAO;AAEb,aAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAElE,YAAM,OAAO,aAAW;AACtB,cAAM,IAAI,QAAQ,IAAI;AACtB,eAAO,EAAE,YAAY;AAAA,MACvB;AACA,YAAM,OAAO,aAAW,QAAQ,IAAI;AACpC,YAAM,SAAS,OAAK,aAAW,KAAK,OAAO,MAAM;AACjD,YAAM,YAAY,aAAW,KAAK,OAAO,MAAM,WAAW,KAAK,OAAO,MAAM;AAC5E,YAAM,YAAY,OAAO,OAAO;AAChC,YAAM,SAAS,OAAO,IAAI;AAC1B,YAAM,aAAa,OAAO,QAAQ;AAClC,YAAM,qBAAqB,OAAO,iBAAiB;AACnD,YAAM,QAAQ,SAAO,OAAK,UAAU,CAAC,KAAK,KAAK,CAAC,MAAM;AAEtD,YAAM,QAAQ,aAAW,aAAa,QAAQ,QAAQ,IAAI,aAAa;AACvE,YAAM,kBAAkB,SAAO,WAAW,GAAG,IAAI,MAAM,MAAM,GAAG;AAChE,YAAM,SAAS,aAAW,SAAS,KAAK,QAAQ,IAAI,UAAU,EAAE,IAAI,aAAa,OAAO;AACxF,YAAM,UAAU,CAAC,SAAS,WAAW;AACnC,cAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,YAAI,MAAM,QAAQ;AAClB,cAAM,MAAM,CAAC;AACb,eAAO,IAAI,eAAe,QAAQ,IAAI,eAAe,QAAW;AAC9D,gBAAM,YAAY,IAAI;AACtB,gBAAM,IAAI,aAAa,QAAQ,SAAS;AACxC,cAAI,KAAK,CAAC;AACV,cAAI,KAAK,CAAC,MAAM,MAAM;AACpB;AAAA,UACF,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,cAAc,aAAW,SAAS,KAAK,QAAQ,IAAI,eAAe,EAAE,IAAI,aAAa,OAAO;AAClG,YAAM,cAAc,aAAW,SAAS,KAAK,QAAQ,IAAI,WAAW,EAAE,IAAI,aAAa,OAAO;AAC9F,YAAM,aAAa,aAAW,IAAI,QAAQ,IAAI,YAAY,aAAa,OAAO;AAC9E,YAAM,UAAU,CAAC,SAAS,UAAU;AAClC,cAAM,KAAK,QAAQ,IAAI;AACvB,eAAO,SAAS,KAAK,GAAG,KAAK,CAAC,EAAE,IAAI,aAAa,OAAO;AAAA,MAC1D;AACA,YAAM,aAAa,aAAW,QAAQ,SAAS,CAAC;AAEhD,YAAM,eAAe,SAAO,mBAAmB,GAAG,KAAK,cAAc,IAAI,IAAI,IAAI;AACjF,YAAM,YAAY,WAAW,QAAQ,UAAU,YAAY,KAAK,WAAW,KAAK,UAAU,WAAW;AACrG,YAAM,cAAc,YAAY,OAAK,aAAa,QAAQ,EAAE,IAAI,YAAY,CAAC,IAAI;AACjF,YAAM,gBAAgB,OAAK;AACzB,cAAM,IAAI,YAAY,CAAC;AACvB,eAAO,aAAa,CAAC,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK;AAAA,MAC5D;AACA,YAAM,gBAAgB,OAAK,aAAa,QAAQ,EAAE,IAAI,IAAI;AAE1D,YAAM,SAAS,aAAW;AACxB,cAAM,MAAM,OAAO,OAAO,IAAI,QAAQ,IAAI,aAAa,QAAQ;AAC/D,YAAI,QAAQ,UAAa,QAAQ,QAAQ,IAAI,kBAAkB,MAAM;AACnE,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,IAAI;AAChB,eAAO,cAAc,aAAa,QAAQ,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG,SAAS,QAAQ,aAAa,CAAC;AAAA,MACpH;AAEA,UAAI,oBAAoB,CAACC,KAAIC,WAAU,OAAO,GAAG,WAAW;AAC1D,YAAID,IAAG,OAAO,CAAC,GAAG;AAChB,iBAAO,SAAS,KAAK,KAAK;AAAA,QAC5B,WAAW,WAAW,MAAM,KAAK,OAAO,KAAK,GAAG;AAC9C,iBAAO,SAAS,KAAK;AAAA,QACvB,OAAO;AACL,iBAAOC,UAAS,OAAO,GAAG,MAAM;AAAA,QAClC;AAAA,MACF;AAEA,YAAM,aAAa,CAAC,OAAO,WAAW,WAAW;AAC/C,YAAI,UAAU,MAAM;AACpB,cAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,eAAO,QAAQ,YAAY;AACzB,oBAAU,QAAQ;AAClB,gBAAM,KAAK,aAAa,QAAQ,OAAO;AACvC,cAAI,UAAU,EAAE,GAAG;AACjB,mBAAO,SAAS,KAAK,EAAE;AAAA,UACzB,WAAW,KAAK,EAAE,GAAG;AACnB;AAAA,UACF;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,YAAY,CAAC,OAAO,WAAW,WAAW;AAC9C,cAAMD,MAAK,CAAC,GAAG,SAAS,KAAK,CAAC;AAC9B,eAAO,kBAAkBA,KAAI,YAAY,OAAO,WAAW,MAAM;AAAA,MACnE;AACA,YAAM,UAAU,CAAC,OAAO,cAAc;AACpC,cAAM,OAAO,UAAQ,UAAU,aAAa,QAAQ,IAAI,CAAC;AACzD,cAAM,SAAS,KAAK,MAAM,IAAI,YAAY,IAAI;AAC9C,eAAO,OAAO,IAAI,aAAa,OAAO;AAAA,MACxC;AAEA,YAAM,WAAW,CAAC,OAAO,UAAU,WAAW,WAAW,OAAO,OAAK,KAAK,GAAG,QAAQ,GAAG,MAAM;AAC9F,YAAM,UAAU,CAAC,OAAO,aAAa,QAAQ,OAAO,OAAK,KAAK,GAAG,QAAQ,CAAC;AAC1E,YAAM,aAAa,CAAC,OAAO,aAAa,IAAI,UAAU,KAAK;AAC3D,YAAM,YAAY,CAAC,OAAO,UAAU,WAAW;AAC7C,cAAMA,MAAK,CAAC,SAASE,cAAa,KAAK,SAASA,SAAQ;AACxD,eAAO,kBAAkBF,KAAI,UAAU,OAAO,UAAU,MAAM;AAAA,MAChE;AAEA,YAAM,UAAU,YAAU,UAAU,QAAQ,mBAAmB;AAC/D,YAAM,aAAa,CAAC,SAAS,iBAAiB,UAAU;AACtD,YAAI,OAAO,OAAO,GAAG;AACnB,iBAAO,QAAQ,IAAI;AAAA,QACrB,OAAO;AACL,iBAAO,QAAQ,OAAO,EAAE,KAAK,SAAS,cAAc,GAAG,cAAY,SAAS,QAAQ,MAAM,MAAM;AAAA,QAClG;AAAA,MACF;AACA,YAAM,WAAW,aAAW,QAAQ,IAAI;AAExC,YAAM,cAAc,SAAO,IAAI,SAAS,YAAY;AACpD,YAAM,UAAU,YAAU,aAAa,QAAQ,OAAO,QAAQ,CAAC;AAC/D,YAAM,YAAY,YAAU,aAAW,GAAG,SAAS,QAAQ,MAAM,CAAC;AAClE,YAAM,iBAAiB,CAAAG,UAAQA,QAAOA,MAAK,QAAQ,OAAO,EAAE,IAAI;AAChE,YAAM,cAAc,CAAAA,UAAQ,gBAAgB,KAAKA,KAAI,IAAIA,QAAO,OAAOA;AACvE,YAAM,oBAAoB,YAAU,aAAa,QAAQ,OAAO,UAAU,SAAS,CAAC;AACpF,YAAM,kBAAkB,YAAU,aAAa,QAAQ,OAAO,UAAU,OAAO,CAAC;AAChF,YAAM,sBAAsB,CAAAC,UAAQ,UAAUA,OAAM,MAAM,OAAO,CAAC,EAAE,OAAO,UAAU;AAErF,YAAM,aAAa,CAAC,OAAO,cAAc,OAAO,WAAW,KAAK,GAAG,SAAS;AAC5E,YAAM,gBAAgB,CAAC,OAAO,cAAc;AAC1C,YAAI,SAAS,CAAC;AACd,aAAK,WAAW,KAAK,GAAG,OAAK;AAC3B,cAAI,UAAU,CAAC,GAAG;AAChB,qBAAS,OAAO,OAAO,CAAC,CAAC,CAAC;AAAA,UAC5B;AACA,mBAAS,OAAO,OAAO,cAAc,GAAG,SAAS,CAAC;AAAA,QACpD,CAAC;AACD,eAAO;AAAA,MACT;AAEA,YAAM,aAAa,CAAC,OAAO,aAAa,WAAW,OAAO,OAAK,KAAK,GAAG,QAAQ,CAAC;AAChF,YAAM,cAAc,CAAC,OAAO,aAAa,MAAM,UAAU,KAAK;AAE9D,YAAM,SAAS,CAAC,KAAKL,MAAK,UAAU;AAClC,YAAI,SAAS,KAAK,KAAK,UAAU,KAAK,KAAK,SAAS,KAAK,GAAG;AAC1D,cAAI,aAAaA,MAAK,QAAQ,EAAE;AAAA,QAClC,OAAO;AACL,kBAAQ,MAAM,uCAAuCA,MAAK,aAAa,OAAO,eAAe,GAAG;AAChG,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QAClD;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,SAASA,MAAK,UAAU;AACrC,eAAO,QAAQ,KAAKA,MAAK,KAAK;AAAA,MAChC;AACA,YAAM,SAAS,CAAC,SAAS,UAAU;AACjC,cAAM,MAAM,QAAQ;AACpB,eAAO,OAAO,CAAC,GAAG,MAAM;AACtB,iBAAO,KAAK,GAAG,CAAC;AAAA,QAClB,CAAC;AAAA,MACH;AACA,YAAM,QAAQ,CAAC,SAASA,SAAQ;AAC9B,cAAM,IAAI,QAAQ,IAAI,aAAaA,IAAG;AACtC,eAAO,MAAM,OAAO,SAAY;AAAA,MAClC;AACA,YAAM,SAAS,CAAC,SAASA,SAAQ,SAAS,KAAK,MAAM,SAASA,IAAG,CAAC;AAClE,YAAM,WAAW,CAAC,SAASA,SAAQ;AACjC,gBAAQ,IAAI,gBAAgBA,IAAG;AAAA,MACjC;AACA,YAAM,QAAQ,aAAW,MAAM,QAAQ,IAAI,YAAY,CAAC,KAAK,SAAS;AACpE,YAAI,KAAK,IAAI,IAAI,KAAK;AACtB,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,YAAM,KAAK,CAAC,KAAK,KAAK,aAAa,iBAAiB,IAAI,OAAO,UAAQ,WAAW,MAAM,GAAG,CAAC;AAC5F,YAAM,MAAM,SAAO;AACjB,cAAM,IAAI,CAAC;AACX,cAAM,OAAO,OAAK;AAChB,YAAE,KAAK,CAAC;AAAA,QACV;AACA,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,CAAC,EAAE,KAAK,IAAI;AAAA,QAClB;AACA,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,GAAG,OAAO,KAAK,GAAG,OAAO,IAAI,SAAS,KAAK,EAAE,GAAG,SAAS,GAAG,GAAG,SAAS,CAAC,CAAC,IAAI,SAAS,KAAK;AACzH,YAAM,UAAU,SAAO,IAAI,KAAK,QAAQ;AACxC,YAAM,SAAS,CAAC,GAAG,MAAM,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK;AAE9D,YAAM,kBAAkB,CAAC,KAAK,aAAa;AACzC,eAAO,IAAI,UAAU,QAAQ;AAAA,MAC/B;AAEA,YAAM,aAAa,CAAC,KAAK,QAAQ,UAAU,WAAW,MAAM,IAAI,UAAU,OAAO,UAAU,IAAI,OAAO,OAAO,QAAQ,OAAO,MAAM,MAAM;AACxI,YAAM,gBAAgB,CAAC,KAAK,WAAW;AACrC,eAAO,WAAW,KAAK,MAAM,IAAI,gBAAgB,KAAK,OAAO,MAAM,IAAI;AAAA,MACzE;AACA,YAAM,aAAa,CAAC,KAAK,WAAW;AAClC,eAAO,WAAW,KAAK,QAAQ,CAAC;AAAA,MAClC;AACA,YAAM,QAAQ,OAAK,OAAK,EAAE,QAAQ,GAAG,EAAE;AACvC,YAAM,OAAO,MAAM,YAAY;AAC/B,YAAM,aAAa,OAAK,EAAE,SAAS;AACnC,YAAM,UAAU,OAAK,CAAC,WAAW,CAAC;AAClC,YAAM,QAAQ,CAAC,OAAO,QAAQ,OAAO;AACnC,cAAM,MAAM,SAAS,OAAO,KAAK;AACjC,eAAO,MAAM,GAAG,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,GAAG;AAAA,MACzD;AACA,YAAM,UAAU,WAAS;AACvB,cAAM,MAAM,WAAW,KAAK;AAC5B,eAAO,MAAM,GAAG,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,GAAG;AAAA,MACzD;AAEA,YAAM,cAAc,SAAO,IAAI,UAAU,UAAa,WAAW,IAAI,MAAM,gBAAgB;AAE3F,YAAM,cAAc,CAAC,KAAK,UAAU,UAAU;AAC5C,YAAI,CAAC,SAAS,KAAK,GAAG;AACpB,kBAAQ,MAAM,sCAAsC,UAAU,aAAa,OAAO,eAAe,GAAG;AACpG,gBAAM,IAAI,MAAM,iCAAiC,KAAK;AAAA,QACxD;AACA,YAAI,YAAY,GAAG,GAAG;AACpB,cAAI,MAAM,YAAY,UAAU,KAAK;AAAA,QACvC;AAAA,MACF;AACA,YAAM,iBAAiB,CAAC,KAAK,aAAa;AACxC,YAAI,YAAY,GAAG,GAAG;AACpB,cAAI,MAAM,eAAe,QAAQ;AAAA,QACnC;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,SAAS,UAAU,UAAU;AAC1C,cAAM,MAAM,QAAQ;AACpB,oBAAY,KAAK,UAAU,KAAK;AAAA,MAClC;AACA,YAAM,QAAQ,CAAC,SAAS,aAAa;AACnC,cAAM,MAAM,QAAQ;AACpB,cAAM,SAAS,OAAO,iBAAiB,GAAG;AAC1C,cAAM,IAAI,OAAO,iBAAiB,QAAQ;AAC1C,eAAO,MAAM,MAAM,CAAC,OAAO,OAAO,IAAI,kBAAkB,KAAK,QAAQ,IAAI;AAAA,MAC3E;AACA,YAAM,oBAAoB,CAAC,KAAK,aAAa,YAAY,GAAG,IAAI,IAAI,MAAM,iBAAiB,QAAQ,IAAI;AACvG,YAAM,SAAS,CAAC,SAAS,aAAa;AACpC,cAAM,MAAM,QAAQ;AACpB,cAAM,MAAM,kBAAkB,KAAK,QAAQ;AAC3C,eAAO,SAAS,KAAK,GAAG,EAAE,OAAO,OAAK,EAAE,SAAS,CAAC;AAAA,MACpD;AACA,YAAM,WAAW,CAAC,SAAS,aAAa;AACtC,cAAM,MAAM,QAAQ;AACpB,uBAAe,KAAK,QAAQ;AAC5B,YAAI,GAAG,OAAO,SAAS,OAAO,EAAE,IAAI,IAAI,GAAG,EAAE,GAAG;AAC9C,mBAAS,SAAS,OAAO;AAAA,QAC3B;AAAA,MACF;AAEA,YAAM,eAAe,CAACK,OAAMN,OAAM,WAAW,MAAM,OAAOM,OAAMN,KAAI,EAAE,IAAI,WAAS,SAAS,OAAO,EAAE,CAAC,EAAE,MAAM,QAAQ;AAEtH,YAAM,aAAa,CAAC,OAAO,aAAa;AACtC,eAAO,iBAAiB,OAAO,UAAU,MAAM;AAAA,MACjD;AACA,YAAM,mBAAmB,CAAC,OAAO,UAAU,cAAc;AACvD,eAAO,KAAK,WAAW,KAAK,GAAG,OAAK;AAClC,cAAI,KAAK,GAAG,QAAQ,GAAG;AACrB,mBAAO,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAAA,UAC/B,OAAO;AACL,mBAAO,iBAAiB,GAAG,UAAU,SAAS;AAAA,UAChD;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,iBAAiB,gBAAc,SAAS,kBAAkB,UAAU;AAC1E,YAAM,OAAO,CAACO,OAAMC,cAAa;AAAA,QAC/B,MAAAD;AAAA,QACA,SAAAC;AAAA,MACF;AACA,YAAM,SAAS,CAAC,SAAS,SAAS,aAAa;AAAA,QAC7C;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,CAAC,SAAS,SAAS,SAAS,KAAK,QAAQ,cAAc;AAAA,QACtE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY,CAAC,SAASC,QAAO,aAAa;AAAA,QAC9C;AAAA,QACA,OAAAA;AAAA,QACA;AAAA,MACF;AACA,YAAM,SAAS,CAAC,UAAU,UAAU,WAAW,eAAe;AAAA,QAC5D;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY,CAAC,SAAS,SAAS,YAAY;AAAA,QAC/C;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,CAAC,SAASD,cAAa;AAAA,QACtC;AAAA,QACA,SAAAA;AAAA,MACF;AAEA,YAAM,SAAS,CAAC,MAAM,SAAS,SAAS,UAAU;AAChD,YAAI,OAAO,OAAO,GAAG;AACnB,iBAAO,SAAS,KAAK;AAAA,QACvB;AACA,YAAI,SAAS,MAAM,KAAK,OAAO,CAAC,GAAG;AACjC,iBAAO,SAAS,KAAK,OAAO;AAAA,QAC9B;AACA,cAAM,qBAAqB,SAAO,KAAK,KAAK,OAAO,KAAK,OAAO,GAAG;AAClE,eAAO,SAAS,SAAS,KAAK,KAAK,GAAG,GAAG,kBAAkB;AAAA,MAC7D;AACA,YAAM,OAAO,CAAC,SAAS,WAAW,OAAO;AAAA,QACvC;AAAA,QACA;AAAA,MACF,GAAG,SAAS,MAAM;AAClB,YAAM,QAAQ,CAAAL,cAAY,WAAWA,WAAU,OAAO;AACtD,YAAM,UAAU,CAAAA,cAAY;AAC1B,YAAI,KAAKA,WAAU,UAAU,GAAG;AAC9B,iBAAO,WAAWA,WAAU,KAAK;AAAA,QACnC,OAAO;AACL,iBAAO,KAAK,aAAaA,SAAQ,GAAG,iBAAe,WAAW,aAAa,KAAK,CAAC;AAAA,QACnF;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,SAAS,WAAW,UAAU,SAAS,SAAS,MAAM;AACrE,YAAM,OAAO,CAAAA,cAAY,WAAWA,WAAU,IAAI;AAClD,YAAM,eAAe,CAAAA,cAAY,MAAMA,SAAQ,EAAE,KAAK,SAAS,CAAC,CAAC,GAAG,CAAAO,WAAS,WAAWA,QAAO,UAAU,CAAC;AAE1G,YAAM,sBAAsB,CAAC,OAAO,eAAe,IAAI,OAAO,SAAO;AACnE,YAAI,KAAK,GAAG,MAAM,YAAY;AAC5B,gBAAMD,SAAQ,IAAI,QAAQ,GAAG,GAAG,YAAU;AACxC,kBAAM,UAAU,aAAa,QAAQ,QAAQ,CAAC;AAC9C,mBAAO,OAAO,QAAQ,GAAG,OAAO;AAAA,UAClC,CAAC;AACD,iBAAO,UAAU,KAAKA,QAAO,UAAU;AAAA,QACzC,OAAO;AACL,gBAAM,UAAU,IAAI,MAAM,GAAG,GAAG,CAAAH,UAAQ;AACtC,kBAAM,UAAU,aAAaA,OAAM,WAAW,CAAC;AAC/C,kBAAM,UAAU,aAAaA,OAAM,WAAW,CAAC;AAC/C,mBAAO,OAAOA,OAAM,SAAS,OAAO;AAAA,UACtC,CAAC;AACD,iBAAO,UAAU,KAAK,SAAS,WAAW,GAAG,CAAC;AAAA,QAChD;AAAA,MACF,CAAC;AACD,YAAM,mBAAmB,WAAS,OAAO,KAAK,EAAE,IAAI,CAAAK,YAAU;AAC5D,cAAM,aAAa,KAAKA,OAAM;AAC9B,eAAO,eAAe,UAAU,IAAI,aAAa;AAAA,MACnD,CAAC,EAAE,MAAM,OAAO;AAChB,YAAM,cAAc,CAAAD,WAAS;AAC3B,cAAM,SAAS,KAAKA,MAAK;AACzB,cAAM,iBAAiB,aAAaA,MAAK;AACzC,cAAM,QAAQ;AAAA,UACZ,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AACA,eAAO,oBAAoB,OAAO,gBAAgB;AAAA,MACpD;AAEA,YAAM,kBAAkB;AACxB,YAAM,4BAA4B,CAAAA,WAAS,OAAOA,QAAO,eAAe,EAAE,KAAK,kBAAgB,SAAS,KAAK,aAAa,MAAM,MAAM,CAAC,CAAC,EAAE,IAAI,gBAAc,YAAY,YAAY,MAAM,CAAC;AAE3L,YAAM,MAAM,CAAC,KAAK,WAAW;AAC3B,eAAO,MAAM,MAAM;AAAA,MACrB;AACA,YAAM,QAAQ,CAAC,WAAW,KAAK,WAAW,SAAS,KAAK,UAAU,OAAO,IAAI,KAAK,MAAM,CAAC,CAAC;AAC1F,YAAM,WAAW,CAAC,WAAW,MAAM,eAAe;AAChD,cAAM,WAAW,YAAY,WAAW,CAAAE,YAAU;AAChD,iBAAO,WAAW,MAAMA,QAAO,OAAO;AAAA,QACxC,CAAC;AACD,eAAO,SAAS,SAAS,IAAI,SAAS,KAAK,SAAS,CAAC,CAAC,IAAI,SAAS,KAAK;AAAA,MAC1E;AACA,YAAM,cAAc,CAAC,WAAW,cAAc;AAC5C,cAAMb,OAAM,KAAK,UAAU,KAAK,OAAK;AACnC,iBAAO,EAAE;AAAA,QACX,CAAC;AACD,eAAO,OAAOA,MAAK,SAAS;AAAA,MAC9B;AACA,YAAM,kBAAkB,aAAW;AACjC,cAAM,eAAe,CAAC;AACtB,YAAI,QAAQ;AACZ,aAAK,QAAQ,OAAO,YAAU;AAC5B,gBAAM,UAAU,OAAO;AACvB,gBAAM,SAAS,iBAAe;AAC5B,kBAAM,WAAW,QAAQ;AACzB,yBAAa,QAAQ,IAAI,UAAU,OAAO,SAAS,SAAS,QAAQ;AAAA,UACtE,CAAC;AACD,mBAAS;AAAA,QACX,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,aAAa,UAAQ;AACzB,cAAM,SAAS,CAAC;AAChB,cAAMU,SAAQ,CAAC;AACf,cAAM,WAAW,KAAK,IAAI,EAAE,IAAI,aAAW,QAAQ,OAAO,EAAE,KAAK,KAAK;AACtE,cAAM,gBAAgB,SAAS,KAAK,yBAAyB,EAAE,MAAM,CAAC,CAAC;AACvE,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,cAAM;AAAA,UACJ,MAAM;AAAA,UACN,MAAMF;AAAA,QACR,IAAI,UAAU,MAAM,aAAW,QAAQ,YAAY,UAAU;AAC7D,aAAKA,OAAM,aAAW;AACpB,gBAAM,aAAa,CAAC;AACpB,eAAK,QAAQ,OAAO,aAAW;AAC7B,gBAAI,QAAQ;AACZ,mBAAO,OAAO,IAAI,UAAU,KAAK,CAAC,MAAM,QAAW;AACjD;AAAA,YACF;AACA,kBAAM,WAAW,kBAAkB,eAAe,MAAM,SAAS,CAAC;AAClE,kBAAM,UAAU,SAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ,SAAS,UAAU,OAAO,QAAQ;AACrG,qBAAS,yBAAyB,GAAG,yBAAyB,QAAQ,SAAS,0BAA0B;AACvG,uBAAS,sBAAsB,GAAG,sBAAsB,QAAQ,SAAS,uBAAuB;AAC9F,sBAAM,cAAc,WAAW;AAC/B,sBAAM,iBAAiB,QAAQ;AAC/B,sBAAM,SAAS,IAAI,aAAa,cAAc;AAC9C,uBAAO,MAAM,IAAI;AACjB,6BAAa,KAAK,IAAI,YAAY,iBAAiB,CAAC;AAAA,cACtD;AAAA,YACF;AACA,uBAAW,KAAK,OAAO;AAAA,UACzB,CAAC;AACD;AACA,UAAAE,OAAM,KAAK,UAAU,QAAQ,SAAS,YAAY,QAAQ,OAAO,CAAC;AAClE;AAAA,QACF,CAAC;AACD,cAAM,EAAC,SAAAD,UAAS,UAAS,IAAI,KAAK,YAAY,EAAE,IAAI,aAAW;AAC7D,gBAAMA,WAAU,gBAAgB,OAAO;AACvC,gBAAM,aAAa,SAAS,QAAQ,SAAS,OAAOA,QAAO,CAAC;AAC5D,iBAAO;AAAA,YACL,WAAW,CAAC,UAAU;AAAA,YACtB,SAAAA;AAAA,UACF;AAAA,QACF,CAAC,EAAE,WAAW,OAAO;AAAA,UACnB,WAAW,CAAC;AAAA,UACZ,SAAS,CAAC;AAAA,QACZ,EAAE;AACF,cAAM,SAAS,KAAK,SAAS,UAAU;AACvC,eAAO;AAAA,UACL,MAAM;AAAA,UACN;AAAA,UACA,KAAKC;AAAA,UACL,SAAAD;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,YAAY,CAAAE,WAAS;AACzB,cAAM,OAAO,YAAYA,MAAK;AAC9B,eAAO,WAAW,IAAI;AAAA,MACxB;AACA,YAAM,YAAY,eAAa,KAAK,UAAU,KAAK,OAAK,EAAE,KAAK;AAC/D,YAAM,cAAc,eAAa,OAAO,UAAU,OAAO;AACzD,YAAM,aAAa,eAAa,KAAK,UAAU,OAAO,EAAE,SAAS;AACjE,YAAM,cAAc,CAAC,WAAW,gBAAgB,SAAS,KAAK,UAAU,QAAQ,WAAW,CAAC;AAC5F,YAAM,YAAY;AAAA,QAChB;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE9D,YAAM,sBAAsB,CAAC,KAAK,KAAKV,UAAS;AAC9C,cAAMS,SAAQ,IAAI,OAAO,SAAS,GAAG;AACrC,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,gBAAM,eAAe,IAAI,SAASA,OAAM,CAAC,GAAGT,KAAI;AAChD,cAAI,YAAY,eAAe,GAAG;AAChC,8BAAkB;AAAA,UACpB;AACA,cAAI,oBAAoB,cAAc;AACpC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,WAAW,CAAC,QAAQ,KAAKA,UAAS;AACtC,iBAAS,KAAK,oBAAoB,MAAM,GAAG,GAAG,WAAS;AACrD,cAAI,UAAUA,OAAM;AAClB,mBAAO,UAAU,OAAO,UAAU,OAAO,CAAC,GAAG,GAAG;AAAA,UAClD;AAAA,QACF,CAAC;AACD,YAAIA,OAAM;AACR,iBAAO,UAAU,MAAM,UAAUA,OAAM,CAAC,GAAG,GAAG;AAAA,QAChD;AAAA,MACF;AACA,YAAM,YAAY,CAAC,QAAQ,KAAKA,UAAS;AACvC,iBAAS,KAAK,oBAAoB,MAAM,GAAG,GAAG,WAAS;AACrD,cAAI,UAAUA,OAAM;AAClB,mBAAO,UAAU,OAAO,WAAW,OAAO,CAAC,GAAG,GAAG;AAAA,UACnD;AAAA,QACF,CAAC;AACD,YAAIA,OAAM;AACR,iBAAO,UAAU,MAAM,WAAWA,OAAM,CAAC,GAAG,GAAG;AAAA,QACjD;AAAA,MACF;AAEA,YAAM,oBAAoB,CAAC,QAAQU,QAAO,SAAS;AACjD,eAAO,SAAS,iBAAiB;AAAA,UAC/B,GAAG;AAAA,UACH,OAAAA;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,CAAC,IAAI,aAAa,QAAQ,EAAE,EAAE,MAAM,QAAQ;AAC7D,YAAM,UAAU,CAAC,SAASV,OAAM,aAAa,SAAS,MAAM,SAASA,KAAI,GAAG,QAAQ;AACpF,YAAM,qBAAqB,CAAC,SAASK,OAAM,OAAO,UAAU;AAC1D,cAAM,eAAe,QAAQ,SAAS,WAAY,KAAM,IAAI,CAAC;AAC7D,cAAM,eAAe,QAAQ,SAAS,WAAY,KAAM,IAAI,CAAC;AAC7D,cAAM,cAAc,QAAQ,SAAS,UAAW,KAAM,UAAU,CAAC;AACjE,cAAM,cAAc,QAAQ,SAAS,UAAW,KAAM,UAAU,CAAC;AACjE,eAAOA,QAAO,eAAe,eAAe,cAAc;AAAA,MAC5D;AACA,YAAM,qBAAqB,CAAC,SAAS,cAAc;AACjD,cAAM,MAAM,QAAQ;AACpB,cAAM,QAAQ,IAAI,sBAAsB,EAAE,SAAS,IAAI;AACvD,eAAO,cAAc,eAAe,QAAQ,mBAAmB,SAAS,OAAO,QAAQ,OAAO;AAAA,MAChG;AACA,YAAM,gBAAgB,aAAW,mBAAmB,SAAS,aAAa;AAE1E,YAAM,WAAW;AAEjB,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,aAAa;AAEvD,YAAM,sBAAsB;AAC5B,YAAM,0BAA0B,MAAM,GAAG,OAAK;AAC5C,cAAMA,QAAO,GAAI,IAAI,CAAE;AACvB,eAAO;AAAA,UACL,OAAOA;AAAA,UACP,OAAOA;AAAA,QACT;AAAA,MACF,CAAC;AACD,YAAM,0BAA0B,IAAI;AAAA,QAClC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,CAAAP,UAAQ;AACT,eAAO;AAAA,UACL,OAAOA;AAAA,UACP,OAAOA,MAAK,YAAY;AAAA,QAC1B;AAAA,MACF,CAAC;AACD,YAAM,eAAe;AACrB,YAAM,sBAAsB,YAAU;AACpC,YAAI;AACJ,cAAM,MAAM,OAAO;AACnB,cAAM,eAAe,KAAK,IAAI,UAAU,OAAO,UAAU,SAAS,GAAG,IAAI,OAAO,OAAO,QAAQ,OAAO,SAAS,KAAK,OAAO,QAAQ;AACnI,eAAO,SAAS,aAAa,QAAQ,WAAW,CAAC,IAAI;AAAA,MACvD;AACA,YAAM,yBAAyB,CAAC,QAAQ,kBAAkB;AACxD,YAAI,mBAAmB,MAAM,KAAK,CAAC,mBAAmB,MAAM,GAAG;AAC7D,iBAAO;AAAA,QACT,WAAW,eAAe,MAAM,GAAG;AACjC,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO,oBAAoB,MAAM;AAAA,UACnC;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,6BAA6B,CAAC,QAAQ,sBAAsB;AAChE,YAAI,mBAAmB,MAAM,KAAK,mBAAmB,MAAM,GAAG;AAC5D,iBAAO;AAAA,QACT,WAAW,eAAe,MAAM,GAAG;AACjC,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO,oBAAoB,MAAM;AAAA,UACnC;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS,CAAAE,UAAQ,YAAU,OAAO,QAAQ,IAAIA,KAAI;AACxD,YAAM,WAAW,YAAU;AACzB,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,uBAAuB;AAAA,UACpC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,uBAAuB;AAAA,UACpC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,qBAAqB;AAAA,UAClC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,oBAAoB;AAAA,UACjC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,gBAAgB;AAAA,UAC7B,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,4BAA4B;AAAA,UACzC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,cAAc;AAAA,UAC3B,WAAW;AAAA,UACX,SAAS,CAAC,SAAS,WAAW,QAAQ;AAAA,QACxC,CAAC;AACD,uBAAe,yBAAyB;AAAA,UACtC,WAAW;AAAA,UACX,SAAS,CAAC;AAAA,QACZ,CAAC;AACD,uBAAe,wBAAwB;AAAA,UACrC,WAAW;AAAA,UACX,SAAS,CAAC;AAAA,QACZ,CAAC;AACD,uBAAe,oBAAoB;AAAA,UACjC,WAAW;AAAA,UACX,SAAS,CAAC;AAAA,QACZ,CAAC;AACD,uBAAe,iBAAiB;AAAA,UAC9B,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,8BAA8B;AAAA,UAC3C,WAAW;AAAA,UACX,SAAS,CAAC;AAAA,QACZ,CAAC;AACD,uBAAe,0BAA0B;AAAA,UACvC,WAAW;AAAA,UACX,SAAS,CAAC;AAAA,QACZ,CAAC;AAAA,MACH;AACA,YAAM,qBAAqB,OAAO,mBAAmB;AACrD,YAAM,uBAAuB,OAAO,qBAAqB;AACzD,YAAM,uBAAuB,OAAO,qBAAqB;AACzD,YAAM,qBAAqB,OAAO,mBAAmB;AACrD,YAAM,oBAAoB,OAAO,kBAAkB;AACnD,YAAM,sBAAsB,OAAO,cAAc;AACjD,YAAM,uBAAuB,OAAO,0BAA0B;AAC9D,YAAM,eAAe,OAAO,YAAY;AACxC,YAAM,qBAAqB,OAAO,oBAAoB;AACtD,YAAM,mBAAmB,OAAO,uBAAuB;AACvD,YAAM,kBAAkB,OAAO,sBAAsB;AACrD,YAAM,oBAAoB,OAAO,kBAAkB;AACnD,YAAM,aAAa,OAAO,eAAe;AACzC,YAAM,6BAA6B,OAAO,4BAA4B;AACtE,YAAM,yBAAyB,OAAO,wBAAwB;AAC9D,YAAM,iBAAiB,YAAU,mBAAmB,MAAM,MAAM;AAChE,YAAM,qBAAqB,YAAU,mBAAmB,MAAM,MAAM;AACpE,YAAM,mBAAmB,YAAU;AACjC,cAAM,UAAU,OAAO;AACvB,cAAM,gBAAgB,QAAQ,IAAI,sBAAsB;AACxD,eAAO,QAAQ,MAAM,sBAAsB,IAAI,gBAAgB,uBAAuB,QAAQ,aAAa;AAAA,MAC7G;AACA,YAAM,uBAAuB,YAAU;AACrC,cAAM,UAAU,OAAO;AACvB,cAAM,oBAAoB,QAAQ,IAAI,0BAA0B;AAChE,eAAO,QAAQ,MAAM,0BAA0B,IAAI,oBAAoB,2BAA2B,QAAQ,iBAAiB;AAAA,MAC7H;AAEA,YAAM,WAAW,CAACa,SAAQD,YAAW;AACnC,eAAOA,QAAO,UAAUC,QAAO,YAAYD,QAAO,SAASA,QAAO,UAAU,KAAKC,QAAO,aAAaD,QAAO,OAAOC,QAAO,YAAYD,QAAO,MAAMA,QAAO,UAAU,KAAKC,QAAO;AAAA,MAClL;AACA,YAAM,gBAAgB,CAAC,WAAWA,YAAW;AAC3C,YAAI,SAAS;AACb,cAAM,iBAAiB,MAAM,UAAUA,OAAM;AAC7C,iBAAS,IAAIA,QAAO,UAAU,KAAKA,QAAO,WAAW,KAAK;AACxD,mBAAS,IAAIA,QAAO,UAAU,KAAKA,QAAO,WAAW,KAAK;AACxD,qBAAS,UAAU,UAAU,MAAM,WAAW,GAAG,CAAC,EAAE,OAAO,cAAc;AAAA,UAC3E;AAAA,QACF;AACA,eAAO,SAAS,SAAS,KAAKA,OAAM,IAAI,SAAS,KAAK;AAAA,MACxD;AAEA,YAAM,YAAY,CAAC,SAAS,YAAY;AACtC,eAAO,OAAO,KAAK,IAAI,QAAQ,KAAK,QAAQ,GAAG,GAAG,KAAK,IAAI,QAAQ,QAAQ,QAAQ,MAAM,GAAG,KAAK,IAAI,QAAQ,MAAM,QAAQ,UAAU,GAAG,QAAQ,MAAM,QAAQ,UAAU,CAAC,GAAG,KAAK,IAAI,QAAQ,SAAS,QAAQ,UAAU,GAAG,QAAQ,SAAS,QAAQ,UAAU,CAAC,CAAC;AAAA,MAClQ;AACA,YAAM,YAAY,CAAC,WAAW,WAAW,eAAe;AACtD,cAAM,cAAc,UAAU,SAAS,WAAW,WAAW,EAAE;AAC/D,cAAM,eAAe,UAAU,SAAS,WAAW,YAAY,EAAE;AACjE,eAAO,YAAY,KAAK,QAAM;AAC5B,iBAAO,aAAa,IAAI,QAAM;AAC5B,mBAAO,UAAU,IAAI,EAAE;AAAA,UACzB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAAC,WAAW,WAAW,eAAe;AACrD,eAAO,UAAU,WAAW,WAAW,UAAU,EAAE,KAAK,CAAAA,YAAU;AAChE,iBAAO,cAAc,WAAWA,OAAM;AAAA,QACxC,CAAC;AAAA,MACH;AAEA,YAAM,SAAS,CAACH,QAAO,OAAOI,UAAS;AACrC,cAAM,YAAY,aAAaJ,MAAK;AACpC,eAAO,SAAS,WAAW,OAAOI,KAAI;AAAA,MACxC;AACA,YAAM,eAAe,UAAU;AAE/B,YAAM,SAAS,CAAC,QAAQ,YAAY;AAClC,cAAM,WAAW,OAAO,MAAM;AAC9B,iBAAS,KAAK,OAAK;AACjB,YAAE,IAAI,aAAa,QAAQ,KAAK,OAAO,GAAG;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,YAAM,UAAU,CAAC,QAAQ,YAAY;AACnC,cAAM,UAAU,YAAY,MAAM;AAClC,gBAAQ,KAAK,MAAM;AACjB,gBAAM,WAAW,OAAO,MAAM;AAC9B,mBAAS,KAAK,OAAK;AACjB,qBAAS,GAAG,OAAO;AAAA,UACrB,CAAC;AAAA,QACH,GAAG,OAAK;AACN,iBAAO,GAAG,OAAO;AAAA,QACnB,CAAC;AAAA,MACH;AACA,YAAM,UAAU,CAACH,SAAQ,YAAY;AACnC,cAAM,eAAe,WAAWA,OAAM;AACtC,qBAAa,KAAK,MAAM;AACtB,mBAASA,SAAQ,OAAO;AAAA,QAC1B,GAAG,OAAK;AACN,UAAAA,QAAO,IAAI,aAAa,QAAQ,KAAK,EAAE,GAAG;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAACA,SAAQ,YAAY;AACpC,QAAAA,QAAO,IAAI,YAAY,QAAQ,GAAG;AAAA,MACpC;AACA,YAAM,OAAO,CAAC,SAAS,YAAY;AACjC,eAAO,SAAS,OAAO;AACvB,iBAAS,SAAS,OAAO;AAAA,MAC3B;AAEA,YAAM,QAAQ,CAAC,QAAQ,aAAa;AAClC,aAAK,UAAU,CAAC,GAAG,MAAM;AACvB,gBAAM,IAAI,MAAM,IAAI,SAAS,SAAS,IAAI,CAAC;AAC3C,kBAAQ,GAAG,CAAC;AAAA,QACd,CAAC;AAAA,MACH;AACA,YAAM,SAAS,CAACA,SAAQ,aAAa;AACnC,aAAK,UAAU,OAAK;AAClB,mBAASA,SAAQ,CAAC;AAAA,QACpB,CAAC;AAAA,MACH;AAEA,YAAM,SAAS,aAAW;AACxB,cAAM,MAAM,QAAQ;AACpB,YAAI,IAAI,eAAe,MAAM;AAC3B,cAAI,WAAW,YAAY,GAAG;AAAA,QAChC;AAAA,MACF;AACA,YAAM,SAAS,aAAW;AACxB,cAAMI,YAAW,WAAW,OAAO;AACnC,YAAIA,UAAS,SAAS,GAAG;AACvB,gBAAM,SAASA,SAAQ;AAAA,QACzB;AACA,eAAO,OAAO;AAAA,MAChB;AAEA,YAAM,YAAY,CAACb,KAAIF,UAAS;AAC9B,cAAMgB,OAAM,aAAW;AACrB,cAAI,CAACd,IAAG,OAAO,GAAG;AAChB,kBAAM,IAAI,MAAM,kBAAkBF,QAAO,iBAAiBA,QAAO,OAAO;AAAA,UAC1E;AACA,iBAAO,UAAU,OAAO,EAAE,MAAM,EAAE;AAAA,QACpC;AACA,cAAM,YAAY,aAAWE,IAAG,OAAO,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,IAAI,SAAS,KAAK;AAChG,cAAMe,OAAM,CAAC,SAAS,UAAU;AAC9B,cAAI,CAACf,IAAG,OAAO,GAAG;AAChB,kBAAM,IAAI,MAAM,sBAAsBF,QAAO,iBAAiBA,QAAO,OAAO;AAAA,UAC9E;AACA,kBAAQ,IAAI,YAAY;AAAA,QAC1B;AACA,eAAO;AAAA,UACL,KAAAgB;AAAA,UACA;AAAA,UACA,KAAAC;AAAA,QACF;AAAA,MACF;AAEA,YAAM,MAAM,UAAU,QAAQ,MAAM;AACpC,YAAM,MAAM,aAAW,IAAI,IAAI,OAAO;AACtC,YAAM,MAAM,CAAC,SAAS,UAAU,IAAI,IAAI,SAAS,KAAK;AAEtD,UAAI,gBAAgB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,UAAI,cAAc,MAAM;AACtB,cAAM,UAAU,aAAW;AACzB,iBAAO,aAAa,QAAQ,QAAQ,IAAI,UAAU,KAAK,CAAC;AAAA,QAC1D;AACA,cAAMC,YAAW,aAAW,gBAAgB,OAAO,EAAE;AACrD,cAAM,aAAa,aAAW;AAC5B,cAAI,CAAC,UAAU,OAAO,GAAG;AACvB,mBAAO;AAAA,UACT;AACA,cAAI,KAAK,OAAO,MAAM,QAAQ;AAC5B,mBAAO;AAAA,UACT;AACA,iBAAO,SAAS,eAAe,KAAK,OAAO,CAAC;AAAA,QAC9C;AACA,cAAM,aAAa,aAAW;AAC5B,cAAI,CAAC,UAAU,OAAO,GAAG;AACvB,mBAAO;AAAA,UACT;AACA,iBAAO,SAAS;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,GAAG,KAAK,OAAO,CAAC;AAAA,QAClB;AACA,cAAM,gBAAgB,aAAW,UAAU,OAAO,KAAK,MAAM,SAAS,iBAAiB,MAAM;AAC7F,cAAM,kBAAkB,CAAC,SAAS,UAAU;AAC1C,iBAAO,QAAQ,IAAI,wBAAwB,MAAM,GAAG;AAAA,QACtD;AACA,cAAM,mBAAmB,CAAC,QAAQ,gBAAgB;AAChD,gBAAM,KAAK,MAAM,MAAM;AACvB,iBAAO,aAAa,EAAE;AAAA,QACxB;AACA,cAAM,YAAY,aAAW;AAC3B,gBAAM,MAAM,KAAK,OAAO;AACxB,iBAAO,SAAS;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,GAAG,GAAG;AAAA,QACR;AACA,cAAM,cAAc,aAAW,UAAU,OAAO,IAAI,OAAO,SAAS,MAAM,IAAI,SAAS,KAAK;AAC5F,eAAO;AAAA,UACL,IAAI,SAAS;AAAA,YACX,UAAU;AAAA,YACV,SAAS;AAAA,YACT,WAAW;AAAA,YACX,KAAK;AAAA,UACP,CAAC;AAAA,UACD,MAAM,SAAS;AAAA,YACb,UAAU;AAAA,YACV,WAAW;AAAA,UACb,CAAC;AAAA,UACD,QAAQ,SAAS;AAAA,YACf,KAAK;AAAA,YACL;AAAA,YACA,KAAK;AAAA,YACL,QAAQ;AAAA,UACV,CAAC;AAAA,UACD,OAAO,SAAS;AAAA,YACd,KAAK;AAAA,YACL,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV,CAAC;AAAA,UACD,QAAQ,SAAS;AAAA,YACf;AAAA,YACA,OAAO;AAAA,YACP,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,WAAW;AAAA,YACX;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,QAAQ,SAAS;AAAA,YACf;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,QAAQ,SAAS;AAAA,YACf,IAAI,aAAa;AAAA,YACjB,OAAO;AAAA,YACP,MAAM,aAAa;AAAA,UACrB,CAAC;AAAA,UACD,OAAO,SAAS;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,UAAU,SAAS;AAAA,YACjB,UAAU;AAAA,YACV;AAAA,YACA;AAAA,YACA,UAAAA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAAS;AAAA,YACT,SAAS;AAAA,YACT;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD;AAAA,UACA,IAAI;AAAA,QACN;AAAA,MACF;AAEA,YAAM,MAAM,CAACC,WAAU,MAAM,UAAU,MAAM;AAC3C,cAAMC,QAAO,SAAS,CAAC;AACvB,cAAM,OAAO,SAAS,MAAM,CAAC;AAC7B,eAAO,EAAED,WAAU,MAAMC,OAAM,IAAI;AAAA,MACrC;AACA,YAAM,SAAS,CAACD,WAAU,MAAM,aAAa;AAC3C,eAAO,SAAS,SAAS,IAAI,IAAIA,WAAU,MAAM,UAAU,SAAS,IAAI,SAAS,KAAK;AAAA,MACxF;AACA,YAAM,YAAY,CAACA,WAAU,MAAMC,OAAM,SAAS;AAChD,cAAM,QAAQ,KAAKD,WAAUC,KAAI;AACjC,eAAO,MAAM,MAAM,CAAC,GAAG,MAAM;AAC3B,gBAAM,UAAU,KAAKD,WAAU,CAAC;AAChC,iBAAO,cAAcA,WAAU,GAAG,OAAO;AAAA,QAC3C,GAAG,KAAK;AAAA,MACV;AACA,YAAM,gBAAgB,CAACA,WAAU,OAAO,QAAQ;AAC9C,eAAO,MAAM,KAAK,OAAK;AACrB,iBAAO,IAAI,OAAO,MAAMA,UAAS,IAAI,CAAC,CAAC;AAAA,QACzC,CAAC;AAAA,MACH;AAEA,YAAM,cAAc;AAEpB,YAAM,WAAW,YAAY;AAC7B,YAAM,YAAY,CAAC,MAAM,aAAa;AACpC,eAAO,YAAY,UAAU,CAAC,WAAW,YAAY;AACnD,iBAAO,KAAK,OAAO;AAAA,QACrB,GAAG,QAAQ;AAAA,MACb;AAEA,YAAM,cAAc,eAAa;AAC/B,eAAO,SAAS,WAAW,OAAO;AAAA,MACpC;AACA,YAAM,aAAa,CAAC,WAAW,aAAa;AAC1C,cAAM,OAAO,YAAY,WAAW,QAAQ;AAC5C,eAAO,KAAK,SAAS,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK;AAAA,MAC/D;AACA,YAAM,WAAW,CAAC,WAAW,uBAAuB,yBAAyB;AAC3E,eAAO,WAAW,WAAW,qBAAqB,EAAE,KAAK,WAAS;AAChE,iBAAO,WAAW,WAAW,oBAAoB,EAAE,KAAK,CAAAL,UAAQ;AAC9D,mBAAO,UAAU,aAAa;AAAA,cAC5B;AAAA,cACAA;AAAA,YACF,CAAC,EAAE,IAAI,CAAAJ,WAAS;AACd,qBAAO;AAAA,gBACL;AAAA,gBACA,MAAAI;AAAA,gBACA,OAAAJ;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,CAAC,WAAW,aAAa;AACxC,eAAO,WAAW,WAAW,QAAQ;AAAA,MACvC;AACA,YAAM,cAAc,CAAC,WAAW,uBAAuB,yBAAyB;AAC9E,eAAO,SAAS,WAAW,uBAAuB,oBAAoB,EAAE,KAAK,WAAS;AACpF,gBAAM,SAAS,CAAAP,cAAY;AACzB,mBAAO,GAAG,WAAWA,SAAQ;AAAA,UAC/B;AACA,gBAAM,kBAAkB;AACxB,gBAAM,gBAAgB,SAAS,MAAM,OAAO,iBAAiB,MAAM;AACnE,gBAAM,eAAe,SAAS,MAAM,MAAM,iBAAiB,MAAM;AACjE,iBAAO,cAAc,KAAK,QAAM;AAC9B,mBAAO,aAAa,KAAK,QAAM;AAC7B,qBAAO,GAAG,IAAI,EAAE,IAAI,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,IAAI,IAAI,SAAS,KAAK;AAAA,YACnF,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,YAAM,UAAU,WAAS,IAAI,OAAO,aAAa,OAAO;AAExD,YAAM,cAAc;AACpB,YAAM,sBAAsB,QAAQ,cAAc,UAAU,cAAc;AAC1E,YAAM,mBAAmB;AACzB,YAAM,2BAA2B,QAAQ,mBAAmB,UAAU,mBAAmB;AACzF,YAAM,kBAAkB;AACxB,YAAM,0BAA0B,QAAQ,kBAAkB,UAAU,kBAAkB;AACtF,YAAM,WAAW;AAAA,QACf,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,uBAAuB;AAAA,QACvB,cAAc;AAAA,QACd,sBAAsB;AAAA,MACxB;AAEA,YAAM,2BAA2B,aAAW,MAAM,OAAO,EAAE,KAAK,CAAAO,WAAS,SAASA,QAAO,SAAS,qBAAqB,CAAC,EAAE,KAAK,SAAS,OAAO,GAAG,CAAAD,WAASA,OAAM,CAAC,CAAC;AACnK,YAAM,2BAA2B,cAAY,CAAC,UAAU,WAAW;AACjE,cAAM,WAAW,KAAK,QAAQ;AAC9B,cAAMH,QAAO,aAAa,SAAS,aAAa,aAAa,yBAAyB,QAAQ,IAAI;AAClG,eAAO,UAAUA,OAAM,UAAU,MAAM;AAAA,MACzC;AACA,YAAM,4BAA4B,yBAAyB,eAAe;AAC1E,YAAM,mBAAmB,yBAAyB,OAAO;AACzD,YAAM,wBAAwB,YAAU,QAAQ,OAAO,MAAM,MAAM,iBAAiB,CAAC;AACrF,YAAM,uBAAuB,CAAC,UAAU,aAAa;AACnD,cAAM,UAAU,iBAAiB,QAAQ;AACzC,cAAM,UAAU,QAAQ,KAAK,CAAAA,UAAQ,MAAMA,KAAI,CAAC,EAAE,IAAI,CAAAI,WAAS,KAAKA,MAAK,CAAC;AAC1E,eAAO,MAAM,SAAS,SAAS,CAACJ,OAAMC,UAAS,OAAOA,OAAM,SAAO,OAAO,QAAQ,IAAI,IAAI,KAAK,GAAG,aAAW,MAAM,SAAS,QAAQ,MAAM,OAAO,GAAG,SAASD,KAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAAA,MAChL;AAEA,YAAM,sBAAsB;AAAA,QAC1B;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAEA,YAAM,YAAY,YAAU,EAAE,OAAO,aAAa,KAAK,EAAE;AACzD,YAAM,iBAAiB;AACvB,YAAM,gBAAgB;AACtB,YAAM,cAAc,SAAO,eAAe,KAAK,GAAG,KAAK,cAAc,KAAK,GAAG;AAC7E,YAAM,eAAe,SAAO,cAAc,KAAK,GAAG,EAAE,YAAY;AAChE,YAAM,eAAe,SAAO,YAAY,GAAG,IAAI,SAAS,KAAK,EAAE,OAAO,aAAa,GAAG,EAAE,CAAC,IAAI,SAAS,KAAK;AAC3G,YAAM,QAAQ,eAAa;AACzB,cAAM,MAAM,UAAU,SAAS,EAAE;AACjC,gBAAQ,IAAI,WAAW,IAAI,MAAM,MAAM,KAAK,YAAY;AAAA,MAC1D;AACA,YAAM,WAAW,CAAAe,gBAAc;AAC7B,cAAM,QAAQ,MAAMA,YAAW,GAAG,IAAI,MAAMA,YAAW,KAAK,IAAI,MAAMA,YAAW,IAAI;AACrF,eAAO,UAAU,KAAK;AAAA,MACxB;AAEA,YAAM,WAAW;AACjB,YAAM,YAAY;AAClB,YAAM,aAAa,CAAC,KAAK,OAAO,MAAM,WAAW;AAAA,QAC/C;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,mBAAmB,CAAC,KAAK,OAAO,MAAM,UAAU;AACpD,cAAM,IAAI,SAAS,KAAK,EAAE;AAC1B,cAAM,IAAI,SAAS,OAAO,EAAE;AAC5B,cAAM,IAAI,SAAS,MAAM,EAAE;AAC3B,cAAM,IAAI,WAAW,KAAK;AAC1B,eAAO,WAAW,GAAG,GAAG,GAAG,CAAC;AAAA,MAC9B;AACA,YAAM,aAAa,gBAAc;AAC/B,YAAI,eAAe,eAAe;AAChC,iBAAO,SAAS,KAAK,WAAW,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,QAC7C;AACA,cAAM,WAAW,SAAS,KAAK,UAAU;AACzC,YAAI,aAAa,MAAM;AACrB,iBAAO,SAAS,KAAK,iBAAiB,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC;AAAA,QACnF;AACA,cAAM,YAAY,UAAU,KAAK,UAAU;AAC3C,YAAI,cAAc,MAAM;AACtB,iBAAO,SAAS,KAAK,iBAAiB,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;AAAA,QAC/F;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AAEA,YAAM,WAAW,WAAS,aAAa,KAAK,EAAE,QAAQ,MAAM,WAAW,KAAK,EAAE,IAAI,QAAQ,CAAC,EAAE,WAAW,MAAM;AAC5G,cAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,eAAO,SAAS;AAChB,eAAO,QAAQ;AACf,cAAM,gBAAgB,OAAO,WAAW,IAAI;AAC5C,sBAAc,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AACzD,sBAAc,YAAY;AAC1B,sBAAc,YAAY;AAC1B,sBAAc,SAAS,GAAG,GAAG,GAAG,CAAC;AACjC,cAAM,OAAO,cAAc,aAAa,GAAG,GAAG,GAAG,CAAC,EAAE;AACpD,cAAM,IAAI,KAAK,CAAC;AAChB,cAAM,IAAI,KAAK,CAAC;AAChB,cAAM,IAAI,KAAK,CAAC;AAChB,cAAM,IAAI,KAAK,CAAC;AAChB,eAAO,SAAS,WAAW,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,MACxC,CAAC;AACD,YAAM,kBAAkB,WAAS,WAAW,KAAK,EAAE,IAAI,QAAQ,EAAE,IAAI,OAAK,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK;AAEpG,YAAM,OAAO,aAAW;AACtB,YAAI,QAAQ;AACZ,cAAML,OAAM,MAAM;AAChB,iBAAO;AAAA,QACT;AACA,cAAMC,OAAM,OAAK;AACf,kBAAQ;AAAA,QACV;AACA,eAAO;AAAA,UACL,KAAAD;AAAA,UACA,KAAAC;AAAA,QACF;AAAA,MACF;AAEA,YAAM,YAAY,cAAY;AAC5B,cAAM,UAAU,KAAK,SAAS,KAAK,CAAC;AACpC,cAAM,SAAS,MAAM,QAAQ,IAAI,EAAE,KAAK,QAAQ;AAChD,cAAM,QAAQ,MAAM;AAClB,iBAAO;AACP,kBAAQ,IAAI,SAAS,KAAK,CAAC;AAAA,QAC7B;AACA,cAAM,QAAQ,MAAM,QAAQ,IAAI,EAAE,OAAO;AACzC,cAAMD,OAAM,MAAM,QAAQ,IAAI;AAC9B,cAAMC,OAAM,OAAK;AACf,iBAAO;AACP,kBAAQ,IAAI,SAAS,KAAK,CAAC,CAAC;AAAA,QAC9B;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,KAAAD;AAAA,UACA,KAAAC;AAAA,QACF;AAAA,MACF;AACA,YAAM,aAAa,MAAM,UAAU,OAAK,EAAE,OAAO,CAAC;AAElD,YAAM,gBAAgB,CAAC,QAAQ,YAAY,gBAAgB;AACzD,eAAO,CAAAK,SAAO;AACZ,gBAAM,gBAAgB,WAAW;AACjC,gBAAM,SAAS,QAAQ,WAAW;AAClC,gBAAM,OAAO,MAAM;AACjB,kBAAM,gBAAgB,sBAAsB,MAAM;AAClD,kBAAM,YAAY,CAAAhB,UAAQ,OAAO,UAAU,MAAM,YAAY,EAAE,OAAO,YAAY,GAAGA,MAAK,KAAK,MAAM;AACrG,gBAAI,QAAQ;AACV,cAAAgB,KAAI,UAAU,CAAC,OAAO,eAAe,SAAS,CAAC;AAC/C,4BAAc,IAAI,OAAO,UAAU,cAAc,YAAY,WAASA,KAAI,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC;AAAA,YACpG,OAAO;AACL,cAAAA,KAAI,UAAU,OAAO,eAAe,SAAS,CAAC;AAC9C,4BAAc,IAAI,OAAO,UAAU,cAAc,YAAYA,KAAI,WAAW,OAAO,EAAE,OAAO,YAAY,CAAC,CAAC;AAAA,YAC5G;AAAA,UACF;AACA,iBAAO,cAAc,KAAK,IAAI,OAAO,GAAG,QAAQ,IAAI;AACpD,iBAAO,cAAc;AAAA,QACvB;AAAA,MACF;AACA,YAAM,cAAc,UAAQ,kBAAkB,MAAM,MAAM;AAC1D,YAAM,iBAAiB,WAAS,IAAI,OAAO,UAAQ;AACjD,cAAM,OAAO,KAAK,QAAQ,KAAK,SAAS;AACxC,YAAI,YAAY,IAAI,GAAG;AACrB,iBAAO;AAAA,YACL;AAAA,YACA,OAAO,eAAe,KAAK,IAAI;AAAA,UACjC;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,YACL;AAAA,YACA,OAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,iBAAiB,CAAC,QAAQ,OAAO,QAAQ,aAAa,IAAI,OAAO,UAAQ;AAC7E,cAAM,OAAO,KAAK,QAAQ,KAAK;AAC/B,YAAI,YAAY,IAAI,GAAG;AACrB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,YACA,iBAAiB,MAAM,eAAe,QAAQ,KAAK,MAAM,QAAQ,QAAQ;AAAA,UAC3E;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,YACL;AAAA,YACA,MAAM;AAAA,YACN,UAAU,MAAM,SAAS,KAAK,KAAK;AAAA,YACnC,SAAS,cAAc,QAAQ,QAAQ,KAAK,KAAK;AAAA,UACnD;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,sBAAsB,CAAC,QAAQ,UAAU,WAAS;AACtD,eAAO,YAAY,0BAA0B,OAAO,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,MACxE;AACA,YAAM,iBAAiB,UAAQ,KAAK,MAAM,UAAQ;AAChD,YAAI,YAAY,IAAI,GAAG;AACrB,iBAAO,CAAC;AAAA,YACJ,GAAG;AAAA,YACH,MAAM,eAAe,KAAK,IAAI;AAAA,UAChC,CAAC;AAAA,QACL,OAAO;AACL,iBAAO,WAAW,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC;AAAA,QAC5C;AAAA,MACF,CAAC;AACD,YAAM,4BAA4B,CAAC,QAAQ,OAAO,QAAQ,aAAa,cAAY,SAAS,eAAe,QAAQ,OAAO,QAAQ,QAAQ,CAAC;AAC3I,YAAM,iBAAiB,CAAC,QAAQ,WAAW,UAAU;AACnD,cAAM,WAAW,IAAI,WAAW,YAAU;AAAA,UACxC,MAAM,MAAM;AAAA,UACZ,OAAO,MAAM,SAAS,MAAM,KAAK,EAAE;AAAA,UACnC,MAAM;AAAA,QACR,EAAE;AACF,eAAO,CAAC;AAAA,UACJ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,UAAU;AAAA,YACR,QAAQ,SAAS,SAAS,IAAI,WAAW;AAAA,YACzC,mBAAmB;AAAA,UACrB;AAAA,UACA,UAAU,UAAQ;AAChB,kBAAM,QAAQ,KAAK,UAAU,WAAW,KAAK,KAAK;AAClD,mBAAO,YAAY,0BAA0B,OAAO,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,UACxE;AAAA,QACF,CAAC;AAAA,MACL;AACA,YAAM,kBAAkB,YAAU,MAAM;AACtC,cAAM,cAAc,OAAO,kBAAkB,iBAAiB;AAC9D,cAAM,UAAU,gBAAgB,WAAW,SAAS;AACpD,eAAO,YAAY,mBAAmB,OAAO,EAAE,MAAM,QAAQ,CAAC;AAAA,MAChE;AACA,YAAM,qBAAqB,YAAU,MAAM;AACzC,cAAM,cAAc,OAAO,kBAAkB,iBAAiB;AAC9D,cAAM,UAAU,gBAAgB,OAAO,OAAO;AAC9C,eAAO,YAAY,mBAAmB,OAAO,EAAE,MAAM,QAAQ,CAAC;AAAA,MAChE;AAEA,YAAM,iBAAiB,YAAU;AAC/B,cAAM,UAAU,eAAe,iBAAiB,MAAM,CAAC;AACvD,YAAI,QAAQ,SAAS,GAAG;AACtB,iBAAO,SAAS,KAAK;AAAA,YACnB,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,WAAW;AAAA,QACf;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,aAAa,YAAU,SAAS,OAAO,eAAe,MAAM,EAAE,QAAQ,CAAC;AAE7E,YAAM,iBAAiB,CAAC,QAAQ,eAAe;AAC7C,cAAM,mBAAmB,CAAC;AAAA,UACtB,MAAM;AAAA,UACN,OAAO;AAAA,QACT,CAAC;AACH,cAAM,cAAc;AAAA,UAClB;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO,iBAAiB,OAAO,eAAe,qBAAqB,MAAM,CAAC,CAAC;AAAA,UAC7E;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,cAAc;AAAA,UAClB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AACA,cAAM,QAAQ,eAAe,SAAS,CAAC,WAAW,EAAE,OAAO,WAAW,IAAI;AAC1E,eAAO;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAEA,YAAM,SAAS,CAAC,QAAQ,YAAY;AAClC,cAAM,MAAM,OAAO;AACnB,cAAM,YAAY,CAAC,MAAM,UAAU;AACjC,cAAI,UAAU,SAAS,MAAM,KAAK;AAAA,QACpC;AACA,cAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAI,SAAS,SAAS,MAAM,KAAK;AAAA,QACnC;AACA,cAAM,YAAY,CAAC,YAAY,UAAU;AACvC,cAAI,UAAU,IAAI;AAChB,mBAAO,UAAU,OAAO,YAAY,EAAE,OAAO,KAAK,GAAG,SAAS,IAAI;AAAA,UACpE,OAAO;AACL,mBAAO,UAAU,MAAM,YAAY,EAAE,MAAM,GAAG,OAAO;AAAA,UACvD;AAAA,QACF;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,cAAc,EAAE,OAAO;AAE7B,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,mBAAmB,CAAC,aAAa,kBAAkB;AACvD,YAAI,eAAe,eAAe;AAChC,iBAAO;AAAA,QACT,WAAW,aAAa;AACtB,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,eAAe,SAAO;AAC1B,cAAM,cAAc,IAAI,YAAY;AACpC,cAAM,gBAAgB,GAAG,mBAAmB,IAAI,KAAK,GAAG,IAAI;AAC5D,YAAI,IAAI,YAAY,SAAS;AAC3B,iBAAO,EAAE,MAAM,SAAS;AAAA,QAC1B,WAAW,eAAe,eAAe;AACvC,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS,iBAAiB,aAAa,aAAa;AAAA,UACtD;AAAA,QACF,OAAO;AACL,iBAAO,EAAE,MAAM,OAAO;AAAA,QACxB;AAAA,MACF;AACA,YAAM,qBAAqB,CAAAb,WAAS;AAClC,cAAM,cAAc,OAAOA,QAAO,CAAAH,UAAQ,aAAaA,MAAK,OAAO,CAAC;AACpE,YAAI,YAAY,WAAW,GAAG;AAC5B,iBAAO,SAAS,KAAK,IAAI;AAAA,QAC3B,WAAW,YAAY,WAAWG,OAAM,QAAQ;AAC9C,iBAAO,SAAS,KAAK,IAAI;AAAA,QAC3B,OAAO;AACL,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,YAAM,oBAAoB,CAAAF,UAAQ;AAChC,cAAM,WAAW,IAAIA,OAAM,SAAO,aAAa,GAAG,EAAE,IAAI;AACxD,cAAM,YAAY,SAAS,UAAU,QAAQ;AAC7C,cAAM,YAAY,SAAS,UAAU,QAAQ;AAC7C,YAAI,CAAC,aAAa,CAAC,WAAW;AAC5B,iBAAO,SAAS,KAAK,MAAM;AAAA,QAC7B,OAAO;AACL,gBAAM,UAAU,SAAS,UAAU,MAAM;AACzC,cAAI,aAAa,CAAC,WAAW,CAAC,WAAW;AACvC,mBAAO,SAAS,KAAK,QAAQ;AAAA,UAC/B,WAAW,CAAC,aAAa,CAAC,WAAW,WAAW;AAC9C,mBAAO,SAAS,KAAK,QAAQ;AAAA,UAC/B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAEA,YAAM,SAAS,OAAK;AAClB,YAAI,SAAS;AACb,YAAI;AACJ,eAAO,IAAI,SAAS;AAClB,cAAI,CAAC,QAAQ;AACX,qBAAS;AACT,gBAAI,EAAE,MAAM,MAAM,IAAI;AAAA,UACxB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,YAAM,kBAAkB,CAAC,WAAW,YAAY,QAAQ,UAAU,KAAK,OAAK,KAAK,EAAE,OAAO,OAAK,GAAG,SAAS,EAAE,OAAO,CAAC,CAAC;AACtH,YAAM,eAAe,CAAC,WAAW,QAAQ,cAAc;AACrD,cAAM,UAAU,IAAI,OAAO,WAAW,YAAU;AAC9C,iBAAO,KAAK,MAAM,EAAE,KAAK,QAAM,gBAAgB,WAAW,EAAE,CAAC,EAAE,OAAO,SAAS;AAAA,QACjF,CAAC;AACD,cAAME,SAAQ,IAAI,OAAO;AACzB,eAAO,OAAOA,OAAM,SAAS,GAAGA,MAAK;AAAA,MACvC;AACA,YAAM,aAAa,CAAC,YAAY,WAAW,OAAO;AAClD,YAAM,eAAe,CAAC,YAAY,WAAW,OAAO;AACpD,YAAM,UAAU,CAAC,WAAW,WAAW,aAAa,WAAW,QAAQ,MAAM;AAC7E,YAAM,sBAAsB,CAAC,WAAWH,UAAS,gBAAgB,WAAWA,KAAI,EAAE,OAAO,CAAAM,YAAU,CAACA,QAAO,QAAQ;AACnH,YAAM,cAAc,CAAC,WAAWH,WAAU,OAAOA,QAAO,CAAAH,UAAQ,oBAAoB,WAAWA,KAAI,CAAC;AACpG,YAAM,qBAAqB,CAAC,WAAW,WAAW,WAAW,WAAW,MAAM,EAAE,OAAO,eAAa,YAAY,WAAW,UAAU,KAAK,CAAC;AAC3I,YAAM,uBAAuB,CAAC,WAAW,WAAW,aAAa,WAAW,MAAM,EAAE,OAAO,CAAAG,WAAS,YAAY,WAAWA,MAAK,CAAC;AAEjI,YAAM,WAAW,WAAS;AACxB,YAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,gBAAM,IAAI,MAAM,wBAAwB;AAAA,QAC1C;AACA,YAAI,MAAM,WAAW,GAAG;AACtB,gBAAM,IAAI,MAAM,iCAAiC;AAAA,QACnD;AACA,cAAM,eAAe,CAAC;AACtB,cAAMc,OAAM,CAAC;AACb,aAAK,OAAO,CAAC,OAAO,UAAU;AAC5B,gBAAM,SAAS,KAAK,KAAK;AACzB,cAAI,OAAO,WAAW,GAAG;AACvB,kBAAM,IAAI,MAAM,gCAAgC;AAAA,UAClD;AACA,gBAAMtB,OAAM,OAAO,CAAC;AACpB,gBAAM,QAAQ,MAAMA,IAAG;AACvB,cAAIsB,KAAItB,IAAG,MAAM,QAAW;AAC1B,kBAAM,IAAI,MAAM,4BAA4BA,IAAG;AAAA,UACjD,WAAWA,SAAQ,QAAQ;AACzB,kBAAM,IAAI,MAAM,uCAAuC;AAAA,UACzD,WAAW,CAAC,QAAQ,KAAK,GAAG;AAC1B,kBAAM,IAAI,MAAM,iCAAiC;AAAA,UACnD;AACA,uBAAa,KAAKA,IAAG;AACrB,UAAAsB,KAAItB,IAAG,IAAI,IAAI,SAAS;AACtB,kBAAM,YAAY,KAAK;AACvB,gBAAI,cAAc,MAAM,QAAQ;AAC9B,oBAAM,IAAI,MAAM,uCAAuCA,OAAM,gBAAgB,MAAM,SAAS,OAAO,QAAQ,YAAY,SAAS;AAAA,YAClI;AACA,kBAAM,QAAQ,cAAY;AACxB,oBAAM,aAAa,KAAK,QAAQ;AAChC,kBAAI,aAAa,WAAW,WAAW,QAAQ;AAC7C,sBAAM,IAAI,MAAM,mDAAmD,aAAa,KAAK,GAAG,IAAI,eAAe,WAAW,KAAK,GAAG,CAAC;AAAA,cACjI;AACA,oBAAM,UAAU,OAAO,cAAc,YAAU;AAC7C,uBAAO,SAAS,YAAY,MAAM;AAAA,cACpC,CAAC;AACD,kBAAI,CAAC,SAAS;AACZ,sBAAM,IAAI,MAAM,kEAAkE,WAAW,KAAK,IAAI,IAAI,iBAAiB,aAAa,KAAK,IAAI,CAAC;AAAA,cACpJ;AACA,qBAAO,SAASA,IAAG,EAAE,MAAM,MAAM,IAAI;AAAA,YACvC;AACA,mBAAO;AAAA,cACL,MAAM,IAAI,aAAa;AACrB,oBAAI,SAAS,WAAW,MAAM,QAAQ;AACpC,wBAAM,IAAI,MAAM,iDAAiD,MAAM,SAAS,WAAW,SAAS,MAAM;AAAA,gBAC5G;AACA,sBAAM,SAAS,SAAS,KAAK;AAC7B,uBAAO,OAAO,MAAM,MAAM,IAAI;AAAA,cAChC;AAAA,cACA;AAAA,cACA,KAAK,WAAS;AACZ,wBAAQ,IAAI,OAAO;AAAA,kBACjB;AAAA,kBACA,aAAaA;AAAA,kBACb,QAAQ;AAAA,gBACV,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAOsB;AAAA,MACT;AACA,YAAM,MAAM,EAAE,SAAS;AAEvB,YAAM,MAAM,IAAI,SAAS;AAAA,QACvB,EAAE,MAAM,CAAC,EAAE;AAAA,QACX,EAAE,MAAM,CAAC,OAAO,EAAE;AAAA,QAClB;AAAA,UACE,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AACD,OAAC,EAAE,GAAG,IAAI;AAEV,YAAM,gBAAgB,CAACb,QAAO,WAAW;AACvC,cAAM,QAAQ,UAAU,UAAUA,MAAK;AACvC,cAAM,UAAU,QAAQ,OAAO,MAAM;AACrC,eAAO,QAAQ,KAAK,mBAAiB;AACnC,gBAAM,mBAAmB,cAAc,cAAc,SAAS,CAAC;AAC/D,gBAAM,cAAc,cAAc,CAAC,EAAE;AACrC,gBAAM,cAAc,iBAAiB,MAAM,iBAAiB;AAC5D,gBAAM,eAAe,MAAM,IAAI,MAAM,aAAa,WAAW;AAC7D,iBAAO,kBAAkB,YAAY;AAAA,QACvC,CAAC,EAAE,MAAM,EAAE;AAAA,MACb;AACA,YAAM,cAAc;AAEpB,YAAM,WAAW,WAAS,WAAW,OAAO,KAAK,IAAI,gBAAgB,KAAK,IAAI;AAC9E,YAAM,wBAAwB,SAAO;AACnC,cAAM,UAAU,aAAa,QAAQ,GAAG;AACxC,eAAO;AAAA,UACL,aAAa,OAAO,SAAS,cAAc,EAAE,MAAM,EAAE;AAAA,UACrD,aAAa,OAAO,SAAS,cAAc,EAAE,MAAM,EAAE;AAAA,UACrD,aAAa,OAAO,SAAS,cAAc,EAAE,IAAI,QAAQ,EAAE,MAAM,EAAE;AAAA,UACnE,iBAAiB,OAAO,SAAS,kBAAkB,EAAE,IAAI,QAAQ,EAAE,MAAM,EAAE;AAAA,QAC7E;AAAA,MACF;AACA,YAAM,kBAAkB,UAAQ;AAC9B,cAAM,WAAW,KAAK,CAAC;AACvB,cAAM,iBAAiB,KAAK,MAAM,CAAC;AACnC,aAAK,gBAAgB,WAAS;AAC5B,eAAK,KAAK,QAAQ,GAAG,CAAAT,SAAO;AAC1B,mBAAO,OAAO,CAAC,WAAW,YAAY;AACpC,oBAAM,kBAAkB,SAASA,IAAG;AACpC,kBAAI,oBAAoB,MAAMA,SAAQ,SAAS;AAC7C,oBAAI,oBAAoB,WAAW;AACjC,2BAASA,IAAG,IAAI;AAAA,gBAClB;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,eAAe,CAAC,SAAS,YAAY,QAAQ,QAAQ,KAAK,SAAS,CAAAD,UAAQ,CAAC,YAAY,OAAO,UAAU,UAAU,KAAK,aAAaA,KAAI,CAAC,CAAC,EAAE,MAAM,EAAE;AAC3J,YAAM,gBAAgB,MAAM,cAAc;AAAA,QACxC;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,OAAO;AACV,YAAM,gBAAgB,MAAM,cAAc;AAAA,QACxC;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,QAAQ;AACX,YAAM,0BAA0B,CAAC,QAAQ,mBAAmB;AAC1D,cAAM,QAAQ,iBAAiB,MAAM;AACrC,cAAM,QAAQ,qBAAqB,MAAM;AACzC,cAAM,2BAA2B,OAAO;AAAA,UACtC,aAAa,MAAM,OAAO,cAAc,EAAE,MAAM,EAAE;AAAA,UAClD,aAAa,SAAS,MAAM,OAAO,cAAc,EAAE,MAAM,EAAE,CAAC;AAAA,UAC5D,iBAAiB,SAAS,MAAM,OAAO,kBAAkB,EAAE,MAAM,EAAE,CAAC;AAAA,QACtE;AACA,cAAM,cAAc;AAAA,UAClB,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa;AAAA,UACb,aAAa;AAAA,UACb,SAAS;AAAA,UACT,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AACA,cAAM,YAAY,MAAM;AACtB,gBAAM,cAAc,MAAM,cAAc;AACxC,cAAI,mBAAmB,MAAM,KAAK,aAAa;AAC7C,mBAAO,EAAE,QAAQ,YAAY;AAAA,UAC/B;AACA,iBAAO,MAAM,OAAO,QAAQ,EAAE,KAAK,OAAO,CAAC,IAAI,aAAW,EAAE,OAAO,EAAE;AAAA,QACvE;AACA,cAAM,WAAW,iBAAiB,yBAAyB,IAAI,CAAC;AAChE,cAAM,4BAA4B,MAAM;AACtC,gBAAM,UAAU,MAAM,OAAO,gBAAgB,EAAE,GAAG,MAAM,OAAO,aAAa,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,kBAAgB,EAAE,YAAY,EAAE;AAChI,gBAAM,UAAU,MAAM,OAAO,gBAAgB,EAAE,GAAG,MAAM,OAAO,aAAa,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,kBAAgB,EAAE,YAAY,EAAE;AAChI,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AACA,cAAM,OAAO;AAAA,UACX,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG,UAAU;AAAA,UACb,GAAG,0BAA0B;AAAA,QAC/B;AACA,eAAO;AAAA,MACT;AACA,YAAM,aAAa,SAAO,MAAM,aAAa,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAAU,WAAS;AACtE,cAAM,SAAS,EAAE,WAAW,QAAQ,IAAI,KAAK,EAAE;AAC/C,eAAO,YAAYA,QAAO,MAAM;AAAA,MAClC,CAAC,EAAE,MAAM,EAAE;AACX,YAAM,8BAA8B,CAAC,QAAQ,KAAK,mBAAmB;AACnE,cAAM,YAAY,CAACc,MAAKC,SAAQ;AAC9B,gBAAM,iBAAiB,OAAO,aAAa,QAAQA,IAAG,GAAG,cAAc;AACvE,cAAI,mBAAmB,MAAM,KAAK,eAAe,OAAO,GAAG;AACzD,mBAAO,eAAe,MAAM,EAAE;AAAA,UAChC;AACA,iBAAOD,KAAI,UAAUC,MAAK,QAAQ,KAAK,oBAAoB,OAAO,KAAKA,MAAK,cAAc,KAAK,oBAAoB,OAAO,KAAKA,MAAK,QAAQ,KAAK;AAAA,QACnJ;AACA,cAAM,MAAM,OAAO;AACnB,cAAM,cAAc,mBAAmB,MAAM,IAAI,IAAI,SAAS,KAAK,gBAAgB,KAAK,IAAI,UAAU,KAAK,aAAa,IAAI,IAAI,UAAU,KAAK,aAAa,KAAK,IAAI,SAAS,KAAK,gBAAgB;AACnM,cAAM,cAAc,mBAAmB,MAAM,IAAI,oBAAoB,KAAK,KAAK,SAAS,KAAK,IAAI,UAAU,KAAK,aAAa,IAAI,IAAI,UAAU,KAAK,aAAa,KAAK,oBAAoB,KAAK,KAAK,SAAS;AAC7M,eAAO;AAAA,UACL,OAAO,IAAI,SAAS,KAAK,OAAO,KAAK,IAAI,UAAU,KAAK,OAAO;AAAA,UAC/D,QAAQ,IAAI,SAAS,KAAK,QAAQ,KAAK,IAAI,UAAU,KAAK,QAAQ;AAAA,UAClE,aAAa,gBAAgB,QAAQ,gBAAgB,SAAS,cAAc;AAAA,UAC5E,aAAa,gBAAgB,QAAQ,gBAAgB,SAAS,cAAc;AAAA,UAC5E,QAAQ,UAAU,KAAK,GAAG;AAAA,UAC1B,SAAS,CAAC,CAAC,IAAI,OAAO,WAAW,GAAG,EAAE,CAAC;AAAA,UACvC,OAAO,IAAI,UAAU,KAAK,SAAS,EAAE;AAAA,UACrC,OAAO,cAAc,QAAQ,GAAG;AAAA,UAChC,GAAG,iBAAiB,sBAAsB,GAAG,IAAI,CAAC;AAAA,QACpD;AAAA,MACF;AACA,YAAM,4BAA4B,CAAC,QAAQ,KAAKC,uBAAsB;AACpE,cAAM,MAAM,OAAO;AACnB,eAAO;AAAA,UACL,QAAQ,IAAI,SAAS,KAAK,QAAQ,KAAK,IAAI,UAAU,KAAK,QAAQ;AAAA,UAClE,OAAO,IAAI,UAAU,KAAK,SAAS,EAAE;AAAA,UACrC,MAAM,WAAW,GAAG;AAAA,UACpB,OAAO,cAAc,QAAQ,GAAG;AAAA,UAChC,GAAGA,qBAAoB,sBAAsB,GAAG,IAAI,CAAC;AAAA,QACvD;AAAA,MACF;AACA,YAAM,6BAA6B,CAAC,QAAQpB,OAAMqB,qBAAoB,WAAW;AAC/E,cAAM,MAAM,OAAO;AACnB,cAAM,SAAS,OAAO,MAAMrB,KAAI;AAChC,cAAM,WAAW,CAAC,SAAS,UAAU,IAAI,SAAS,SAAS,KAAK,KAAK,IAAI,UAAU,SAAS,KAAK;AACjG,eAAO;AAAA,UACL,OAAO,SAAS,QAAQ,OAAO;AAAA,UAC/B,QAAQ,SAASA,OAAM,QAAQ;AAAA,UAC/B,OAAO,IAAI,UAAUA,OAAM,OAAO;AAAA,UAClC,UAAU,YAAYA,KAAI;AAAA,UAC1B,OAAO,IAAI,UAAUA,OAAM,SAAS,EAAE;AAAA,UACtC,QAAQ,cAAc,QAAQA,KAAI;AAAA,UAClC,QAAQ,cAAc,QAAQA,KAAI;AAAA,UAClC,GAAGqB,sBAAqB,sBAAsBrB,KAAI,IAAI,CAAC;AAAA,QACzD;AAAA,MACF;AAEA,YAAM,mBAAmB,CAACI,QAAOD,WAAU;AACzC,cAAM,YAAY,UAAU,UAAUC,MAAK;AAC3C,cAAM,WAAW,UAAU,UAAU,SAAS;AAC9C,cAAM,WAAW,OAAO,UAAU,WAAS,OAAOD,QAAO,WAAS,GAAG,MAAM,SAAS,KAAK,CAAC,CAAC;AAC3F,eAAO,IAAI,UAAU,CAAAH,WAAS;AAAA,UAC5B,SAASA,MAAK,QAAQ;AAAA,UACtB,QAAQ,UAAU,YAAY,WAAWA,MAAK,MAAM,EAAE,IAAI,SAAO,IAAI,QAAQ,GAAG;AAAA,QAClF,EAAE;AAAA,MACJ;AACA,YAAM,sBAAsB,CAAC,UAAU,aAAa,MAAM,iBAAiB;AACzE,YAAI,aAAa,OAAO,GAAG;AACzB,mBAAS,UAAU,SAAS,KAAK,KAAK;AAAA,QACxC;AACA,YAAI,aAAa,OAAO,GAAG;AACzB,mBAAS,UAAU,SAAS,KAAK,KAAK;AAAA,QACxC;AACA,YAAI,aAAa,QAAQ,GAAG;AAC1B,mBAAS,SAAS,UAAU,YAAY,KAAK,MAAM,CAAC;AAAA,QACtD;AACA,YAAI,aAAa,OAAO,GAAG;AACzB,sBAAY,SAAS,SAAS,YAAY,KAAK,KAAK,CAAC;AAAA,QACvD;AAAA,MACF;AACA,YAAM,wBAAwB,CAAC,UAAU,MAAM,iBAAiB;AAC9D,YAAI,aAAa,iBAAiB,GAAG;AACnC,mBAAS,UAAU,4BAA4B,KAAK,eAAe;AAAA,QACrE;AACA,YAAI,aAAa,aAAa,GAAG;AAC/B,mBAAS,UAAU,wBAAwB,KAAK,WAAW;AAAA,QAC7D;AACA,YAAI,aAAa,aAAa,GAAG;AAC/B,mBAAS,UAAU,wBAAwB,KAAK,WAAW;AAAA,QAC7D;AACA,YAAI,aAAa,aAAa,GAAG;AAC/B,mBAAS,UAAU,wBAAwB,YAAY,KAAK,WAAW,CAAC;AAAA,QAC1E;AAAA,MACF;AACA,YAAM,mBAAmB,CAAC,QAAQG,QAAO,MAAM,eAAe;AAC5D,cAAM,eAAeA,OAAM,WAAW;AACtC,aAAKA,QAAO,UAAQ;AAClB,gBAAM,UAAU,KAAK;AACrB,gBAAM,6BAA6B,eAAe,SAAS;AAC3D,gBAAM,WAAW,YAAY,OAAO,QAAQ,OAAO;AACnD,gBAAM,cAAc,KAAK,OAAO,IAAI,SAAO,YAAY,OAAO,QAAQ,GAAG,CAAC,EAAE,MAAM,QAAQ;AAC1F,8BAAoB,UAAU,aAAa,MAAM,0BAA0B;AAC3E,cAAI,mBAAmB,MAAM,GAAG;AAC9B,kCAAsB,UAAU,MAAM,0BAA0B;AAAA,UAClE;AACA,cAAI,WAAW,QAAQ,GAAG;AACxB,qBAAS,QAAQ,SAAS,KAAK,MAAM;AAAA,UACvC;AACA,cAAI,WAAW,QAAQ,GAAG;AACxB,sBAAU,QAAQ,SAAS,KAAK,MAAM;AAAA,UACxC;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,uBAAuB,CAAC,QAAQ,SAAS;AAC7C,eAAO,YAAY,oBAAoB,OAAO;AAAA,UAC5C,MAAM,KAAK;AAAA,UACX,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB,CAAC,QAAQA,QAAO,SAAS,SAAS;AACtD,cAAM,eAAe,SAAS,MAAM,CAAC,OAAOR,SAAQ,QAAQA,IAAG,MAAM,KAAK;AAC1E,YAAI,KAAK,YAAY,IAAI,KAAKQ,OAAM,UAAU,GAAG;AAC/C,gBAAMA,OAAM,CAAC,CAAC,EAAE,KAAK,CAAAC,WAAS;AAC5B,kBAAM,gBAAgB,iBAAiBA,QAAOD,MAAK;AACnD,kBAAM,gBAAgB,KAAK,SAAS,cAAc,CAAC,QAAQR,SAAQA,SAAQ,WAAWA,SAAQ,UAAU,CAAC,IAAI;AAC7G,kBAAM,oBAAoB,IAAI,cAAc,UAAU;AACtD,gBAAI,iBAAiB,IAAI,cAAc,OAAO,GAAG;AAC/C,+BAAiB,QAAQ,eAAe,MAAM,MAAM,KAAK,YAAY,CAAC;AAAA,YACxE;AACA,gBAAI,mBAAmB;AACrB,mCAAqB,QAAQ,IAAI;AAAA,YACnC;AACA,8BAAkB,QAAQS,OAAM,KAAK;AAAA,cACnC,WAAW;AAAA,cACX,OAAO;AAAA,YACT,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,mBAAmB,CAAC,QAAQD,QAAO,SAASa,SAAQ;AACxD,cAAM,OAAOA,KAAI,QAAQ;AACzB,QAAAA,KAAI,MAAM;AACV,eAAO,YAAY,SAAS,MAAM;AAChC,wBAAc,QAAQb,QAAO,SAAS,IAAI;AAC1C,iBAAO,MAAM;AAAA,QACf,CAAC;AAAA,MACH;AACA,YAAM,YAAY,CAAC,QAAQA,WAAU;AACnC,cAAM,YAAY,MAAMA,OAAM,CAAC,CAAC,EAAE,IAAI,CAAAC,WAAS,IAAI,iBAAiBA,QAAOD,MAAK,GAAG,UAAQ,2BAA2B,QAAQ,KAAK,SAAS,mBAAmB,MAAM,GAAG,KAAK,MAAM,CAAC,CAAC;AACrL,eAAO,gBAAgB,UAAU,SAAS,CAAC;AAAA,MAC7C;AACA,YAAM,SAAS,YAAU;AACvB,cAAMA,SAAQ,sBAAsB,MAAM;AAC1C,YAAIA,OAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,OAAO,UAAU,QAAQA,MAAK;AACpC,cAAM,iBAAiB;AAAA,UACrB,MAAM;AAAA,UACN,MAAM;AAAA,YACJ;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,OAAO,WAAW,MAAM;AAAA,YAC1B;AAAA,YACA,eAAe,QAAQ,MAAM;AAAA,UAC/B;AAAA,QACF;AACA,cAAM,cAAc;AAAA,UAClB,MAAM;AAAA,UACN,OAAO,CAAC;AAAA,YACJ,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO,WAAW,MAAM;AAAA,UAC1B,CAAC;AAAA,QACL;AACA,eAAO,cAAc,KAAK;AAAA,UACxB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM,mBAAmB,MAAM,IAAI,iBAAiB;AAAA,UACpD,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA,aAAa;AAAA,UACb,UAAU,MAAM,kBAAkB,QAAQA,QAAO,IAAI;AAAA,QACvD,CAAC;AAAA,MACH;AAEA,YAAM,eAAe,YAAU;AAC7B,cAAM,UAAU,eAAe,gBAAgB,MAAM,CAAC;AACtD,YAAI,QAAQ,SAAS,GAAG;AACtB,iBAAO,SAAS,KAAK;AAAA,YACnB,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,eAAe;AAAA,QACnB;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,MACF;AACA,YAAM,aAAa,YAAU,aAAa,OAAO,aAAa,MAAM,EAAE,QAAQ,CAAC;AAE/E,YAAM,oBAAoB,CAAC,UAAU,MAAM,iBAAiB;AAC1D,YAAI,aAAa,OAAO,GAAG;AACzB,mBAAS,UAAU,SAAS,KAAK,KAAK;AAAA,QACxC;AACA,YAAI,aAAa,QAAQ,GAAG;AAC1B,mBAAS,SAAS,UAAU,YAAY,KAAK,MAAM,CAAC;AAAA,QACtD;AAAA,MACF;AACA,YAAM,sBAAsB,CAAC,UAAU,MAAM,iBAAiB;AAC5D,YAAI,aAAa,iBAAiB,GAAG;AACnC,mBAAS,SAAS,oBAAoB,KAAK,eAAe;AAAA,QAC5D;AACA,YAAI,aAAa,aAAa,GAAG;AAC/B,mBAAS,SAAS,gBAAgB,KAAK,WAAW;AAAA,QACpD;AACA,YAAI,aAAa,aAAa,GAAG;AAC/B,mBAAS,SAAS,gBAAgB,KAAK,WAAW;AAAA,QACpD;AAAA,MACF;AACA,YAAM,iBAAiB,CAAC,QAAQF,OAAM,MAAM,eAAe;AACzD,cAAM,cAAcA,MAAK,WAAW;AACpC,cAAM,6BAA6B,cAAc,SAAS;AAC1D,aAAKA,OAAM,YAAU;AACnB,gBAAM,WAAW,YAAY,OAAO,QAAQ,MAAM;AAClD,4BAAkB,UAAU,MAAM,0BAA0B;AAC5D,cAAI,kBAAkB,MAAM,GAAG;AAC7B,gCAAoB,UAAU,MAAM,0BAA0B;AAAA,UAChE;AACA,cAAI,WAAW,OAAO,GAAG;AACvB,qBAAS,QAAQ,QAAQ,KAAK,KAAK;AAAA,UACrC;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,qBAAqB,CAAC,QAAQ,SAAS;AAC3C,eAAO,YAAY,mBAAmB,OAAO;AAAA,UAC3C,MAAM,KAAK;AAAA,UACX,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AACA,YAAM,eAAe,CAAC,QAAQA,OAAM,SAAS,SAAS;AACpD,cAAM,eAAe,SAAS,MAAM,CAAC,OAAON,SAAQ,QAAQA,IAAG,MAAM,KAAK;AAC1E,YAAI,KAAK,YAAY,IAAI,GAAG;AAC1B,gBAAM,eAAe,IAAI,cAAc,MAAM;AAC7C,gBAAM,gBAAgB,eAAe,KAAK,YAAY,IAAI,IAAI;AAC9D,cAAI,eAAe;AACjB,2BAAe,QAAQM,OAAM,MAAM,MAAM,KAAK,YAAY,CAAC;AAAA,UAC7D;AACA,cAAI,cAAc;AAChB,+BAAmB,QAAQ,IAAI;AAAA,UACjC;AACA,gBAAM,aAAa,QAAQA,MAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAAG,WAAS,kBAAkB,QAAQA,OAAM,KAAK;AAAA,YACtF,WAAW;AAAA,YACX,OAAO;AAAA,UACT,CAAC,CAAC;AAAA,QACJ;AAAA,MACF;AACA,YAAM,kBAAkB,CAAC,QAAQH,OAAM,SAASe,SAAQ;AACtD,cAAM,OAAOA,KAAI,QAAQ;AACzB,QAAAA,KAAI,MAAM;AACV,eAAO,YAAY,SAAS,MAAM;AAChC,uBAAa,QAAQf,OAAM,SAAS,IAAI;AACxC,iBAAO,MAAM;AAAA,QACf,CAAC;AAAA,MACH;AACA,YAAM,SAAS,YAAU;AACvB,cAAMA,QAAO,qBAAqB,kBAAkB,MAAM,GAAG,SAAS,QAAQ;AAC9E,YAAIA,MAAK,WAAW,GAAG;AACrB;AAAA,QACF;AACA,cAAM,WAAW,IAAIA,OAAM,YAAU,0BAA0B,QAAQ,OAAO,KAAK,kBAAkB,MAAM,CAAC,CAAC;AAC7G,cAAM,OAAO,gBAAgB,QAAQ;AACrC,cAAM,iBAAiB;AAAA,UACrB,MAAM;AAAA,UACN,MAAM;AAAA,YACJ;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,OAAO,WAAW,MAAM;AAAA,YAC1B;AAAA,YACA,eAAe,QAAQ,KAAK;AAAA,UAC9B;AAAA,QACF;AACA,cAAM,cAAc;AAAA,UAClB,MAAM;AAAA,UACN,OAAO,CAAC;AAAA,YACJ,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO,WAAW,MAAM;AAAA,UAC1B,CAAC;AAAA,QACL;AACA,eAAO,cAAc,KAAK;AAAA,UACxB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM,kBAAkB,MAAM,IAAI,iBAAiB;AAAA,UACnD,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA,aAAa;AAAA,UACb,UAAU,MAAM,iBAAiB,QAAQ,IAAIA,OAAM,OAAK,EAAE,GAAG,GAAG,IAAI;AAAA,QACtE,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,CAAC,QAAQ,SAAS,mBAAmB;AACpD,cAAM,mBAAmB,CAAC,iBAAiB,CAAC,IAAI;AAAA,UAC9C;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,QACF;AACA,cAAM,cAAc;AAAA,UAClB;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,kBAAkB,qBAAqB,MAAM,IAAI;AAAA,UACrD;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO,CAAC;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,YACT,CAAC;AAAA,UACL;AAAA,QACF,IAAI,CAAC;AACL,cAAM,gBAAgB,CAAC;AAAA,UACnB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF,CAAC;AACH,cAAM,gBAAgB,QAAQ,SAAS,IAAI,CAAC;AAAA,UACxC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,QACT,CAAC,IAAI,CAAC;AACR,eAAO,iBAAiB,OAAO,WAAW,EAAE,OAAO,eAAe,EAAE,OAAO,aAAa,EAAE,OAAO,aAAa;AAAA,MAChH;AAEA,YAAM,YAAY,CAAC,KAAK,KAAKP,OAAM,UAAU;AAC3C,YAAI,IAAI,YAAY,QAAQ,IAAI,YAAY,MAAM;AAChD,cAAI,SAASA,KAAI,KAAK,cAAc,KAAK,GAAG;AAC1C,gBAAI,SAAS,KAAKA,OAAM,KAAK;AAAA,UAC/B,OAAO;AACL,gBAAI,UAAU,KAAKA,KAAI;AAAA,UACzB;AAAA,QACF,OAAO;AACL,cAAI,IAAI,UAAU;AAChB,qBAAS,IAAI,GAAG,IAAI,IAAI,SAAS,QAAQ,KAAK;AAC5C,wBAAU,KAAK,IAAI,SAAS,CAAC,GAAGA,OAAM,KAAK;AAAA,YAC7C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,qBAAqB,CAAC,QAAQ,UAAU,MAAM,sBAAsB;AACxE,cAAM,MAAM,OAAO;AACnB,cAAM,QAAQ,CAAC;AACf,cAAM,SAAS,CAAC;AAChB,cAAM,uBAAuB,mBAAmB,MAAM;AACtD,cAAM,wBAAwB,oBAAoB,MAAM;AACxD,YAAI,CAAC,YAAY,KAAK,KAAK,GAAG;AAC5B,gBAAM,QAAQ,KAAK;AAAA,QACrB;AACA,eAAO,SAAS,YAAY,KAAK,MAAM;AACvC,YAAI,sBAAsB;AACxB,iBAAO,QAAQ,YAAY,KAAK,KAAK;AAAA,QACvC,WAAW,IAAI,UAAU,UAAU,OAAO,GAAG;AAC3C,gBAAM,QAAQ,eAAe,KAAK,KAAK;AAAA,QACzC;AACA,YAAI,sBAAsB;AACxB,iBAAO,cAAc,IAAI,YAAY,KAAK,MAAM;AAChD,iBAAO,gBAAgB,IAAI,YAAY,KAAK,WAAW;AAAA,QACzD,OAAO;AACL,gBAAM,SAAS,KAAK;AACpB,gBAAM,cAAc,KAAK;AACzB,gBAAM,cAAc,KAAK;AAAA,QAC3B;AACA,YAAI,wBAAwB,SAAS,UAAU;AAC7C,gBAAM,aAAa,CAAC;AACpB,cAAI,kBAAkB,QAAQ;AAC5B,uBAAW,cAAc,IAAI,YAAY,KAAK,MAAM;AAAA,UACtD;AACA,cAAI,kBAAkB,aAAa;AACjC,uBAAW,UAAU,YAAY,KAAK,WAAW;AAAA,UACnD;AACA,cAAI,yBAAyB,kBAAkB,aAAa;AAC1D,uBAAW,cAAc,IAAI,KAAK;AAAA,UACpC;AACA,cAAI,CAAC,UAAU,UAAU,GAAG;AAC1B,qBAAS,IAAI,GAAG,IAAI,SAAS,SAAS,QAAQ,KAAK;AACjD,wBAAU,KAAK,SAAS,SAAS,CAAC,GAAG,UAAU;AAAA,YACjD;AAAA,UACF;AAAA,QACF;AACA,YAAI,uBAAuB;AACzB,gBAAM,UAAU;AAChB,iBAAO,kBAAkB,IAAI,QAAQ;AACrC,iBAAO,cAAc,IAAI,QAAQ;AACjC,iBAAO,cAAc,IAAI,QAAQ;AAAA,QACnC;AACA,cAAM,QAAQ,IAAI,eAAe;AAAA,UAC/B,GAAG,iBAAiB,MAAM;AAAA,UAC1B,GAAG;AAAA,QACL,CAAC;AACD,YAAI,WAAW,UAAU;AAAA,UACvB,GAAG,qBAAqB,MAAM;AAAA,UAC9B,GAAG;AAAA,QACL,CAAC;AAAA,MACH;AACA,YAAM,oBAAoB,CAAC,QAAQ,UAAU,SAASsB,SAAQ;AAC5D,cAAM,MAAM,OAAO;AACnB,cAAM,OAAOA,KAAI,QAAQ;AACzB,cAAM,eAAe,SAAS,MAAM,CAAC,OAAOrB,SAAQ,QAAQA,IAAG,MAAM,KAAK;AAC1E,QAAAqB,KAAI,MAAM;AACV,YAAI,KAAK,UAAU,IAAI;AACrB,iBAAO,KAAK;AAAA,QACd;AACA,eAAO,YAAY,SAAS,MAAM;AAChC,cAAI,CAAC,UAAU;AACb,kBAAM,OAAO,MAAM,KAAK,IAAI,EAAE,MAAM,CAAC;AACrC,kBAAMf,QAAO,MAAM,KAAK,IAAI,EAAE,MAAM,CAAC;AACrC,mBAAO,YAAY,kBAAkB,OAAO;AAAA,cAC1C,MAAAA;AAAA,cACA,SAAS;AAAA,YACX,CAAC;AACD,uBAAW,iBAAiB,kBAAkB,MAAM,GAAG,UAAU,MAAM,CAAC,EAAE,KAAK,CAAAD,UAAQ,MAAMA,OAAM,UAAU,MAAM,CAAC,CAAC,EAAE,IAAI,CAAAI,WAASA,OAAM,GAAG,EAAE,SAAS;AAAA,UAC1J;AACA,cAAI,KAAK,YAAY,IAAI,GAAG;AAC1B,kBAAM,2BAA2B;AAAA,cAC/B,QAAQ,IAAI,cAAc,QAAQ;AAAA,cAClC,aAAa,IAAI,cAAc,aAAa;AAAA,cAC5C,aAAa,IAAI,cAAc,aAAa;AAAA,YAC9C;AACA,+BAAmB,QAAQ,UAAU,MAAM,wBAAwB;AACnE,kBAAM,aAAa,IAAI,OAAO,WAAW,QAAQ,EAAE,CAAC;AACpD,gBAAI,cAAc,CAAC,KAAK,WAAW,CAAC,cAAc,KAAK,SAAS;AAC9D,qBAAO,YAAY,uBAAuB;AAAA,YAC5C;AACA,qBAAS,QAAQ,UAAU,KAAK,KAAK;AAAA,UACvC;AACA,iBAAO,MAAM;AACb,iBAAO,UAAU;AACjB,cAAI,KAAK,YAAY,IAAI,GAAG;AAC1B,kBAAM,kBAAkB,IAAI,cAAc,SAAS;AACnD,kBAAM,gBAAgB,kBAAkB,KAAK,YAAY,IAAI,IAAI;AACjE,8BAAkB,QAAQ,UAAU;AAAA,cAClC,WAAW;AAAA,cACX,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,OAAO,CAAC,QAAQ,mBAAmB;AACvC,cAAM,MAAM,OAAO;AACnB,YAAI;AACJ,YAAI,OAAO,wBAAwB,QAAQ,oBAAoB,MAAM,CAAC;AACtE,YAAI,gBAAgB;AAClB,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,cAAI,oBAAoB,MAAM,GAAG;AAC/B,iBAAK,cAAc;AACnB,iBAAK,cAAc;AACnB,iBAAK,kBAAkB;AAAA,UACzB;AAAA,QACF,OAAO;AACL,qBAAW,IAAI,UAAU,OAAO,UAAU,SAAS,GAAG,SAAS,OAAO,QAAQ,CAAC;AAC/E,cAAI,UAAU;AACZ,mBAAO,4BAA4B,QAAQ,UAAU,oBAAoB,MAAM,CAAC;AAAA,UAClF,OAAO;AACL,gBAAI,oBAAoB,MAAM,GAAG;AAC/B,mBAAK,cAAc;AACnB,mBAAK,cAAc;AACnB,mBAAK,kBAAkB;AAAA,YACzB;AAAA,UACF;AAAA,QACF;AACA,cAAM,UAAU,eAAe,kBAAkB,MAAM,CAAC;AACxD,YAAI,QAAQ,SAAS,GAAG;AACtB,cAAI,KAAK,OAAO;AACd,iBAAK,QAAQ,KAAK,MAAM,QAAQ,2BAA2B,EAAE;AAAA,UAC/D;AAAA,QACF;AACA,cAAM,eAAe;AAAA,UACnB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,OAAO,SAAS,QAAQ,SAAS,cAAc;AAAA,QACjD;AACA,cAAM,kBAAkB,OAAO;AAAA,UAC7B,MAAM;AAAA,UACN,OAAO,CAAC,YAAY;AAAA,QACtB;AACA,cAAM,eAAe,OAAO;AAAA,UAC1B,MAAM;AAAA,UACN,MAAM;AAAA,YACJ;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,OAAO,CAAC,YAAY;AAAA,YACtB;AAAA,YACA,eAAe,QAAQ,OAAO;AAAA,UAChC;AAAA,QACF;AACA,cAAM,aAAa,oBAAoB,MAAM,IAAI,aAAa,IAAI,gBAAgB;AAClF,eAAO,cAAc,KAAK;AAAA,UACxB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU,MAAM,mBAAmB,QAAQ,UAAU,IAAI;AAAA,UACzD,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA,aAAa;AAAA,QACf,CAAC;AAAA,MACH;AAEA,YAAM,mBAAmB,YAAU;AACjC,cAAM,YAAY,OAAK;AACrB,cAAI,oBAAoB,kBAAkB,MAAM,CAAC,GAAG;AAClD,cAAE;AAAA,UACJ;AAAA,QACF;AACA,eAAO;AAAA,UACL,eAAe,MAAM,MAAM,QAAQ,KAAK;AAAA,UACxC,kBAAkB,MAAM,QAAQ,MAAM;AAAA,UACtC,mBAAmB,MAAM,QAAQ,MAAM;AAAA,UACvC,sBAAsB,MAAM,MAAM,QAAQ,IAAI;AAAA,QAChD,GAAG,CAAC,MAAMV,UAAS,OAAO,WAAWA,OAAM,MAAM,UAAU,IAAI,CAAC,CAAC;AAAA,MACnE;AAEA,YAAM,QAAQ,CAAC,OAAO,aAAa,QAAQ,OAAO,QAAQ,EAAE,OAAO;AAEnE,YAAM,YAAY;AAClB,YAAM,aAAa,mBAAiB;AAClC,cAAM,UAAU,CAAC,MAAMF,UAAS,OAAO,MAAMA,KAAI,EAAE,OAAO,UAAQ,SAAS,MAAM,EAAE,IAAI,CAAC;AACxF,cAAM,kBAAkB,UAAQ,QAAQ,MAAM,SAAS,KAAK,QAAQ,MAAM,SAAS;AACnF,eAAO,cAAc,SAAS,KAAK,OAAO,eAAe,eAAe,IAAI,SAAS,KAAK,aAAa,IAAI,SAAS,KAAK;AAAA,MAC3H;AACA,YAAM,WAAW,CAACY,QAAO,eAAekB,cAAa;AACnD,YAAI,cAAc,UAAU,GAAG;AAC7B,iBAAO,SAAS,KAAK;AAAA,QACvB,OAAO;AACL,iBAAO,YAAYlB,QAAOkB,UAAS,uBAAuBA,UAAS,oBAAoB,EAAE,IAAI,CAAAf,aAAW;AAAA,YACtG,QAAAA;AAAA,YACA,OAAO;AAAA,UACT,EAAE;AAAA,QACJ;AAAA,MACF;AAEA,YAAM,SAAS,CAAAP,WAAS;AAAA,QACtB,SAASA;AAAA,QACT,UAAU,SAAS,KAAK;AAAA,QACxB,YAAY,SAAS,KAAK;AAAA,QAC1B,WAAW,CAACA,KAAI;AAAA,MAClB;AACA,YAAM,UAAU,CAAC,eAAeI,QAAOJ,WAAU;AAAA,QAC/C,SAASA;AAAA,QACT,UAAU,SAASI,QAAO,eAAe,QAAQ;AAAA,QACjD,YAAY,WAAW,aAAa;AAAA,QACpC,WAAW,UAAU,aAAa;AAAA,MACpC;AAEA,YAAM,sBAAsB,YAAU;AACpC,cAAM,UAAU,KAAK,SAAS,KAAK,CAAC;AACpC,cAAM,iBAAiB,KAAK,CAAC,CAAC;AAC9B,YAAI,mBAAmB,SAAS,KAAK;AACrC,cAAM,YAAY,MAAM,SAAS;AACjC,cAAM,yBAAyB,CAAAT,SAAO,iBAAiB,OAAO,aAAW,CAAC,QAAQA,IAAG,CAAC;AACtF,cAAM,WAAW,MAAM,0BAA0B,kBAAkB,MAAM,GAAG,UAAU,MAAM,CAAC;AAC7F,cAAM,SAAS,MAAM,0BAA0B,gBAAgB,MAAM,GAAG,UAAU,MAAM,CAAC;AACzF,cAAM,cAAc,MAAM,SAAS,EAAE,KAAK,wBAAsB,QAAQ,MAAM,MAAM,kBAAkB,GAAG,OAAO,EAAE,KAAK,KAAK,GAAG,CAAC,YAAY,aAAa;AACvJ,cAAI,GAAG,YAAY,QAAQ,GAAG;AAC5B,gBAAI,UAAU,kBAAkB,GAAG;AACjC,qBAAO,SAAS,KAAK,OAAO,kBAAkB,CAAC;AAAA,YACjD,OAAO;AACL,qBAAO,SAAS,KAAK,QAAQ,sBAAsB,MAAM,GAAG,YAAY,kBAAkB,CAAC;AAAA,YAC7F;AAAA,UACF;AACA,iBAAO,SAAS,KAAK;AAAA,QACvB,CAAC,CAAC,CAAC;AACH,cAAM,sBAAsB,CAAA4B,aAAW;AACrC,gBAAM,WAAW,MAAMA,SAAQ,OAAO;AACtC,iBAAO,SAAS,IAAI,CAAAnB,WAAS;AAC3B,kBAAM,YAAY,UAAU,UAAUA,MAAK;AAC3C,kBAAM,gBAAgB,QAAQ,WAAWmB,QAAO,EAAE,MAAM,CAAC,CAAC;AAC1D,kBAAM,SAAS,MAAM,eAAe,CAAC,KAAKvB,UAAS;AACjD,kBAAIA,MAAK,UAAU;AACjB,oBAAI,QAAQ;AACZ,oBAAIA,MAAK,WAAW,GAAG;AACrB,sBAAI,UAAU;AAAA,gBAChB,WAAWA,MAAK,SAASA,MAAK,WAAW,UAAU,KAAK,SAAS;AAC/D,sBAAI,SAAS;AAAA,gBACf;AAAA,cACF;AACA,qBAAO;AAAA,YACT,GAAG;AAAA,cACD,OAAO;AAAA,cACP,SAAS;AAAA,cACT,QAAQ;AAAA,YACV,CAAC;AACD,mBAAO;AAAA,cACL,WAAW,mBAAmB,WAAWuB,QAAO,EAAE,OAAO;AAAA,cACzD,aAAa,qBAAqB,WAAWA,QAAO,EAAE,OAAO;AAAA,cAC7D;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AACA,cAAM,eAAe,MAAM;AACzB,kBAAQ,IAAI,OAAO,WAAW,EAAE,CAAC;AACjC,6BAAmB,QAAQ,IAAI,EAAE,KAAK,mBAAmB;AACzD,eAAK,eAAe,IAAI,GAAG,IAAI;AAAA,QACjC;AACA,cAAM,eAAe,aAAW;AAC9B,kBAAQ;AACR,yBAAe,IAAI,eAAe,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AACzD,iBAAO,MAAM;AACX,2BAAe,IAAI,OAAO,eAAe,IAAI,GAAG,OAAK,MAAM,OAAO,CAAC;AAAA,UACrE;AAAA,QACF;AACA,cAAM,UAAU,CAACP,MAAK,eAAe,aAAa,MAAM,QAAQ,IAAI,EAAE,KAAK,MAAM;AAC/E,UAAAA,KAAI,WAAW,KAAK;AAAA,QACtB,GAAG,CAAAO,aAAW;AACZ,UAAAP,KAAI,WAAW,CAAC,WAAWO,QAAO,KAAK,OAAO,UAAU,WAAW,CAAC;AAAA,QACtE,CAAC,CAAC;AACF,cAAM,oBAAoB,CAACP,MAAK,YAAY,aAAa,aAAa,MAAM,QAAQ,IAAI,EAAE,KAAK,MAAM;AACnG,UAAAA,KAAI,WAAW,KAAK;AACpB,UAAAA,KAAI,UAAU,KAAK;AAAA,QACrB,GAAG,CAAAO,aAAW;AACZ,UAAAP,KAAI,WAAW,CAAC,WAAWO,QAAO,KAAK,OAAO,UAAU,WAAW,CAAC;AACpE,UAAAP,KAAI,UAAU,SAASO,QAAO,CAAC;AAAA,QACjC,CAAC,CAAC;AACF,cAAM,uBAAuB,mBAAiB,iBAAiB,OAAO,aAAW,QAAQ,OAAO,aAAa,CAAC;AAC9G,cAAM,eAAe,CAAAP,SAAO,QAAQA,MAAK,OAAK,KAAK;AACnD,cAAM,mBAAmB,CAAAA,SAAO,QAAQA,MAAK,CAAAO,aAAW,UAAUA,SAAQ,OAAO,CAAC;AAClF,cAAM,gBAAgB,mBAAiB,CAAAP,SAAO,QAAQA,MAAK,CAAAO,aAAW,UAAUA,SAAQ,OAAO,KAAK,qBAAqB,aAAa,CAAC;AACvI,cAAM,mBAAmB,sBAAoB,CAAAP,SAAO,QAAQA,MAAK,CAAAO,aAAW,UAAUA,SAAQ,OAAO,KAAK,iBAAiB,EAAE,OAAO,CAAC;AACrI,cAAM,yBAAyB,CAAC,kBAAkB,kBAAkB,CAAAP,SAAO,QAAQA,MAAK,CAAAO,aAAW,UAAUA,SAAQ,OAAO,KAAK,iBAAiB,EAAE,OAAO,KAAK,qBAAqB,aAAa,CAAC;AACnM,cAAM,mBAAmB,CAAAP,SAAO,QAAQA,MAAK,cAAY,uBAAuB,WAAW,CAAC;AAC5F,cAAM,qBAAqB,CAAAA,SAAO,QAAQA,MAAK,cAAY,uBAAuB,aAAa,CAAC;AAChG,cAAM,0BAA0B,CAAAA,SAAO;AACrC,iBAAO,kBAAkBA,MAAK,OAAO,CAAAO,aAAW;AAC9C,kBAAM,WAAW,MAAMA,SAAQ,SAAS,UAAU,MAAM,CAAC;AACzD,mBAAO,SAAS,OAAO,CAAAnB,WAAS,MAAMA,QAAO,SAAS,CAAC;AAAA,UACzD,CAAC;AAAA,QACH;AACA,cAAM,sBAAsB,CAAC,SAAS,eAAe,CAAAY,SAAO;AAC1D,iBAAO,kBAAkBA,MAAK,CAAAO,aAAW,UAAUA,SAAQ,OAAO,GAAG,MAAM,OAAO,kBAAkB,OAAO,MAAM,UAAU;AAAA,QAC7H;AACA,cAAM,yBAAyB,oBAAoB,mBAAmB,QAAQ;AAC9E,cAAM,4BAA4B,oBAAoB,mBAAmB,IAAI;AAC7E,eAAO,GAAG,8CAA8C,YAAY;AACpE,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,QAAQ;AAAA,QACnB;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAE/D,YAAM,gBAAgB;AACtB,YAAM,eAAe,gBAAgB;AACrC,YAAM,kBAAkB,gBAAgB;AACxC,YAAM,UAAU,CAAA/B,UAAQ;AACtB,YAAI;AACJ,cAAM,SAAS,KAAK,OAAO,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AACrE,eAAO,QAAQ,OAAO,UAAQ,SAAS,KAAK,KAAK,QAAQA,KAAI,CAAC,CAAC;AAAA,MACjE;AACA,YAAM,UAAU,MAAM,QAAQ,YAAY;AAC1C,YAAM,aAAa,MAAM,QAAQ,eAAe;AAEhD,YAAM,oBAAoB,YAAU,CAAAwB,SAAO;AACzC,cAAM,cAAc,MAAM;AACxB,UAAAA,KAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,QAC9C;AACA,eAAO,GAAG,cAAc,WAAW;AACnC,oBAAY;AACZ,eAAO,MAAM;AACX,iBAAO,IAAI,cAAc,WAAW;AAAA,QACtC;AAAA,MACF;AACA,YAAM,aAAa,CAAC,QAAQ,qBAAqB;AAC/C,eAAO,GAAG,SAAS,cAAc,SAAS;AAAA,UACxC,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,kBAAkB,MAAM;AAAA,UACjC,OAAO,cAAY,SAAS,uEAAuE;AAAA,QACrG,CAAC;AACD,cAAM,MAAM,aAAW,MAAM,OAAO,YAAY,OAAO;AACvD,cAAM,wBAAwB,CAACtB,OAAM,SAAS;AAC5C,cAAI,OAAO,sBAAsB,KAAK,OAAO,GAAG;AAC9C,mBAAO,GAAG,SAAS,UAAUA,OAAM;AAAA,cACjC,GAAG;AAAA,cACH,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI,KAAK,OAAO;AAAA,YACxE,CAAC;AAAA,UACH;AAAA,QACF;AACA,cAAM,8BAA8B,CAACA,OAAM,SAAS;AAClD,cAAI,OAAO,sBAAsB,KAAK,OAAO,GAAG;AAC9C,mBAAO,GAAG,SAAS,gBAAgBA,OAAM;AAAA,cACvC,GAAG;AAAA,cACH,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI,KAAK,OAAO;AAAA,YACxE,CAAC;AAAA,UACH;AAAA,QACF;AACA,8BAAsB,cAAc;AAAA,UAClC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,8BAAsB,eAAe;AAAA,UACnC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,8BAAsB,kBAAkB;AAAA,UACtC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,8BAAsB,mBAAmB;AAAA,UACvC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,8BAAsB,mBAAmB;AAAA,UACvC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,8BAAsB,wBAAwB;AAAA,UAC5C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,8BAAsB,uBAAuB;AAAA,UAC3C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,8BAAsB,kBAAkB;AAAA,UACtC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,8BAAsB,iBAAiB;AAAA,UACrC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,8BAAsB,wBAAwB;AAAA,UAC5C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB,cAAc,SAAS;AAAA,QACnD,CAAC;AACD,8BAAsB,uBAAuB;AAAA,UAC3C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB,cAAc,QAAQ;AAAA,QAClD,CAAC;AACD,8BAAsB,kBAAkB;AAAA,UACtC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB,cAAc,OAAO;AAAA,QACjD,CAAC;AACD,8BAAsB,eAAe;AAAA,UACnC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,8BAAsB,gBAAgB;AAAA,UACpC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,8BAAsB,uBAAuB;AAAA,UAC3C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB,iBAAiB,OAAO;AAAA,QACpD,CAAC;AACD,8BAAsB,sBAAsB;AAAA,UAC1C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB,iBAAiB,OAAO;AAAA,QACpD,CAAC;AACD,8BAAsB,eAAe;AAAA,UACnC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB,cAAc,OAAO;AAAA,QACjD,CAAC;AACD,8BAAsB,gBAAgB;AAAA,UACpC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB,cAAc,OAAO;AAAA,QACjD,CAAC;AACD,8BAAsB,uBAAuB;AAAA,UAC3C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB,uBAAuB,YAAY,SAAS;AAAA,QACxE,CAAC;AACD,8BAAsB,sBAAsB;AAAA,UAC1C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,iBAAiB,uBAAuB,YAAY,QAAQ;AAAA,QACvE,CAAC;AACD,8BAAsB,qBAAqB;AAAA,UACzC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS,kBAAkB,MAAM;AAAA,QACnC,CAAC;AACD,cAAM,iBAAiB,eAAe,kBAAkB,MAAM,CAAC;AAC/D,YAAI,eAAe,WAAW,KAAK,OAAO,sBAAsB,qBAAqB,GAAG;AACtF,iBAAO,GAAG,SAAS,cAAc,cAAc;AAAA,YAC7C,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO,0BAA0B,QAAQ,gBAAgB,cAAc,WAAS,OAAO,YAAY,uBAAuB,OAAO,KAAK,CAAC;AAAA,YACvI,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,QACH;AACA,cAAM,qBAAqB,eAAe,iBAAiB,MAAM,CAAC;AAClE,YAAI,mBAAmB,WAAW,KAAK,OAAO,sBAAsB,yBAAyB,GAAG;AAC9F,iBAAO,GAAG,SAAS,cAAc,kBAAkB;AAAA,YACjD,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO,0BAA0B,QAAQ,oBAAoB,kBAAkB,WAAS,OAAO,YAAY,2BAA2B,OAAO,KAAK,CAAC;AAAA,YACnJ,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,QACH;AACA,YAAI,OAAO,sBAAsB,wBAAwB,GAAG;AAC1D,iBAAO,GAAG,SAAS,cAAc,mBAAmB;AAAA,YAClD,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO,0BAA0B,QAAQ,qBAAqB,0BAA0B,oBAAoB,QAAQ,gBAAgB,CAAC;AAAA,YACrI,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AACD,iBAAO,GAAG,SAAS,cAAc,wBAAwB;AAAA,YACvD,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO,0BAA0B,QAAQ,qBAAqB,MAAM,GAAG,wBAAwB,oBAAoB,QAAQ,cAAc,CAAC;AAAA,YAC1I,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AACD,iBAAO,GAAG,SAAS,cAAc,wBAAwB;AAAA,YACvD,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO,0BAA0B,QAAQ,qBAAqB,MAAM,GAAG,wBAAwB,oBAAoB,QAAQ,cAAc,CAAC;AAAA,YAC1I,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AACD,iBAAO,GAAG,SAAS,cAAc,4BAA4B;AAAA,YAC3D,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO,cAAY,SAAS,eAAe,QAAQ,2BAA2B,MAAM,GAAG,kBAAkB,CAAC;AAAA,YAC1G,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AACD,iBAAO,GAAG,SAAS,cAAc,wBAAwB;AAAA,YACvD,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO,cAAY,SAAS,eAAe,QAAQ,uBAAuB,MAAM,GAAG,cAAc,CAAC;AAAA,YAClG,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,QACH;AACA,oCAA4B,gBAAgB;AAAA,UAC1C,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,oCAA4B,kBAAkB;AAAA,UAC5C,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,gBAAgB,MAAM;AAAA,UAChC,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,oCAA4B,kBAAkB;AAAA,UAC5C,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,mBAAmB,MAAM;AAAA,UACnC,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AAAA,MACH;AACA,YAAM,cAAc,YAAU;AAC5B,cAAM,kBAAkB,CAAAU,WAAS,OAAO,IAAI,GAAGA,QAAO,OAAO,KAAK,OAAO,QAAQ,EAAE,SAASA,MAAK,KAAK,OAAO,IAAI,WAAWA,OAAM,UAAU;AAC5I,cAAM,UAAU,WAAW,MAAM;AACjC,YAAI,QAAQ,SAAS,GAAG;AACtB,iBAAO,GAAG,SAAS,kBAAkB,SAAS;AAAA,YAC5C,WAAW;AAAA,YACX,OAAO;AAAA,YACP,OAAO;AAAA,YACP,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,kBAAkB,YAAU,CAAAY,SAAO;AACvC,cAAM,cAAc,MAAM;AACxB,UAAAA,KAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,QAC9C;AACA,eAAO,GAAG,cAAc,WAAW;AACnC,oBAAY;AACZ,eAAO,MAAM;AACX,iBAAO,IAAI,cAAc,WAAW;AAAA,QACtC;AAAA,MACF;AACA,YAAM,eAAe,CAAC,QAAQ,qBAAqB;AACjD,cAAM,MAAM,aAAW,MAAM,OAAO,YAAY,OAAO;AACvD,cAAM,sBAAsB,CAACtB,OAAM,SAAS;AAC1C,cAAI,OAAO,sBAAsB,KAAK,OAAO,GAAG;AAC9C,mBAAO,GAAG,SAAS,YAAYA,OAAM;AAAA,cACnC,GAAG;AAAA,cACH,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI,KAAK,OAAO;AAAA,YACxE,CAAC;AACD,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,4BAA4B,CAACA,OAAM,SAAS;AAChD,cAAI,OAAO,sBAAsB,KAAK,OAAO,GAAG;AAC9C,mBAAO,GAAG,SAAS,kBAAkBA,OAAM;AAAA,cACzC,GAAG;AAAA,cACH,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI,KAAK,OAAO;AAAA,YACxE,CAAC;AAAA,UACH;AAAA,QACF;AACA,cAAM,oBAAoB,UAAQ;AAChC,iBAAO,YAAY,kBAAkB,OAAO;AAAA,YAC1C,MAAM,KAAK;AAAA,YACX,SAAS,KAAK;AAAA,UAChB,CAAC;AAAA,QACH;AACA,cAAM,kBAAkB;AAAA,UACtB,oBAAoB,wBAAwB;AAAA,YAC1C,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,UACD,oBAAoB,uBAAuB;AAAA,YACzC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,UACD,oBAAoB,kBAAkB;AAAA,YACpC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,UACD,oBAAoB,iBAAiB;AAAA,YACnC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,UACD,oBAAoB,eAAe;AAAA,YACjC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,UACD,oBAAoB,gBAAgB;AAAA,YAClC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,UACD,oBAAoB,uBAAuB;AAAA,YACzC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB,iBAAiB,OAAO;AAAA,UACpD,CAAC;AAAA,UACD,oBAAoB,sBAAsB;AAAA,YACxC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB,iBAAiB,OAAO;AAAA,UACpD,CAAC;AAAA,QACH;AACA,cAAM,qBAAqB;AAAA,UACzB,oBAAoB,2BAA2B;AAAA,YAC7C,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB,cAAc,SAAS;AAAA,UACnD,CAAC;AAAA,UACD,oBAAoB,0BAA0B;AAAA,YAC5C,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB,cAAc,QAAQ;AAAA,UAClD,CAAC;AAAA,UACD,oBAAoB,qBAAqB;AAAA,YACvC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB,cAAc,OAAO;AAAA,UACjD,CAAC;AAAA,UACD,oBAAoB,kBAAkB;AAAA,YACpC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB,cAAc,OAAO;AAAA,UACjD,CAAC;AAAA,UACD,oBAAoB,mBAAmB;AAAA,YACrC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB,cAAc,OAAO;AAAA,UACjD,CAAC;AAAA,UACD,oBAAoB,0BAA0B;AAAA,YAC5C,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB,uBAAuB,YAAY,SAAS;AAAA,UACxE,CAAC;AAAA,UACD,oBAAoB,yBAAyB;AAAA,YAC3C,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB,uBAAuB,YAAY,QAAQ;AAAA,UACvE,CAAC;AAAA,QACH;AACA,cAAM,mBAAmB;AAAA,UACvB,oBAAoB,kBAAkB;AAAA,YACpC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,UACD,oBAAoB,mBAAmB;AAAA,YACrC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,UACD,oBAAoB,mBAAmB;AAAA,YACrC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,QACH;AACA,YAAI,CAAC,aAAa,MAAM,GAAG;AACzB,iBAAO,GAAG,SAAS,YAAY,eAAe;AAAA,YAC5C,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU,IAAI,sBAAsB;AAAA,YACpC,SAAS,gBAAgB,MAAM;AAAA,UACjC,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,GAAG,SAAS,kBAAkB,eAAe;AAAA,YAClD,MAAM;AAAA,YACN,MAAM;AAAA,YACN,iBAAiB,MAAM,CAAC;AAAA,cACpB,MAAM;AAAA,cACN,WAAW;AAAA,cACX,UAAU;AAAA,YACZ,CAAC;AAAA,YACH,SAAS,gBAAgB,MAAM;AAAA,UACjC,CAAC;AAAA,QACH;AACA,eAAO,GAAG,SAAS,YAAY,qBAAqB;AAAA,UAClD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU,IAAI,sBAAsB;AAAA,UACpC,SAAS,gBAAgB,MAAM;AAAA,QACjC,CAAC;AACD,4BAAoB,cAAc;AAAA,UAChC,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,UAC1B,SAAS;AAAA,QACX,CAAC;AACD,4BAAoB,eAAe;AAAA,UACjC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS,iBAAiB;AAAA,UAC1B,SAAS;AAAA,QACX,CAAC;AACD,YAAI,SAAS,iBAAiB,IAAI,GAAG;AACnC,iBAAO,GAAG,SAAS,kBAAkB,OAAO;AAAA,YAC1C,MAAM;AAAA,YACN,MAAM;AAAA,YACN,iBAAiB,SAAS,yIAAyI;AAAA,UACrK,CAAC;AAAA,QACH;AACA,YAAI,SAAS,oBAAoB,IAAI,GAAG;AACtC,iBAAO,GAAG,SAAS,kBAAkB,UAAU;AAAA,YAC7C,MAAM;AAAA,YACN,MAAM;AAAA,YACN,iBAAiB,SAAS,gJAAgJ;AAAA,UAC5K,CAAC;AAAA,QACH;AACA,YAAI,SAAS,kBAAkB,IAAI,GAAG;AACpC,iBAAO,GAAG,SAAS,kBAAkB,QAAQ;AAAA,YAC3C,MAAM;AAAA,YACN,MAAM;AAAA,YACN,iBAAiB,SAAS,gDAAgD;AAAA,UAC5E,CAAC;AAAA,QACH;AACA,eAAO,GAAG,SAAS,eAAe,SAAS;AAAA,UACzC,QAAQ,MAAM;AACZ,6BAAiB,aAAa;AAC9B,mBAAO,iBAAiB,QAAQ,EAAE,KAAK,SAAS,EAAE,GAAG,aAAW;AAC9D,kBAAI,KAAK,QAAQ,OAAO,MAAM,WAAW;AACvC,uBAAO;AAAA,cACT,OAAO;AACL,uBAAO;AAAA,cACT;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AACD,cAAM,iBAAiB,eAAe,kBAAkB,MAAM,CAAC;AAC/D,YAAI,eAAe,WAAW,KAAK,OAAO,sBAAsB,qBAAqB,GAAG;AACtF,iBAAO,GAAG,SAAS,kBAAkB,cAAc;AAAA,YACjD,MAAM;AAAA,YACN,MAAM;AAAA,YACN,iBAAiB,MAAM,eAAe,QAAQ,gBAAgB,cAAc,WAAS,OAAO,YAAY,uBAAuB,OAAO,KAAK,CAAC;AAAA,YAC5I,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,QACH;AACA,cAAM,qBAAqB,eAAe,iBAAiB,MAAM,CAAC;AAClE,YAAI,mBAAmB,WAAW,KAAK,OAAO,sBAAsB,yBAAyB,GAAG;AAC9F,iBAAO,GAAG,SAAS,kBAAkB,kBAAkB;AAAA,YACrD,MAAM;AAAA,YACN,MAAM;AAAA,YACN,iBAAiB,MAAM,eAAe,QAAQ,oBAAoB,kBAAkB,WAAS,OAAO,YAAY,2BAA2B,OAAO,KAAK,CAAC;AAAA,YACxJ,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,QACH;AACA,YAAI,OAAO,sBAAsB,wBAAwB,GAAG;AAC1D,iBAAO,GAAG,SAAS,kBAAkB,mBAAmB;AAAA,YACtD,MAAM;AAAA,YACN,MAAM;AAAA,YACN,iBAAiB,MAAM,eAAe,QAAQ,qBAAqB,0BAA0B,oBAAoB,QAAQ,gBAAgB,CAAC;AAAA,YAC1I,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AACD,iBAAO,GAAG,SAAS,kBAAkB,wBAAwB;AAAA,YAC3D,MAAM;AAAA,YACN,MAAM;AAAA,YACN,iBAAiB,MAAM,eAAe,QAAQ,qBAAqB,MAAM,GAAG,wBAAwB,oBAAoB,QAAQ,cAAc,CAAC;AAAA,YAC/I,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AACD,iBAAO,GAAG,SAAS,kBAAkB,wBAAwB;AAAA,YAC3D,MAAM;AAAA,YACN,MAAM;AAAA,YACN,iBAAiB,MAAM,eAAe,QAAQ,qBAAqB,MAAM,GAAG,wBAAwB,oBAAoB,QAAQ,cAAc,CAAC;AAAA,YAC/I,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AACD,iBAAO,GAAG,SAAS,kBAAkB,4BAA4B;AAAA,YAC/D,MAAM;AAAA,YACN,MAAM;AAAA,YACN,iBAAiB,MAAM,eAAe,QAAQ,2BAA2B,MAAM,GAAG,kBAAkB;AAAA,YACpG,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AACD,iBAAO,GAAG,SAAS,kBAAkB,wBAAwB;AAAA,YAC3D,MAAM;AAAA,YACN,MAAM;AAAA,YACN,iBAAiB,MAAM,eAAe,QAAQ,uBAAuB,MAAM,GAAG,cAAc;AAAA,YAC5F,SAAS,iBAAiB;AAAA,UAC5B,CAAC;AAAA,QACH;AACA,kCAA0B,gBAAgB;AAAA,UACxC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,kCAA0B,kBAAkB;AAAA,UAC1C,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,gBAAgB,MAAM;AAAA,UAChC,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AACD,kCAA0B,kBAAkB;AAAA,UAC1C,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,mBAAmB,MAAM;AAAA,UACnC,SAAS,iBAAiB;AAAA,QAC5B,CAAC;AAAA,MACH;AAEA,YAAM,SAAS,YAAU;AACvB,cAAM,mBAAmB,oBAAoB,MAAM;AACnD,iBAAS,MAAM;AACf,yBAAiB,MAAM;AACvB,qBAAa,QAAQ,gBAAgB;AACrC,mBAAW,QAAQ,gBAAgB;AACnC,oBAAY,MAAM;AAAA,MACpB;AACA,UAAI,WAAW,MAAM;AACnB,iBAAS,IAAI,SAAS,MAAM;AAAA,MAC9B;AAEA,eAAS;AAAA,IAEb,GAAG;AAAA;AAAA;;;AC/3GH;", "names": ["type", "all", "name", "key", "is", "ancestor", "selector", "size", "cell", "rows", "columns", "cells", "table", "parent", "detail", "bounds", "last", "children", "get", "set", "document", "universe", "head", "rgbaColour", "api", "adt", "dom", "elm", "hasAdvancedRowTab", "hasAdvancedCellTab", "ephemera", "targets"]}