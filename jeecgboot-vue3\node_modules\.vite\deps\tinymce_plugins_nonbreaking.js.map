{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/nonbreaking/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/nonbreaking/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const isSimpleType = type => value => typeof value === type;\n    const isBoolean = isSimpleType('boolean');\n    const isNumber = isSimpleType('number');\n\n    const option = name => editor => editor.options.get(name);\n    const register$2 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('nonbreaking_force_tab', {\n        processor: value => {\n          if (isBoolean(value)) {\n            return {\n              value: value ? 3 : 0,\n              valid: true\n            };\n          } else if (isNumber(value)) {\n            return {\n              value,\n              valid: true\n            };\n          } else {\n            return {\n              valid: false,\n              message: 'Must be a boolean or number.'\n            };\n          }\n        },\n        default: false\n      });\n      registerOption('nonbreaking_wrap', {\n        processor: 'boolean',\n        default: true\n      });\n    };\n    const getKeyboardSpaces = option('nonbreaking_force_tab');\n    const wrapNbsps = option('nonbreaking_wrap');\n\n    const stringRepeat = (string, repeats) => {\n      let str = '';\n      for (let index = 0; index < repeats; index++) {\n        str += string;\n      }\n      return str;\n    };\n    const isVisualCharsEnabled = editor => editor.plugins.visualchars ? editor.plugins.visualchars.isEnabled() : false;\n    const insertNbsp = (editor, times) => {\n      const classes = () => isVisualCharsEnabled(editor) ? 'mce-nbsp-wrap mce-nbsp' : 'mce-nbsp-wrap';\n      const nbspSpan = () => `<span class=\"${ classes() }\" contenteditable=\"false\">${ stringRepeat('&nbsp;', times) }</span>`;\n      const shouldWrap = wrapNbsps(editor);\n      const html = shouldWrap || editor.plugins.visualchars ? nbspSpan() : stringRepeat('&nbsp;', times);\n      editor.undoManager.transact(() => editor.insertContent(html));\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('mceNonBreaking', () => {\n        insertNbsp(editor, 1);\n      });\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    const setup = editor => {\n      const spaces = getKeyboardSpaces(editor);\n      if (spaces > 0) {\n        editor.on('keydown', e => {\n          if (e.keyCode === global.TAB && !e.isDefaultPrevented()) {\n            if (e.shiftKey) {\n              return;\n            }\n            e.preventDefault();\n            e.stopImmediatePropagation();\n            insertNbsp(editor, spaces);\n          }\n        });\n      }\n    };\n\n    const onSetupEditable = editor => api => {\n      const nodeChanged = () => {\n        api.setEnabled(editor.selection.isEditable());\n      };\n      editor.on('NodeChange', nodeChanged);\n      nodeChanged();\n      return () => {\n        editor.off('NodeChange', nodeChanged);\n      };\n    };\n    const register = editor => {\n      const onAction = () => editor.execCommand('mceNonBreaking');\n      editor.ui.registry.addButton('nonbreaking', {\n        icon: 'non-breaking',\n        tooltip: 'Nonbreaking space',\n        onAction,\n        onSetup: onSetupEditable(editor)\n      });\n      editor.ui.registry.addMenuItem('nonbreaking', {\n        icon: 'non-breaking',\n        text: 'Nonbreaking space',\n        onAction,\n        onSetup: onSetupEditable(editor)\n      });\n    };\n\n    var Plugin = () => {\n      global$1.add('nonbreaking', editor => {\n        register$2(editor);\n        register$1(editor);\n        register(editor);\n        setup(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"nonbreaking\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/nonbreaking')\n//   ES2015:\n//     import 'tinymce/plugins/nonbreaking'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,eAAe,UAAQ,WAAS,OAAO,UAAU;AACvD,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,WAAW,aAAa,QAAQ;AAEtC,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,aAAa,YAAU;AAC3B,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,yBAAyB;AAAA,UACtC,WAAW,WAAS;AAClB,gBAAI,UAAU,KAAK,GAAG;AACpB,qBAAO;AAAA,gBACL,OAAO,QAAQ,IAAI;AAAA,gBACnB,OAAO;AAAA,cACT;AAAA,YACF,WAAW,SAAS,KAAK,GAAG;AAC1B,qBAAO;AAAA,gBACL;AAAA,gBACA,OAAO;AAAA,cACT;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,gBACL,OAAO;AAAA,gBACP,SAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,oBAAoB;AAAA,UACjC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,oBAAoB,OAAO,uBAAuB;AACxD,YAAM,YAAY,OAAO,kBAAkB;AAE3C,YAAM,eAAe,CAAC,QAAQ,YAAY;AACxC,YAAI,MAAM;AACV,iBAAS,QAAQ,GAAG,QAAQ,SAAS,SAAS;AAC5C,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,YAAM,uBAAuB,YAAU,OAAO,QAAQ,cAAc,OAAO,QAAQ,YAAY,UAAU,IAAI;AAC7G,YAAM,aAAa,CAAC,QAAQ,UAAU;AACpC,cAAM,UAAU,MAAM,qBAAqB,MAAM,IAAI,2BAA2B;AAChF,cAAM,WAAW,MAAM,gBAAiB,QAAQ,CAAE,6BAA8B,aAAa,UAAU,KAAK,CAAE;AAC9G,cAAM,aAAa,UAAU,MAAM;AACnC,cAAM,OAAO,cAAc,OAAO,QAAQ,cAAc,SAAS,IAAI,aAAa,UAAU,KAAK;AACjG,eAAO,YAAY,SAAS,MAAM,OAAO,cAAc,IAAI,CAAC;AAAA,MAC9D;AAEA,YAAM,aAAa,YAAU;AAC3B,eAAO,WAAW,kBAAkB,MAAM;AACxC,qBAAW,QAAQ,CAAC;AAAA,QACtB,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,iBAAiB;AAEzD,YAAM,QAAQ,YAAU;AACtB,cAAM,SAAS,kBAAkB,MAAM;AACvC,YAAI,SAAS,GAAG;AACd,iBAAO,GAAG,WAAW,OAAK;AACxB,gBAAI,EAAE,YAAY,OAAO,OAAO,CAAC,EAAE,mBAAmB,GAAG;AACvD,kBAAI,EAAE,UAAU;AACd;AAAA,cACF;AACA,gBAAE,eAAe;AACjB,gBAAE,yBAAyB;AAC3B,yBAAW,QAAQ,MAAM;AAAA,YAC3B;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,kBAAkB,YAAU,SAAO;AACvC,cAAM,cAAc,MAAM;AACxB,cAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,QAC9C;AACA,eAAO,GAAG,cAAc,WAAW;AACnC,oBAAY;AACZ,eAAO,MAAM;AACX,iBAAO,IAAI,cAAc,WAAW;AAAA,QACtC;AAAA,MACF;AACA,YAAM,WAAW,YAAU;AACzB,cAAM,WAAW,MAAM,OAAO,YAAY,gBAAgB;AAC1D,eAAO,GAAG,SAAS,UAAU,eAAe;AAAA,UAC1C,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA,UACA,SAAS,gBAAgB,MAAM;AAAA,QACjC,CAAC;AACD,eAAO,GAAG,SAAS,YAAY,eAAe;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,SAAS,gBAAgB,MAAM;AAAA,QACjC,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,eAAe,YAAU;AACpC,qBAAW,MAAM;AACjB,qBAAW,MAAM;AACjB,mBAAS,MAAM;AACf,gBAAM,MAAM;AAAA,QACd,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;ACpHH;", "names": []}