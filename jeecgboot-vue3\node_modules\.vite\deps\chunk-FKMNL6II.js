import {
  AntdIcon_default
} from "./chunk-5U5LUU7L.js";
import {
  createVNode
} from "./chunk-GWERTEXJ.js";

// node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/EllipsisOutlined.js
var EllipsisOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z" } }] }, "name": "ellipsis", "theme": "outlined" };
var EllipsisOutlined_default = EllipsisOutlined;

// node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.13_typescript@4.9.5_/node_modules/@ant-design/icons-vue/es/icons/EllipsisOutlined.js
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? Object(arguments[i]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var EllipsisOutlined2 = function EllipsisOutlined3(props, context) {
  var p = _objectSpread({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread({}, p, {
    "icon": EllipsisOutlined_default
  }), null);
};
EllipsisOutlined2.displayName = "EllipsisOutlined";
EllipsisOutlined2.inheritAttrs = false;
var EllipsisOutlined_default2 = EllipsisOutlined2;

export {
  EllipsisOutlined_default2 as EllipsisOutlined_default
};
//# sourceMappingURL=chunk-FKMNL6II.js.map
