{"version": 3, "sources": ["../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/_util/shallowequal.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/hooks/useMenuContext.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/layout/injectionKey.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/hooks/useKeyPath.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/vc-tooltip/src/placements.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/vc-tooltip/src/Content.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/vc-tooltip/src/Tooltip.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/vc-tooltip/index.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/tooltip/abstractTooltipProps.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/_util/placements.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/_util/firstNotUndefined.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/_util/colors.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/tooltip/util.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/style/placementArrow.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/tooltip/style/index.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/tooltip/Tooltip.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/tooltip/index.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/hooks/useDirectionStyle.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/MenuItem.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/placements.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/PopupTrigger.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/SubMenuList.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/InlineSubMenuList.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/SubMenu.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/vc-util/Dom/class.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/_util/collapseMotion.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/ItemGroup.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/Divider.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/hooks/useItems.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/style/horizontal.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/style/rtl.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/style/theme.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/style/vertical.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/style/index.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/OverrideContext.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/src/Menu.js", "../../.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@4.9.5_/node_modules/ant-design-vue/es/menu/index.js"], "sourcesContent": ["import { toRaw } from 'vue';\nfunction shallowEqual(objA, objB, compare, compareContext) {\n  let ret = compare ? compare.call(compareContext, objA, objB) : void 0;\n  if (ret !== void 0) {\n    return !!ret;\n  }\n  if (objA === objB) {\n    return true;\n  }\n  if (typeof objA !== 'object' || !objA || typeof objB !== 'object' || !objB) {\n    return false;\n  }\n  const keysA = Object.keys(objA);\n  const keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n  const bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n  // Test for A's keys different from B.\n  for (let idx = 0; idx < keysA.length; idx++) {\n    const key = keysA[idx];\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n    const valueA = objA[key];\n    const valueB = objB[key];\n    ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n    if (ret === false || ret === void 0 && valueA !== valueB) {\n      return false;\n    }\n  }\n  return true;\n}\nexport default function (value, other) {\n  return shallowEqual(toRaw(value), toRaw(other));\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { defineComponent, inject, provide, toRef } from 'vue';\nconst MenuContextKey = Symbol('menuContextKey');\nconst useProvideMenu = props => {\n  provide(MenuContextKey, props);\n};\nconst useInjectMenu = () => {\n  return inject(MenuContextKey);\n};\nconst ForceRenderKey = Symbol('ForceRenderKey');\nexport const useProvideForceRender = forceRender => {\n  provide(ForceRenderKey, forceRender);\n};\nexport const useInjectForceRender = () => {\n  return inject(ForceRenderKey, false);\n};\nconst MenuFirstLevelContextKey = Symbol('menuFirstLevelContextKey');\nconst useProvideFirstLevel = firstLevel => {\n  provide(MenuFirstLevelContextKey, firstLevel);\n};\nconst useInjectFirstLevel = () => {\n  return inject(MenuFirstLevelContextKey, true);\n};\nconst MenuContextProvider = defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'MenuContextProvider',\n  inheritAttrs: false,\n  props: {\n    mode: {\n      type: String,\n      default: undefined\n    },\n    overflowDisabled: {\n      type: Boolean,\n      default: undefined\n    }\n  },\n  setup(props, _ref) {\n    let {\n      slots\n    } = _ref;\n    const menuContext = useInjectMenu();\n    const newContext = _extends({}, menuContext);\n    // 确保传入的属性不会动态增删\n    // 不需要 watch 变化\n    if (props.mode !== undefined) {\n      newContext.mode = toRef(props, 'mode');\n    }\n    if (props.overflowDisabled !== undefined) {\n      newContext.overflowDisabled = toRef(props, 'overflowDisabled');\n    }\n    useProvideMenu(newContext);\n    return () => {\n      var _a;\n      return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n    };\n  }\n});\nexport { useProvideMenu, MenuContextKey, useInjectMenu, MenuFirstLevelContextKey, useProvideFirstLevel, useInjectFirstLevel, MenuContextProvider };\nexport default useProvideMenu;", "export const SiderCollapsedKey = Symbol('siderCollapsed');\nexport const SiderHookProviderKey = Symbol('siderHookProvider');", "import { computed, inject, provide, defineComponent } from 'vue';\nexport const OVERFLOW_KEY = '$$__vc-menu-more__key';\nconst KeyPathContext = Symbol('KeyPathContext');\nconst useInjectKeyPath = () => {\n  return inject(KeyPathContext, {\n    parentEventKeys: computed(() => []),\n    parentKeys: computed(() => []),\n    parentInfo: {}\n  });\n};\nconst useProvideKeyPath = (eventKey, key, menuInfo) => {\n  const {\n    parentEventKeys,\n    parentKeys\n  } = useInjectKeyPath();\n  const eventKeys = computed(() => [...parentEventKeys.value, eventKey]);\n  const keys = computed(() => [...parentKeys.value, key]);\n  provide(KeyPathContext, {\n    parentEventKeys: eventKeys,\n    parentKeys: keys,\n    parentInfo: menuInfo\n  });\n  return keys;\n};\nconst measure = Symbol('measure');\nexport const PathContext = defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  setup(_props, _ref) {\n    let {\n      slots\n    } = _ref;\n    // 不需要响应式\n    provide(measure, true);\n    return () => {\n      var _a;\n      return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n    };\n  }\n});\nexport const useMeasure = () => {\n  return inject(measure, false);\n};\nexport { useProvideKeyPath, useInjectKeyPath, KeyPathContext };\nexport default useProvideKeyPath;", "const autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nconst targetOffset = [0, 0];\nexport const placements = {\n  left: {\n    points: ['cr', 'cl'],\n    overflow: autoAdjustOverflow,\n    offset: [-4, 0],\n    targetOffset\n  },\n  right: {\n    points: ['cl', 'cr'],\n    overflow: autoAdjustOverflow,\n    offset: [4, 0],\n    targetOffset\n  },\n  top: {\n    points: ['bc', 'tc'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset\n  },\n  bottom: {\n    points: ['tc', 'bc'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset\n  },\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset\n  },\n  leftTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [-4, 0],\n    targetOffset\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset\n  },\n  rightTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [4, 0],\n    targetOffset\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset\n  },\n  rightBottom: {\n    points: ['bl', 'br'],\n    overflow: autoAdjustOverflow,\n    offset: [4, 0],\n    targetOffset\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset\n  },\n  leftBottom: {\n    points: ['br', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [-4, 0],\n    targetOffset\n  }\n};\nexport default placements;", "import { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from 'vue';\nimport PropTypes from '../../_util/vue-types';\nconst tooltipContentProps = {\n  prefixCls: String,\n  id: String,\n  overlayInnerStyle: PropTypes.any\n};\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'TooltipContent',\n  props: tooltipContentProps,\n  setup(props, _ref) {\n    let {\n      slots\n    } = _ref;\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": `${props.prefixCls}-inner`,\n        \"id\": props.id,\n        \"role\": \"tooltip\",\n        \"style\": props.overlayInnerStyle\n      }, [(_a = slots.overlay) === null || _a === void 0 ? void 0 : _a.call(slots)]);\n    };\n  }\n});", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { resolveDirective as _resolveDirective, createVNode as _createVNode } from \"vue\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport PropTypes from '../../_util/vue-types';\nimport Trigger from '../../vc-trigger';\nimport { placements } from './placements';\nimport Content from './Content';\nimport { getPropsSlot } from '../../_util/props-util';\nimport { defineComponent, shallowRef, watchEffect } from 'vue';\nfunction noop() {}\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'Tooltip',\n  inheritAttrs: false,\n  props: {\n    trigger: PropTypes.any.def(['hover']),\n    defaultVisible: {\n      type: Boolean,\n      default: undefined\n    },\n    visible: {\n      type: Boolean,\n      default: undefined\n    },\n    placement: PropTypes.string.def('right'),\n    transitionName: String,\n    animation: PropTypes.any,\n    afterVisibleChange: PropTypes.func.def(() => {}),\n    overlayStyle: {\n      type: Object,\n      default: undefined\n    },\n    overlayClassName: String,\n    prefixCls: PropTypes.string.def('rc-tooltip'),\n    mouseEnterDelay: PropTypes.number.def(0.1),\n    mouseLeaveDelay: PropTypes.number.def(0.1),\n    getPopupContainer: Function,\n    destroyTooltipOnHide: {\n      type: Boolean,\n      default: false\n    },\n    align: PropTypes.object.def(() => ({})),\n    arrowContent: PropTypes.any.def(null),\n    tipId: String,\n    builtinPlacements: PropTypes.object,\n    overlayInnerStyle: {\n      type: Object,\n      default: undefined\n    },\n    popupVisible: {\n      type: Boolean,\n      default: undefined\n    },\n    onVisibleChange: Function,\n    onPopupAlign: Function,\n    arrow: {\n      type: Boolean,\n      default: true\n    }\n  },\n  setup(props, _ref) {\n    let {\n      slots,\n      attrs,\n      expose\n    } = _ref;\n    const triggerDOM = shallowRef();\n    const getPopupElement = () => {\n      const {\n        prefixCls,\n        tipId,\n        overlayInnerStyle\n      } = props;\n      return [!!props.arrow ? _createVNode(\"div\", {\n        \"class\": `${prefixCls}-arrow`,\n        \"key\": \"arrow\"\n      }, [getPropsSlot(slots, props, 'arrowContent')]) : null, _createVNode(Content, {\n        \"key\": \"content\",\n        \"prefixCls\": prefixCls,\n        \"id\": tipId,\n        \"overlayInnerStyle\": overlayInnerStyle\n      }, {\n        overlay: slots.overlay\n      })];\n    };\n    const getPopupDomNode = () => {\n      return triggerDOM.value.getPopupDomNode();\n    };\n    expose({\n      getPopupDomNode,\n      triggerDOM,\n      forcePopupAlign: () => {\n        var _a;\n        return (_a = triggerDOM.value) === null || _a === void 0 ? void 0 : _a.forcePopupAlign();\n      }\n    });\n    const destroyTooltip = shallowRef(false);\n    const autoDestroy = shallowRef(false);\n    watchEffect(() => {\n      const {\n        destroyTooltipOnHide\n      } = props;\n      if (typeof destroyTooltipOnHide === 'boolean') {\n        destroyTooltip.value = destroyTooltipOnHide;\n      } else if (destroyTooltipOnHide && typeof destroyTooltipOnHide === 'object') {\n        const {\n          keepParent\n        } = destroyTooltipOnHide;\n        destroyTooltip.value = keepParent === true;\n        autoDestroy.value = keepParent === false;\n      }\n    });\n    return () => {\n      const {\n          overlayClassName,\n          trigger,\n          mouseEnterDelay,\n          mouseLeaveDelay,\n          overlayStyle,\n          prefixCls,\n          afterVisibleChange,\n          transitionName,\n          animation,\n          placement,\n          align,\n          destroyTooltipOnHide,\n          defaultVisible\n        } = props,\n        restProps = __rest(props, [\"overlayClassName\", \"trigger\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"overlayStyle\", \"prefixCls\", \"afterVisibleChange\", \"transitionName\", \"animation\", \"placement\", \"align\", \"destroyTooltipOnHide\", \"defaultVisible\"]);\n      const extraProps = _extends({}, restProps);\n      if (props.visible !== undefined) {\n        extraProps.popupVisible = props.visible;\n      }\n      const triggerProps = _extends(_extends(_extends({\n        popupClassName: overlayClassName,\n        prefixCls,\n        action: trigger,\n        builtinPlacements: placements,\n        popupPlacement: placement,\n        popupAlign: align,\n        afterPopupVisibleChange: afterVisibleChange,\n        popupTransitionName: transitionName,\n        popupAnimation: animation,\n        defaultPopupVisible: defaultVisible,\n        destroyPopupOnHide: destroyTooltip.value,\n        autoDestroy: autoDestroy.value,\n        mouseLeaveDelay,\n        popupStyle: overlayStyle,\n        mouseEnterDelay\n      }, extraProps), attrs), {\n        onPopupVisibleChange: props.onVisibleChange || noop,\n        onPopupAlign: props.onPopupAlign || noop,\n        ref: triggerDOM,\n        arrow: !!props.arrow,\n        popup: getPopupElement()\n      });\n      return _createVNode(Trigger, triggerProps, {\n        default: slots.default\n      });\n    };\n  }\n});", "// base rc-tooltip 5.1.1\nimport Tooltip from './src/Tooltip';\nexport default Tooltip;", "import { objectType } from '../_util/type';\nexport default (() => ({\n  trigger: [String, Array],\n  open: {\n    type: Boolean,\n    default: undefined\n  },\n  /** @deprecated Please use `open` instead. */\n  visible: {\n    type: Boolean,\n    default: undefined\n  },\n  placement: String,\n  color: String,\n  transitionName: String,\n  overlayStyle: objectType(),\n  overlayInnerStyle: objectType(),\n  overlayClassName: String,\n  openClassName: String,\n  prefixCls: String,\n  mouseEnterDelay: Number,\n  mouseLeaveDelay: Number,\n  getPopupContainer: Function,\n  /**@deprecated Please use `arrow={{ pointAtCenter: true }}` instead. */\n  arrowPointAtCenter: {\n    type: Boolean,\n    default: undefined\n  },\n  arrow: {\n    type: [Boolean, Object],\n    default: true\n  },\n  autoAdjustOverflow: {\n    type: [Boolean, Object],\n    default: undefined\n  },\n  destroyTooltipOnHide: {\n    type: Boolean,\n    default: undefined\n  },\n  align: objectType(),\n  builtinPlacements: objectType(),\n  children: Array,\n  /** @deprecated Please use `onOpenChange` instead. */\n  onVisibleChange: Function,\n  /** @deprecated Please use `onUpdate:open` instead. */\n  'onUpdate:visible': Function,\n  onOpenChange: Function,\n  'onUpdate:open': Function\n}));", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { placements } from '../vc-tooltip/src/placements';\nconst autoAdjustOverflowEnabled = {\n  adjustX: 1,\n  adjustY: 1\n};\nconst autoAdjustOverflowDisabled = {\n  adjustX: 0,\n  adjustY: 0\n};\nconst targetOffset = [0, 0];\nexport function getOverflowOptions(autoAdjustOverflow) {\n  if (typeof autoAdjustOverflow === 'boolean') {\n    return autoAdjustOverflow ? autoAdjustOverflowEnabled : autoAdjustOverflowDisabled;\n  }\n  return _extends(_extends({}, autoAdjustOverflowDisabled), autoAdjustOverflow);\n}\nexport default function getPlacements(config) {\n  const {\n    arrowWidth = 4,\n    horizontalArrowShift = 16,\n    verticalArrowShift = 8,\n    autoAdjustOverflow,\n    arrowPointAtCenter\n  } = config;\n  const placementMap = {\n    left: {\n      points: ['cr', 'cl'],\n      offset: [-4, 0]\n    },\n    right: {\n      points: ['cl', 'cr'],\n      offset: [4, 0]\n    },\n    top: {\n      points: ['bc', 'tc'],\n      offset: [0, -4]\n    },\n    bottom: {\n      points: ['tc', 'bc'],\n      offset: [0, 4]\n    },\n    topLeft: {\n      points: ['bl', 'tc'],\n      offset: [-(horizontalArrowShift + arrowWidth), -4]\n    },\n    leftTop: {\n      points: ['tr', 'cl'],\n      offset: [-4, -(verticalArrowShift + arrowWidth)]\n    },\n    topRight: {\n      points: ['br', 'tc'],\n      offset: [horizontalArrowShift + arrowWidth, -4]\n    },\n    rightTop: {\n      points: ['tl', 'cr'],\n      offset: [4, -(verticalArrowShift + arrowWidth)]\n    },\n    bottomRight: {\n      points: ['tr', 'bc'],\n      offset: [horizontalArrowShift + arrowWidth, 4]\n    },\n    rightBottom: {\n      points: ['bl', 'cr'],\n      offset: [4, verticalArrowShift + arrowWidth]\n    },\n    bottomLeft: {\n      points: ['tl', 'bc'],\n      offset: [-(horizontalArrowShift + arrowWidth), 4]\n    },\n    leftBottom: {\n      points: ['br', 'cl'],\n      offset: [-4, verticalArrowShift + arrowWidth]\n    }\n  };\n  Object.keys(placementMap).forEach(key => {\n    placementMap[key] = arrowPointAtCenter ? _extends(_extends({}, placementMap[key]), {\n      overflow: getOverflowOptions(autoAdjustOverflow),\n      targetOffset\n    }) : _extends(_extends({}, placements[key]), {\n      overflow: getOverflowOptions(autoAdjustOverflow)\n    });\n    placementMap[key].ignoreShake = true;\n  });\n  return placementMap;\n}", "function firstNotUndefined() {\n  let arr = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  for (let i = 0, len = arr.length; i < len; i++) {\n    if (arr[i] !== undefined) {\n      return arr[i];\n    }\n  }\n  return undefined;\n}\nexport default firstNotUndefined;", "import { PresetColors } from '../theme/interface';\nconst inverseColors = PresetColors.map(color => `${color}-inverse`);\nexport const PresetStatusColorTypes = ['success', 'processing', 'error', 'default', 'warning'];\n/**\n * determine if the color keyword belongs to the `Ant Design` {@link PresetColors}.\n * @param color color to be judged\n * @param includeInverse whether to include reversed colors\n */\nexport function isPresetColor(color) {\n  let includeInverse = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (includeInverse) {\n    return [...inverseColors, ...PresetColors].includes(color);\n  }\n  return PresetColors.includes(color);\n}\nexport function isPresetStatusColor(color) {\n  return PresetStatusColorTypes.includes(color);\n}", "import classNames from '../_util/classNames';\nimport { isPresetColor } from '../_util/colors';\nexport function parseColor(prefixCls, color) {\n  const isInternalColor = isPresetColor(color);\n  const className = classNames({\n    [`${prefixCls}-${color}`]: color && isInternalColor\n  });\n  const overlayStyle = {};\n  const arrowStyle = {};\n  if (color && !isInternalColor) {\n    overlayStyle.background = color;\n    // @ts-ignore\n    arrowStyle['--antd-arrow-background-color'] = color;\n  }\n  return {\n    className,\n    overlayStyle,\n    arrowStyle\n  };\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { roundedArrow } from './roundedArrow';\nfunction connectArrowCls(classList) {\n  let showArrowCls = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  return classList.map(cls => `${showArrowCls}${cls}`).join(',');\n}\nexport const MAX_VERTICAL_CONTENT_RADIUS = 8;\nexport function getArrowOffset(options) {\n  const maxVerticalContentRadius = MAX_VERTICAL_CONTENT_RADIUS;\n  const {\n    sizePopupArrow,\n    contentRadius,\n    borderRadiusOuter,\n    limitVerticalRadius\n  } = options;\n  const arrowInnerOffset = sizePopupArrow / 2 - Math.ceil(borderRadiusOuter * (Math.sqrt(2) - 1));\n  const dropdownArrowOffset = (contentRadius > 12 ? contentRadius + 2 : 12) - arrowInnerOffset;\n  const dropdownArrowOffsetVertical = limitVerticalRadius ? maxVerticalContentRadius - arrowInnerOffset : dropdownArrowOffset;\n  return {\n    dropdownArrowOffset,\n    dropdownArrowOffsetVertical\n  };\n}\nexport default function getArrowStyle(token, options) {\n  const {\n    componentCls,\n    sizePopupArrow,\n    marginXXS,\n    borderRadiusXS,\n    borderRadiusOuter,\n    boxShadowPopoverArrow\n  } = token;\n  const {\n    colorBg,\n    showArrowCls,\n    contentRadius = token.borderRadiusLG,\n    limitVerticalRadius\n  } = options;\n  const {\n    dropdownArrowOffsetVertical,\n    dropdownArrowOffset\n  } = getArrowOffset({\n    sizePopupArrow,\n    contentRadius,\n    borderRadiusOuter,\n    limitVerticalRadius\n  });\n  const dropdownArrowDistance = sizePopupArrow / 2 + marginXXS;\n  return {\n    [componentCls]: {\n      // ============================ Basic ============================\n      [`${componentCls}-arrow`]: [_extends(_extends({\n        position: 'absolute',\n        zIndex: 1,\n        display: 'block'\n      }, roundedArrow(sizePopupArrow, borderRadiusXS, borderRadiusOuter, colorBg, boxShadowPopoverArrow)), {\n        '&:before': {\n          background: colorBg\n        }\n      })],\n      // ========================== Placement ==========================\n      // Here handle the arrow position and rotate stuff\n      // >>>>> Top\n      [[`&-placement-top ${componentCls}-arrow`, `&-placement-topLeft ${componentCls}-arrow`, `&-placement-topRight ${componentCls}-arrow`].join(',')]: {\n        bottom: 0,\n        transform: 'translateY(100%) rotate(180deg)'\n      },\n      [`&-placement-top ${componentCls}-arrow`]: {\n        left: {\n          _skip_check_: true,\n          value: '50%'\n        },\n        transform: 'translateX(-50%) translateY(100%) rotate(180deg)'\n      },\n      [`&-placement-topLeft ${componentCls}-arrow`]: {\n        left: {\n          _skip_check_: true,\n          value: dropdownArrowOffset\n        }\n      },\n      [`&-placement-topRight ${componentCls}-arrow`]: {\n        right: {\n          _skip_check_: true,\n          value: dropdownArrowOffset\n        }\n      },\n      // >>>>> Bottom\n      [[`&-placement-bottom ${componentCls}-arrow`, `&-placement-bottomLeft ${componentCls}-arrow`, `&-placement-bottomRight ${componentCls}-arrow`].join(',')]: {\n        top: 0,\n        transform: `translateY(-100%)`\n      },\n      [`&-placement-bottom ${componentCls}-arrow`]: {\n        left: {\n          _skip_check_: true,\n          value: '50%'\n        },\n        transform: `translateX(-50%) translateY(-100%)`\n      },\n      [`&-placement-bottomLeft ${componentCls}-arrow`]: {\n        left: {\n          _skip_check_: true,\n          value: dropdownArrowOffset\n        }\n      },\n      [`&-placement-bottomRight ${componentCls}-arrow`]: {\n        right: {\n          _skip_check_: true,\n          value: dropdownArrowOffset\n        }\n      },\n      // >>>>> Left\n      [[`&-placement-left ${componentCls}-arrow`, `&-placement-leftTop ${componentCls}-arrow`, `&-placement-leftBottom ${componentCls}-arrow`].join(',')]: {\n        right: {\n          _skip_check_: true,\n          value: 0\n        },\n        transform: 'translateX(100%) rotate(90deg)'\n      },\n      [`&-placement-left ${componentCls}-arrow`]: {\n        top: {\n          _skip_check_: true,\n          value: '50%'\n        },\n        transform: 'translateY(-50%) translateX(100%) rotate(90deg)'\n      },\n      [`&-placement-leftTop ${componentCls}-arrow`]: {\n        top: dropdownArrowOffsetVertical\n      },\n      [`&-placement-leftBottom ${componentCls}-arrow`]: {\n        bottom: dropdownArrowOffsetVertical\n      },\n      // >>>>> Right\n      [[`&-placement-right ${componentCls}-arrow`, `&-placement-rightTop ${componentCls}-arrow`, `&-placement-rightBottom ${componentCls}-arrow`].join(',')]: {\n        left: {\n          _skip_check_: true,\n          value: 0\n        },\n        transform: 'translateX(-100%) rotate(-90deg)'\n      },\n      [`&-placement-right ${componentCls}-arrow`]: {\n        top: {\n          _skip_check_: true,\n          value: '50%'\n        },\n        transform: 'translateY(-50%) translateX(-100%) rotate(-90deg)'\n      },\n      [`&-placement-rightTop ${componentCls}-arrow`]: {\n        top: dropdownArrowOffsetVertical\n      },\n      [`&-placement-rightBottom ${componentCls}-arrow`]: {\n        bottom: dropdownArrowOffsetVertical\n      },\n      // =========================== Offset ============================\n      // Offset the popover to account for the dropdown arrow\n      // >>>>> Top\n      [connectArrowCls([`&-placement-topLeft`, `&-placement-top`, `&-placement-topRight`].map(cls => cls += ':not(&-arrow-hidden)'), showArrowCls)]: {\n        paddingBottom: dropdownArrowDistance\n      },\n      // >>>>> Bottom\n      [connectArrowCls([`&-placement-bottomLeft`, `&-placement-bottom`, `&-placement-bottomRight`].map(cls => cls += ':not(&-arrow-hidden)'), showArrowCls)]: {\n        paddingTop: dropdownArrowDistance\n      },\n      // >>>>> Left\n      [connectArrowCls([`&-placement-leftTop`, `&-placement-left`, `&-placement-leftBottom`].map(cls => cls += ':not(&-arrow-hidden)'), showArrowCls)]: {\n        paddingRight: {\n          _skip_check_: true,\n          value: dropdownArrowDistance\n        }\n      },\n      // >>>>> Right\n      [connectArrowCls([`&-placement-rightTop`, `&-placement-right`, `&-placement-rightBottom`].map(cls => cls += ':not(&-arrow-hidden)'), showArrowCls)]: {\n        paddingLeft: {\n          _skip_check_: true,\n          value: dropdownArrowDistance\n        }\n      }\n    }\n  };\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { initZoomMotion } from '../../style/motion';\nimport { genComponentStyleHook, mergeToken } from '../../theme/internal';\nimport { genPresetColor, resetComponent } from '../../style';\nimport getArrowStyle, { MAX_VERTICAL_CONTENT_RADIUS } from '../../style/placementArrow';\nimport 'vue';\nconst genTooltipStyle = token => {\n  const {\n    componentCls,\n    // ant-tooltip\n    tooltipMaxWidth,\n    tooltipColor,\n    tooltipBg,\n    tooltipBorderRadius,\n    zIndexPopup,\n    controlHeight,\n    boxShadowSecondary,\n    paddingSM,\n    paddingXS,\n    tooltipRadiusOuter\n  } = token;\n  return [{\n    [componentCls]: _extends(_extends(_extends(_extends({}, resetComponent(token)), {\n      position: 'absolute',\n      zIndex: zIndexPopup,\n      display: 'block',\n      '&': [{\n        width: 'max-content'\n      }, {\n        width: 'intrinsic'\n      }],\n      maxWidth: tooltipMaxWidth,\n      visibility: 'visible',\n      '&-hidden': {\n        display: 'none'\n      },\n      '--antd-arrow-background-color': tooltipBg,\n      // Wrapper for the tooltip content\n      [`${componentCls}-inner`]: {\n        minWidth: controlHeight,\n        minHeight: controlHeight,\n        padding: `${paddingSM / 2}px ${paddingXS}px`,\n        color: tooltipColor,\n        textAlign: 'start',\n        textDecoration: 'none',\n        wordWrap: 'break-word',\n        backgroundColor: tooltipBg,\n        borderRadius: tooltipBorderRadius,\n        boxShadow: boxShadowSecondary\n      },\n      // Limit left and right placement radius\n      [[`&-placement-left`, `&-placement-leftTop`, `&-placement-leftBottom`, `&-placement-right`, `&-placement-rightTop`, `&-placement-rightBottom`].join(',')]: {\n        [`${componentCls}-inner`]: {\n          borderRadius: Math.min(tooltipBorderRadius, MAX_VERTICAL_CONTENT_RADIUS)\n        }\n      },\n      [`${componentCls}-content`]: {\n        position: 'relative'\n      }\n    }), genPresetColor(token, (colorKey, _ref) => {\n      let {\n        darkColor\n      } = _ref;\n      return {\n        [`&${componentCls}-${colorKey}`]: {\n          [`${componentCls}-inner`]: {\n            backgroundColor: darkColor\n          },\n          [`${componentCls}-arrow`]: {\n            '--antd-arrow-background-color': darkColor\n          }\n        }\n      };\n    })), {\n      // RTL\n      '&-rtl': {\n        direction: 'rtl'\n      }\n    })\n  },\n  // Arrow Style\n  getArrowStyle(mergeToken(token, {\n    borderRadiusOuter: tooltipRadiusOuter\n  }), {\n    colorBg: 'var(--antd-arrow-background-color)',\n    showArrowCls: '',\n    contentRadius: tooltipBorderRadius,\n    limitVerticalRadius: true\n  }),\n  // Pure Render\n  {\n    [`${componentCls}-pure`]: {\n      position: 'relative',\n      maxWidth: 'none'\n    }\n  }];\n};\n// ============================== Export ==============================\nexport default ((prefixCls, injectStyle) => {\n  const useOriginHook = genComponentStyleHook('Tooltip', token => {\n    // Popover use Tooltip as internal component. We do not need to handle this.\n    if ((injectStyle === null || injectStyle === void 0 ? void 0 : injectStyle.value) === false) {\n      return [];\n    }\n    const {\n      borderRadius,\n      colorTextLightSolid,\n      colorBgDefault,\n      borderRadiusOuter\n    } = token;\n    const TooltipToken = mergeToken(token, {\n      // default variables\n      tooltipMaxWidth: 250,\n      tooltipColor: colorTextLightSolid,\n      tooltipBorderRadius: borderRadius,\n      tooltipBg: colorBgDefault,\n      tooltipRadiusOuter: borderRadiusOuter > 4 ? 4 : borderRadiusOuter\n    });\n    return [genTooltipStyle(TooltipToken), initZoomMotion(token, 'zoom-big-fast')];\n  }, _ref2 => {\n    let {\n      zIndexPopupBase,\n      colorBgSpotlight\n    } = _ref2;\n    return {\n      zIndexPopup: zIndexPopupBase + 70,\n      colorBgDefault: colorBgSpotlight\n    };\n  });\n  return useOriginHook(prefixCls);\n});", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { resolveDirective as _resolveDirective, createVNode as _createVNode } from \"vue\";\nimport { computed, watch, defineComponent, ref } from 'vue';\nimport VcTooltip from '../vc-tooltip';\nimport classNames from '../_util/classNames';\nimport PropTypes from '../_util/vue-types';\nimport warning from '../_util/warning';\nimport { getStyle, filterEmpty, isValidElement, initDefaultProps, isFragment } from '../_util/props-util';\nimport { cloneElement } from '../_util/vnode';\nimport abstractTooltipProps from './abstractTooltipProps';\nimport useConfigInject from '../config-provider/hooks/useConfigInject';\nimport getPlacements from '../_util/placements';\nimport firstNotUndefined from '../_util/firstNotUndefined';\nimport raf from '../_util/raf';\nimport { parseColor } from './util';\nimport useStyle from './style';\nimport { getTransitionName } from '../_util/transition';\nconst splitObject = (obj, keys) => {\n  const picked = {};\n  const omitted = _extends({}, obj);\n  keys.forEach(key => {\n    if (obj && key in obj) {\n      picked[key] = obj[key];\n      delete omitted[key];\n    }\n  });\n  return {\n    picked,\n    omitted\n  };\n};\nexport const tooltipProps = () => _extends(_extends({}, abstractTooltipProps()), {\n  title: PropTypes.any\n});\nexport const tooltipDefaultProps = () => ({\n  trigger: 'hover',\n  align: {},\n  placement: 'top',\n  mouseEnterDelay: 0.1,\n  mouseLeaveDelay: 0.1,\n  arrowPointAtCenter: false,\n  autoAdjustOverflow: true\n});\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'ATooltip',\n  inheritAttrs: false,\n  props: initDefaultProps(tooltipProps(), {\n    trigger: 'hover',\n    align: {},\n    placement: 'top',\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1,\n    arrowPointAtCenter: false,\n    autoAdjustOverflow: true\n  }),\n  slots: Object,\n  // emits: ['update:visible', 'visibleChange'],\n  setup(props, _ref) {\n    let {\n      slots,\n      emit,\n      attrs,\n      expose\n    } = _ref;\n    if (process.env.NODE_ENV !== 'production') {\n      [['visible', 'open'], ['onVisibleChange', 'onOpenChange']].forEach(_ref2 => {\n        let [deprecatedName, newName] = _ref2;\n        warning(props[deprecatedName] === undefined, 'Tooltip', `\\`${deprecatedName}\\` is deprecated, please use \\`${newName}\\` instead.`);\n      });\n    }\n    const {\n      prefixCls,\n      getPopupContainer,\n      direction,\n      rootPrefixCls\n    } = useConfigInject('tooltip', props);\n    const mergedOpen = computed(() => {\n      var _a;\n      return (_a = props.open) !== null && _a !== void 0 ? _a : props.visible;\n    });\n    const innerOpen = ref(firstNotUndefined([props.open, props.visible]));\n    const tooltip = ref();\n    let rafId;\n    watch(mergedOpen, val => {\n      raf.cancel(rafId);\n      rafId = raf(() => {\n        innerOpen.value = !!val;\n      });\n    });\n    const isNoTitle = () => {\n      var _a;\n      const title = (_a = props.title) !== null && _a !== void 0 ? _a : slots.title;\n      return !title && title !== 0;\n    };\n    const handleVisibleChange = val => {\n      const noTitle = isNoTitle();\n      if (mergedOpen.value === undefined) {\n        innerOpen.value = noTitle ? false : val;\n      }\n      if (!noTitle) {\n        emit('update:visible', val);\n        emit('visibleChange', val);\n        emit('update:open', val);\n        emit('openChange', val);\n      }\n    };\n    const getPopupDomNode = () => {\n      return tooltip.value.getPopupDomNode();\n    };\n    expose({\n      getPopupDomNode,\n      open: innerOpen,\n      forcePopupAlign: () => {\n        var _a;\n        return (_a = tooltip.value) === null || _a === void 0 ? void 0 : _a.forcePopupAlign();\n      }\n    });\n    const tooltipPlacements = computed(() => {\n      var _a;\n      const {\n        builtinPlacements,\n        autoAdjustOverflow,\n        arrow,\n        arrowPointAtCenter\n      } = props;\n      let mergedArrowPointAtCenter = arrowPointAtCenter;\n      if (typeof arrow === 'object') {\n        mergedArrowPointAtCenter = (_a = arrow.pointAtCenter) !== null && _a !== void 0 ? _a : arrowPointAtCenter;\n      }\n      return builtinPlacements || getPlacements({\n        arrowPointAtCenter: mergedArrowPointAtCenter,\n        autoAdjustOverflow\n      });\n    });\n    const isTrueProps = val => {\n      return val || val === '';\n    };\n    const getDisabledCompatibleChildren = ele => {\n      const elementType = ele.type;\n      if (typeof elementType === 'object' && ele.props) {\n        if ((elementType.__ANT_BUTTON === true || elementType === 'button') && isTrueProps(ele.props.disabled) || elementType.__ANT_SWITCH === true && (isTrueProps(ele.props.disabled) || isTrueProps(ele.props.loading)) || elementType.__ANT_RADIO === true && isTrueProps(ele.props.disabled)) {\n          // Pick some layout related style properties up to span\n          // Prevent layout bugs like https://github.com/ant-design/ant-design/issues/5254\n          const {\n            picked,\n            omitted\n          } = splitObject(getStyle(ele), ['position', 'left', 'right', 'top', 'bottom', 'float', 'display', 'zIndex']);\n          const spanStyle = _extends(_extends({\n            display: 'inline-block'\n          }, picked), {\n            cursor: 'not-allowed',\n            lineHeight: 1,\n            width: ele.props && ele.props.block ? '100%' : undefined\n          });\n          const buttonStyle = _extends(_extends({}, omitted), {\n            pointerEvents: 'none'\n          });\n          const child = cloneElement(ele, {\n            style: buttonStyle\n          }, true);\n          return _createVNode(\"span\", {\n            \"style\": spanStyle,\n            \"class\": `${prefixCls.value}-disabled-compatible-wrapper`\n          }, [child]);\n        }\n      }\n      return ele;\n    };\n    const getOverlay = () => {\n      var _a, _b;\n      return (_a = props.title) !== null && _a !== void 0 ? _a : (_b = slots.title) === null || _b === void 0 ? void 0 : _b.call(slots);\n    };\n    const onPopupAlign = (domNode, align) => {\n      const placements = tooltipPlacements.value;\n      // 当前返回的位置\n      const placement = Object.keys(placements).find(key => {\n        var _a, _b;\n        return placements[key].points[0] === ((_a = align.points) === null || _a === void 0 ? void 0 : _a[0]) && placements[key].points[1] === ((_b = align.points) === null || _b === void 0 ? void 0 : _b[1]);\n      });\n      if (placement) {\n        // 根据当前坐标设置动画点\n        const rect = domNode.getBoundingClientRect();\n        const transformOrigin = {\n          top: '50%',\n          left: '50%'\n        };\n        if (placement.indexOf('top') >= 0 || placement.indexOf('Bottom') >= 0) {\n          transformOrigin.top = `${rect.height - align.offset[1]}px`;\n        } else if (placement.indexOf('Top') >= 0 || placement.indexOf('bottom') >= 0) {\n          transformOrigin.top = `${-align.offset[1]}px`;\n        }\n        if (placement.indexOf('left') >= 0 || placement.indexOf('Right') >= 0) {\n          transformOrigin.left = `${rect.width - align.offset[0]}px`;\n        } else if (placement.indexOf('right') >= 0 || placement.indexOf('Left') >= 0) {\n          transformOrigin.left = `${-align.offset[0]}px`;\n        }\n        domNode.style.transformOrigin = `${transformOrigin.left} ${transformOrigin.top}`;\n      }\n    };\n    const colorInfo = computed(() => parseColor(prefixCls.value, props.color));\n    const injectFromPopover = computed(() => attrs['data-popover-inject']);\n    const [wrapSSR, hashId] = useStyle(prefixCls, computed(() => !injectFromPopover.value));\n    return () => {\n      var _a, _b;\n      const {\n        openClassName,\n        overlayClassName,\n        overlayStyle,\n        overlayInnerStyle\n      } = props;\n      let children = (_b = filterEmpty((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots))) !== null && _b !== void 0 ? _b : null;\n      children = children.length === 1 ? children[0] : children;\n      let tempVisible = innerOpen.value;\n      // Hide tooltip when there is no title\n      if (mergedOpen.value === undefined && isNoTitle()) {\n        tempVisible = false;\n      }\n      if (!children) {\n        return null;\n      }\n      const child = getDisabledCompatibleChildren(isValidElement(children) && !isFragment(children) ? children : _createVNode(\"span\", null, [children]));\n      const childCls = classNames({\n        [openClassName || `${prefixCls.value}-open`]: true,\n        [child.props && child.props.class]: child.props && child.props.class\n      });\n      const customOverlayClassName = classNames(overlayClassName, {\n        [`${prefixCls.value}-rtl`]: direction.value === 'rtl'\n      }, colorInfo.value.className, hashId.value);\n      const formattedOverlayInnerStyle = _extends(_extends({}, colorInfo.value.overlayStyle), overlayInnerStyle);\n      const arrowContentStyle = colorInfo.value.arrowStyle;\n      const vcTooltipProps = _extends(_extends(_extends({}, attrs), props), {\n        prefixCls: prefixCls.value,\n        arrow: !!props.arrow,\n        getPopupContainer: getPopupContainer === null || getPopupContainer === void 0 ? void 0 : getPopupContainer.value,\n        builtinPlacements: tooltipPlacements.value,\n        visible: tempVisible,\n        ref: tooltip,\n        overlayClassName: customOverlayClassName,\n        overlayStyle: _extends(_extends({}, arrowContentStyle), overlayStyle),\n        overlayInnerStyle: formattedOverlayInnerStyle,\n        onVisibleChange: handleVisibleChange,\n        onPopupAlign,\n        transitionName: getTransitionName(rootPrefixCls.value, 'zoom-big-fast', props.transitionName)\n      });\n      return wrapSSR(_createVNode(VcTooltip, vcTooltipProps, {\n        default: () => [innerOpen.value ? cloneElement(child, {\n          class: childCls\n        }) : child],\n        arrowContent: () => _createVNode(\"span\", {\n          \"class\": `${prefixCls.value}-arrow-content`\n        }, null),\n        overlay: getOverlay\n      }));\n    };\n  }\n});", "import { withInstall } from '../_util/type';\nimport ToolTip, { tooltipProps } from './Tooltip';\nexport { tooltipProps };\nexport default withInstall(ToolTip);", "import { computed } from 'vue';\nimport { useInjectMenu } from './useMenuContext';\nexport default function useDirectionStyle(level) {\n  const {\n    mode,\n    rtl,\n    inlineIndent\n  } = useInjectMenu();\n  return computed(() => mode.value !== 'inline' ? null : rtl.value ? {\n    paddingRight: `${level.value * inlineIndent.value}px`\n  } : {\n    paddingLeft: `${level.value * inlineIndent.value}px`\n  });\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { createVNode as _createVNode } from \"vue\";\nimport { flattenChildren, isValidElement } from '../../_util/props-util';\nimport PropTypes from '../../_util/vue-types';\nimport { computed, defineComponent, getCurrentInstance, onBeforeUnmount, shallowRef, watch } from 'vue';\nimport { useInjectKeyPath, useMeasure } from './hooks/useKeyPath';\nimport { useInjectFirstLevel, useInjectMenu } from './hooks/useMenuContext';\nimport { cloneElement } from '../../_util/vnode';\nimport Tooltip from '../../tooltip';\nimport KeyCode from '../../_util/KeyCode';\nimport useDirectionStyle from './hooks/useDirectionStyle';\nimport Overflow from '../../vc-overflow';\nimport devWarning from '../../vc-util/devWarning';\nimport { objectType } from '../../_util/type';\nlet indexGuid = 0;\nexport const menuItemProps = () => ({\n  id: String,\n  role: String,\n  disabled: Boolean,\n  danger: Boolean,\n  title: {\n    type: [String, Boolean],\n    default: undefined\n  },\n  icon: PropTypes.any,\n  onMouseenter: Function,\n  onMouseleave: Function,\n  onClick: Function,\n  onKeydown: Function,\n  onFocus: Function,\n  // Internal user prop\n  originItemValue: objectType()\n});\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'AMenuItem',\n  inheritAttrs: false,\n  props: menuItemProps(),\n  slots: Object,\n  setup(props, _ref) {\n    let {\n      slots,\n      emit,\n      attrs\n    } = _ref;\n    const instance = getCurrentInstance();\n    const isMeasure = useMeasure();\n    const key = typeof instance.vnode.key === 'symbol' ? String(instance.vnode.key) : instance.vnode.key;\n    devWarning(typeof instance.vnode.key !== 'symbol', 'MenuItem', `MenuItem \\`:key=\"${String(key)}\"\\` not support Symbol type`);\n    const eventKey = `menu_item_${++indexGuid}_$$_${key}`;\n    const {\n      parentEventKeys,\n      parentKeys\n    } = useInjectKeyPath();\n    const {\n      prefixCls,\n      activeKeys,\n      disabled,\n      changeActiveKeys,\n      rtl,\n      inlineCollapsed,\n      siderCollapsed,\n      onItemClick,\n      selectedKeys,\n      registerMenuInfo,\n      unRegisterMenuInfo\n    } = useInjectMenu();\n    const firstLevel = useInjectFirstLevel();\n    const isActive = shallowRef(false);\n    const keysPath = computed(() => {\n      return [...parentKeys.value, key];\n    });\n    // const keysPath = computed(() => [...parentEventKeys.value, eventKey]);\n    const menuInfo = {\n      eventKey,\n      key,\n      parentEventKeys,\n      parentKeys,\n      isLeaf: true\n    };\n    registerMenuInfo(eventKey, menuInfo);\n    onBeforeUnmount(() => {\n      unRegisterMenuInfo(eventKey);\n    });\n    watch(activeKeys, () => {\n      isActive.value = !!activeKeys.value.find(val => val === key);\n    }, {\n      immediate: true\n    });\n    const mergedDisabled = computed(() => disabled.value || props.disabled);\n    const selected = computed(() => selectedKeys.value.includes(key));\n    const classNames = computed(() => {\n      const itemCls = `${prefixCls.value}-item`;\n      return {\n        [`${itemCls}`]: true,\n        [`${itemCls}-danger`]: props.danger,\n        [`${itemCls}-active`]: isActive.value,\n        [`${itemCls}-selected`]: selected.value,\n        [`${itemCls}-disabled`]: mergedDisabled.value\n      };\n    });\n    const getEventInfo = e => {\n      return {\n        key,\n        eventKey,\n        keyPath: keysPath.value,\n        eventKeyPath: [...parentEventKeys.value, eventKey],\n        domEvent: e,\n        item: _extends(_extends({}, props), attrs)\n      };\n    };\n    // ============================ Events ============================\n    const onInternalClick = e => {\n      if (mergedDisabled.value) {\n        return;\n      }\n      const info = getEventInfo(e);\n      emit('click', e);\n      onItemClick(info);\n    };\n    const onMouseEnter = event => {\n      if (!mergedDisabled.value) {\n        changeActiveKeys(keysPath.value);\n        emit('mouseenter', event);\n      }\n    };\n    const onMouseLeave = event => {\n      if (!mergedDisabled.value) {\n        changeActiveKeys([]);\n        emit('mouseleave', event);\n      }\n    };\n    const onInternalKeyDown = e => {\n      emit('keydown', e);\n      if (e.which === KeyCode.ENTER) {\n        const info = getEventInfo(e);\n        // Legacy. Key will also trigger click event\n        emit('click', e);\n        onItemClick(info);\n      }\n    };\n    /**\n     * Used for accessibility. Helper will focus element without key board.\n     * We should manually trigger an active\n     */\n    const onInternalFocus = e => {\n      changeActiveKeys(keysPath.value);\n      emit('focus', e);\n    };\n    const renderItemChildren = (icon, children) => {\n      const wrapNode = _createVNode(\"span\", {\n        \"class\": `${prefixCls.value}-title-content`\n      }, [children]);\n      // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n      // ref: https://github.com/ant-design/ant-design/pull/23456\n      if (!icon || isValidElement(children) && children.type === 'span') {\n        if (children && inlineCollapsed.value && firstLevel && typeof children === 'string') {\n          return _createVNode(\"div\", {\n            \"class\": `${prefixCls.value}-inline-collapsed-noicon`\n          }, [children.charAt(0)]);\n        }\n      }\n      return wrapNode;\n    };\n    // ========================== DirectionStyle ==========================\n    const directionStyle = useDirectionStyle(computed(() => keysPath.value.length));\n    return () => {\n      var _a, _b, _c, _d, _e;\n      if (isMeasure) return null;\n      const title = (_a = props.title) !== null && _a !== void 0 ? _a : (_b = slots.title) === null || _b === void 0 ? void 0 : _b.call(slots);\n      const children = flattenChildren((_c = slots.default) === null || _c === void 0 ? void 0 : _c.call(slots));\n      const childrenLength = children.length;\n      let tooltipTitle = title;\n      if (typeof title === 'undefined') {\n        tooltipTitle = firstLevel && childrenLength ? children : '';\n      } else if (title === false) {\n        tooltipTitle = '';\n      }\n      const tooltipProps = {\n        title: tooltipTitle\n      };\n      if (!siderCollapsed.value && !inlineCollapsed.value) {\n        tooltipProps.title = null;\n        // Reset `visible` to fix control mode tooltip display not correct\n        // ref: https://github.com/ant-design/ant-design/issues/16742\n        tooltipProps.open = false;\n      }\n      // ============================ Render ============================\n      const optionRoleProps = {};\n      if (props.role === 'option') {\n        optionRoleProps['aria-selected'] = selected.value;\n      }\n      const icon = (_d = props.icon) !== null && _d !== void 0 ? _d : (_e = slots.icon) === null || _e === void 0 ? void 0 : _e.call(slots, props);\n      return _createVNode(Tooltip, _objectSpread(_objectSpread({}, tooltipProps), {}, {\n        \"placement\": rtl.value ? 'left' : 'right',\n        \"overlayClassName\": `${prefixCls.value}-inline-collapsed-tooltip`\n      }), {\n        default: () => [_createVNode(Overflow.Item, _objectSpread(_objectSpread(_objectSpread({\n          \"component\": \"li\"\n        }, attrs), {}, {\n          \"id\": props.id,\n          \"style\": _extends(_extends({}, attrs.style || {}), directionStyle.value),\n          \"class\": [classNames.value, {\n            [`${attrs.class}`]: !!attrs.class,\n            [`${prefixCls.value}-item-only-child`]: (icon ? childrenLength + 1 : childrenLength) === 1\n          }],\n          \"role\": props.role || 'menuitem',\n          \"tabindex\": props.disabled ? null : -1,\n          \"data-menu-id\": key,\n          \"aria-disabled\": props.disabled\n        }, optionRoleProps), {}, {\n          \"onMouseenter\": onMouseEnter,\n          \"onMouseleave\": onMouseLeave,\n          \"onClick\": onInternalClick,\n          \"onKeydown\": onInternalKeyDown,\n          \"onFocus\": onInternalFocus,\n          \"title\": typeof title === 'string' ? title : undefined\n        }), {\n          default: () => [cloneElement(typeof icon === 'function' ? icon(props.originItemValue) : icon, {\n            class: `${prefixCls.value}-item-icon`\n          }, false), renderItemChildren(icon, children)]\n        })]\n      });\n    };\n  }\n});", "const autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nexport const placements = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -7]\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 7]\n  },\n  leftTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [-4, 0]\n  },\n  rightTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [4, 0]\n  }\n};\nexport const placementsRtl = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -7]\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 7]\n  },\n  rightTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [-4, 0]\n  },\n  leftTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [4, 0]\n  }\n};\nexport default placements;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { createVNode as _createVNode, resolveDirective as _resolveDirective } from \"vue\";\nimport Trigger from '../../vc-trigger';\nimport { computed, defineComponent, onBeforeUnmount, shallowRef, watch } from 'vue';\nimport { useInjectForceRender, useInjectMenu } from './hooks/useMenuContext';\nimport { placements, placementsRtl } from './placements';\nimport raf from '../../_util/raf';\nimport classNames from '../../_util/classNames';\nimport { getTransitionProps } from '../../_util/transition';\nconst popupPlacementMap = {\n  horizontal: 'bottomLeft',\n  vertical: 'rightTop',\n  'vertical-left': 'rightTop',\n  'vertical-right': 'leftTop'\n};\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'PopupTrigger',\n  inheritAttrs: false,\n  props: {\n    prefixCls: String,\n    mode: String,\n    visible: Boolean,\n    // popup: React.ReactNode;\n    popupClassName: String,\n    popupOffset: Array,\n    disabled: Boolean,\n    onVisibleChange: Function\n  },\n  slots: Object,\n  emits: ['visibleChange'],\n  setup(props, _ref) {\n    let {\n      slots,\n      emit\n    } = _ref;\n    const innerVisible = shallowRef(false);\n    const {\n      getPopupContainer,\n      rtl,\n      subMenuOpenDelay,\n      subMenuCloseDelay,\n      builtinPlacements,\n      triggerSubMenuAction,\n      forceSubMenuRender,\n      motion,\n      defaultMotions,\n      rootClassName\n    } = useInjectMenu();\n    const forceRender = useInjectForceRender();\n    const placement = computed(() => rtl.value ? _extends(_extends({}, placementsRtl), builtinPlacements.value) : _extends(_extends({}, placements), builtinPlacements.value));\n    const popupPlacement = computed(() => popupPlacementMap[props.mode]);\n    const visibleRef = shallowRef();\n    watch(() => props.visible, visible => {\n      raf.cancel(visibleRef.value);\n      visibleRef.value = raf(() => {\n        innerVisible.value = visible;\n      });\n    }, {\n      immediate: true\n    });\n    onBeforeUnmount(() => {\n      raf.cancel(visibleRef.value);\n    });\n    const onVisibleChange = visible => {\n      emit('visibleChange', visible);\n    };\n    const mergedMotion = computed(() => {\n      var _a, _b;\n      const m = motion.value || ((_a = defaultMotions.value) === null || _a === void 0 ? void 0 : _a[props.mode]) || ((_b = defaultMotions.value) === null || _b === void 0 ? void 0 : _b.other);\n      const res = typeof m === 'function' ? m() : m;\n      return res ? getTransitionProps(res.name, {\n        css: true\n      }) : undefined;\n    });\n    return () => {\n      const {\n        prefixCls,\n        popupClassName,\n        mode,\n        popupOffset,\n        disabled\n      } = props;\n      return _createVNode(Trigger, {\n        \"prefixCls\": prefixCls,\n        \"popupClassName\": classNames(`${prefixCls}-popup`, {\n          [`${prefixCls}-rtl`]: rtl.value\n        }, popupClassName, rootClassName.value),\n        \"stretch\": mode === 'horizontal' ? 'minWidth' : null,\n        \"getPopupContainer\": getPopupContainer.value,\n        \"builtinPlacements\": placement.value,\n        \"popupPlacement\": popupPlacement.value,\n        \"popupVisible\": innerVisible.value,\n        \"popupAlign\": popupOffset && {\n          offset: popupOffset\n        },\n        \"action\": disabled ? [] : [triggerSubMenuAction.value],\n        \"mouseEnterDelay\": subMenuOpenDelay.value,\n        \"mouseLeaveDelay\": subMenuCloseDelay.value,\n        \"onPopupVisibleChange\": onVisibleChange,\n        \"forceRender\": forceRender || forceSubMenuRender.value,\n        \"popupAnimation\": mergedMotion.value\n      }, {\n        popup: slots.popup,\n        default: slots.default\n      });\n    };\n  }\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { createVNode as _createVNode } from \"vue\";\nimport classNames from '../../_util/classNames';\nimport { useInjectMenu } from './hooks/useMenuContext';\nconst InternalSubMenuList = (_props, _ref) => {\n  let {\n    slots,\n    attrs\n  } = _ref;\n  var _a;\n  const {\n    prefixCls,\n    mode\n  } = useInjectMenu();\n  return _createVNode(\"ul\", _objectSpread(_objectSpread({}, attrs), {}, {\n    \"class\": classNames(prefixCls.value, `${prefixCls.value}-sub`, `${prefixCls.value}-${mode.value === 'inline' ? 'inline' : 'vertical'}`),\n    \"data-menu-list\": true\n  }), [(_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]);\n};\nInternalSubMenuList.displayName = 'SubMenuList';\nexport default InternalSubMenuList;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { withDirectives as _withDirectives, createVNode as _createVNode, vShow as _vShow } from \"vue\";\nimport { computed, Transition, defineComponent, ref, watch } from 'vue';\nimport { useInjectMenu, MenuContextProvider } from './hooks/useMenuContext';\nimport SubMenuList from './SubMenuList';\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'InlineSubMenuList',\n  inheritAttrs: false,\n  props: {\n    id: String,\n    open: Boolean,\n    keyPath: Array\n  },\n  setup(props, _ref) {\n    let {\n      slots\n    } = _ref;\n    const fixedMode = computed(() => 'inline');\n    const {\n      motion,\n      mode,\n      defaultMotions\n    } = useInjectMenu();\n    const sameModeRef = computed(() => mode.value === fixedMode.value);\n    const destroy = ref(!sameModeRef.value);\n    const mergedOpen = computed(() => sameModeRef.value ? props.open : false);\n    // ================================= Effect =================================\n    // Reset destroy state when mode change back\n    watch(mode, () => {\n      if (sameModeRef.value) {\n        destroy.value = false;\n      }\n    }, {\n      flush: 'post'\n    });\n    const mergedMotion = computed(() => {\n      var _a, _b;\n      const m = motion.value || ((_a = defaultMotions.value) === null || _a === void 0 ? void 0 : _a[fixedMode.value]) || ((_b = defaultMotions.value) === null || _b === void 0 ? void 0 : _b.other);\n      const res = typeof m === 'function' ? m() : m;\n      return _extends(_extends({}, res), {\n        appear: props.keyPath.length <= 1\n      });\n    });\n    return () => {\n      var _a;\n      if (destroy.value) {\n        return null;\n      }\n      return _createVNode(MenuContextProvider, {\n        \"mode\": fixedMode.value\n      }, {\n        default: () => [_createVNode(Transition, mergedMotion.value, {\n          default: () => [_withDirectives(_createVNode(SubMenuList, {\n            \"id\": props.id\n          }, {\n            default: () => [(_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]\n          }), [[_vShow, mergedOpen.value]])]\n        })]\n      });\n    };\n  }\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { resolveDirective as _resolveDirective, Fragment as _Fragment, createVNode as _createVNode } from \"vue\";\nimport PropTypes from '../../_util/vue-types';\nimport { computed, defineComponent, getCurrentInstance, shallowRef, watch, onBeforeUnmount } from 'vue';\nimport useProvideKeyPath, { useInjectKeyPath, useMeasure } from './hooks/useKeyPath';\nimport { useInjectMenu, useProvideFirstLevel, MenuContextProvider, useProvideForceRender, useInjectForceRender } from './hooks/useMenuContext';\nimport { getPropsSlot, isValidElement } from '../../_util/props-util';\nimport classNames from '../../_util/classNames';\nimport useDirectionStyle from './hooks/useDirectionStyle';\nimport PopupTrigger from './PopupTrigger';\nimport SubMenuList from './SubMenuList';\nimport InlineSubMenuList from './InlineSubMenuList';\nimport { cloneElement } from '../../_util/vnode';\nimport Overflow from '../../vc-overflow';\nimport devWarning from '../../vc-util/devWarning';\nimport isValid from '../../_util/isValid';\nimport { objectType } from '../../_util/type';\nlet indexGuid = 0;\nexport const subMenuProps = () => ({\n  icon: PropTypes.any,\n  title: PropTypes.any,\n  disabled: Boolean,\n  level: Number,\n  popupClassName: String,\n  popupOffset: Array,\n  internalPopupClose: Boolean,\n  eventKey: String,\n  expandIcon: Function,\n  theme: String,\n  onMouseenter: Function,\n  onMouseleave: Function,\n  onTitleClick: Function,\n  // Internal user prop\n  originItemValue: objectType()\n});\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'ASubMenu',\n  inheritAttrs: false,\n  props: subMenuProps(),\n  slots: Object,\n  setup(props, _ref) {\n    let {\n      slots,\n      attrs,\n      emit\n    } = _ref;\n    var _a, _b;\n    useProvideFirstLevel(false);\n    const isMeasure = useMeasure();\n    const instance = getCurrentInstance();\n    const vnodeKey = typeof instance.vnode.key === 'symbol' ? String(instance.vnode.key) : instance.vnode.key;\n    devWarning(typeof instance.vnode.key !== 'symbol', 'SubMenu', `SubMenu \\`:key=\"${String(vnodeKey)}\"\\` not support Symbol type`);\n    const key = isValid(vnodeKey) ? vnodeKey : `sub_menu_${++indexGuid}_$$_not_set_key`;\n    const eventKey = (_a = props.eventKey) !== null && _a !== void 0 ? _a : isValid(vnodeKey) ? `sub_menu_${++indexGuid}_$$_${vnodeKey}` : key;\n    const {\n      parentEventKeys,\n      parentInfo,\n      parentKeys\n    } = useInjectKeyPath();\n    const keysPath = computed(() => [...parentKeys.value, key]);\n    const childrenEventKeys = shallowRef([]);\n    const menuInfo = {\n      eventKey,\n      key,\n      parentEventKeys,\n      childrenEventKeys,\n      parentKeys\n    };\n    (_b = parentInfo.childrenEventKeys) === null || _b === void 0 ? void 0 : _b.value.push(eventKey);\n    onBeforeUnmount(() => {\n      var _a;\n      if (parentInfo.childrenEventKeys) {\n        parentInfo.childrenEventKeys.value = (_a = parentInfo.childrenEventKeys) === null || _a === void 0 ? void 0 : _a.value.filter(k => k != eventKey);\n      }\n    });\n    useProvideKeyPath(eventKey, key, menuInfo);\n    const {\n      prefixCls,\n      activeKeys,\n      disabled: contextDisabled,\n      changeActiveKeys,\n      mode,\n      inlineCollapsed,\n      openKeys,\n      overflowDisabled,\n      onOpenChange,\n      registerMenuInfo,\n      unRegisterMenuInfo,\n      selectedSubMenuKeys,\n      expandIcon: menuExpandIcon,\n      theme\n    } = useInjectMenu();\n    const hasKey = vnodeKey !== undefined && vnodeKey !== null;\n    // If not set key, use forceRender = true for children\n    // 如果没有 key，强制 render 子元素\n    const forceRender = !isMeasure && (useInjectForceRender() || !hasKey);\n    useProvideForceRender(forceRender);\n    if (isMeasure && hasKey || !isMeasure && !hasKey || forceRender) {\n      registerMenuInfo(eventKey, menuInfo);\n      onBeforeUnmount(() => {\n        unRegisterMenuInfo(eventKey);\n      });\n    }\n    const subMenuPrefixCls = computed(() => `${prefixCls.value}-submenu`);\n    const mergedDisabled = computed(() => contextDisabled.value || props.disabled);\n    const elementRef = shallowRef();\n    const popupRef = shallowRef();\n    // // ================================ Icon ================================\n    // const mergedItemIcon = itemIcon || contextItemIcon;\n    // const mergedExpandIcon = expandIcon || contextExpandIcon;\n    // ================================ Open ================================\n    const originOpen = computed(() => openKeys.value.includes(key));\n    const open = computed(() => !overflowDisabled.value && originOpen.value);\n    // =============================== Select ===============================\n    const childrenSelected = computed(() => {\n      return selectedSubMenuKeys.value.includes(key);\n    });\n    const isActive = shallowRef(false);\n    watch(activeKeys, () => {\n      isActive.value = !!activeKeys.value.find(val => val === key);\n    }, {\n      immediate: true\n    });\n    // =============================== Events ===============================\n    // >>>> Title click\n    const onInternalTitleClick = e => {\n      // Skip if disabled\n      if (mergedDisabled.value) {\n        return;\n      }\n      emit('titleClick', e, key);\n      // Trigger open by click when mode is `inline`\n      if (mode.value === 'inline') {\n        onOpenChange(key, !originOpen.value);\n      }\n    };\n    const onMouseEnter = event => {\n      if (!mergedDisabled.value) {\n        changeActiveKeys(keysPath.value);\n        emit('mouseenter', event);\n      }\n    };\n    const onMouseLeave = event => {\n      if (!mergedDisabled.value) {\n        changeActiveKeys([]);\n        emit('mouseleave', event);\n      }\n    };\n    // ========================== DirectionStyle ==========================\n    const directionStyle = useDirectionStyle(computed(() => keysPath.value.length));\n    // >>>>> Visible change\n    const onPopupVisibleChange = newVisible => {\n      if (mode.value !== 'inline') {\n        onOpenChange(key, newVisible);\n      }\n    };\n    /**\n     * Used for accessibility. Helper will focus element without key board.\n     * We should manually trigger an active\n     */\n    const onInternalFocus = () => {\n      changeActiveKeys(keysPath.value);\n    };\n    // =============================== Render ===============================\n    const popupId = eventKey && `${eventKey}-popup`;\n    const popupClassName = computed(() => classNames(prefixCls.value, `${prefixCls.value}-${props.theme || theme.value}`, props.popupClassName));\n    const renderTitle = (title, icon) => {\n      if (!icon) {\n        return inlineCollapsed.value && !parentKeys.value.length && title && typeof title === 'string' ? _createVNode(\"div\", {\n          \"class\": `${prefixCls.value}-inline-collapsed-noicon`\n        }, [title.charAt(0)]) : _createVNode(\"span\", {\n          \"class\": `${prefixCls.value}-title-content`\n        }, [title]);\n      }\n      // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n      // ref: https://github.com/ant-design/ant-design/pull/23456\n      const titleIsSpan = isValidElement(title) && title.type === 'span';\n      return _createVNode(_Fragment, null, [cloneElement(typeof icon === 'function' ? icon(props.originItemValue) : icon, {\n        class: `${prefixCls.value}-item-icon`\n      }, false), titleIsSpan ? title : _createVNode(\"span\", {\n        \"class\": `${prefixCls.value}-title-content`\n      }, [title])]);\n    };\n    // Cache mode if it change to `inline` which do not have popup motion\n    const triggerModeRef = computed(() => {\n      return mode.value !== 'inline' && keysPath.value.length > 1 ? 'vertical' : mode.value;\n    });\n    const renderMode = computed(() => mode.value === 'horizontal' ? 'vertical' : mode.value);\n    const subMenuTriggerModeRef = computed(() => triggerModeRef.value === 'horizontal' ? 'vertical' : triggerModeRef.value);\n    const baseTitleNode = () => {\n      var _a, _b;\n      const subMenuPrefixClsValue = subMenuPrefixCls.value;\n      const icon = (_a = props.icon) !== null && _a !== void 0 ? _a : (_b = slots.icon) === null || _b === void 0 ? void 0 : _b.call(slots, props);\n      const expandIcon = props.expandIcon || slots.expandIcon || menuExpandIcon.value;\n      const title = renderTitle(getPropsSlot(slots, props, 'title'), icon);\n      return _createVNode(\"div\", {\n        \"style\": directionStyle.value,\n        \"class\": `${subMenuPrefixClsValue}-title`,\n        \"tabindex\": mergedDisabled.value ? null : -1,\n        \"ref\": elementRef,\n        \"title\": typeof title === 'string' ? title : null,\n        \"data-menu-id\": key,\n        \"aria-expanded\": open.value,\n        \"aria-haspopup\": true,\n        \"aria-controls\": popupId,\n        \"aria-disabled\": mergedDisabled.value,\n        \"onClick\": onInternalTitleClick,\n        \"onFocus\": onInternalFocus\n      }, [title, mode.value !== 'horizontal' && expandIcon ? expandIcon(_extends(_extends({}, props), {\n        isOpen: open.value\n      })) : _createVNode(\"i\", {\n        \"class\": `${subMenuPrefixClsValue}-arrow`\n      }, null)]);\n    };\n    return () => {\n      var _a;\n      if (isMeasure) {\n        if (!hasKey) {\n          return null;\n        }\n        return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n      }\n      const subMenuPrefixClsValue = subMenuPrefixCls.value;\n      let titleNode = () => null;\n      if (!overflowDisabled.value && mode.value !== 'inline') {\n        const popupOffset = mode.value === 'horizontal' ? [0, 8] : [10, 0];\n        titleNode = () => _createVNode(PopupTrigger, {\n          \"mode\": triggerModeRef.value,\n          \"prefixCls\": subMenuPrefixClsValue,\n          \"visible\": !props.internalPopupClose && open.value,\n          \"popupClassName\": popupClassName.value,\n          \"popupOffset\": props.popupOffset || popupOffset,\n          \"disabled\": mergedDisabled.value,\n          \"onVisibleChange\": onPopupVisibleChange\n        }, {\n          default: () => [baseTitleNode()],\n          popup: () => _createVNode(MenuContextProvider, {\n            \"mode\": subMenuTriggerModeRef.value\n          }, {\n            default: () => [_createVNode(SubMenuList, {\n              \"id\": popupId,\n              \"ref\": popupRef\n            }, {\n              default: slots.default\n            })]\n          })\n        });\n      } else {\n        // 包裹一层，保持结构一致，防止动画丢失\n        // https://github.com/vueComponent/ant-design-vue/issues/4325\n        titleNode = () => _createVNode(PopupTrigger, null, {\n          default: baseTitleNode\n        });\n      }\n      return _createVNode(MenuContextProvider, {\n        \"mode\": renderMode.value\n      }, {\n        default: () => [_createVNode(Overflow.Item, _objectSpread(_objectSpread({\n          \"component\": \"li\"\n        }, attrs), {}, {\n          \"role\": \"none\",\n          \"class\": classNames(subMenuPrefixClsValue, `${subMenuPrefixClsValue}-${mode.value}`, attrs.class, {\n            [`${subMenuPrefixClsValue}-open`]: open.value,\n            [`${subMenuPrefixClsValue}-active`]: isActive.value,\n            [`${subMenuPrefixClsValue}-selected`]: childrenSelected.value,\n            [`${subMenuPrefixClsValue}-disabled`]: mergedDisabled.value\n          }),\n          \"onMouseenter\": onMouseEnter,\n          \"onMouseleave\": onMouseLeave,\n          \"data-submenu-id\": key\n        }), {\n          default: () => {\n            return _createVNode(_Fragment, null, [titleNode(), !overflowDisabled.value && _createVNode(InlineSubMenuList, {\n              \"id\": popupId,\n              \"open\": open.value,\n              \"keyPath\": keysPath.value\n            }, {\n              default: slots.default\n            })]);\n          }\n        })]\n      });\n    };\n  }\n});", "export function hasClass(node, className) {\n  if (node.classList) {\n    return node.classList.contains(className);\n  }\n  const originClass = node.className;\n  return ` ${originClass} `.indexOf(` ${className} `) > -1;\n}\nexport function addClass(node, className) {\n  if (node.classList) {\n    node.classList.add(className);\n  } else {\n    if (!hasClass(node, className)) {\n      node.className = `${node.className} ${className}`;\n    }\n  }\n}\nexport function removeClass(node, className) {\n  if (node.classList) {\n    node.classList.remove(className);\n  } else {\n    if (hasClass(node, className)) {\n      const originClass = node.className;\n      node.className = ` ${originClass} `.replace(` ${className} `, ' ');\n    }\n  }\n}", "import { nextTick } from 'vue';\nimport { addClass, removeClass } from '../vc-util/Dom/class';\nconst collapseMotion = function () {\n  let name = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'ant-motion-collapse';\n  let appear = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  return {\n    name,\n    appear,\n    css: true,\n    onBeforeEnter: node => {\n      node.style.height = '0px';\n      node.style.opacity = '0';\n      addClass(node, name);\n    },\n    onEnter: node => {\n      nextTick(() => {\n        node.style.height = `${node.scrollHeight}px`;\n        node.style.opacity = '1';\n      });\n    },\n    onAfterEnter: node => {\n      if (node) {\n        removeClass(node, name);\n        node.style.height = null;\n        node.style.opacity = null;\n      }\n    },\n    onBeforeLeave: node => {\n      addClass(node, name);\n      node.style.height = `${node.offsetHeight}px`;\n      node.style.opacity = null;\n    },\n    onLeave: node => {\n      setTimeout(() => {\n        node.style.height = '0px';\n        node.style.opacity = '0';\n      });\n    },\n    onAfterLeave: node => {\n      if (node) {\n        removeClass(node, name);\n        if (node.style) {\n          node.style.height = null;\n          node.style.opacity = null;\n        }\n      }\n    }\n  };\n};\nexport default collapseMotion;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { createVNode as _createVNode } from \"vue\";\nimport { getPropsSlot } from '../../_util/props-util';\nimport { computed, defineComponent } from 'vue';\nimport PropTypes from '../../_util/vue-types';\nimport { useInjectMenu } from './hooks/useMenuContext';\nimport { useMeasure } from './hooks/useKeyPath';\nimport { objectType } from '../../_util/type';\nexport const menuItemGroupProps = () => ({\n  title: PropTypes.any,\n  // Internal user prop\n  originItemValue: objectType()\n});\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'AMenuItemGroup',\n  inheritAttrs: false,\n  props: menuItemGroupProps(),\n  slots: Object,\n  setup(props, _ref) {\n    let {\n      slots,\n      attrs\n    } = _ref;\n    const {\n      prefixCls\n    } = useInjectMenu();\n    const groupPrefixCls = computed(() => `${prefixCls.value}-item-group`);\n    const isMeasure = useMeasure();\n    return () => {\n      var _a, _b;\n      if (isMeasure) return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n      return _createVNode(\"li\", _objectSpread(_objectSpread({}, attrs), {}, {\n        \"onClick\": e => e.stopPropagation(),\n        \"class\": groupPrefixCls.value\n      }), [_createVNode(\"div\", {\n        \"title\": typeof props.title === 'string' ? props.title : undefined,\n        \"class\": `${groupPrefixCls.value}-title`\n      }, [getPropsSlot(slots, props, 'title')]), _createVNode(\"ul\", {\n        \"class\": `${groupPrefixCls.value}-list`\n      }, [(_b = slots.default) === null || _b === void 0 ? void 0 : _b.call(slots)])]);\n    };\n  }\n});", "import { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from 'vue';\nimport { useInjectMenu } from './hooks/useMenuContext';\nexport const menuDividerProps = () => ({\n  prefixCls: String,\n  dashed: Boolean\n});\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'AMenuDivider',\n  props: menuDividerProps(),\n  setup(props) {\n    const {\n      prefixCls\n    } = useInjectMenu();\n    const cls = computed(() => {\n      return {\n        [`${prefixCls.value}-item-divider`]: true,\n        [`${prefixCls.value}-item-divider-dashed`]: !!props.dashed\n      };\n    });\n    return () => {\n      return _createVNode(\"li\", {\n        \"class\": cls.value\n      }, null);\n    };\n  }\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { createVNode as _createVNode } from \"vue\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport SubMenu from '../SubMenu';\nimport ItemGroup from '../ItemGroup';\nimport MenuDivider from '../Divider';\nimport MenuItem from '../MenuItem';\nimport { ref, shallowRef, watch } from 'vue';\nfunction convertItemsToNodes(list, store, parentMenuInfo) {\n  return (list || []).map((opt, index) => {\n    if (opt && typeof opt === 'object') {\n      const _a = opt,\n        {\n          label,\n          children,\n          key,\n          type\n        } = _a,\n        restProps = __rest(_a, [\"label\", \"children\", \"key\", \"type\"]);\n      const mergedKey = key !== null && key !== void 0 ? key : `tmp-${index}`;\n      // 此处 eventKey === key, 移除 children 后可以移除 eventKey\n      const parentKeys = parentMenuInfo ? parentMenuInfo.parentKeys.slice() : [];\n      const childrenEventKeys = [];\n      // if\n      const menuInfo = {\n        eventKey: mergedKey,\n        key: mergedKey,\n        parentEventKeys: ref(parentKeys),\n        parentKeys: ref(parentKeys),\n        childrenEventKeys: ref(childrenEventKeys),\n        isLeaf: false\n      };\n      // MenuItemGroup & SubMenuItem\n      if (children || type === 'group') {\n        if (type === 'group') {\n          const childrenNodes = convertItemsToNodes(children, store, parentMenuInfo);\n          // Group\n          return _createVNode(ItemGroup, _objectSpread(_objectSpread({\n            \"key\": mergedKey\n          }, restProps), {}, {\n            \"title\": label,\n            \"originItemValue\": opt\n          }), {\n            default: () => [childrenNodes]\n          });\n        }\n        store.set(mergedKey, menuInfo);\n        if (parentMenuInfo) {\n          parentMenuInfo.childrenEventKeys.push(mergedKey);\n        }\n        // Sub Menu\n        const childrenNodes = convertItemsToNodes(children, store, {\n          childrenEventKeys,\n          parentKeys: [].concat(parentKeys, mergedKey)\n        });\n        return _createVNode(SubMenu, _objectSpread(_objectSpread({\n          \"key\": mergedKey\n        }, restProps), {}, {\n          \"title\": label,\n          \"originItemValue\": opt\n        }), {\n          default: () => [childrenNodes]\n        });\n      }\n      // MenuItem & Divider\n      if (type === 'divider') {\n        return _createVNode(MenuDivider, _objectSpread({\n          \"key\": mergedKey\n        }, restProps), null);\n      }\n      menuInfo.isLeaf = true;\n      store.set(mergedKey, menuInfo);\n      return _createVNode(MenuItem, _objectSpread(_objectSpread({\n        \"key\": mergedKey\n      }, restProps), {}, {\n        \"originItemValue\": opt\n      }), {\n        default: () => [label]\n      });\n    }\n    return null;\n  }).filter(opt => opt);\n}\n// FIXME: Move logic here in v4\n/**\n * We simply convert `items` to VueNode for reuse origin component logic. But we need move all the\n * logic from component into this hooks when in v4\n */\nexport default function useItems(props) {\n  const itemsNodes = shallowRef([]);\n  const hasItmes = shallowRef(false);\n  const store = shallowRef(new Map());\n  watch(() => props.items, () => {\n    const newStore = new Map();\n    hasItmes.value = false;\n    if (props.items) {\n      hasItmes.value = true;\n      itemsNodes.value = convertItemsToNodes(props.items, newStore);\n    } else {\n      itemsNodes.value = undefined;\n    }\n    store.value = newStore;\n  }, {\n    immediate: true,\n    deep: true\n  });\n  return {\n    itemsNodes,\n    store,\n    hasItmes\n  };\n}", "const getHorizontalStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    menuHorizontalHeight,\n    colorSplit,\n    lineWidth,\n    lineType,\n    menuItemPaddingInline\n  } = token;\n  return {\n    [`${componentCls}-horizontal`]: {\n      lineHeight: `${menuHorizontalHeight}px`,\n      border: 0,\n      borderBottom: `${lineWidth}px ${lineType} ${colorSplit}`,\n      boxShadow: 'none',\n      '&::after': {\n        display: 'block',\n        clear: 'both',\n        height: 0,\n        content: '\"\\\\20\"'\n      },\n      // ======================= Item =======================\n      [`${componentCls}-item, ${componentCls}-submenu`]: {\n        position: 'relative',\n        display: 'inline-block',\n        verticalAlign: 'bottom',\n        paddingInline: menuItemPaddingInline\n      },\n      [`> ${componentCls}-item:hover,\n        > ${componentCls}-item-active,\n        > ${componentCls}-submenu ${componentCls}-submenu-title:hover`]: {\n        backgroundColor: 'transparent'\n      },\n      [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n        transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`].join(',')\n      },\n      // ===================== Sub Menu =====================\n      [`${componentCls}-submenu-arrow`]: {\n        display: 'none'\n      }\n    }\n  };\n};\nexport default getHorizontalStyle;", "const getRTLStyle = _ref => {\n  let {\n    componentCls,\n    menuArrowOffset\n  } = _ref;\n  return {\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    },\n    [`${componentCls}-submenu-rtl`]: {\n      transformOrigin: '100% 0'\n    },\n    // Vertical Arrow\n    [`${componentCls}-rtl${componentCls}-vertical,\n    ${componentCls}-submenu-rtl ${componentCls}-vertical`]: {\n      [`${componentCls}-submenu-arrow`]: {\n        '&::before': {\n          transform: `rotate(-45deg) translateY(-${menuArrowOffset})`\n        },\n        '&::after': {\n          transform: `rotate(45deg) translateY(${menuArrowOffset})`\n        }\n      }\n    }\n  };\n};\nexport default getRTLStyle;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { genFocusOutline } from '../../style';\nconst accessibilityFocus = token => _extends({}, genFocusOutline(token));\nconst getThemeStyle = (token, themeSuffix) => {\n  const {\n    componentCls,\n    colorItemText,\n    colorItemTextSelected,\n    colorGroupTitle,\n    colorItemBg,\n    colorSubItemBg,\n    colorItemBgSelected,\n    colorActiveBarHeight,\n    colorActiveBarWidth,\n    colorActiveBarBorderSize,\n    motionDurationSlow,\n    motionEaseInOut,\n    motionEaseOut,\n    menuItemPaddingInline,\n    motionDurationMid,\n    colorItemTextHover,\n    lineType,\n    colorSplit,\n    // Disabled\n    colorItemTextDisabled,\n    // Danger\n    colorDangerItemText,\n    colorDangerItemTextHover,\n    colorDangerItemTextSelected,\n    colorDangerItemBgActive,\n    colorDangerItemBgSelected,\n    colorItemBgHover,\n    menuSubMenuBg,\n    // Horizontal\n    colorItemTextSelectedHorizontal,\n    colorItemBgSelectedHorizontal\n  } = token;\n  return {\n    [`${componentCls}-${themeSuffix}`]: {\n      color: colorItemText,\n      background: colorItemBg,\n      [`&${componentCls}-root:focus-visible`]: _extends({}, accessibilityFocus(token)),\n      // ======================== Item ========================\n      [`${componentCls}-item-group-title`]: {\n        color: colorGroupTitle\n      },\n      [`${componentCls}-submenu-selected`]: {\n        [`> ${componentCls}-submenu-title`]: {\n          color: colorItemTextSelected\n        }\n      },\n      // Disabled\n      [`${componentCls}-item-disabled, ${componentCls}-submenu-disabled`]: {\n        color: `${colorItemTextDisabled} !important`\n      },\n      // Hover\n      [`${componentCls}-item:hover, ${componentCls}-submenu-title:hover`]: {\n        [`&:not(${componentCls}-item-selected):not(${componentCls}-submenu-selected)`]: {\n          color: colorItemTextHover\n        }\n      },\n      [`&:not(${componentCls}-horizontal)`]: {\n        [`${componentCls}-item:not(${componentCls}-item-selected)`]: {\n          '&:hover': {\n            backgroundColor: colorItemBgHover\n          },\n          '&:active': {\n            backgroundColor: colorItemBgSelected\n          }\n        },\n        [`${componentCls}-submenu-title`]: {\n          '&:hover': {\n            backgroundColor: colorItemBgHover\n          },\n          '&:active': {\n            backgroundColor: colorItemBgSelected\n          }\n        }\n      },\n      // Danger - only Item has\n      [`${componentCls}-item-danger`]: {\n        color: colorDangerItemText,\n        [`&${componentCls}-item:hover`]: {\n          [`&:not(${componentCls}-item-selected):not(${componentCls}-submenu-selected)`]: {\n            color: colorDangerItemTextHover\n          }\n        },\n        [`&${componentCls}-item:active`]: {\n          background: colorDangerItemBgActive\n        }\n      },\n      [`${componentCls}-item a`]: {\n        '&, &:hover': {\n          color: 'inherit'\n        }\n      },\n      [`${componentCls}-item-selected`]: {\n        color: colorItemTextSelected,\n        // Danger\n        [`&${componentCls}-item-danger`]: {\n          color: colorDangerItemTextSelected\n        },\n        [`a, a:hover`]: {\n          color: 'inherit'\n        }\n      },\n      [`& ${componentCls}-item-selected`]: {\n        backgroundColor: colorItemBgSelected,\n        // Danger\n        [`&${componentCls}-item-danger`]: {\n          backgroundColor: colorDangerItemBgSelected\n        }\n      },\n      [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n        [`&:not(${componentCls}-item-disabled):focus-visible`]: _extends({}, accessibilityFocus(token))\n      },\n      [`&${componentCls}-submenu > ${componentCls}`]: {\n        backgroundColor: menuSubMenuBg\n      },\n      [`&${componentCls}-popup > ${componentCls}`]: {\n        backgroundColor: colorItemBg\n      },\n      // ====================== Horizontal ======================\n      [`&${componentCls}-horizontal`]: _extends(_extends({}, themeSuffix === 'dark' ? {\n        borderBottom: 0\n      } : {}), {\n        [`> ${componentCls}-item, > ${componentCls}-submenu`]: {\n          top: colorActiveBarBorderSize,\n          marginTop: -colorActiveBarBorderSize,\n          marginBottom: 0,\n          borderRadius: 0,\n          '&::after': {\n            position: 'absolute',\n            insetInline: menuItemPaddingInline,\n            bottom: 0,\n            borderBottom: `${colorActiveBarHeight}px solid transparent`,\n            transition: `border-color ${motionDurationSlow} ${motionEaseInOut}`,\n            content: '\"\"'\n          },\n          [`&:hover, &-active, &-open`]: {\n            '&::after': {\n              borderBottomWidth: colorActiveBarHeight,\n              borderBottomColor: colorItemTextSelectedHorizontal\n            }\n          },\n          [`&-selected`]: {\n            color: colorItemTextSelectedHorizontal,\n            backgroundColor: colorItemBgSelectedHorizontal,\n            '&::after': {\n              borderBottomWidth: colorActiveBarHeight,\n              borderBottomColor: colorItemTextSelectedHorizontal\n            }\n          }\n        }\n      }),\n      // ================== Inline & Vertical ===================\n      //\n      [`&${componentCls}-root`]: {\n        [`&${componentCls}-inline, &${componentCls}-vertical`]: {\n          borderInlineEnd: `${colorActiveBarBorderSize}px ${lineType} ${colorSplit}`\n        }\n      },\n      // ======================== Inline ========================\n      [`&${componentCls}-inline`]: {\n        // Sub\n        [`${componentCls}-sub${componentCls}-inline`]: {\n          background: colorSubItemBg\n        },\n        // Item\n        [`${componentCls}-item, ${componentCls}-submenu-title`]: colorActiveBarBorderSize && colorActiveBarWidth ? {\n          width: `calc(100% + ${colorActiveBarBorderSize}px)`\n        } : {},\n        [`${componentCls}-item`]: {\n          position: 'relative',\n          '&::after': {\n            position: 'absolute',\n            insetBlock: 0,\n            insetInlineEnd: 0,\n            borderInlineEnd: `${colorActiveBarWidth}px solid ${colorItemTextSelected}`,\n            transform: 'scaleY(0.0001)',\n            opacity: 0,\n            transition: [`transform ${motionDurationMid} ${motionEaseOut}`, `opacity ${motionDurationMid} ${motionEaseOut}`].join(','),\n            content: '\"\"'\n          },\n          // Danger\n          [`&${componentCls}-item-danger`]: {\n            '&::after': {\n              borderInlineEndColor: colorDangerItemTextSelected\n            }\n          }\n        },\n        [`${componentCls}-selected, ${componentCls}-item-selected`]: {\n          '&::after': {\n            transform: 'scaleY(1)',\n            opacity: 1,\n            transition: [`transform ${motionDurationMid} ${motionEaseInOut}`, `opacity ${motionDurationMid} ${motionEaseInOut}`].join(',')\n          }\n        }\n      }\n    }\n  };\n};\nexport default getThemeStyle;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { textEllipsis } from '../../style';\nconst getVerticalInlineStyle = token => {\n  const {\n    componentCls,\n    menuItemHeight,\n    itemMarginInline,\n    padding,\n    menuArrowSize,\n    marginXS,\n    marginXXS\n  } = token;\n  const paddingWithArrow = padding + menuArrowSize + marginXS;\n  return {\n    [`${componentCls}-item`]: {\n      position: 'relative'\n    },\n    [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n      height: menuItemHeight,\n      lineHeight: `${menuItemHeight}px`,\n      paddingInline: padding,\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      marginInline: itemMarginInline,\n      marginBlock: marginXXS,\n      width: `calc(100% - ${itemMarginInline * 2}px)`\n    },\n    // disable margin collapsed\n    [`${componentCls}-submenu`]: {\n      paddingBottom: 0.02\n    },\n    [`> ${componentCls}-item,\n            > ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n      height: menuItemHeight,\n      lineHeight: `${menuItemHeight}px`\n    },\n    [`${componentCls}-item-group-list ${componentCls}-submenu-title,\n            ${componentCls}-submenu-title`]: {\n      paddingInlineEnd: paddingWithArrow\n    }\n  };\n};\nconst getVerticalStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    menuItemHeight,\n    colorTextLightSolid,\n    dropdownWidth,\n    controlHeightLG,\n    motionDurationMid,\n    motionEaseOut,\n    paddingXL,\n    fontSizeSM,\n    fontSizeLG,\n    motionDurationSlow,\n    paddingXS,\n    boxShadowSecondary\n  } = token;\n  const inlineItemStyle = {\n    height: menuItemHeight,\n    lineHeight: `${menuItemHeight}px`,\n    listStylePosition: 'inside',\n    listStyleType: 'disc'\n  };\n  return [{\n    [componentCls]: {\n      [`&-inline, &-vertical`]: _extends({\n        [`&${componentCls}-root`]: {\n          boxShadow: 'none'\n        }\n      }, getVerticalInlineStyle(token))\n    },\n    [`${componentCls}-submenu-popup`]: {\n      [`${componentCls}-vertical`]: _extends(_extends({}, getVerticalInlineStyle(token)), {\n        boxShadow: boxShadowSecondary\n      })\n    }\n  },\n  // Vertical only\n  {\n    [`${componentCls}-submenu-popup ${componentCls}-vertical${componentCls}-sub`]: {\n      minWidth: dropdownWidth,\n      maxHeight: `calc(100vh - ${controlHeightLG * 2.5}px)`,\n      padding: '0',\n      overflow: 'hidden',\n      borderInlineEnd: 0,\n      // https://github.com/ant-design/ant-design/issues/22244\n      // https://github.com/ant-design/ant-design/issues/26812\n      \"&:not([class*='-active'])\": {\n        overflowX: 'hidden',\n        overflowY: 'auto'\n      }\n    }\n  },\n  // Inline Only\n  {\n    [`${componentCls}-inline`]: {\n      width: '100%',\n      // Motion enhance for first level\n      [`&${componentCls}-root`]: {\n        [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n          display: 'flex',\n          alignItems: 'center',\n          transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`, `padding ${motionDurationMid} ${motionEaseOut}`].join(','),\n          [`> ${componentCls}-title-content`]: {\n            flex: 'auto',\n            minWidth: 0,\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          },\n          '> *': {\n            flex: 'none'\n          }\n        }\n      },\n      // >>>>> Sub\n      [`${componentCls}-sub${componentCls}-inline`]: {\n        padding: 0,\n        border: 0,\n        borderRadius: 0,\n        boxShadow: 'none',\n        [`& > ${componentCls}-submenu > ${componentCls}-submenu-title`]: inlineItemStyle,\n        [`& ${componentCls}-item-group-title`]: {\n          paddingInlineStart: paddingXL\n        }\n      },\n      // >>>>> Item\n      [`${componentCls}-item`]: inlineItemStyle\n    }\n  },\n  // Inline Collapse Only\n  {\n    [`${componentCls}-inline-collapsed`]: {\n      width: menuItemHeight * 2,\n      [`&${componentCls}-root`]: {\n        [`${componentCls}-item, ${componentCls}-submenu ${componentCls}-submenu-title`]: {\n          [`> ${componentCls}-inline-collapsed-noicon`]: {\n            fontSize: fontSizeLG,\n            textAlign: 'center'\n          }\n        }\n      },\n      [`> ${componentCls}-item,\n          > ${componentCls}-item-group > ${componentCls}-item-group-list > ${componentCls}-item,\n          > ${componentCls}-item-group > ${componentCls}-item-group-list > ${componentCls}-submenu > ${componentCls}-submenu-title,\n          > ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n        insetInlineStart: 0,\n        paddingInline: `calc(50% - ${fontSizeSM}px)`,\n        textOverflow: 'clip',\n        [`\n            ${componentCls}-submenu-arrow,\n            ${componentCls}-submenu-expand-icon\n          `]: {\n          opacity: 0\n        },\n        [`${componentCls}-item-icon, ${iconCls}`]: {\n          margin: 0,\n          fontSize: fontSizeLG,\n          lineHeight: `${menuItemHeight}px`,\n          '+ span': {\n            display: 'inline-block',\n            opacity: 0\n          }\n        }\n      },\n      [`${componentCls}-item-icon, ${iconCls}`]: {\n        display: 'inline-block'\n      },\n      '&-tooltip': {\n        pointerEvents: 'none',\n        [`${componentCls}-item-icon, ${iconCls}`]: {\n          display: 'none'\n        },\n        'a, a:hover': {\n          color: colorTextLightSolid\n        }\n      },\n      [`${componentCls}-item-group-title`]: _extends(_extends({}, textEllipsis), {\n        paddingInline: paddingXS\n      })\n    }\n  }];\n};\nexport default getVerticalStyle;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { TinyColor } from '@ctrl/tinycolor';\nimport { genCollapseMotion, initSlideMotion, initZoomMotion } from '../../style/motion';\nimport { genComponentStyleHook, mergeToken } from '../../theme/internal';\nimport getHorizontalStyle from './horizontal';\nimport getRTLStyle from './rtl';\nimport getThemeStyle from './theme';\nimport getVerticalStyle from './vertical';\nimport { clearFix, resetComponent, resetIcon } from '../../style';\nimport 'vue';\nconst genMenuItemStyle = token => {\n  const {\n    componentCls,\n    fontSize,\n    motionDurationSlow,\n    motionDurationMid,\n    motionEaseInOut,\n    motionEaseOut,\n    iconCls,\n    controlHeightSM\n  } = token;\n  return {\n    // >>>>> Item\n    [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n      position: 'relative',\n      display: 'block',\n      margin: 0,\n      whiteSpace: 'nowrap',\n      cursor: 'pointer',\n      transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`, `padding ${motionDurationSlow} ${motionEaseInOut}`].join(','),\n      [`${componentCls}-item-icon, ${iconCls}`]: {\n        minWidth: fontSize,\n        fontSize,\n        transition: [`font-size ${motionDurationMid} ${motionEaseOut}`, `margin ${motionDurationSlow} ${motionEaseInOut}`, `color ${motionDurationSlow}`].join(','),\n        '+ span': {\n          marginInlineStart: controlHeightSM - fontSize,\n          opacity: 1,\n          transition: [`opacity ${motionDurationSlow} ${motionEaseInOut}`, `margin ${motionDurationSlow}`, `color ${motionDurationSlow}`].join(',')\n        }\n      },\n      [`${componentCls}-item-icon`]: _extends({}, resetIcon()),\n      [`&${componentCls}-item-only-child`]: {\n        [`> ${iconCls}, > ${componentCls}-item-icon`]: {\n          marginInlineEnd: 0\n        }\n      }\n    },\n    // Disabled state sets text to gray and nukes hover/tab effects\n    [`${componentCls}-item-disabled, ${componentCls}-submenu-disabled`]: {\n      background: 'none !important',\n      cursor: 'not-allowed',\n      '&::after': {\n        borderColor: 'transparent !important'\n      },\n      a: {\n        color: 'inherit !important'\n      },\n      [`> ${componentCls}-submenu-title`]: {\n        color: 'inherit !important',\n        cursor: 'not-allowed'\n      }\n    }\n  };\n};\nconst genSubMenuArrowStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    motionEaseInOut,\n    borderRadius,\n    menuArrowSize,\n    menuArrowOffset\n  } = token;\n  return {\n    [`${componentCls}-submenu`]: {\n      [`&-expand-icon, &-arrow`]: {\n        position: 'absolute',\n        top: '50%',\n        insetInlineEnd: token.margin,\n        width: menuArrowSize,\n        color: 'currentcolor',\n        transform: 'translateY(-50%)',\n        transition: `transform ${motionDurationSlow} ${motionEaseInOut}, opacity ${motionDurationSlow}`\n      },\n      '&-arrow': {\n        // →\n        '&::before, &::after': {\n          position: 'absolute',\n          width: menuArrowSize * 0.6,\n          height: menuArrowSize * 0.15,\n          backgroundColor: 'currentcolor',\n          borderRadius,\n          transition: [`background ${motionDurationSlow} ${motionEaseInOut}`, `transform ${motionDurationSlow} ${motionEaseInOut}`, `top ${motionDurationSlow} ${motionEaseInOut}`, `color ${motionDurationSlow} ${motionEaseInOut}`].join(','),\n          content: '\"\"'\n        },\n        '&::before': {\n          transform: `rotate(45deg) translateY(-${menuArrowOffset})`\n        },\n        '&::after': {\n          transform: `rotate(-45deg) translateY(${menuArrowOffset})`\n        }\n      }\n    }\n  };\n};\n// =============================== Base ===============================\nconst getBaseStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    fontSize,\n    motionDurationSlow,\n    motionDurationMid,\n    motionEaseInOut,\n    lineHeight,\n    paddingXS,\n    padding,\n    colorSplit,\n    lineWidth,\n    zIndexPopup,\n    borderRadiusLG,\n    radiusSubMenuItem,\n    menuArrowSize,\n    menuArrowOffset,\n    lineType,\n    menuPanelMaskInset\n  } = token;\n  return [\n  // Misc\n  {\n    '': {\n      [`${componentCls}`]: _extends(_extends({}, clearFix()), {\n        // Hidden\n        [`&-hidden`]: {\n          display: 'none'\n        }\n      })\n    },\n    [`${componentCls}-submenu-hidden`]: {\n      display: 'none'\n    }\n  }, {\n    [componentCls]: _extends(_extends(_extends(_extends(_extends(_extends(_extends({}, resetComponent(token)), clearFix()), {\n      marginBottom: 0,\n      paddingInlineStart: 0,\n      // Override default ul/ol\n      fontSize,\n      lineHeight: 0,\n      listStyle: 'none',\n      outline: 'none',\n      transition: `width ${motionDurationSlow} cubic-bezier(0.2, 0, 0, 1) 0s`,\n      [`ul, ol`]: {\n        margin: 0,\n        padding: 0,\n        listStyle: 'none'\n      },\n      // Overflow ellipsis\n      [`&-overflow`]: {\n        display: 'flex',\n        [`${componentCls}-item`]: {\n          flex: 'none'\n        }\n      },\n      [`${componentCls}-item, ${componentCls}-submenu, ${componentCls}-submenu-title`]: {\n        borderRadius: token.radiusItem\n      },\n      [`${componentCls}-item-group-title`]: {\n        padding: `${paddingXS}px ${padding}px`,\n        fontSize,\n        lineHeight,\n        transition: `all ${motionDurationSlow}`\n      },\n      [`&-horizontal ${componentCls}-submenu`]: {\n        transition: [`border-color ${motionDurationSlow} ${motionEaseInOut}`, `background ${motionDurationSlow} ${motionEaseInOut}`].join(',')\n      },\n      [`${componentCls}-submenu, ${componentCls}-submenu-inline`]: {\n        transition: [`border-color ${motionDurationSlow} ${motionEaseInOut}`, `background ${motionDurationSlow} ${motionEaseInOut}`, `padding ${motionDurationMid} ${motionEaseInOut}`].join(',')\n      },\n      [`${componentCls}-submenu ${componentCls}-sub`]: {\n        cursor: 'initial',\n        transition: [`background ${motionDurationSlow} ${motionEaseInOut}`, `padding ${motionDurationSlow} ${motionEaseInOut}`].join(',')\n      },\n      [`${componentCls}-title-content`]: {\n        transition: `color ${motionDurationSlow}`\n      },\n      [`${componentCls}-item a`]: {\n        '&::before': {\n          position: 'absolute',\n          inset: 0,\n          backgroundColor: 'transparent',\n          content: '\"\"'\n        }\n      },\n      // Removed a Badge related style seems it's safe\n      // https://github.com/ant-design/ant-design/issues/19809\n      // >>>>> Divider\n      [`${componentCls}-item-divider`]: {\n        overflow: 'hidden',\n        lineHeight: 0,\n        borderColor: colorSplit,\n        borderStyle: lineType,\n        borderWidth: 0,\n        borderTopWidth: lineWidth,\n        marginBlock: lineWidth,\n        padding: 0,\n        '&-dashed': {\n          borderStyle: 'dashed'\n        }\n      }\n    }), genMenuItemStyle(token)), {\n      [`${componentCls}-item-group`]: {\n        [`${componentCls}-item-group-list`]: {\n          margin: 0,\n          padding: 0,\n          [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n            paddingInline: `${fontSize * 2}px ${padding}px`\n          }\n        }\n      },\n      // ======================= Sub Menu =======================\n      '&-submenu': {\n        '&-popup': {\n          position: 'absolute',\n          zIndex: zIndexPopup,\n          background: 'transparent',\n          borderRadius: borderRadiusLG,\n          boxShadow: 'none',\n          transformOrigin: '0 0',\n          // https://github.com/ant-design/ant-design/issues/13955\n          '&::before': {\n            position: 'absolute',\n            inset: `${menuPanelMaskInset}px 0 0`,\n            zIndex: -1,\n            width: '100%',\n            height: '100%',\n            opacity: 0,\n            content: '\"\"'\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/13955\n        '&-placement-rightTop::before': {\n          top: 0,\n          insetInlineStart: menuPanelMaskInset\n        },\n        [`> ${componentCls}`]: _extends(_extends(_extends({\n          borderRadius: borderRadiusLG\n        }, genMenuItemStyle(token)), genSubMenuArrowStyle(token)), {\n          [`${componentCls}-item, ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n            borderRadius: radiusSubMenuItem\n          },\n          [`${componentCls}-submenu-title::after`]: {\n            transition: `transform ${motionDurationSlow} ${motionEaseInOut}`\n          }\n        })\n      }\n    }), genSubMenuArrowStyle(token)), {\n      [`&-inline-collapsed ${componentCls}-submenu-arrow,\n        &-inline ${componentCls}-submenu-arrow`]: {\n        // ↓\n        '&::before': {\n          transform: `rotate(-45deg) translateX(${menuArrowOffset})`\n        },\n        '&::after': {\n          transform: `rotate(45deg) translateX(-${menuArrowOffset})`\n        }\n      },\n      [`${componentCls}-submenu-open${componentCls}-submenu-inline > ${componentCls}-submenu-title > ${componentCls}-submenu-arrow`]: {\n        // ↑\n        transform: `translateY(-${menuArrowSize * 0.2}px)`,\n        '&::after': {\n          transform: `rotate(-45deg) translateX(-${menuArrowOffset})`\n        },\n        '&::before': {\n          transform: `rotate(45deg) translateX(${menuArrowOffset})`\n        }\n      }\n    })\n  },\n  // Integration with header element so menu items have the same height\n  {\n    [`${antCls}-layout-header`]: {\n      [componentCls]: {\n        lineHeight: 'inherit'\n      }\n    }\n  }];\n};\n// ============================== Export ==============================\nexport default ((prefixCls, injectStyle) => {\n  const useOriginHook = genComponentStyleHook('Menu', (token, _ref) => {\n    let {\n      overrideComponentToken\n    } = _ref;\n    // Dropdown will handle menu style self. We do not need to handle this.\n    if ((injectStyle === null || injectStyle === void 0 ? void 0 : injectStyle.value) === false) {\n      return [];\n    }\n    const {\n      colorBgElevated,\n      colorPrimary,\n      colorError,\n      colorErrorHover,\n      colorTextLightSolid\n    } = token;\n    const {\n      controlHeightLG,\n      fontSize\n    } = token;\n    const menuArrowSize = fontSize / 7 * 5;\n    // Menu Token\n    const menuToken = mergeToken(token, {\n      menuItemHeight: controlHeightLG,\n      menuItemPaddingInline: token.margin,\n      menuArrowSize,\n      menuHorizontalHeight: controlHeightLG * 1.15,\n      menuArrowOffset: `${menuArrowSize * 0.25}px`,\n      menuPanelMaskInset: -7,\n      menuSubMenuBg: colorBgElevated\n    });\n    const colorTextDark = new TinyColor(colorTextLightSolid).setAlpha(0.65).toRgbString();\n    const menuDarkToken = mergeToken(menuToken, {\n      colorItemText: colorTextDark,\n      colorItemTextHover: colorTextLightSolid,\n      colorGroupTitle: colorTextDark,\n      colorItemTextSelected: colorTextLightSolid,\n      colorItemBg: '#001529',\n      colorSubItemBg: '#000c17',\n      colorItemBgActive: 'transparent',\n      colorItemBgSelected: colorPrimary,\n      colorActiveBarWidth: 0,\n      colorActiveBarHeight: 0,\n      colorActiveBarBorderSize: 0,\n      // Disabled\n      colorItemTextDisabled: new TinyColor(colorTextLightSolid).setAlpha(0.25).toRgbString(),\n      // Danger\n      colorDangerItemText: colorError,\n      colorDangerItemTextHover: colorErrorHover,\n      colorDangerItemTextSelected: colorTextLightSolid,\n      colorDangerItemBgActive: colorError,\n      colorDangerItemBgSelected: colorError,\n      menuSubMenuBg: '#001529',\n      // Horizontal\n      colorItemTextSelectedHorizontal: colorTextLightSolid,\n      colorItemBgSelectedHorizontal: colorPrimary\n    }, _extends({}, overrideComponentToken));\n    return [\n    // Basic\n    getBaseStyle(menuToken),\n    // Horizontal\n    getHorizontalStyle(menuToken),\n    // Vertical\n    getVerticalStyle(menuToken),\n    // Theme\n    getThemeStyle(menuToken, 'light'), getThemeStyle(menuDarkToken, 'dark'),\n    // RTL\n    getRTLStyle(menuToken),\n    // Motion\n    genCollapseMotion(menuToken), initSlideMotion(menuToken, 'slide-up'), initSlideMotion(menuToken, 'slide-down'), initZoomMotion(menuToken, 'zoom-big')];\n  }, token => {\n    const {\n      colorPrimary,\n      colorError,\n      colorTextDisabled,\n      colorErrorBg,\n      colorText,\n      colorTextDescription,\n      colorBgContainer,\n      colorFillAlter,\n      colorFillContent,\n      lineWidth,\n      lineWidthBold,\n      controlItemBgActive,\n      colorBgTextHover\n    } = token;\n    return {\n      dropdownWidth: 160,\n      zIndexPopup: token.zIndexPopupBase + 50,\n      radiusItem: token.borderRadiusLG,\n      radiusSubMenuItem: token.borderRadiusSM,\n      colorItemText: colorText,\n      colorItemTextHover: colorText,\n      colorItemTextHoverHorizontal: colorPrimary,\n      colorGroupTitle: colorTextDescription,\n      colorItemTextSelected: colorPrimary,\n      colorItemTextSelectedHorizontal: colorPrimary,\n      colorItemBg: colorBgContainer,\n      colorItemBgHover: colorBgTextHover,\n      colorItemBgActive: colorFillContent,\n      colorSubItemBg: colorFillAlter,\n      colorItemBgSelected: controlItemBgActive,\n      colorItemBgSelectedHorizontal: 'transparent',\n      colorActiveBarWidth: 0,\n      colorActiveBarHeight: lineWidthBold,\n      colorActiveBarBorderSize: lineWidth,\n      // Disabled\n      colorItemTextDisabled: colorTextDisabled,\n      // Danger\n      colorDangerItemText: colorError,\n      colorDangerItemTextHover: colorError,\n      colorDangerItemTextSelected: colorError,\n      colorDangerItemBgActive: colorErrorBg,\n      colorDangerItemBgSelected: colorErrorBg,\n      itemMarginInline: token.marginXXS\n    };\n  });\n  return useOriginHook(prefixCls);\n});", "import { provide, computed, inject } from 'vue';\nexport const OverrideContextKey = Symbol('OverrideContextKey');\nexport const useInjectOverride = () => {\n  return inject(OverrideContextKey, undefined);\n};\nexport const useProvideOverride = props => {\n  var _a, _b, _c;\n  const {\n    prefixCls,\n    mode,\n    selectable,\n    validator,\n    onClick,\n    expandIcon\n  } = useInjectOverride() || {};\n  provide(OverrideContextKey, {\n    prefixCls: computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.prefixCls) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : prefixCls === null || prefixCls === void 0 ? void 0 : prefixCls.value;\n    }),\n    mode: computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.mode) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : mode === null || mode === void 0 ? void 0 : mode.value;\n    }),\n    selectable: computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.selectable) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : selectable === null || selectable === void 0 ? void 0 : selectable.value;\n    }),\n    validator: (_a = props.validator) !== null && _a !== void 0 ? _a : validator,\n    onClick: (_b = props.onClick) !== null && _b !== void 0 ? _b : onClick,\n    expandIcon: (_c = props.expandIcon) !== null && _c !== void 0 ? _c : expandIcon === null || expandIcon === void 0 ? void 0 : expandIcon.value\n  });\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { Fragment as _Fragment, createVNode as _createVNode, resolveDirective as _resolveDirective } from \"vue\";\nimport { shallowRef, Teleport, computed, defineComponent, ref, inject, watchEffect, watch, onMounted, unref } from 'vue';\nimport shallowEqual from '../../_util/shallowequal';\nimport useProvideMenu, { MenuContextProvider, useProvideFirstLevel } from './hooks/useMenuContext';\nimport useConfigInject from '../../config-provider/hooks/useConfigInject';\nimport devWarning from '../../vc-util/devWarning';\nimport uniq from 'lodash-es/uniq';\nimport { SiderCollapsedKey } from '../../layout/injectionKey';\nimport { flattenChildren } from '../../_util/props-util';\nimport Overflow from '../../vc-overflow';\nimport MenuItem from './MenuItem';\nimport SubMenu from './SubMenu';\nimport EllipsisOutlined from \"@ant-design/icons-vue/es/icons/EllipsisOutlined\";\nimport { cloneElement } from '../../_util/vnode';\nimport { OVERFLOW_KEY, PathContext } from './hooks/useKeyPath';\nimport collapseMotion from '../../_util/collapseMotion';\nimport useItems from './hooks/useItems';\nimport useStyle from '../style';\nimport { useInjectOverride } from './OverrideContext';\nexport const menuProps = () => ({\n  id: String,\n  prefixCls: String,\n  // donot use items, now only support inner use\n  items: Array,\n  disabled: Boolean,\n  inlineCollapsed: Boolean,\n  disabledOverflow: Boolean,\n  forceSubMenuRender: Boolean,\n  openKeys: Array,\n  selectedKeys: Array,\n  activeKey: String,\n  selectable: {\n    type: Boolean,\n    default: true\n  },\n  multiple: {\n    type: Boolean,\n    default: false\n  },\n  tabindex: {\n    type: [Number, String]\n  },\n  motion: Object,\n  role: String,\n  theme: {\n    type: String,\n    default: 'light'\n  },\n  mode: {\n    type: String,\n    default: 'vertical'\n  },\n  inlineIndent: {\n    type: Number,\n    default: 24\n  },\n  subMenuOpenDelay: {\n    type: Number,\n    default: 0\n  },\n  subMenuCloseDelay: {\n    type: Number,\n    default: 0.1\n  },\n  builtinPlacements: {\n    type: Object\n  },\n  triggerSubMenuAction: {\n    type: String,\n    default: 'hover'\n  },\n  getPopupContainer: Function,\n  expandIcon: Function,\n  onOpenChange: Function,\n  onSelect: Function,\n  onDeselect: Function,\n  onClick: [Function, Array],\n  onFocus: Function,\n  onBlur: Function,\n  onMousedown: Function,\n  'onUpdate:openKeys': Function,\n  'onUpdate:selectedKeys': Function,\n  'onUpdate:activeKey': Function\n});\nconst EMPTY_LIST = [];\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'AMenu',\n  inheritAttrs: false,\n  props: menuProps(),\n  slots: Object,\n  setup(props, _ref) {\n    let {\n      slots,\n      emit,\n      attrs\n    } = _ref;\n    const {\n      direction,\n      getPrefixCls\n    } = useConfigInject('menu', props);\n    const override = useInjectOverride();\n    const prefixCls = computed(() => {\n      var _a;\n      return getPrefixCls('menu', props.prefixCls || ((_a = override === null || override === void 0 ? void 0 : override.prefixCls) === null || _a === void 0 ? void 0 : _a.value));\n    });\n    const [wrapSSR, hashId] = useStyle(prefixCls, computed(() => {\n      return !override;\n    }));\n    const store = shallowRef(new Map());\n    const siderCollapsed = inject(SiderCollapsedKey, ref(undefined));\n    const inlineCollapsed = computed(() => {\n      if (siderCollapsed.value !== undefined) {\n        return siderCollapsed.value;\n      }\n      return props.inlineCollapsed;\n    });\n    const {\n      itemsNodes\n    } = useItems(props);\n    const isMounted = shallowRef(false);\n    onMounted(() => {\n      isMounted.value = true;\n    });\n    watchEffect(() => {\n      devWarning(!(props.inlineCollapsed === true && props.mode !== 'inline'), 'Menu', '`inlineCollapsed` should only be used when `mode` is inline.');\n      devWarning(!(siderCollapsed.value !== undefined && props.inlineCollapsed === true), 'Menu', '`inlineCollapsed` not control Menu under Sider. Should set `collapsed` on Sider instead.');\n      // devWarning(\n      //   !!props.items && !slots.default,\n      //   'Menu',\n      //   '`children` will be removed in next major version. Please use `items` instead.',\n      // );\n    });\n    const activeKeys = ref([]);\n    const mergedSelectedKeys = ref([]);\n    const keyMapStore = ref({});\n    watch(store, () => {\n      const newKeyMapStore = {};\n      for (const menuInfo of store.value.values()) {\n        newKeyMapStore[menuInfo.key] = menuInfo;\n      }\n      keyMapStore.value = newKeyMapStore;\n    }, {\n      flush: 'post'\n    });\n    watchEffect(() => {\n      if (props.activeKey !== undefined) {\n        let keys = [];\n        const menuInfo = props.activeKey ? keyMapStore.value[props.activeKey] : undefined;\n        if (menuInfo && props.activeKey !== undefined) {\n          keys = uniq([].concat(unref(menuInfo.parentKeys), props.activeKey));\n        } else {\n          keys = [];\n        }\n        if (!shallowEqual(activeKeys.value, keys)) {\n          activeKeys.value = keys;\n        }\n      }\n    });\n    watch(() => props.selectedKeys, selectedKeys => {\n      if (selectedKeys) {\n        mergedSelectedKeys.value = selectedKeys.slice();\n      }\n    }, {\n      immediate: true,\n      deep: true\n    });\n    const selectedSubMenuKeys = ref([]);\n    watch([keyMapStore, mergedSelectedKeys], () => {\n      let subMenuParentKeys = [];\n      mergedSelectedKeys.value.forEach(key => {\n        const menuInfo = keyMapStore.value[key];\n        if (menuInfo) {\n          subMenuParentKeys = subMenuParentKeys.concat(unref(menuInfo.parentKeys));\n        }\n      });\n      subMenuParentKeys = uniq(subMenuParentKeys);\n      if (!shallowEqual(selectedSubMenuKeys.value, subMenuParentKeys)) {\n        selectedSubMenuKeys.value = subMenuParentKeys;\n      }\n    }, {\n      immediate: true\n    });\n    // >>>>> Trigger select\n    const triggerSelection = info => {\n      if (props.selectable) {\n        // Insert or Remove\n        const {\n          key: targetKey\n        } = info;\n        const exist = mergedSelectedKeys.value.includes(targetKey);\n        let newSelectedKeys;\n        if (props.multiple) {\n          if (exist) {\n            newSelectedKeys = mergedSelectedKeys.value.filter(key => key !== targetKey);\n          } else {\n            newSelectedKeys = [...mergedSelectedKeys.value, targetKey];\n          }\n        } else {\n          newSelectedKeys = [targetKey];\n        }\n        // Trigger event\n        const selectInfo = _extends(_extends({}, info), {\n          selectedKeys: newSelectedKeys\n        });\n        if (!shallowEqual(newSelectedKeys, mergedSelectedKeys.value)) {\n          if (props.selectedKeys === undefined) {\n            mergedSelectedKeys.value = newSelectedKeys;\n          }\n          emit('update:selectedKeys', newSelectedKeys);\n          if (exist && props.multiple) {\n            emit('deselect', selectInfo);\n          } else {\n            emit('select', selectInfo);\n          }\n        }\n      }\n      // Whatever selectable, always close it\n      if (mergedMode.value !== 'inline' && !props.multiple && mergedOpenKeys.value.length) {\n        triggerOpenKeys(EMPTY_LIST);\n      }\n    };\n    const mergedOpenKeys = ref([]);\n    watch(() => props.openKeys, function () {\n      let openKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : mergedOpenKeys.value;\n      if (!shallowEqual(mergedOpenKeys.value, openKeys)) {\n        mergedOpenKeys.value = openKeys.slice();\n      }\n    }, {\n      immediate: true,\n      deep: true\n    });\n    let timeout;\n    const changeActiveKeys = keys => {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        if (props.activeKey === undefined) {\n          activeKeys.value = keys;\n        }\n        emit('update:activeKey', keys[keys.length - 1]);\n      });\n    };\n    const disabled = computed(() => !!props.disabled);\n    const isRtl = computed(() => direction.value === 'rtl');\n    const mergedMode = ref('vertical');\n    const mergedInlineCollapsed = shallowRef(false);\n    watchEffect(() => {\n      var _a;\n      if ((props.mode === 'inline' || props.mode === 'vertical') && inlineCollapsed.value) {\n        mergedMode.value = 'vertical';\n        mergedInlineCollapsed.value = inlineCollapsed.value;\n      } else {\n        mergedMode.value = props.mode;\n        mergedInlineCollapsed.value = false;\n      }\n      if ((_a = override === null || override === void 0 ? void 0 : override.mode) === null || _a === void 0 ? void 0 : _a.value) {\n        mergedMode.value = override.mode.value;\n      }\n    });\n    const isInlineMode = computed(() => mergedMode.value === 'inline');\n    const triggerOpenKeys = keys => {\n      mergedOpenKeys.value = keys;\n      emit('update:openKeys', keys);\n      emit('openChange', keys);\n    };\n    // >>>>> Cache & Reset open keys when inlineCollapsed changed\n    const inlineCacheOpenKeys = ref(mergedOpenKeys.value);\n    const mountRef = shallowRef(false);\n    // Cache\n    watch(mergedOpenKeys, () => {\n      if (isInlineMode.value) {\n        inlineCacheOpenKeys.value = mergedOpenKeys.value;\n      }\n    }, {\n      immediate: true\n    });\n    // Restore\n    watch(isInlineMode, () => {\n      if (!mountRef.value) {\n        mountRef.value = true;\n        return;\n      }\n      if (isInlineMode.value) {\n        mergedOpenKeys.value = inlineCacheOpenKeys.value;\n      } else {\n        // Trigger open event in case its in control\n        triggerOpenKeys(EMPTY_LIST);\n      }\n    }, {\n      immediate: true\n    });\n    const className = computed(() => {\n      return {\n        [`${prefixCls.value}`]: true,\n        [`${prefixCls.value}-root`]: true,\n        [`${prefixCls.value}-${mergedMode.value}`]: true,\n        [`${prefixCls.value}-inline-collapsed`]: mergedInlineCollapsed.value,\n        [`${prefixCls.value}-rtl`]: isRtl.value,\n        [`${prefixCls.value}-${props.theme}`]: true\n      };\n    });\n    const rootPrefixCls = computed(() => getPrefixCls());\n    const defaultMotions = computed(() => ({\n      horizontal: {\n        name: `${rootPrefixCls.value}-slide-up`\n      },\n      inline: collapseMotion(`${rootPrefixCls.value}-motion-collapse`),\n      other: {\n        name: `${rootPrefixCls.value}-zoom-big`\n      }\n    }));\n    useProvideFirstLevel(true);\n    const getChildrenKeys = function () {\n      let eventKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n      const keys = [];\n      const storeValue = store.value;\n      eventKeys.forEach(eventKey => {\n        const {\n          key,\n          childrenEventKeys\n        } = storeValue.get(eventKey);\n        keys.push(key, ...getChildrenKeys(unref(childrenEventKeys)));\n      });\n      return keys;\n    };\n    // ========================= Open =========================\n    /**\n     * Click for item. SubMenu do not have selection status\n     */\n    const onInternalClick = info => {\n      var _a;\n      emit('click', info);\n      triggerSelection(info);\n      (_a = override === null || override === void 0 ? void 0 : override.onClick) === null || _a === void 0 ? void 0 : _a.call(override);\n    };\n    const onInternalOpenChange = (key, open) => {\n      var _a;\n      const childrenEventKeys = ((_a = keyMapStore.value[key]) === null || _a === void 0 ? void 0 : _a.childrenEventKeys) || [];\n      let newOpenKeys = mergedOpenKeys.value.filter(k => k !== key);\n      if (open) {\n        newOpenKeys.push(key);\n      } else if (mergedMode.value !== 'inline') {\n        // We need find all related popup to close\n        const subPathKeys = getChildrenKeys(unref(childrenEventKeys));\n        newOpenKeys = uniq(newOpenKeys.filter(k => !subPathKeys.includes(k)));\n      }\n      if (!shallowEqual(mergedOpenKeys, newOpenKeys)) {\n        triggerOpenKeys(newOpenKeys);\n      }\n    };\n    const registerMenuInfo = (key, info) => {\n      store.value.set(key, info);\n      store.value = new Map(store.value);\n    };\n    const unRegisterMenuInfo = key => {\n      store.value.delete(key);\n      store.value = new Map(store.value);\n    };\n    const lastVisibleIndex = ref(0);\n    const expandIcon = computed(() => {\n      var _a;\n      return props.expandIcon || slots.expandIcon || ((_a = override === null || override === void 0 ? void 0 : override.expandIcon) === null || _a === void 0 ? void 0 : _a.value) ? opt => {\n        let icon = props.expandIcon || slots.expandIcon;\n        icon = typeof icon === 'function' ? icon(opt) : icon;\n        return cloneElement(icon, {\n          class: `${prefixCls.value}-submenu-expand-icon`\n        }, false);\n      } : null;\n    });\n    useProvideMenu({\n      prefixCls,\n      activeKeys,\n      openKeys: mergedOpenKeys,\n      selectedKeys: mergedSelectedKeys,\n      changeActiveKeys,\n      disabled,\n      rtl: isRtl,\n      mode: mergedMode,\n      inlineIndent: computed(() => props.inlineIndent),\n      subMenuCloseDelay: computed(() => props.subMenuCloseDelay),\n      subMenuOpenDelay: computed(() => props.subMenuOpenDelay),\n      builtinPlacements: computed(() => props.builtinPlacements),\n      triggerSubMenuAction: computed(() => props.triggerSubMenuAction),\n      getPopupContainer: computed(() => props.getPopupContainer),\n      inlineCollapsed: mergedInlineCollapsed,\n      theme: computed(() => props.theme),\n      siderCollapsed,\n      defaultMotions: computed(() => isMounted.value ? defaultMotions.value : null),\n      motion: computed(() => isMounted.value ? props.motion : null),\n      overflowDisabled: shallowRef(undefined),\n      onOpenChange: onInternalOpenChange,\n      onItemClick: onInternalClick,\n      registerMenuInfo,\n      unRegisterMenuInfo,\n      selectedSubMenuKeys,\n      expandIcon,\n      forceSubMenuRender: computed(() => props.forceSubMenuRender),\n      rootClassName: hashId\n    });\n    const getChildrenList = () => {\n      var _a;\n      return itemsNodes.value || flattenChildren((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots));\n    };\n    return () => {\n      var _a;\n      const childList = getChildrenList();\n      const allVisible = lastVisibleIndex.value >= childList.length - 1 || mergedMode.value !== 'horizontal' || props.disabledOverflow;\n      // >>>>> Children\n      const getWrapperList = childList => {\n        return mergedMode.value !== 'horizontal' || props.disabledOverflow ? childList :\n        // Need wrap for overflow dropdown that do not response for open\n        childList.map((child, index) => // Always wrap provider to avoid sub node re-mount\n        _createVNode(MenuContextProvider, {\n          \"key\": child.key,\n          \"overflowDisabled\": index > lastVisibleIndex.value\n        }, {\n          default: () => child\n        }));\n      };\n      const overflowedIndicator = ((_a = slots.overflowedIndicator) === null || _a === void 0 ? void 0 : _a.call(slots)) || _createVNode(EllipsisOutlined, null, null);\n      return wrapSSR(_createVNode(Overflow, _objectSpread(_objectSpread({}, attrs), {}, {\n        \"onMousedown\": props.onMousedown,\n        \"prefixCls\": `${prefixCls.value}-overflow`,\n        \"component\": \"ul\",\n        \"itemComponent\": MenuItem,\n        \"class\": [className.value, attrs.class, hashId.value],\n        \"role\": \"menu\",\n        \"id\": props.id,\n        \"data\": getWrapperList(childList),\n        \"renderRawItem\": node => node,\n        \"renderRawRest\": omitItems => {\n          // We use origin list since wrapped list use context to prevent open\n          const len = omitItems.length;\n          const originOmitItems = len ? childList.slice(-len) : null;\n          return _createVNode(_Fragment, null, [_createVNode(SubMenu, {\n            \"eventKey\": OVERFLOW_KEY,\n            \"key\": OVERFLOW_KEY,\n            \"title\": overflowedIndicator,\n            \"disabled\": allVisible,\n            \"internalPopupClose\": len === 0\n          }, {\n            default: () => originOmitItems\n          }), _createVNode(PathContext, null, {\n            default: () => [_createVNode(SubMenu, {\n              \"eventKey\": OVERFLOW_KEY,\n              \"key\": OVERFLOW_KEY,\n              \"title\": overflowedIndicator,\n              \"disabled\": allVisible,\n              \"internalPopupClose\": len === 0\n            }, {\n              default: () => originOmitItems\n            })]\n          })]);\n        },\n        \"maxCount\": mergedMode.value !== 'horizontal' || props.disabledOverflow ? Overflow.INVALIDATE : Overflow.RESPONSIVE,\n        \"ssr\": \"full\",\n        \"data-menu-list\": true,\n        \"onVisibleChange\": newLastIndex => {\n          lastVisibleIndex.value = newLastIndex;\n        }\n      }), {\n        default: () => [_createVNode(Teleport, {\n          \"to\": \"body\"\n        }, {\n          default: () => [_createVNode(\"div\", {\n            \"style\": {\n              display: 'none'\n            },\n            \"aria-hidden\": true\n          }, [_createVNode(PathContext, null, {\n            default: () => [getWrapperList(getChildrenList())]\n          })])]\n        })]\n      }));\n    };\n  }\n});", "import Menu from './src/Menu';\nimport MenuItem from './src/MenuItem';\nimport SubMenu from './src/SubMenu';\nimport ItemGroup from './src/ItemGroup';\nimport Divider from './src/Divider';\n/* istanbul ignore next */\nMenu.install = function (app) {\n  app.component(Menu.name, Menu);\n  app.component(MenuItem.name, MenuItem);\n  app.component(SubMenu.name, SubMenu);\n  app.component(Divider.name, Divider);\n  app.component(ItemGroup.name, ItemGroup);\n  return app;\n};\nMenu.Item = MenuItem;\nMenu.Divider = Divider;\nMenu.SubMenu = SubMenu;\nMenu.ItemGroup = ItemGroup;\nexport { SubMenu, MenuItem as Item, MenuItem, ItemGroup, ItemGroup as MenuItemGroup, Divider, Divider as MenuDivider };\nexport default Menu;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAS,aAAa,MAAM,MAAM,SAAS,gBAAgB;AACzD,MAAI,MAAM,UAAU,QAAQ,KAAK,gBAAgB,MAAM,IAAI,IAAI;AAC/D,MAAI,QAAQ,QAAQ;AAClB,WAAO,CAAC,CAAC;AAAA,EACX;AACA,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,YAAY,CAAC,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM;AAC1E,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,OAAO,UAAU,eAAe,KAAK,IAAI;AAEjE,WAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO;AAC3C,UAAM,MAAM,MAAM,GAAG;AACrB,QAAI,CAAC,gBAAgB,GAAG,GAAG;AACzB,aAAO;AAAA,IACT;AACA,UAAM,SAAS,KAAK,GAAG;AACvB,UAAM,SAAS,KAAK,GAAG;AACvB,UAAM,UAAU,QAAQ,KAAK,gBAAgB,QAAQ,QAAQ,GAAG,IAAI;AACpE,QAAI,QAAQ,SAAS,QAAQ,UAAU,WAAW,QAAQ;AACxD,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACe,SAAR,qBAAkB,OAAO,OAAO;AACrC,SAAO,aAAa,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC;AAChD;;;ACjCA,IAAM,iBAAiB,OAAO,gBAAgB;AAC9C,IAAM,iBAAiB,WAAS;AAC9B,UAAQ,gBAAgB,KAAK;AAC/B;AACA,IAAM,gBAAgB,MAAM;AAC1B,SAAO,OAAO,cAAc;AAC9B;AACA,IAAM,iBAAiB,OAAO,gBAAgB;AACvC,IAAM,wBAAwB,iBAAe;AAClD,UAAQ,gBAAgB,WAAW;AACrC;AACO,IAAM,uBAAuB,MAAM;AACxC,SAAO,OAAO,gBAAgB,KAAK;AACrC;AACA,IAAM,2BAA2B,OAAO,0BAA0B;AAClE,IAAM,uBAAuB,gBAAc;AACzC,UAAQ,0BAA0B,UAAU;AAC9C;AACA,IAAM,sBAAsB,MAAM;AAChC,SAAO,OAAO,0BAA0B,IAAI;AAC9C;AACA,IAAM,sBAAsB,gBAAgB;AAAA,EAC1C,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,kBAAkB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,UAAM,cAAc,cAAc;AAClC,UAAM,aAAa,SAAS,CAAC,GAAG,WAAW;AAG3C,QAAI,MAAM,SAAS,QAAW;AAC5B,iBAAW,OAAO,MAAM,OAAO,MAAM;AAAA,IACvC;AACA,QAAI,MAAM,qBAAqB,QAAW;AACxC,iBAAW,mBAAmB,MAAM,OAAO,kBAAkB;AAAA,IAC/D;AACA,mBAAe,UAAU;AACzB,WAAO,MAAM;AACX,UAAI;AACJ,cAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,IAChF;AAAA,EACF;AACF,CAAC;AAED,IAAO,yBAAQ;;;AC7DR,IAAM,oBAAoB,OAAO,gBAAgB;AACjD,IAAM,uBAAuB,OAAO,mBAAmB;;;ACAvD,IAAM,eAAe;AAC5B,IAAM,iBAAiB,OAAO,gBAAgB;AAC9C,IAAM,mBAAmB,MAAM;AAC7B,SAAO,OAAO,gBAAgB;AAAA,IAC5B,iBAAiB,SAAS,MAAM,CAAC,CAAC;AAAA,IAClC,YAAY,SAAS,MAAM,CAAC,CAAC;AAAA,IAC7B,YAAY,CAAC;AAAA,EACf,CAAC;AACH;AACA,IAAM,oBAAoB,CAAC,UAAU,KAAK,aAAa;AACrD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB;AACrB,QAAM,YAAY,SAAS,MAAM,CAAC,GAAG,gBAAgB,OAAO,QAAQ,CAAC;AACrE,QAAM,OAAO,SAAS,MAAM,CAAC,GAAG,WAAW,OAAO,GAAG,CAAC;AACtD,UAAQ,gBAAgB;AAAA,IACtB,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,YAAY;AAAA,EACd,CAAC;AACD,SAAO;AACT;AACA,IAAM,UAAU,OAAO,SAAS;AACzB,IAAM,cAAc,gBAAgB;AAAA,EACzC,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AAEJ,YAAQ,SAAS,IAAI;AACrB,WAAO,MAAM;AACX,UAAI;AACJ,cAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,IAChF;AAAA,EACF;AACF,CAAC;AACM,IAAM,aAAa,MAAM;AAC9B,SAAO,OAAO,SAAS,KAAK;AAC9B;AAEA,IAAO,qBAAQ;;;AC7Cf,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAM,eAAe,CAAC,GAAG,CAAC;AACnB,IAAM,aAAa;AAAA,EACxB,MAAM;AAAA,IACJ,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,IAAI,CAAC;AAAA,IACd;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EACA,KAAK;AAAA,IACH,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,EAAE;AAAA,IACd;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,EAAE;AAAA,IACd;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,IAAI,CAAC;AAAA,IACd;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,EAAE;AAAA,IACd;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,IAAI,CAAC;AAAA,IACd;AAAA,EACF;AACF;;;AC3EA,IAAM,sBAAsB;AAAA,EAC1B,WAAW;AAAA,EACX,IAAI;AAAA,EACJ,mBAAmB,kBAAU;AAC/B;AACA,IAAO,kBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,WAAO,MAAM;AACX,UAAI;AACJ,aAAO,YAAa,OAAO;AAAA,QACzB,SAAS,GAAG,MAAM,SAAS;AAAA,QAC3B,MAAM,MAAM;AAAA,QACZ,QAAQ;AAAA,QACR,SAAS,MAAM;AAAA,MACjB,GAAG,EAAE,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,IAC/E;AAAA,EACF;AACF,CAAC;;;AC1BD,IAAI,SAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAOA,SAAS,OAAO;AAAC;AACjB,IAAO,kBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,SAAS,kBAAU,IAAI,IAAI,CAAC,OAAO,CAAC;AAAA,IACpC,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW,kBAAU,OAAO,IAAI,OAAO;AAAA,IACvC,gBAAgB;AAAA,IAChB,WAAW,kBAAU;AAAA,IACrB,oBAAoB,kBAAU,KAAK,IAAI,MAAM;AAAA,IAAC,CAAC;AAAA,IAC/C,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,kBAAkB;AAAA,IAClB,WAAW,kBAAU,OAAO,IAAI,YAAY;AAAA,IAC5C,iBAAiB,kBAAU,OAAO,IAAI,GAAG;AAAA,IACzC,iBAAiB,kBAAU,OAAO,IAAI,GAAG;AAAA,IACzC,mBAAmB;AAAA,IACnB,sBAAsB;AAAA,MACpB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO,kBAAU,OAAO,IAAI,OAAO,CAAC,EAAE;AAAA,IACtC,cAAc,kBAAU,IAAI,IAAI,IAAI;AAAA,IACpC,OAAO;AAAA,IACP,mBAAmB,kBAAU;AAAA,IAC7B,mBAAmB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,WAAW;AAC9B,UAAM,kBAAkB,MAAM;AAC5B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,aAAO,CAAC,CAAC,CAAC,MAAM,QAAQ,YAAa,OAAO;AAAA,QAC1C,SAAS,GAAG,SAAS;AAAA,QACrB,OAAO;AAAA,MACT,GAAG,CAAC,aAAa,OAAO,OAAO,cAAc,CAAC,CAAC,IAAI,MAAM,YAAa,iBAAS;AAAA,QAC7E,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,qBAAqB;AAAA,MACvB,GAAG;AAAA,QACD,SAAS,MAAM;AAAA,MACjB,CAAC,CAAC;AAAA,IACJ;AACA,UAAM,kBAAkB,MAAM;AAC5B,aAAO,WAAW,MAAM,gBAAgB;AAAA,IAC1C;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,iBAAiB,MAAM;AACrB,YAAI;AACJ,gBAAQ,KAAK,WAAW,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB;AAAA,MACzF;AAAA,IACF,CAAC;AACD,UAAM,iBAAiB,WAAW,KAAK;AACvC,UAAM,cAAc,WAAW,KAAK;AACpC,gBAAY,MAAM;AAChB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,OAAO,yBAAyB,WAAW;AAC7C,uBAAe,QAAQ;AAAA,MACzB,WAAW,wBAAwB,OAAO,yBAAyB,UAAU;AAC3E,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,uBAAe,QAAQ,eAAe;AACtC,oBAAY,QAAQ,eAAe;AAAA,MACrC;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AACX,YAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,OACJ,YAAY,OAAO,OAAO,CAAC,oBAAoB,WAAW,mBAAmB,mBAAmB,gBAAgB,aAAa,sBAAsB,kBAAkB,aAAa,aAAa,SAAS,wBAAwB,gBAAgB,CAAC;AACnP,YAAM,aAAa,SAAS,CAAC,GAAG,SAAS;AACzC,UAAI,MAAM,YAAY,QAAW;AAC/B,mBAAW,eAAe,MAAM;AAAA,MAClC;AACA,YAAM,eAAe,SAAS,SAAS,SAAS;AAAA,QAC9C,gBAAgB;AAAA,QAChB;AAAA,QACA,QAAQ;AAAA,QACR,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,yBAAyB;AAAA,QACzB,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,oBAAoB,eAAe;AAAA,QACnC,aAAa,YAAY;AAAA,QACzB;AAAA,QACA,YAAY;AAAA,QACZ;AAAA,MACF,GAAG,UAAU,GAAG,KAAK,GAAG;AAAA,QACtB,sBAAsB,MAAM,mBAAmB;AAAA,QAC/C,cAAc,MAAM,gBAAgB;AAAA,QACpC,KAAK;AAAA,QACL,OAAO,CAAC,CAAC,MAAM;AAAA,QACf,OAAO,gBAAgB;AAAA,MACzB,CAAC;AACD,aAAO,YAAa,oBAAS,cAAc;AAAA,QACzC,SAAS,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACxKD,IAAO,qBAAQ;;;ACDf,IAAO,+BAAS,OAAO;AAAA,EACrB,SAAS,CAAC,QAAQ,KAAK;AAAA,EACvB,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,cAAc,WAAW;AAAA,EACzB,mBAAmB,WAAW;AAAA,EAC9B,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA;AAAA,EAEnB,oBAAoB;AAAA,IAClB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA,oBAAoB;AAAA,IAClB,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA,sBAAsB;AAAA,IACpB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,OAAO,WAAW;AAAA,EAClB,mBAAmB,WAAW;AAAA,EAC9B,UAAU;AAAA;AAAA,EAEV,iBAAiB;AAAA;AAAA,EAEjB,oBAAoB;AAAA,EACpB,cAAc;AAAA,EACd,iBAAiB;AACnB;;;AC/CA,IAAM,4BAA4B;AAAA,EAChC,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAM,6BAA6B;AAAA,EACjC,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAMA,gBAAe,CAAC,GAAG,CAAC;AACnB,SAAS,mBAAmBC,qBAAoB;AACrD,MAAI,OAAOA,wBAAuB,WAAW;AAC3C,WAAOA,sBAAqB,4BAA4B;AAAA,EAC1D;AACA,SAAO,SAAS,SAAS,CAAC,GAAG,0BAA0B,GAAGA,mBAAkB;AAC9E;AACe,SAAR,cAA+B,QAAQ;AAC5C,QAAM;AAAA,IACJ,aAAa;AAAA,IACb,uBAAuB;AAAA,IACvB,qBAAqB;AAAA,IACrB,oBAAAA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,eAAe;AAAA,IACnB,MAAM;AAAA,MACJ,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,CAAC,IAAI,CAAC;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,CAAC,GAAG,CAAC;AAAA,IACf;AAAA,IACA,KAAK;AAAA,MACH,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,CAAC,GAAG,EAAE;AAAA,IAChB;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,CAAC,GAAG,CAAC;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,CAAC,EAAE,uBAAuB,aAAa,EAAE;AAAA,IACnD;AAAA,IACA,SAAS;AAAA,MACP,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,CAAC,IAAI,EAAE,qBAAqB,WAAW;AAAA,IACjD;AAAA,IACA,UAAU;AAAA,MACR,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,CAAC,uBAAuB,YAAY,EAAE;AAAA,IAChD;AAAA,IACA,UAAU;AAAA,MACR,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,CAAC,GAAG,EAAE,qBAAqB,WAAW;AAAA,IAChD;AAAA,IACA,aAAa;AAAA,MACX,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,CAAC,uBAAuB,YAAY,CAAC;AAAA,IAC/C;AAAA,IACA,aAAa;AAAA,MACX,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,CAAC,GAAG,qBAAqB,UAAU;AAAA,IAC7C;AAAA,IACA,YAAY;AAAA,MACV,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,CAAC,EAAE,uBAAuB,aAAa,CAAC;AAAA,IAClD;AAAA,IACA,YAAY;AAAA,MACV,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,CAAC,IAAI,qBAAqB,UAAU;AAAA,IAC9C;AAAA,EACF;AACA,SAAO,KAAK,YAAY,EAAE,QAAQ,SAAO;AACvC,iBAAa,GAAG,IAAI,qBAAqB,SAAS,SAAS,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG;AAAA,MACjF,UAAU,mBAAmBA,mBAAkB;AAAA,MAC/C,cAAAD;AAAA,IACF,CAAC,IAAI,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG;AAAA,MAC3C,UAAU,mBAAmBC,mBAAkB;AAAA,IACjD,CAAC;AACD,iBAAa,GAAG,EAAE,cAAc;AAAA,EAClC,CAAC;AACD,SAAO;AACT;;;ACrFA,SAAS,oBAAoB;AAC3B,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC/E,WAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC9C,QAAI,IAAI,CAAC,MAAM,QAAW;AACxB,aAAO,IAAI,CAAC;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAO,4BAAQ;;;ACRf,IAAM,gBAAgB,aAAa,IAAI,WAAS,GAAG,KAAK,UAAU;AAC3D,IAAM,yBAAyB,CAAC,WAAW,cAAc,SAAS,WAAW,SAAS;AAMtF,SAAS,cAAc,OAAO;AACnC,MAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACzF,MAAI,gBAAgB;AAClB,WAAO,CAAC,GAAG,eAAe,GAAG,YAAY,EAAE,SAAS,KAAK;AAAA,EAC3D;AACA,SAAO,aAAa,SAAS,KAAK;AACpC;AACO,SAAS,oBAAoB,OAAO;AACzC,SAAO,uBAAuB,SAAS,KAAK;AAC9C;;;ACfO,SAAS,WAAW,WAAW,OAAO;AAC3C,QAAM,kBAAkB,cAAc,KAAK;AAC3C,QAAM,YAAY,mBAAW;AAAA,IAC3B,CAAC,GAAG,SAAS,IAAI,KAAK,EAAE,GAAG,SAAS;AAAA,EACtC,CAAC;AACD,QAAM,eAAe,CAAC;AACtB,QAAM,aAAa,CAAC;AACpB,MAAI,SAAS,CAAC,iBAAiB;AAC7B,iBAAa,aAAa;AAE1B,eAAW,+BAA+B,IAAI;AAAA,EAChD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACjBA,SAAS,gBAAgB,WAAW;AAClC,MAAI,eAAe,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACvF,SAAO,UAAU,IAAI,SAAO,GAAG,YAAY,GAAG,GAAG,EAAE,EAAE,KAAK,GAAG;AAC/D;AACO,IAAM,8BAA8B;AACpC,SAAS,eAAe,SAAS;AACtC,QAAM,2BAA2B;AACjC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,mBAAmB,iBAAiB,IAAI,KAAK,KAAK,qBAAqB,KAAK,KAAK,CAAC,IAAI,EAAE;AAC9F,QAAM,uBAAuB,gBAAgB,KAAK,gBAAgB,IAAI,MAAM;AAC5E,QAAM,8BAA8B,sBAAsB,2BAA2B,mBAAmB;AACxG,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACe,SAAR,cAA+B,OAAO,SAAS;AACpD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,gBAAgB,MAAM;AAAA,IACtB;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,wBAAwB,iBAAiB,IAAI;AACnD,SAAO;AAAA,IACL,CAAC,YAAY,GAAG;AAAA;AAAA,MAEd,CAAC,GAAG,YAAY,QAAQ,GAAG,CAAC,SAAS,SAAS;AAAA,QAC5C,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,SAAS;AAAA,MACX,GAAG,aAAa,gBAAgB,gBAAgB,mBAAmB,SAAS,qBAAqB,CAAC,GAAG;AAAA,QACnG,YAAY;AAAA,UACV,YAAY;AAAA,QACd;AAAA,MACF,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,MAIF,CAAC,CAAC,mBAAmB,YAAY,UAAU,uBAAuB,YAAY,UAAU,wBAAwB,YAAY,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG;AAAA,QAChJ,QAAQ;AAAA,QACR,WAAW;AAAA,MACb;AAAA,MACA,CAAC,mBAAmB,YAAY,QAAQ,GAAG;AAAA,QACzC,MAAM;AAAA,UACJ,cAAc;AAAA,UACd,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,MACb;AAAA,MACA,CAAC,uBAAuB,YAAY,QAAQ,GAAG;AAAA,QAC7C,MAAM;AAAA,UACJ,cAAc;AAAA,UACd,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,CAAC,wBAAwB,YAAY,QAAQ,GAAG;AAAA,QAC9C,OAAO;AAAA,UACL,cAAc;AAAA,UACd,OAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,CAAC,sBAAsB,YAAY,UAAU,0BAA0B,YAAY,UAAU,2BAA2B,YAAY,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG;AAAA,QACzJ,KAAK;AAAA,QACL,WAAW;AAAA,MACb;AAAA,MACA,CAAC,sBAAsB,YAAY,QAAQ,GAAG;AAAA,QAC5C,MAAM;AAAA,UACJ,cAAc;AAAA,UACd,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,MACb;AAAA,MACA,CAAC,0BAA0B,YAAY,QAAQ,GAAG;AAAA,QAChD,MAAM;AAAA,UACJ,cAAc;AAAA,UACd,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,CAAC,2BAA2B,YAAY,QAAQ,GAAG;AAAA,QACjD,OAAO;AAAA,UACL,cAAc;AAAA,UACd,OAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,CAAC,oBAAoB,YAAY,UAAU,uBAAuB,YAAY,UAAU,0BAA0B,YAAY,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG;AAAA,QACnJ,OAAO;AAAA,UACL,cAAc;AAAA,UACd,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,MACb;AAAA,MACA,CAAC,oBAAoB,YAAY,QAAQ,GAAG;AAAA,QAC1C,KAAK;AAAA,UACH,cAAc;AAAA,UACd,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,MACb;AAAA,MACA,CAAC,uBAAuB,YAAY,QAAQ,GAAG;AAAA,QAC7C,KAAK;AAAA,MACP;AAAA,MACA,CAAC,0BAA0B,YAAY,QAAQ,GAAG;AAAA,QAChD,QAAQ;AAAA,MACV;AAAA;AAAA,MAEA,CAAC,CAAC,qBAAqB,YAAY,UAAU,wBAAwB,YAAY,UAAU,2BAA2B,YAAY,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG;AAAA,QACtJ,MAAM;AAAA,UACJ,cAAc;AAAA,UACd,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,MACb;AAAA,MACA,CAAC,qBAAqB,YAAY,QAAQ,GAAG;AAAA,QAC3C,KAAK;AAAA,UACH,cAAc;AAAA,UACd,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,MACb;AAAA,MACA,CAAC,wBAAwB,YAAY,QAAQ,GAAG;AAAA,QAC9C,KAAK;AAAA,MACP;AAAA,MACA,CAAC,2BAA2B,YAAY,QAAQ,GAAG;AAAA,QACjD,QAAQ;AAAA,MACV;AAAA;AAAA;AAAA;AAAA,MAIA,CAAC,gBAAgB,CAAC,uBAAuB,mBAAmB,sBAAsB,EAAE,IAAI,SAAO,OAAO,sBAAsB,GAAG,YAAY,CAAC,GAAG;AAAA,QAC7I,eAAe;AAAA,MACjB;AAAA;AAAA,MAEA,CAAC,gBAAgB,CAAC,0BAA0B,sBAAsB,yBAAyB,EAAE,IAAI,SAAO,OAAO,sBAAsB,GAAG,YAAY,CAAC,GAAG;AAAA,QACtJ,YAAY;AAAA,MACd;AAAA;AAAA,MAEA,CAAC,gBAAgB,CAAC,uBAAuB,oBAAoB,wBAAwB,EAAE,IAAI,SAAO,OAAO,sBAAsB,GAAG,YAAY,CAAC,GAAG;AAAA,QAChJ,cAAc;AAAA,UACZ,cAAc;AAAA,UACd,OAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,gBAAgB,CAAC,wBAAwB,qBAAqB,yBAAyB,EAAE,IAAI,SAAO,OAAO,sBAAsB,GAAG,YAAY,CAAC,GAAG;AAAA,QACnJ,aAAa;AAAA,UACX,cAAc;AAAA,UACd,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC5KA,IAAM,kBAAkB,WAAS;AAC/B,QAAM;AAAA,IACJ;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IAAC;AAAA,MACN,CAAC,YAAY,GAAG,SAAS,SAAS,SAAS,SAAS,CAAC,GAAG,eAAe,KAAK,CAAC,GAAG;AAAA,QAC9E,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,KAAK,CAAC;AAAA,UACJ,OAAO;AAAA,QACT,GAAG;AAAA,UACD,OAAO;AAAA,QACT,CAAC;AAAA,QACD,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,UACV,SAAS;AAAA,QACX;AAAA,QACA,iCAAiC;AAAA;AAAA,QAEjC,CAAC,GAAG,YAAY,QAAQ,GAAG;AAAA,UACzB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,SAAS,GAAG,YAAY,CAAC,MAAM,SAAS;AAAA,UACxC,OAAO;AAAA,UACP,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,UAAU;AAAA,UACV,iBAAiB;AAAA,UACjB,cAAc;AAAA,UACd,WAAW;AAAA,QACb;AAAA;AAAA,QAEA,CAAC,CAAC,oBAAoB,uBAAuB,0BAA0B,qBAAqB,wBAAwB,yBAAyB,EAAE,KAAK,GAAG,CAAC,GAAG;AAAA,UACzJ,CAAC,GAAG,YAAY,QAAQ,GAAG;AAAA,YACzB,cAAc,KAAK,IAAI,qBAAqB,2BAA2B;AAAA,UACzE;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,UAAU,GAAG;AAAA,UAC3B,UAAU;AAAA,QACZ;AAAA,MACF,CAAC,GAAG,eAAe,OAAO,CAAC,UAAU,SAAS;AAC5C,YAAI;AAAA,UACF;AAAA,QACF,IAAI;AACJ,eAAO;AAAA,UACL,CAAC,IAAI,YAAY,IAAI,QAAQ,EAAE,GAAG;AAAA,YAChC,CAAC,GAAG,YAAY,QAAQ,GAAG;AAAA,cACzB,iBAAiB;AAAA,YACnB;AAAA,YACA,CAAC,GAAG,YAAY,QAAQ,GAAG;AAAA,cACzB,iCAAiC;AAAA,YACnC;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC,CAAC,GAAG;AAAA;AAAA,QAEH,SAAS;AAAA,UACP,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAEA,cAAc,MAAW,OAAO;AAAA,MAC9B,mBAAmB;AAAA,IACrB,CAAC,GAAG;AAAA,MACF,SAAS;AAAA,MACT,cAAc;AAAA,MACd,eAAe;AAAA,MACf,qBAAqB;AAAA,IACvB,CAAC;AAAA;AAAA,IAED;AAAA,MACE,CAAC,GAAG,YAAY,OAAO,GAAG;AAAA,QACxB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EAAC;AACH;AAEA,IAAO,gBAAS,CAAC,WAAW,gBAAgB;AAC1C,QAAM,gBAAgB,sBAAsB,WAAW,WAAS;AAE9D,SAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,WAAW,OAAO;AAC3F,aAAO,CAAC;AAAA,IACV;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,MAAW,OAAO;AAAA;AAAA,MAErC,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,WAAW;AAAA,MACX,oBAAoB,oBAAoB,IAAI,IAAI;AAAA,IAClD,CAAC;AACD,WAAO,CAAC,gBAAgB,YAAY,GAAG,eAAe,OAAO,eAAe,CAAC;AAAA,EAC/E,GAAG,WAAS;AACV,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO;AAAA,MACL,aAAa,kBAAkB;AAAA,MAC/B,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,SAAO,cAAc,SAAS;AAChC;;;ACjHA,IAAM,cAAc,CAAC,KAAK,SAAS;AACjC,QAAM,SAAS,CAAC;AAChB,QAAM,UAAU,SAAS,CAAC,GAAG,GAAG;AAChC,OAAK,QAAQ,SAAO;AAClB,QAAI,OAAO,OAAO,KAAK;AACrB,aAAO,GAAG,IAAI,IAAI,GAAG;AACrB,aAAO,QAAQ,GAAG;AAAA,IACpB;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACO,IAAM,eAAe,MAAM,SAAS,SAAS,CAAC,GAAG,6BAAqB,CAAC,GAAG;AAAA,EAC/E,OAAO,kBAAU;AACnB,CAAC;AACM,IAAM,sBAAsB,OAAO;AAAA,EACxC,SAAS;AAAA,EACT,OAAO,CAAC;AAAA,EACR,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,oBAAoB;AACtB;AACA,IAAOC,mBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,yBAAiB,aAAa,GAAG;AAAA,IACtC,SAAS;AAAA,IACT,OAAO,CAAC;AAAA,IACR,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,EACtB,CAAC;AAAA,EACD,OAAO;AAAA;AAAA,EAEP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,MAAuC;AACzC,OAAC,CAAC,WAAW,MAAM,GAAG,CAAC,mBAAmB,cAAc,CAAC,EAAE,QAAQ,WAAS;AAC1E,YAAI,CAAC,gBAAgB,OAAO,IAAI;AAChC,wBAAQ,MAAM,cAAc,MAAM,QAAW,WAAW,KAAK,cAAc,kCAAkC,OAAO,aAAa;AAAA,MACnI,CAAC;AAAA,IACH;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,wBAAgB,WAAW,KAAK;AACpC,UAAM,aAAa,SAAS,MAAM;AAChC,UAAI;AACJ,cAAQ,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,KAAK,MAAM;AAAA,IAClE,CAAC;AACD,UAAM,YAAY,IAAI,0BAAkB,CAAC,MAAM,MAAM,MAAM,OAAO,CAAC,CAAC;AACpE,UAAM,UAAU,IAAI;AACpB,QAAI;AACJ,UAAM,YAAY,SAAO;AACvB,iBAAI,OAAO,KAAK;AAChB,cAAQ,WAAI,MAAM;AAChB,kBAAU,QAAQ,CAAC,CAAC;AAAA,MACtB,CAAC;AAAA,IACH,CAAC;AACD,UAAM,YAAY,MAAM;AACtB,UAAI;AACJ,YAAM,SAAS,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,KAAK,MAAM;AACxE,aAAO,CAAC,SAAS,UAAU;AAAA,IAC7B;AACA,UAAM,sBAAsB,SAAO;AACjC,YAAM,UAAU,UAAU;AAC1B,UAAI,WAAW,UAAU,QAAW;AAClC,kBAAU,QAAQ,UAAU,QAAQ;AAAA,MACtC;AACA,UAAI,CAAC,SAAS;AACZ,aAAK,kBAAkB,GAAG;AAC1B,aAAK,iBAAiB,GAAG;AACzB,aAAK,eAAe,GAAG;AACvB,aAAK,cAAc,GAAG;AAAA,MACxB;AAAA,IACF;AACA,UAAM,kBAAkB,MAAM;AAC5B,aAAO,QAAQ,MAAM,gBAAgB;AAAA,IACvC;AACA,WAAO;AAAA,MACL;AAAA,MACA,MAAM;AAAA,MACN,iBAAiB,MAAM;AACrB,YAAI;AACJ,gBAAQ,KAAK,QAAQ,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB;AAAA,MACtF;AAAA,IACF,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACvC,UAAI;AACJ,YAAM;AAAA,QACJ;AAAA,QACA,oBAAAC;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,2BAA2B;AAC/B,UAAI,OAAO,UAAU,UAAU;AAC7B,oCAA4B,KAAK,MAAM,mBAAmB,QAAQ,OAAO,SAAS,KAAK;AAAA,MACzF;AACA,aAAO,qBAAqB,cAAc;AAAA,QACxC,oBAAoB;AAAA,QACpB,oBAAAA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,UAAM,cAAc,SAAO;AACzB,aAAO,OAAO,QAAQ;AAAA,IACxB;AACA,UAAM,gCAAgC,SAAO;AAC3C,YAAM,cAAc,IAAI;AACxB,UAAI,OAAO,gBAAgB,YAAY,IAAI,OAAO;AAChD,aAAK,YAAY,iBAAiB,QAAQ,gBAAgB,aAAa,YAAY,IAAI,MAAM,QAAQ,KAAK,YAAY,iBAAiB,SAAS,YAAY,IAAI,MAAM,QAAQ,KAAK,YAAY,IAAI,MAAM,OAAO,MAAM,YAAY,gBAAgB,QAAQ,YAAY,IAAI,MAAM,QAAQ,GAAG;AAGzR,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,YAAY,SAAS,GAAG,GAAG,CAAC,YAAY,QAAQ,SAAS,OAAO,UAAU,SAAS,WAAW,QAAQ,CAAC;AAC3G,gBAAM,YAAY,SAAS,SAAS;AAAA,YAClC,SAAS;AAAA,UACX,GAAG,MAAM,GAAG;AAAA,YACV,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,OAAO,IAAI,SAAS,IAAI,MAAM,QAAQ,SAAS;AAAA,UACjD,CAAC;AACD,gBAAM,cAAc,SAAS,SAAS,CAAC,GAAG,OAAO,GAAG;AAAA,YAClD,eAAe;AAAA,UACjB,CAAC;AACD,gBAAM,QAAQ,aAAa,KAAK;AAAA,YAC9B,OAAO;AAAA,UACT,GAAG,IAAI;AACP,iBAAO,YAAa,QAAQ;AAAA,YAC1B,SAAS;AAAA,YACT,SAAS,GAAG,UAAU,KAAK;AAAA,UAC7B,GAAG,CAAC,KAAK,CAAC;AAAA,QACZ;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,UAAM,aAAa,MAAM;AACvB,UAAI,IAAI;AACR,cAAQ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,MAAM,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,IAClI;AACA,UAAM,eAAe,CAAC,SAAS,UAAU;AACvC,YAAMC,cAAa,kBAAkB;AAErC,YAAM,YAAY,OAAO,KAAKA,WAAU,EAAE,KAAK,SAAO;AACpD,YAAI,IAAI;AACR,eAAOA,YAAW,GAAG,EAAE,OAAO,CAAC,QAAQ,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,MAAMA,YAAW,GAAG,EAAE,OAAO,CAAC,QAAQ,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC;AAAA,MACvM,CAAC;AACD,UAAI,WAAW;AAEb,cAAM,OAAO,QAAQ,sBAAsB;AAC3C,cAAM,kBAAkB;AAAA,UACtB,KAAK;AAAA,UACL,MAAM;AAAA,QACR;AACA,YAAI,UAAU,QAAQ,KAAK,KAAK,KAAK,UAAU,QAAQ,QAAQ,KAAK,GAAG;AACrE,0BAAgB,MAAM,GAAG,KAAK,SAAS,MAAM,OAAO,CAAC,CAAC;AAAA,QACxD,WAAW,UAAU,QAAQ,KAAK,KAAK,KAAK,UAAU,QAAQ,QAAQ,KAAK,GAAG;AAC5E,0BAAgB,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC,CAAC;AAAA,QAC3C;AACA,YAAI,UAAU,QAAQ,MAAM,KAAK,KAAK,UAAU,QAAQ,OAAO,KAAK,GAAG;AACrE,0BAAgB,OAAO,GAAG,KAAK,QAAQ,MAAM,OAAO,CAAC,CAAC;AAAA,QACxD,WAAW,UAAU,QAAQ,OAAO,KAAK,KAAK,UAAU,QAAQ,MAAM,KAAK,GAAG;AAC5E,0BAAgB,OAAO,GAAG,CAAC,MAAM,OAAO,CAAC,CAAC;AAAA,QAC5C;AACA,gBAAQ,MAAM,kBAAkB,GAAG,gBAAgB,IAAI,IAAI,gBAAgB,GAAG;AAAA,MAChF;AAAA,IACF;AACA,UAAM,YAAY,SAAS,MAAM,WAAW,UAAU,OAAO,MAAM,KAAK,CAAC;AACzE,UAAM,oBAAoB,SAAS,MAAM,MAAM,qBAAqB,CAAC;AACrE,UAAM,CAAC,SAAS,MAAM,IAAI,cAAS,WAAW,SAAS,MAAM,CAAC,kBAAkB,KAAK,CAAC;AACtF,WAAO,MAAM;AACX,UAAI,IAAI;AACR,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,YAAY,KAAK,aAAa,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC,OAAO,QAAQ,OAAO,SAAS,KAAK;AAC7I,iBAAW,SAAS,WAAW,IAAI,SAAS,CAAC,IAAI;AACjD,UAAI,cAAc,UAAU;AAE5B,UAAI,WAAW,UAAU,UAAa,UAAU,GAAG;AACjD,sBAAc;AAAA,MAChB;AACA,UAAI,CAAC,UAAU;AACb,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,8BAA8B,eAAe,QAAQ,KAAK,CAAC,WAAW,QAAQ,IAAI,WAAW,YAAa,QAAQ,MAAM,CAAC,QAAQ,CAAC,CAAC;AACjJ,YAAM,WAAW,mBAAW;AAAA,QAC1B,CAAC,iBAAiB,GAAG,UAAU,KAAK,OAAO,GAAG;AAAA,QAC9C,CAAC,MAAM,SAAS,MAAM,MAAM,KAAK,GAAG,MAAM,SAAS,MAAM,MAAM;AAAA,MACjE,CAAC;AACD,YAAM,yBAAyB,mBAAW,kBAAkB;AAAA,QAC1D,CAAC,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU;AAAA,MAClD,GAAG,UAAU,MAAM,WAAW,OAAO,KAAK;AAC1C,YAAM,6BAA6B,SAAS,SAAS,CAAC,GAAG,UAAU,MAAM,YAAY,GAAG,iBAAiB;AACzG,YAAM,oBAAoB,UAAU,MAAM;AAC1C,YAAM,iBAAiB,SAAS,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG;AAAA,QACpE,WAAW,UAAU;AAAA,QACrB,OAAO,CAAC,CAAC,MAAM;AAAA,QACf,mBAAmB,sBAAsB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB;AAAA,QAC3G,mBAAmB,kBAAkB;AAAA,QACrC,SAAS;AAAA,QACT,KAAK;AAAA,QACL,kBAAkB;AAAA,QAClB,cAAc,SAAS,SAAS,CAAC,GAAG,iBAAiB,GAAG,YAAY;AAAA,QACpE,mBAAmB;AAAA,QACnB,iBAAiB;AAAA,QACjB;AAAA,QACA,gBAAgB,kBAAkB,cAAc,OAAO,iBAAiB,MAAM,cAAc;AAAA,MAC9F,CAAC;AACD,aAAO,QAAQ,YAAa,oBAAW,gBAAgB;AAAA,QACrD,SAAS,MAAM,CAAC,UAAU,QAAQ,aAAa,OAAO;AAAA,UACpD,OAAO;AAAA,QACT,CAAC,IAAI,KAAK;AAAA,QACV,cAAc,MAAM,YAAa,QAAQ;AAAA,UACvC,SAAS,GAAG,UAAU,KAAK;AAAA,QAC7B,GAAG,IAAI;AAAA,QACP,SAAS;AAAA,MACX,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACF,CAAC;;;AC/PD,IAAO,kBAAQ,YAAYC,gBAAO;;;ACDnB,SAAR,kBAAmC,OAAO;AAC/C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,cAAc;AAClB,SAAO,SAAS,MAAM,KAAK,UAAU,WAAW,OAAO,IAAI,QAAQ;AAAA,IACjE,cAAc,GAAG,MAAM,QAAQ,aAAa,KAAK;AAAA,EACnD,IAAI;AAAA,IACF,aAAa,GAAG,MAAM,QAAQ,aAAa,KAAK;AAAA,EAClD,CAAC;AACH;;;ACEA,IAAI,YAAY;AACT,IAAM,gBAAgB,OAAO;AAAA,EAClC,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,MAAM,CAAC,QAAQ,OAAO;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA,MAAM,kBAAU;AAAA,EAChB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA;AAAA,EAET,iBAAiB,WAAW;AAC9B;AACA,IAAO,mBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,cAAc;AAAA,EACrB,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,mBAAmB;AACpC,UAAM,YAAY,WAAW;AAC7B,UAAM,MAAM,OAAO,SAAS,MAAM,QAAQ,WAAW,OAAO,SAAS,MAAM,GAAG,IAAI,SAAS,MAAM;AACjG,uBAAW,OAAO,SAAS,MAAM,QAAQ,UAAU,YAAY,oBAAoB,OAAO,GAAG,CAAC,6BAA6B;AAC3H,UAAM,WAAW,aAAa,EAAE,SAAS,OAAO,GAAG;AACnD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,iBAAiB;AACrB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,cAAc;AAClB,UAAM,aAAa,oBAAoB;AACvC,UAAM,WAAW,WAAW,KAAK;AACjC,UAAM,WAAW,SAAS,MAAM;AAC9B,aAAO,CAAC,GAAG,WAAW,OAAO,GAAG;AAAA,IAClC,CAAC;AAED,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,IACV;AACA,qBAAiB,UAAU,QAAQ;AACnC,oBAAgB,MAAM;AACpB,yBAAmB,QAAQ;AAAA,IAC7B,CAAC;AACD,UAAM,YAAY,MAAM;AACtB,eAAS,QAAQ,CAAC,CAAC,WAAW,MAAM,KAAK,SAAO,QAAQ,GAAG;AAAA,IAC7D,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AACD,UAAM,iBAAiB,SAAS,MAAM,SAAS,SAAS,MAAM,QAAQ;AACtE,UAAM,WAAW,SAAS,MAAM,aAAa,MAAM,SAAS,GAAG,CAAC;AAChE,UAAM,aAAa,SAAS,MAAM;AAChC,YAAM,UAAU,GAAG,UAAU,KAAK;AAClC,aAAO;AAAA,QACL,CAAC,GAAG,OAAO,EAAE,GAAG;AAAA,QAChB,CAAC,GAAG,OAAO,SAAS,GAAG,MAAM;AAAA,QAC7B,CAAC,GAAG,OAAO,SAAS,GAAG,SAAS;AAAA,QAChC,CAAC,GAAG,OAAO,WAAW,GAAG,SAAS;AAAA,QAClC,CAAC,GAAG,OAAO,WAAW,GAAG,eAAe;AAAA,MAC1C;AAAA,IACF,CAAC;AACD,UAAM,eAAe,OAAK;AACxB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,SAAS,SAAS;AAAA,QAClB,cAAc,CAAC,GAAG,gBAAgB,OAAO,QAAQ;AAAA,QACjD,UAAU;AAAA,QACV,MAAM,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,KAAK;AAAA,MAC3C;AAAA,IACF;AAEA,UAAM,kBAAkB,OAAK;AAC3B,UAAI,eAAe,OAAO;AACxB;AAAA,MACF;AACA,YAAM,OAAO,aAAa,CAAC;AAC3B,WAAK,SAAS,CAAC;AACf,kBAAY,IAAI;AAAA,IAClB;AACA,UAAM,eAAe,WAAS;AAC5B,UAAI,CAAC,eAAe,OAAO;AACzB,yBAAiB,SAAS,KAAK;AAC/B,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,UAAM,eAAe,WAAS;AAC5B,UAAI,CAAC,eAAe,OAAO;AACzB,yBAAiB,CAAC,CAAC;AACnB,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,UAAM,oBAAoB,OAAK;AAC7B,WAAK,WAAW,CAAC;AACjB,UAAI,EAAE,UAAU,gBAAQ,OAAO;AAC7B,cAAM,OAAO,aAAa,CAAC;AAE3B,aAAK,SAAS,CAAC;AACf,oBAAY,IAAI;AAAA,MAClB;AAAA,IACF;AAKA,UAAM,kBAAkB,OAAK;AAC3B,uBAAiB,SAAS,KAAK;AAC/B,WAAK,SAAS,CAAC;AAAA,IACjB;AACA,UAAM,qBAAqB,CAAC,MAAM,aAAa;AAC7C,YAAM,WAAW,YAAa,QAAQ;AAAA,QACpC,SAAS,GAAG,UAAU,KAAK;AAAA,MAC7B,GAAG,CAAC,QAAQ,CAAC;AAGb,UAAI,CAAC,QAAQ,eAAe,QAAQ,KAAK,SAAS,SAAS,QAAQ;AACjE,YAAI,YAAY,gBAAgB,SAAS,cAAc,OAAO,aAAa,UAAU;AACnF,iBAAO,YAAa,OAAO;AAAA,YACzB,SAAS,GAAG,UAAU,KAAK;AAAA,UAC7B,GAAG,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC;AAAA,QACzB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,UAAM,iBAAiB,kBAAkB,SAAS,MAAM,SAAS,MAAM,MAAM,CAAC;AAC9E,WAAO,MAAM;AACX,UAAI,IAAI,IAAI,IAAI,IAAI;AACpB,UAAI,UAAW,QAAO;AACtB,YAAM,SAAS,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,MAAM,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AACvI,YAAM,WAAW,iBAAiB,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC;AACzG,YAAM,iBAAiB,SAAS;AAChC,UAAI,eAAe;AACnB,UAAI,OAAO,UAAU,aAAa;AAChC,uBAAe,cAAc,iBAAiB,WAAW;AAAA,MAC3D,WAAW,UAAU,OAAO;AAC1B,uBAAe;AAAA,MACjB;AACA,YAAMC,gBAAe;AAAA,QACnB,OAAO;AAAA,MACT;AACA,UAAI,CAAC,eAAe,SAAS,CAAC,gBAAgB,OAAO;AACnD,QAAAA,cAAa,QAAQ;AAGrB,QAAAA,cAAa,OAAO;AAAA,MACtB;AAEA,YAAM,kBAAkB,CAAC;AACzB,UAAI,MAAM,SAAS,UAAU;AAC3B,wBAAgB,eAAe,IAAI,SAAS;AAAA,MAC9C;AACA,YAAM,QAAQ,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,MAAM,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAO,KAAK;AAC3I,aAAO,YAAa,iBAAS,eAAc,eAAc,CAAC,GAAGA,aAAY,GAAG,CAAC,GAAG;AAAA,QAC9E,aAAa,IAAI,QAAQ,SAAS;AAAA,QAClC,oBAAoB,GAAG,UAAU,KAAK;AAAA,MACxC,CAAC,GAAG;AAAA,QACF,SAAS,MAAM,CAAC,YAAa,oBAAS,MAAM,eAAc,eAAc,eAAc;AAAA,UACpF,aAAa;AAAA,QACf,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,UACb,MAAM,MAAM;AAAA,UACZ,SAAS,SAAS,SAAS,CAAC,GAAG,MAAM,SAAS,CAAC,CAAC,GAAG,eAAe,KAAK;AAAA,UACvE,SAAS,CAAC,WAAW,OAAO;AAAA,YAC1B,CAAC,GAAG,MAAM,KAAK,EAAE,GAAG,CAAC,CAAC,MAAM;AAAA,YAC5B,CAAC,GAAG,UAAU,KAAK,kBAAkB,IAAI,OAAO,iBAAiB,IAAI,oBAAoB;AAAA,UAC3F,CAAC;AAAA,UACD,QAAQ,MAAM,QAAQ;AAAA,UACtB,YAAY,MAAM,WAAW,OAAO;AAAA,UACpC,gBAAgB;AAAA,UAChB,iBAAiB,MAAM;AAAA,QACzB,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,UACvB,gBAAgB;AAAA,UAChB,gBAAgB;AAAA,UAChB,WAAW;AAAA,UACX,aAAa;AAAA,UACb,WAAW;AAAA,UACX,SAAS,OAAO,UAAU,WAAW,QAAQ;AAAA,QAC/C,CAAC,GAAG;AAAA,UACF,SAAS,MAAM,CAAC,aAAa,OAAO,SAAS,aAAa,KAAK,MAAM,eAAe,IAAI,MAAM;AAAA,YAC5F,OAAO,GAAG,UAAU,KAAK;AAAA,UAC3B,GAAG,KAAK,GAAG,mBAAmB,MAAM,QAAQ,CAAC;AAAA,QAC/C,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACpOD,IAAMC,sBAAqB;AAAA,EACzB,SAAS;AAAA,EACT,SAAS;AACX;AACO,IAAMC,cAAa;AAAA,EACxB,SAAS;AAAA,IACP,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAUD;AAAA,IACV,QAAQ,CAAC,GAAG,EAAE;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAUA;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAUA;AAAA,IACV,QAAQ,CAAC,IAAI,CAAC;AAAA,EAChB;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAUA;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,EACf;AACF;AACO,IAAM,gBAAgB;AAAA,EAC3B,SAAS;AAAA,IACP,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAUA;AAAA,IACV,QAAQ,CAAC,GAAG,EAAE;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAUA;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAUA;AAAA,IACV,QAAQ,CAAC,IAAI,CAAC;AAAA,EAChB;AAAA,EACA,SAAS;AAAA,IACP,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAUA;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,EACf;AACF;;;ACtCA,IAAM,oBAAoB;AAAA,EACxB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,kBAAkB;AACpB;AACA,IAAO,uBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,WAAW;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,IAET,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,iBAAiB;AAAA,EACnB;AAAA,EACA,OAAO;AAAA,EACP,OAAO,CAAC,eAAe;AAAA,EACvB,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,WAAW,KAAK;AACrC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,cAAc;AAClB,UAAM,cAAc,qBAAqB;AACzC,UAAM,YAAY,SAAS,MAAM,IAAI,QAAQ,SAAS,SAAS,CAAC,GAAG,aAAa,GAAG,kBAAkB,KAAK,IAAI,SAAS,SAAS,CAAC,GAAGE,WAAU,GAAG,kBAAkB,KAAK,CAAC;AACzK,UAAM,iBAAiB,SAAS,MAAM,kBAAkB,MAAM,IAAI,CAAC;AACnE,UAAM,aAAa,WAAW;AAC9B,UAAM,MAAM,MAAM,SAAS,aAAW;AACpC,iBAAI,OAAO,WAAW,KAAK;AAC3B,iBAAW,QAAQ,WAAI,MAAM;AAC3B,qBAAa,QAAQ;AAAA,MACvB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AACD,oBAAgB,MAAM;AACpB,iBAAI,OAAO,WAAW,KAAK;AAAA,IAC7B,CAAC;AACD,UAAM,kBAAkB,aAAW;AACjC,WAAK,iBAAiB,OAAO;AAAA,IAC/B;AACA,UAAM,eAAe,SAAS,MAAM;AAClC,UAAI,IAAI;AACR,YAAM,IAAI,OAAO,WAAW,KAAK,eAAe,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,IAAI,QAAQ,KAAK,eAAe,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AACpL,YAAM,MAAM,OAAO,MAAM,aAAa,EAAE,IAAI;AAC5C,aAAO,MAAM,mBAAmB,IAAI,MAAM;AAAA,QACxC,KAAK;AAAA,MACP,CAAC,IAAI;AAAA,IACP,CAAC;AACD,WAAO,MAAM;AACX,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,aAAO,YAAa,oBAAS;AAAA,QAC3B,aAAa;AAAA,QACb,kBAAkB,mBAAW,GAAG,SAAS,UAAU;AAAA,UACjD,CAAC,GAAG,SAAS,MAAM,GAAG,IAAI;AAAA,QAC5B,GAAG,gBAAgB,cAAc,KAAK;AAAA,QACtC,WAAW,SAAS,eAAe,aAAa;AAAA,QAChD,qBAAqB,kBAAkB;AAAA,QACvC,qBAAqB,UAAU;AAAA,QAC/B,kBAAkB,eAAe;AAAA,QACjC,gBAAgB,aAAa;AAAA,QAC7B,cAAc,eAAe;AAAA,UAC3B,QAAQ;AAAA,QACV;AAAA,QACA,UAAU,WAAW,CAAC,IAAI,CAAC,qBAAqB,KAAK;AAAA,QACrD,mBAAmB,iBAAiB;AAAA,QACpC,mBAAmB,kBAAkB;AAAA,QACrC,wBAAwB;AAAA,QACxB,eAAe,eAAe,mBAAmB;AAAA,QACjD,kBAAkB,aAAa;AAAA,MACjC,GAAG;AAAA,QACD,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AC1GD,IAAM,sBAAsB,CAAC,QAAQ,SAAS;AAC5C,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc;AAClB,SAAO,YAAa,MAAM,eAAc,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,IACpE,SAAS,mBAAW,UAAU,OAAO,GAAG,UAAU,KAAK,QAAQ,GAAG,UAAU,KAAK,IAAI,KAAK,UAAU,WAAW,WAAW,UAAU,EAAE;AAAA,IACtI,kBAAkB;AAAA,EACpB,CAAC,GAAG,EAAE,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC,CAAC;AAChF;AACA,oBAAoB,cAAc;AAClC,IAAO,sBAAQ;;;ACff,IAAO,4BAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,UAAM,YAAY,SAAS,MAAM,QAAQ;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,cAAc;AAClB,UAAM,cAAc,SAAS,MAAM,KAAK,UAAU,UAAU,KAAK;AACjE,UAAM,UAAU,IAAI,CAAC,YAAY,KAAK;AACtC,UAAM,aAAa,SAAS,MAAM,YAAY,QAAQ,MAAM,OAAO,KAAK;AAGxE,UAAM,MAAM,MAAM;AAChB,UAAI,YAAY,OAAO;AACrB,gBAAQ,QAAQ;AAAA,MAClB;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,IACT,CAAC;AACD,UAAM,eAAe,SAAS,MAAM;AAClC,UAAI,IAAI;AACR,YAAM,IAAI,OAAO,WAAW,KAAK,eAAe,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,KAAK,QAAQ,KAAK,eAAe,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AACzL,YAAM,MAAM,OAAO,MAAM,aAAa,EAAE,IAAI;AAC5C,aAAO,SAAS,SAAS,CAAC,GAAG,GAAG,GAAG;AAAA,QACjC,QAAQ,MAAM,QAAQ,UAAU;AAAA,MAClC,CAAC;AAAA,IACH,CAAC;AACD,WAAO,MAAM;AACX,UAAI;AACJ,UAAI,QAAQ,OAAO;AACjB,eAAO;AAAA,MACT;AACA,aAAO,YAAa,qBAAqB;AAAA,QACvC,QAAQ,UAAU;AAAA,MACpB,GAAG;AAAA,QACD,SAAS,MAAM,CAAC,YAAa,YAAY,aAAa,OAAO;AAAA,UAC3D,SAAS,MAAM,CAAC,eAAgB,YAAa,qBAAa;AAAA,YACxD,MAAM,MAAM;AAAA,UACd,GAAG;AAAA,YACD,SAAS,MAAM,EAAE,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC;AAAA,UAC1F,CAAC,GAAG,CAAC,CAAC,OAAQ,WAAW,KAAK,CAAC,CAAC,CAAC;AAAA,QACnC,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AC9CD,IAAIC,aAAY;AACT,IAAM,eAAe,OAAO;AAAA,EACjC,MAAM,kBAAU;AAAA,EAChB,OAAO,kBAAU;AAAA,EACjB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA;AAAA,EAEd,iBAAiB,WAAW;AAC9B;AACA,IAAO,kBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,aAAa;AAAA,EACpB,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,IAAI;AACR,yBAAqB,KAAK;AAC1B,UAAM,YAAY,WAAW;AAC7B,UAAM,WAAW,mBAAmB;AACpC,UAAM,WAAW,OAAO,SAAS,MAAM,QAAQ,WAAW,OAAO,SAAS,MAAM,GAAG,IAAI,SAAS,MAAM;AACtG,uBAAW,OAAO,SAAS,MAAM,QAAQ,UAAU,WAAW,mBAAmB,OAAO,QAAQ,CAAC,6BAA6B;AAC9H,UAAM,MAAM,gBAAQ,QAAQ,IAAI,WAAW,YAAY,EAAEA,UAAS;AAClE,UAAM,YAAY,KAAK,MAAM,cAAc,QAAQ,OAAO,SAAS,KAAK,gBAAQ,QAAQ,IAAI,YAAY,EAAEA,UAAS,OAAO,QAAQ,KAAK;AACvI,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,iBAAiB;AACrB,UAAM,WAAW,SAAS,MAAM,CAAC,GAAG,WAAW,OAAO,GAAG,CAAC;AAC1D,UAAM,oBAAoB,WAAW,CAAC,CAAC;AACvC,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,KAAC,KAAK,WAAW,uBAAuB,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,KAAK,QAAQ;AAC/F,oBAAgB,MAAM;AACpB,UAAIC;AACJ,UAAI,WAAW,mBAAmB;AAChC,mBAAW,kBAAkB,SAASA,MAAK,WAAW,uBAAuB,QAAQA,QAAO,SAAS,SAASA,IAAG,MAAM,OAAO,OAAK,KAAK,QAAQ;AAAA,MAClJ;AAAA,IACF,CAAC;AACD,uBAAkB,UAAU,KAAK,QAAQ;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,IACF,IAAI,cAAc;AAClB,UAAM,SAAS,aAAa,UAAa,aAAa;AAGtD,UAAM,cAAc,CAAC,cAAc,qBAAqB,KAAK,CAAC;AAC9D,0BAAsB,WAAW;AACjC,QAAI,aAAa,UAAU,CAAC,aAAa,CAAC,UAAU,aAAa;AAC/D,uBAAiB,UAAU,QAAQ;AACnC,sBAAgB,MAAM;AACpB,2BAAmB,QAAQ;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,UAAM,mBAAmB,SAAS,MAAM,GAAG,UAAU,KAAK,UAAU;AACpE,UAAM,iBAAiB,SAAS,MAAM,gBAAgB,SAAS,MAAM,QAAQ;AAC7E,UAAM,aAAa,WAAW;AAC9B,UAAM,WAAW,WAAW;AAK5B,UAAM,aAAa,SAAS,MAAM,SAAS,MAAM,SAAS,GAAG,CAAC;AAC9D,UAAM,OAAO,SAAS,MAAM,CAAC,iBAAiB,SAAS,WAAW,KAAK;AAEvE,UAAM,mBAAmB,SAAS,MAAM;AACtC,aAAO,oBAAoB,MAAM,SAAS,GAAG;AAAA,IAC/C,CAAC;AACD,UAAM,WAAW,WAAW,KAAK;AACjC,UAAM,YAAY,MAAM;AACtB,eAAS,QAAQ,CAAC,CAAC,WAAW,MAAM,KAAK,SAAO,QAAQ,GAAG;AAAA,IAC7D,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AAGD,UAAM,uBAAuB,OAAK;AAEhC,UAAI,eAAe,OAAO;AACxB;AAAA,MACF;AACA,WAAK,cAAc,GAAG,GAAG;AAEzB,UAAI,KAAK,UAAU,UAAU;AAC3B,qBAAa,KAAK,CAAC,WAAW,KAAK;AAAA,MACrC;AAAA,IACF;AACA,UAAM,eAAe,WAAS;AAC5B,UAAI,CAAC,eAAe,OAAO;AACzB,yBAAiB,SAAS,KAAK;AAC/B,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,UAAM,eAAe,WAAS;AAC5B,UAAI,CAAC,eAAe,OAAO;AACzB,yBAAiB,CAAC,CAAC;AACnB,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF;AAEA,UAAM,iBAAiB,kBAAkB,SAAS,MAAM,SAAS,MAAM,MAAM,CAAC;AAE9E,UAAM,uBAAuB,gBAAc;AACzC,UAAI,KAAK,UAAU,UAAU;AAC3B,qBAAa,KAAK,UAAU;AAAA,MAC9B;AAAA,IACF;AAKA,UAAM,kBAAkB,MAAM;AAC5B,uBAAiB,SAAS,KAAK;AAAA,IACjC;AAEA,UAAM,UAAU,YAAY,GAAG,QAAQ;AACvC,UAAM,iBAAiB,SAAS,MAAM,mBAAW,UAAU,OAAO,GAAG,UAAU,KAAK,IAAI,MAAM,SAAS,MAAM,KAAK,IAAI,MAAM,cAAc,CAAC;AAC3I,UAAM,cAAc,CAAC,OAAO,SAAS;AACnC,UAAI,CAAC,MAAM;AACT,eAAO,gBAAgB,SAAS,CAAC,WAAW,MAAM,UAAU,SAAS,OAAO,UAAU,WAAW,YAAa,OAAO;AAAA,UACnH,SAAS,GAAG,UAAU,KAAK;AAAA,QAC7B,GAAG,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,IAAI,YAAa,QAAQ;AAAA,UAC3C,SAAS,GAAG,UAAU,KAAK;AAAA,QAC7B,GAAG,CAAC,KAAK,CAAC;AAAA,MACZ;AAGA,YAAM,cAAc,eAAe,KAAK,KAAK,MAAM,SAAS;AAC5D,aAAO,YAAa,UAAW,MAAM,CAAC,aAAa,OAAO,SAAS,aAAa,KAAK,MAAM,eAAe,IAAI,MAAM;AAAA,QAClH,OAAO,GAAG,UAAU,KAAK;AAAA,MAC3B,GAAG,KAAK,GAAG,cAAc,QAAQ,YAAa,QAAQ;AAAA,QACpD,SAAS,GAAG,UAAU,KAAK;AAAA,MAC7B,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AAAA,IACd;AAEA,UAAM,iBAAiB,SAAS,MAAM;AACpC,aAAO,KAAK,UAAU,YAAY,SAAS,MAAM,SAAS,IAAI,aAAa,KAAK;AAAA,IAClF,CAAC;AACD,UAAM,aAAa,SAAS,MAAM,KAAK,UAAU,eAAe,aAAa,KAAK,KAAK;AACvF,UAAM,wBAAwB,SAAS,MAAM,eAAe,UAAU,eAAe,aAAa,eAAe,KAAK;AACtH,UAAM,gBAAgB,MAAM;AAC1B,UAAIA,KAAIC;AACR,YAAM,wBAAwB,iBAAiB;AAC/C,YAAM,QAAQD,MAAK,MAAM,UAAU,QAAQA,QAAO,SAASA,OAAMC,MAAK,MAAM,UAAU,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,OAAO,KAAK;AAC3I,YAAM,aAAa,MAAM,cAAc,MAAM,cAAc,eAAe;AAC1E,YAAM,QAAQ,YAAY,aAAa,OAAO,OAAO,OAAO,GAAG,IAAI;AACnE,aAAO,YAAa,OAAO;AAAA,QACzB,SAAS,eAAe;AAAA,QACxB,SAAS,GAAG,qBAAqB;AAAA,QACjC,YAAY,eAAe,QAAQ,OAAO;AAAA,QAC1C,OAAO;AAAA,QACP,SAAS,OAAO,UAAU,WAAW,QAAQ;AAAA,QAC7C,gBAAgB;AAAA,QAChB,iBAAiB,KAAK;AAAA,QACtB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB,eAAe;AAAA,QAChC,WAAW;AAAA,QACX,WAAW;AAAA,MACb,GAAG,CAAC,OAAO,KAAK,UAAU,gBAAgB,aAAa,WAAW,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,QAC9F,QAAQ,KAAK;AAAA,MACf,CAAC,CAAC,IAAI,YAAa,KAAK;AAAA,QACtB,SAAS,GAAG,qBAAqB;AAAA,MACnC,GAAG,IAAI,CAAC,CAAC;AAAA,IACX;AACA,WAAO,MAAM;AACX,UAAID;AACJ,UAAI,WAAW;AACb,YAAI,CAAC,QAAQ;AACX,iBAAO;AAAA,QACT;AACA,gBAAQA,MAAK,MAAM,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,KAAK;AAAA,MAChF;AACA,YAAM,wBAAwB,iBAAiB;AAC/C,UAAI,YAAY,MAAM;AACtB,UAAI,CAAC,iBAAiB,SAAS,KAAK,UAAU,UAAU;AACtD,cAAM,cAAc,KAAK,UAAU,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AACjE,oBAAY,MAAM,YAAa,sBAAc;AAAA,UAC3C,QAAQ,eAAe;AAAA,UACvB,aAAa;AAAA,UACb,WAAW,CAAC,MAAM,sBAAsB,KAAK;AAAA,UAC7C,kBAAkB,eAAe;AAAA,UACjC,eAAe,MAAM,eAAe;AAAA,UACpC,YAAY,eAAe;AAAA,UAC3B,mBAAmB;AAAA,QACrB,GAAG;AAAA,UACD,SAAS,MAAM,CAAC,cAAc,CAAC;AAAA,UAC/B,OAAO,MAAM,YAAa,qBAAqB;AAAA,YAC7C,QAAQ,sBAAsB;AAAA,UAChC,GAAG;AAAA,YACD,SAAS,MAAM,CAAC,YAAa,qBAAa;AAAA,cACxC,MAAM;AAAA,cACN,OAAO;AAAA,YACT,GAAG;AAAA,cACD,SAAS,MAAM;AAAA,YACjB,CAAC,CAAC;AAAA,UACJ,CAAC;AAAA,QACH,CAAC;AAAA,MACH,OAAO;AAGL,oBAAY,MAAM,YAAa,sBAAc,MAAM;AAAA,UACjD,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,aAAO,YAAa,qBAAqB;AAAA,QACvC,QAAQ,WAAW;AAAA,MACrB,GAAG;AAAA,QACD,SAAS,MAAM,CAAC,YAAa,oBAAS,MAAM,eAAc,eAAc;AAAA,UACtE,aAAa;AAAA,QACf,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,UACb,QAAQ;AAAA,UACR,SAAS,mBAAW,uBAAuB,GAAG,qBAAqB,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO;AAAA,YAChG,CAAC,GAAG,qBAAqB,OAAO,GAAG,KAAK;AAAA,YACxC,CAAC,GAAG,qBAAqB,SAAS,GAAG,SAAS;AAAA,YAC9C,CAAC,GAAG,qBAAqB,WAAW,GAAG,iBAAiB;AAAA,YACxD,CAAC,GAAG,qBAAqB,WAAW,GAAG,eAAe;AAAA,UACxD,CAAC;AAAA,UACD,gBAAgB;AAAA,UAChB,gBAAgB;AAAA,UAChB,mBAAmB;AAAA,QACrB,CAAC,GAAG;AAAA,UACF,SAAS,MAAM;AACb,mBAAO,YAAa,UAAW,MAAM,CAAC,UAAU,GAAG,CAAC,iBAAiB,SAAS,YAAa,2BAAmB;AAAA,cAC5G,MAAM;AAAA,cACN,QAAQ,KAAK;AAAA,cACb,WAAW,SAAS;AAAA,YACtB,GAAG;AAAA,cACD,SAAS,MAAM;AAAA,YACjB,CAAC,CAAC,CAAC;AAAA,UACL;AAAA,QACF,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AChSM,SAAS,SAAS,MAAM,WAAW;AACxC,MAAI,KAAK,WAAW;AAClB,WAAO,KAAK,UAAU,SAAS,SAAS;AAAA,EAC1C;AACA,QAAM,cAAc,KAAK;AACzB,SAAO,IAAI,WAAW,IAAI,QAAQ,IAAI,SAAS,GAAG,IAAI;AACxD;AACO,SAAS,SAAS,MAAM,WAAW;AACxC,MAAI,KAAK,WAAW;AAClB,SAAK,UAAU,IAAI,SAAS;AAAA,EAC9B,OAAO;AACL,QAAI,CAAC,SAAS,MAAM,SAAS,GAAG;AAC9B,WAAK,YAAY,GAAG,KAAK,SAAS,IAAI,SAAS;AAAA,IACjD;AAAA,EACF;AACF;AACO,SAAS,YAAY,MAAM,WAAW;AAC3C,MAAI,KAAK,WAAW;AAClB,SAAK,UAAU,OAAO,SAAS;AAAA,EACjC,OAAO;AACL,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,YAAM,cAAc,KAAK;AACzB,WAAK,YAAY,IAAI,WAAW,IAAI,QAAQ,IAAI,SAAS,KAAK,GAAG;AAAA,IACnE;AAAA,EACF;AACF;;;ACvBA,IAAM,iBAAiB,WAAY;AACjC,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC/E,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,eAAe,UAAQ;AACrB,WAAK,MAAM,SAAS;AACpB,WAAK,MAAM,UAAU;AACrB,eAAS,MAAM,IAAI;AAAA,IACrB;AAAA,IACA,SAAS,UAAQ;AACf,eAAS,MAAM;AACb,aAAK,MAAM,SAAS,GAAG,KAAK,YAAY;AACxC,aAAK,MAAM,UAAU;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,IACA,cAAc,UAAQ;AACpB,UAAI,MAAM;AACR,oBAAY,MAAM,IAAI;AACtB,aAAK,MAAM,SAAS;AACpB,aAAK,MAAM,UAAU;AAAA,MACvB;AAAA,IACF;AAAA,IACA,eAAe,UAAQ;AACrB,eAAS,MAAM,IAAI;AACnB,WAAK,MAAM,SAAS,GAAG,KAAK,YAAY;AACxC,WAAK,MAAM,UAAU;AAAA,IACvB;AAAA,IACA,SAAS,UAAQ;AACf,iBAAW,MAAM;AACf,aAAK,MAAM,SAAS;AACpB,aAAK,MAAM,UAAU;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,IACA,cAAc,UAAQ;AACpB,UAAI,MAAM;AACR,oBAAY,MAAM,IAAI;AACtB,YAAI,KAAK,OAAO;AACd,eAAK,MAAM,SAAS;AACpB,eAAK,MAAM,UAAU;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,yBAAQ;;;ACzCR,IAAM,qBAAqB,OAAO;AAAA,EACvC,OAAO,kBAAU;AAAA;AAAA,EAEjB,iBAAiB,WAAW;AAC9B;AACA,IAAO,oBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,mBAAmB;AAAA,EAC1B,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc;AAClB,UAAM,iBAAiB,SAAS,MAAM,GAAG,UAAU,KAAK,aAAa;AACrE,UAAM,YAAY,WAAW;AAC7B,WAAO,MAAM;AACX,UAAI,IAAI;AACR,UAAI,UAAW,SAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAC7F,aAAO,YAAa,MAAM,eAAc,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACpE,WAAW,OAAK,EAAE,gBAAgB;AAAA,QAClC,SAAS,eAAe;AAAA,MAC1B,CAAC,GAAG,CAAC,YAAa,OAAO;AAAA,QACvB,SAAS,OAAO,MAAM,UAAU,WAAW,MAAM,QAAQ;AAAA,QACzD,SAAS,GAAG,eAAe,KAAK;AAAA,MAClC,GAAG,CAAC,aAAa,OAAO,OAAO,OAAO,CAAC,CAAC,GAAG,YAAa,MAAM;AAAA,QAC5D,SAAS,GAAG,eAAe,KAAK;AAAA,MAClC,GAAG,EAAE,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;AAAA,IACjF;AAAA,EACF;AACF,CAAC;;;AC1CM,IAAM,mBAAmB,OAAO;AAAA,EACrC,WAAW;AAAA,EACX,QAAQ;AACV;AACA,IAAO,kBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,OAAO,iBAAiB;AAAA,EACxB,MAAM,OAAO;AACX,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc;AAClB,UAAM,MAAM,SAAS,MAAM;AACzB,aAAO;AAAA,QACL,CAAC,GAAG,UAAU,KAAK,eAAe,GAAG;AAAA,QACrC,CAAC,GAAG,UAAU,KAAK,sBAAsB,GAAG,CAAC,CAAC,MAAM;AAAA,MACtD;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AACX,aAAO,YAAa,MAAM;AAAA,QACxB,SAAS,IAAI;AAAA,MACf,GAAG,IAAI;AAAA,IACT;AAAA,EACF;AACF,CAAC;;;AC3BD,IAAIE,UAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAMA,SAAS,oBAAoB,MAAM,OAAO,gBAAgB;AACxD,UAAQ,QAAQ,CAAC,GAAG,IAAI,CAAC,KAAK,UAAU;AACtC,QAAI,OAAO,OAAO,QAAQ,UAAU;AAClC,YAAM,KAAK,KACT;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,IACJ,YAAYA,QAAO,IAAI,CAAC,SAAS,YAAY,OAAO,MAAM,CAAC;AAC7D,YAAM,YAAY,QAAQ,QAAQ,QAAQ,SAAS,MAAM,OAAO,KAAK;AAErE,YAAM,aAAa,iBAAiB,eAAe,WAAW,MAAM,IAAI,CAAC;AACzE,YAAM,oBAAoB,CAAC;AAE3B,YAAM,WAAW;AAAA,QACf,UAAU;AAAA,QACV,KAAK;AAAA,QACL,iBAAiB,IAAI,UAAU;AAAA,QAC/B,YAAY,IAAI,UAAU;AAAA,QAC1B,mBAAmB,IAAI,iBAAiB;AAAA,QACxC,QAAQ;AAAA,MACV;AAEA,UAAI,YAAY,SAAS,SAAS;AAChC,YAAI,SAAS,SAAS;AACpB,gBAAMC,iBAAgB,oBAAoB,UAAU,OAAO,cAAc;AAEzE,iBAAO,YAAa,mBAAW,eAAc,eAAc;AAAA,YACzD,OAAO;AAAA,UACT,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,YACjB,SAAS;AAAA,YACT,mBAAmB;AAAA,UACrB,CAAC,GAAG;AAAA,YACF,SAAS,MAAM,CAACA,cAAa;AAAA,UAC/B,CAAC;AAAA,QACH;AACA,cAAM,IAAI,WAAW,QAAQ;AAC7B,YAAI,gBAAgB;AAClB,yBAAe,kBAAkB,KAAK,SAAS;AAAA,QACjD;AAEA,cAAM,gBAAgB,oBAAoB,UAAU,OAAO;AAAA,UACzD;AAAA,UACA,YAAY,CAAC,EAAE,OAAO,YAAY,SAAS;AAAA,QAC7C,CAAC;AACD,eAAO,YAAa,iBAAS,eAAc,eAAc;AAAA,UACvD,OAAO;AAAA,QACT,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,UACjB,SAAS;AAAA,UACT,mBAAmB;AAAA,QACrB,CAAC,GAAG;AAAA,UACF,SAAS,MAAM,CAAC,aAAa;AAAA,QAC/B,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,WAAW;AACtB,eAAO,YAAa,iBAAa,eAAc;AAAA,UAC7C,OAAO;AAAA,QACT,GAAG,SAAS,GAAG,IAAI;AAAA,MACrB;AACA,eAAS,SAAS;AAClB,YAAM,IAAI,WAAW,QAAQ;AAC7B,aAAO,YAAa,kBAAU,eAAc,eAAc;AAAA,QACxD,OAAO;AAAA,MACT,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,QACjB,mBAAmB;AAAA,MACrB,CAAC,GAAG;AAAA,QACF,SAAS,MAAM,CAAC,KAAK;AAAA,MACvB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC,EAAE,OAAO,SAAO,GAAG;AACtB;AAMe,SAAR,SAA0B,OAAO;AACtC,QAAM,aAAa,WAAW,CAAC,CAAC;AAChC,QAAM,WAAW,WAAW,KAAK;AACjC,QAAM,QAAQ,WAAW,oBAAI,IAAI,CAAC;AAClC,QAAM,MAAM,MAAM,OAAO,MAAM;AAC7B,UAAM,WAAW,oBAAI,IAAI;AACzB,aAAS,QAAQ;AACjB,QAAI,MAAM,OAAO;AACf,eAAS,QAAQ;AACjB,iBAAW,QAAQ,oBAAoB,MAAM,OAAO,QAAQ;AAAA,IAC9D,OAAO;AACL,iBAAW,QAAQ;AAAA,IACrB;AACA,UAAM,QAAQ;AAAA,EAChB,GAAG;AAAA,IACD,WAAW;AAAA,IACX,MAAM;AAAA,EACR,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACtHA,IAAM,qBAAqB,WAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,GAAG,YAAY,aAAa,GAAG;AAAA,MAC9B,YAAY,GAAG,oBAAoB;AAAA,MACnC,QAAQ;AAAA,MACR,cAAc,GAAG,SAAS,MAAM,QAAQ,IAAI,UAAU;AAAA,MACtD,WAAW;AAAA,MACX,YAAY;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA;AAAA,MAEA,CAAC,GAAG,YAAY,UAAU,YAAY,UAAU,GAAG;AAAA,QACjD,UAAU;AAAA,QACV,SAAS;AAAA,QACT,eAAe;AAAA,QACf,eAAe;AAAA,MACjB;AAAA,MACA,CAAC,KAAK,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ,YAAY,YAAY,YAAY,sBAAsB,GAAG;AAAA,QACjE,iBAAiB;AAAA,MACnB;AAAA,MACA,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG;AAAA,QACvD,YAAY,CAAC,gBAAgB,kBAAkB,IAAI,cAAc,kBAAkB,EAAE,EAAE,KAAK,GAAG;AAAA,MACjG;AAAA;AAAA,MAEA,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,QACjC,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,qBAAQ;;;AC5Cf,IAAM,cAAc,UAAQ;AAC1B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,GAAG,YAAY,MAAM,GAAG;AAAA,MACvB,WAAW;AAAA,IACb;AAAA,IACA,CAAC,GAAG,YAAY,cAAc,GAAG;AAAA,MAC/B,iBAAiB;AAAA,IACnB;AAAA;AAAA,IAEA,CAAC,GAAG,YAAY,OAAO,YAAY;AAAA,MACjC,YAAY,gBAAgB,YAAY,WAAW,GAAG;AAAA,MACtD,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,QACjC,aAAa;AAAA,UACX,WAAW,8BAA8B,eAAe;AAAA,QAC1D;AAAA,QACA,YAAY;AAAA,UACV,WAAW,4BAA4B,eAAe;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,cAAQ;;;ACxBf,IAAM,qBAAqB,WAAS,SAAS,CAAC,GAAG,gBAAgB,KAAK,CAAC;AACvE,IAAM,gBAAgB,CAAC,OAAO,gBAAgB;AAC5C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,GAAG,YAAY,IAAI,WAAW,EAAE,GAAG;AAAA,MAClC,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,CAAC,IAAI,YAAY,qBAAqB,GAAG,SAAS,CAAC,GAAG,mBAAmB,KAAK,CAAC;AAAA;AAAA,MAE/E,CAAC,GAAG,YAAY,mBAAmB,GAAG;AAAA,QACpC,OAAO;AAAA,MACT;AAAA,MACA,CAAC,GAAG,YAAY,mBAAmB,GAAG;AAAA,QACpC,CAAC,KAAK,YAAY,gBAAgB,GAAG;AAAA,UACnC,OAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,GAAG,YAAY,mBAAmB,YAAY,mBAAmB,GAAG;AAAA,QACnE,OAAO,GAAG,qBAAqB;AAAA,MACjC;AAAA;AAAA,MAEA,CAAC,GAAG,YAAY,gBAAgB,YAAY,sBAAsB,GAAG;AAAA,QACnE,CAAC,SAAS,YAAY,uBAAuB,YAAY,oBAAoB,GAAG;AAAA,UAC9E,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,CAAC,SAAS,YAAY,cAAc,GAAG;AAAA,QACrC,CAAC,GAAG,YAAY,aAAa,YAAY,iBAAiB,GAAG;AAAA,UAC3D,WAAW;AAAA,YACT,iBAAiB;AAAA,UACnB;AAAA,UACA,YAAY;AAAA,YACV,iBAAiB;AAAA,UACnB;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,UACjC,WAAW;AAAA,YACT,iBAAiB;AAAA,UACnB;AAAA,UACA,YAAY;AAAA,YACV,iBAAiB;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,GAAG,YAAY,cAAc,GAAG;AAAA,QAC/B,OAAO;AAAA,QACP,CAAC,IAAI,YAAY,aAAa,GAAG;AAAA,UAC/B,CAAC,SAAS,YAAY,uBAAuB,YAAY,oBAAoB,GAAG;AAAA,YAC9E,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,CAAC,IAAI,YAAY,cAAc,GAAG;AAAA,UAChC,YAAY;AAAA,QACd;AAAA,MACF;AAAA,MACA,CAAC,GAAG,YAAY,SAAS,GAAG;AAAA,QAC1B,cAAc;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,QACjC,OAAO;AAAA;AAAA,QAEP,CAAC,IAAI,YAAY,cAAc,GAAG;AAAA,UAChC,OAAO;AAAA,QACT;AAAA,QACA,CAAC,YAAY,GAAG;AAAA,UACd,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,CAAC,KAAK,YAAY,gBAAgB,GAAG;AAAA,QACnC,iBAAiB;AAAA;AAAA,QAEjB,CAAC,IAAI,YAAY,cAAc,GAAG;AAAA,UAChC,iBAAiB;AAAA,QACnB;AAAA,MACF;AAAA,MACA,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG;AAAA,QACvD,CAAC,SAAS,YAAY,+BAA+B,GAAG,SAAS,CAAC,GAAG,mBAAmB,KAAK,CAAC;AAAA,MAChG;AAAA,MACA,CAAC,IAAI,YAAY,cAAc,YAAY,EAAE,GAAG;AAAA,QAC9C,iBAAiB;AAAA,MACnB;AAAA,MACA,CAAC,IAAI,YAAY,YAAY,YAAY,EAAE,GAAG;AAAA,QAC5C,iBAAiB;AAAA,MACnB;AAAA;AAAA,MAEA,CAAC,IAAI,YAAY,aAAa,GAAG,SAAS,SAAS,CAAC,GAAG,gBAAgB,SAAS;AAAA,QAC9E,cAAc;AAAA,MAChB,IAAI,CAAC,CAAC,GAAG;AAAA,QACP,CAAC,KAAK,YAAY,YAAY,YAAY,UAAU,GAAG;AAAA,UACrD,KAAK;AAAA,UACL,WAAW,CAAC;AAAA,UACZ,cAAc;AAAA,UACd,cAAc;AAAA,UACd,YAAY;AAAA,YACV,UAAU;AAAA,YACV,aAAa;AAAA,YACb,QAAQ;AAAA,YACR,cAAc,GAAG,oBAAoB;AAAA,YACrC,YAAY,gBAAgB,kBAAkB,IAAI,eAAe;AAAA,YACjE,SAAS;AAAA,UACX;AAAA,UACA,CAAC,2BAA2B,GAAG;AAAA,YAC7B,YAAY;AAAA,cACV,mBAAmB;AAAA,cACnB,mBAAmB;AAAA,YACrB;AAAA,UACF;AAAA,UACA,CAAC,YAAY,GAAG;AAAA,YACd,OAAO;AAAA,YACP,iBAAiB;AAAA,YACjB,YAAY;AAAA,cACV,mBAAmB;AAAA,cACnB,mBAAmB;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA;AAAA;AAAA,MAGD,CAAC,IAAI,YAAY,OAAO,GAAG;AAAA,QACzB,CAAC,IAAI,YAAY,aAAa,YAAY,WAAW,GAAG;AAAA,UACtD,iBAAiB,GAAG,wBAAwB,MAAM,QAAQ,IAAI,UAAU;AAAA,QAC1E;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,IAAI,YAAY,SAAS,GAAG;AAAA;AAAA,QAE3B,CAAC,GAAG,YAAY,OAAO,YAAY,SAAS,GAAG;AAAA,UAC7C,YAAY;AAAA,QACd;AAAA;AAAA,QAEA,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG,4BAA4B,sBAAsB;AAAA,UACzG,OAAO,eAAe,wBAAwB;AAAA,QAChD,IAAI,CAAC;AAAA,QACL,CAAC,GAAG,YAAY,OAAO,GAAG;AAAA,UACxB,UAAU;AAAA,UACV,YAAY;AAAA,YACV,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,gBAAgB;AAAA,YAChB,iBAAiB,GAAG,mBAAmB,YAAY,qBAAqB;AAAA,YACxE,WAAW;AAAA,YACX,SAAS;AAAA,YACT,YAAY,CAAC,aAAa,iBAAiB,IAAI,aAAa,IAAI,WAAW,iBAAiB,IAAI,aAAa,EAAE,EAAE,KAAK,GAAG;AAAA,YACzH,SAAS;AAAA,UACX;AAAA;AAAA,UAEA,CAAC,IAAI,YAAY,cAAc,GAAG;AAAA,YAChC,YAAY;AAAA,cACV,sBAAsB;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,cAAc,YAAY,gBAAgB,GAAG;AAAA,UAC3D,YAAY;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,YACT,YAAY,CAAC,aAAa,iBAAiB,IAAI,eAAe,IAAI,WAAW,iBAAiB,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAAA,UAC/H;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,gBAAQ;;;ACxMf,IAAM,yBAAyB,WAAS;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,mBAAmB,UAAU,gBAAgB;AACnD,SAAO;AAAA,IACL,CAAC,GAAG,YAAY,OAAO,GAAG;AAAA,MACxB,UAAU;AAAA,IACZ;AAAA,IACA,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG;AAAA,MACvD,QAAQ;AAAA,MACR,YAAY,GAAG,cAAc;AAAA,MAC7B,eAAe;AAAA,MACf,UAAU;AAAA,MACV,cAAc;AAAA,MACd,cAAc;AAAA,MACd,aAAa;AAAA,MACb,OAAO,eAAe,mBAAmB,CAAC;AAAA,IAC5C;AAAA;AAAA,IAEA,CAAC,GAAG,YAAY,UAAU,GAAG;AAAA,MAC3B,eAAe;AAAA,IACjB;AAAA,IACA,CAAC,KAAK,YAAY;AAAA,gBACN,YAAY,cAAc,YAAY,gBAAgB,GAAG;AAAA,MACnE,QAAQ;AAAA,MACR,YAAY,GAAG,cAAc;AAAA,IAC/B;AAAA,IACA,CAAC,GAAG,YAAY,oBAAoB,YAAY;AAAA,cACtC,YAAY,gBAAgB,GAAG;AAAA,MACvC,kBAAkB;AAAA,IACpB;AAAA,EACF;AACF;AACA,IAAM,mBAAmB,WAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB;AAAA,IACtB,QAAQ;AAAA,IACR,YAAY,GAAG,cAAc;AAAA,IAC7B,mBAAmB;AAAA,IACnB,eAAe;AAAA,EACjB;AACA,SAAO;AAAA,IAAC;AAAA,MACN,CAAC,YAAY,GAAG;AAAA,QACd,CAAC,sBAAsB,GAAG,SAAS;AAAA,UACjC,CAAC,IAAI,YAAY,OAAO,GAAG;AAAA,YACzB,WAAW;AAAA,UACb;AAAA,QACF,GAAG,uBAAuB,KAAK,CAAC;AAAA,MAClC;AAAA,MACA,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,QACjC,CAAC,GAAG,YAAY,WAAW,GAAG,SAAS,SAAS,CAAC,GAAG,uBAAuB,KAAK,CAAC,GAAG;AAAA,UAClF,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA,IAEA;AAAA,MACE,CAAC,GAAG,YAAY,kBAAkB,YAAY,YAAY,YAAY,MAAM,GAAG;AAAA,QAC7E,UAAU;AAAA,QACV,WAAW,gBAAgB,kBAAkB,GAAG;AAAA,QAChD,SAAS;AAAA,QACT,UAAU;AAAA,QACV,iBAAiB;AAAA;AAAA;AAAA,QAGjB,6BAA6B;AAAA,UAC3B,WAAW;AAAA,UACX,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA;AAAA,MACE,CAAC,GAAG,YAAY,SAAS,GAAG;AAAA,QAC1B,OAAO;AAAA;AAAA,QAEP,CAAC,IAAI,YAAY,OAAO,GAAG;AAAA,UACzB,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG;AAAA,YACvD,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,YAAY,CAAC,gBAAgB,kBAAkB,IAAI,cAAc,kBAAkB,IAAI,WAAW,iBAAiB,IAAI,aAAa,EAAE,EAAE,KAAK,GAAG;AAAA,YAChJ,CAAC,KAAK,YAAY,gBAAgB,GAAG;AAAA,cACnC,MAAM;AAAA,cACN,UAAU;AAAA,cACV,UAAU;AAAA,cACV,cAAc;AAAA,YAChB;AAAA,YACA,OAAO;AAAA,cACL,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAEA,CAAC,GAAG,YAAY,OAAO,YAAY,SAAS,GAAG;AAAA,UAC7C,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,WAAW;AAAA,UACX,CAAC,OAAO,YAAY,cAAc,YAAY,gBAAgB,GAAG;AAAA,UACjE,CAAC,KAAK,YAAY,mBAAmB,GAAG;AAAA,YACtC,oBAAoB;AAAA,UACtB;AAAA,QACF;AAAA;AAAA,QAEA,CAAC,GAAG,YAAY,OAAO,GAAG;AAAA,MAC5B;AAAA,IACF;AAAA;AAAA,IAEA;AAAA,MACE,CAAC,GAAG,YAAY,mBAAmB,GAAG;AAAA,QACpC,OAAO,iBAAiB;AAAA,QACxB,CAAC,IAAI,YAAY,OAAO,GAAG;AAAA,UACzB,CAAC,GAAG,YAAY,UAAU,YAAY,YAAY,YAAY,gBAAgB,GAAG;AAAA,YAC/E,CAAC,KAAK,YAAY,0BAA0B,GAAG;AAAA,cAC7C,UAAU;AAAA,cACV,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,QACA,CAAC,KAAK,YAAY;AAAA,cACV,YAAY,iBAAiB,YAAY,sBAAsB,YAAY;AAAA,cAC3E,YAAY,iBAAiB,YAAY,sBAAsB,YAAY,cAAc,YAAY;AAAA,cACrG,YAAY,cAAc,YAAY,gBAAgB,GAAG;AAAA,UAC/D,kBAAkB;AAAA,UAClB,eAAe,cAAc,UAAU;AAAA,UACvC,cAAc;AAAA,UACd,CAAC;AAAA,cACK,YAAY;AAAA,cACZ,YAAY;AAAA,WACf,GAAG;AAAA,YACJ,SAAS;AAAA,UACX;AAAA,UACA,CAAC,GAAG,YAAY,eAAe,OAAO,EAAE,GAAG;AAAA,YACzC,QAAQ;AAAA,YACR,UAAU;AAAA,YACV,YAAY,GAAG,cAAc;AAAA,YAC7B,UAAU;AAAA,cACR,SAAS;AAAA,cACT,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,eAAe,OAAO,EAAE,GAAG;AAAA,UACzC,SAAS;AAAA,QACX;AAAA,QACA,aAAa;AAAA,UACX,eAAe;AAAA,UACf,CAAC,GAAG,YAAY,eAAe,OAAO,EAAE,GAAG;AAAA,YACzC,SAAS;AAAA,UACX;AAAA,UACA,cAAc;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,mBAAmB,GAAG,SAAS,SAAS,CAAC,GAAG,YAAY,GAAG;AAAA,UACzE,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EAAC;AACH;AACA,IAAO,mBAAQ;;;AC9Kf,IAAM,mBAAmB,WAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA;AAAA,IAEL,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG;AAAA,MACvD,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,YAAY,CAAC,gBAAgB,kBAAkB,IAAI,cAAc,kBAAkB,IAAI,WAAW,kBAAkB,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAAA,MACnJ,CAAC,GAAG,YAAY,eAAe,OAAO,EAAE,GAAG;AAAA,QACzC,UAAU;AAAA,QACV;AAAA,QACA,YAAY,CAAC,aAAa,iBAAiB,IAAI,aAAa,IAAI,UAAU,kBAAkB,IAAI,eAAe,IAAI,SAAS,kBAAkB,EAAE,EAAE,KAAK,GAAG;AAAA,QAC1J,UAAU;AAAA,UACR,mBAAmB,kBAAkB;AAAA,UACrC,SAAS;AAAA,UACT,YAAY,CAAC,WAAW,kBAAkB,IAAI,eAAe,IAAI,UAAU,kBAAkB,IAAI,SAAS,kBAAkB,EAAE,EAAE,KAAK,GAAG;AAAA,QAC1I;AAAA,MACF;AAAA,MACA,CAAC,GAAG,YAAY,YAAY,GAAG,SAAS,CAAC,GAAG,UAAU,CAAC;AAAA,MACvD,CAAC,IAAI,YAAY,kBAAkB,GAAG;AAAA,QACpC,CAAC,KAAK,OAAO,OAAO,YAAY,YAAY,GAAG;AAAA,UAC7C,iBAAiB;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA,CAAC,GAAG,YAAY,mBAAmB,YAAY,mBAAmB,GAAG;AAAA,MACnE,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,MACA,GAAG;AAAA,QACD,OAAO;AAAA,MACT;AAAA,MACA,CAAC,KAAK,YAAY,gBAAgB,GAAG;AAAA,QACnC,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,uBAAuB,WAAS;AACpC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,GAAG,YAAY,UAAU,GAAG;AAAA,MAC3B,CAAC,wBAAwB,GAAG;AAAA,QAC1B,UAAU;AAAA,QACV,KAAK;AAAA,QACL,gBAAgB,MAAM;AAAA,QACtB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,QACX,YAAY,aAAa,kBAAkB,IAAI,eAAe,aAAa,kBAAkB;AAAA,MAC/F;AAAA,MACA,WAAW;AAAA;AAAA,QAET,uBAAuB;AAAA,UACrB,UAAU;AAAA,UACV,OAAO,gBAAgB;AAAA,UACvB,QAAQ,gBAAgB;AAAA,UACxB,iBAAiB;AAAA,UACjB;AAAA,UACA,YAAY,CAAC,cAAc,kBAAkB,IAAI,eAAe,IAAI,aAAa,kBAAkB,IAAI,eAAe,IAAI,OAAO,kBAAkB,IAAI,eAAe,IAAI,SAAS,kBAAkB,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAAA,UACpO,SAAS;AAAA,QACX;AAAA,QACA,aAAa;AAAA,UACX,WAAW,6BAA6B,eAAe;AAAA,QACzD;AAAA,QACA,YAAY;AAAA,UACV,WAAW,6BAA6B,eAAe;AAAA,QACzD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,eAAe,WAAS;AAC5B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA;AAAA,IAEP;AAAA,MACE,IAAI;AAAA,QACF,CAAC,GAAG,YAAY,EAAE,GAAG,SAAS,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG;AAAA;AAAA,UAEtD,CAAC,UAAU,GAAG;AAAA,YACZ,SAAS;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,CAAC,GAAG,YAAY,iBAAiB,GAAG;AAAA,QAClC,SAAS;AAAA,MACX;AAAA,IACF;AAAA,IAAG;AAAA,MACD,CAAC,YAAY,GAAG,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,CAAC,GAAG,eAAe,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG;AAAA,QACtH,cAAc;AAAA,QACd,oBAAoB;AAAA;AAAA,QAEpB;AAAA,QACA,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA,QACT,YAAY,SAAS,kBAAkB;AAAA,QACvC,CAAC,QAAQ,GAAG;AAAA,UACV,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,WAAW;AAAA,QACb;AAAA;AAAA,QAEA,CAAC,YAAY,GAAG;AAAA,UACd,SAAS;AAAA,UACT,CAAC,GAAG,YAAY,OAAO,GAAG;AAAA,YACxB,MAAM;AAAA,UACR;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,UAAU,YAAY,aAAa,YAAY,gBAAgB,GAAG;AAAA,UAChF,cAAc,MAAM;AAAA,QACtB;AAAA,QACA,CAAC,GAAG,YAAY,mBAAmB,GAAG;AAAA,UACpC,SAAS,GAAG,SAAS,MAAM,OAAO;AAAA,UAClC;AAAA,UACA;AAAA,UACA,YAAY,OAAO,kBAAkB;AAAA,QACvC;AAAA,QACA,CAAC,gBAAgB,YAAY,UAAU,GAAG;AAAA,UACxC,YAAY,CAAC,gBAAgB,kBAAkB,IAAI,eAAe,IAAI,cAAc,kBAAkB,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAAA,QACvI;AAAA,QACA,CAAC,GAAG,YAAY,aAAa,YAAY,iBAAiB,GAAG;AAAA,UAC3D,YAAY,CAAC,gBAAgB,kBAAkB,IAAI,eAAe,IAAI,cAAc,kBAAkB,IAAI,eAAe,IAAI,WAAW,iBAAiB,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAAA,QAC1L;AAAA,QACA,CAAC,GAAG,YAAY,YAAY,YAAY,MAAM,GAAG;AAAA,UAC/C,QAAQ;AAAA,UACR,YAAY,CAAC,cAAc,kBAAkB,IAAI,eAAe,IAAI,WAAW,kBAAkB,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAAA,QAClI;AAAA,QACA,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,UACjC,YAAY,SAAS,kBAAkB;AAAA,QACzC;AAAA,QACA,CAAC,GAAG,YAAY,SAAS,GAAG;AAAA,UAC1B,aAAa;AAAA,YACX,UAAU;AAAA,YACV,OAAO;AAAA,YACP,iBAAiB;AAAA,YACjB,SAAS;AAAA,UACX;AAAA,QACF;AAAA;AAAA;AAAA;AAAA,QAIA,CAAC,GAAG,YAAY,eAAe,GAAG;AAAA,UAChC,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,aAAa;AAAA,UACb,aAAa;AAAA,UACb,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,SAAS;AAAA,UACT,YAAY;AAAA,YACV,aAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF,CAAC,GAAG,iBAAiB,KAAK,CAAC,GAAG;AAAA,QAC5B,CAAC,GAAG,YAAY,aAAa,GAAG;AAAA,UAC9B,CAAC,GAAG,YAAY,kBAAkB,GAAG;AAAA,YACnC,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG;AAAA,cACvD,eAAe,GAAG,WAAW,CAAC,MAAM,OAAO;AAAA,YAC7C;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAEA,aAAa;AAAA,UACX,WAAW;AAAA,YACT,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,WAAW;AAAA,YACX,iBAAiB;AAAA;AAAA,YAEjB,aAAa;AAAA,cACX,UAAU;AAAA,cACV,OAAO,GAAG,kBAAkB;AAAA,cAC5B,QAAQ;AAAA,cACR,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,SAAS;AAAA,cACT,SAAS;AAAA,YACX;AAAA,UACF;AAAA;AAAA,UAEA,gCAAgC;AAAA,YAC9B,KAAK;AAAA,YACL,kBAAkB;AAAA,UACpB;AAAA,UACA,CAAC,KAAK,YAAY,EAAE,GAAG,SAAS,SAAS,SAAS;AAAA,YAChD,cAAc;AAAA,UAChB,GAAG,iBAAiB,KAAK,CAAC,GAAG,qBAAqB,KAAK,CAAC,GAAG;AAAA,YACzD,CAAC,GAAG,YAAY,UAAU,YAAY,cAAc,YAAY,gBAAgB,GAAG;AAAA,cACjF,cAAc;AAAA,YAChB;AAAA,YACA,CAAC,GAAG,YAAY,uBAAuB,GAAG;AAAA,cACxC,YAAY,aAAa,kBAAkB,IAAI,eAAe;AAAA,YAChE;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC,GAAG,qBAAqB,KAAK,CAAC,GAAG;AAAA,QAChC,CAAC,sBAAsB,YAAY;AAAA,mBACtB,YAAY,gBAAgB,GAAG;AAAA;AAAA,UAE1C,aAAa;AAAA,YACX,WAAW,6BAA6B,eAAe;AAAA,UACzD;AAAA,UACA,YAAY;AAAA,YACV,WAAW,6BAA6B,eAAe;AAAA,UACzD;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,gBAAgB,YAAY,qBAAqB,YAAY,oBAAoB,YAAY,gBAAgB,GAAG;AAAA;AAAA,UAE9H,WAAW,eAAe,gBAAgB,GAAG;AAAA,UAC7C,YAAY;AAAA,YACV,WAAW,8BAA8B,eAAe;AAAA,UAC1D;AAAA,UACA,aAAa;AAAA,YACX,WAAW,4BAA4B,eAAe;AAAA,UACxD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAEA;AAAA,MACE,CAAC,GAAG,MAAM,gBAAgB,GAAG;AAAA,QAC3B,CAAC,YAAY,GAAG;AAAA,UACd,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA,EAAC;AACH;AAEA,IAAOC,iBAAS,CAAC,WAAW,gBAAgB;AAC1C,QAAM,gBAAgB,sBAAsB,QAAQ,CAAC,OAAO,SAAS;AACnE,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AAEJ,SAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,WAAW,OAAO;AAC3F,aAAO,CAAC;AAAA,IACV;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,gBAAgB,WAAW,IAAI;AAErC,UAAM,YAAY,MAAW,OAAO;AAAA,MAClC,gBAAgB;AAAA,MAChB,uBAAuB,MAAM;AAAA,MAC7B;AAAA,MACA,sBAAsB,kBAAkB;AAAA,MACxC,iBAAiB,GAAG,gBAAgB,IAAI;AAAA,MACxC,oBAAoB;AAAA,MACpB,eAAe;AAAA,IACjB,CAAC;AACD,UAAM,gBAAgB,IAAI,UAAU,mBAAmB,EAAE,SAAS,IAAI,EAAE,YAAY;AACpF,UAAM,gBAAgB,MAAW,WAAW;AAAA,MAC1C,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,iBAAiB;AAAA,MACjB,uBAAuB;AAAA,MACvB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,0BAA0B;AAAA;AAAA,MAE1B,uBAAuB,IAAI,UAAU,mBAAmB,EAAE,SAAS,IAAI,EAAE,YAAY;AAAA;AAAA,MAErF,qBAAqB;AAAA,MACrB,0BAA0B;AAAA,MAC1B,6BAA6B;AAAA,MAC7B,yBAAyB;AAAA,MACzB,2BAA2B;AAAA,MAC3B,eAAe;AAAA;AAAA,MAEf,iCAAiC;AAAA,MACjC,+BAA+B;AAAA,IACjC,GAAG,SAAS,CAAC,GAAG,sBAAsB,CAAC;AACvC,WAAO;AAAA;AAAA,MAEP,aAAa,SAAS;AAAA;AAAA,MAEtB,mBAAmB,SAAS;AAAA;AAAA,MAE5B,iBAAiB,SAAS;AAAA;AAAA,MAE1B,cAAc,WAAW,OAAO;AAAA,MAAG,cAAc,eAAe,MAAM;AAAA;AAAA,MAEtE,YAAY,SAAS;AAAA;AAAA,MAErB,iBAAkB,SAAS;AAAA,MAAG,gBAAgB,WAAW,UAAU;AAAA,MAAG,gBAAgB,WAAW,YAAY;AAAA,MAAG,eAAe,WAAW,UAAU;AAAA,IAAC;AAAA,EACvJ,GAAG,WAAS;AACV,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO;AAAA,MACL,eAAe;AAAA,MACf,aAAa,MAAM,kBAAkB;AAAA,MACrC,YAAY,MAAM;AAAA,MAClB,mBAAmB,MAAM;AAAA,MACzB,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,8BAA8B;AAAA,MAC9B,iBAAiB;AAAA,MACjB,uBAAuB;AAAA,MACvB,iCAAiC;AAAA,MACjC,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,+BAA+B;AAAA,MAC/B,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,0BAA0B;AAAA;AAAA,MAE1B,uBAAuB;AAAA;AAAA,MAEvB,qBAAqB;AAAA,MACrB,0BAA0B;AAAA,MAC1B,6BAA6B;AAAA,MAC7B,yBAAyB;AAAA,MACzB,2BAA2B;AAAA,MAC3B,kBAAkB,MAAM;AAAA,IAC1B;AAAA,EACF,CAAC;AACD,SAAO,cAAc,SAAS;AAChC;;;ACrZO,IAAM,qBAAqB,OAAO,oBAAoB;AACtD,IAAM,oBAAoB,MAAM;AACrC,SAAO,OAAO,oBAAoB,MAAS;AAC7C;AACO,IAAM,qBAAqB,WAAS;AACzC,MAAI,IAAI,IAAI;AACZ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,kBAAkB,KAAK,CAAC;AAC5B,UAAQ,oBAAoB;AAAA,IAC1B,WAAW,SAAS,MAAM;AACxB,UAAIC,KAAIC;AACR,cAAQA,OAAMD,MAAK,MAAM,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW,QAAQC,QAAO,SAASA,MAAK,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU;AAAA,IACtL,CAAC;AAAA,IACD,MAAM,SAAS,MAAM;AACnB,UAAID,KAAIC;AACR,cAAQA,OAAMD,MAAK,MAAM,UAAU,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW,QAAQC,QAAO,SAASA,MAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AAAA,IAClK,CAAC;AAAA,IACD,YAAY,SAAS,MAAM;AACzB,UAAID,KAAIC;AACR,cAAQA,OAAMD,MAAK,MAAM,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW,QAAQC,QAAO,SAASA,MAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW;AAAA,IAC1L,CAAC;AAAA,IACD,YAAY,KAAK,MAAM,eAAe,QAAQ,OAAO,SAAS,KAAK;AAAA,IACnE,UAAU,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,KAAK;AAAA,IAC/D,aAAa,KAAK,MAAM,gBAAgB,QAAQ,OAAO,SAAS,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW;AAAA,EAC1I,CAAC;AACH;;;ACXO,IAAM,YAAY,OAAO;AAAA,EAC9B,IAAI;AAAA,EACJ,WAAW;AAAA;AAAA,EAEX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,UAAU;AAAA,IACR,MAAM,CAAC,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,mBAAmB;AAAA,IACjB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,mBAAmB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA,sBAAsB;AAAA,IACpB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS,CAAC,UAAU,KAAK;AAAA,EACzB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AACxB;AACA,IAAM,aAAa,CAAC;AACpB,IAAO,eAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,UAAU;AAAA,EACjB,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,wBAAgB,QAAQ,KAAK;AACjC,UAAM,WAAW,kBAAkB;AACnC,UAAM,YAAY,SAAS,MAAM;AAC/B,UAAI;AACJ,aAAO,aAAa,QAAQ,MAAM,eAAe,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,IAC9K,CAAC;AACD,UAAM,CAAC,SAAS,MAAM,IAAIC,eAAS,WAAW,SAAS,MAAM;AAC3D,aAAO,CAAC;AAAA,IACV,CAAC,CAAC;AACF,UAAM,QAAQ,WAAW,oBAAI,IAAI,CAAC;AAClC,UAAM,iBAAiB,OAAO,mBAAmB,IAAI,MAAS,CAAC;AAC/D,UAAM,kBAAkB,SAAS,MAAM;AACrC,UAAI,eAAe,UAAU,QAAW;AACtC,eAAO,eAAe;AAAA,MACxB;AACA,aAAO,MAAM;AAAA,IACf,CAAC;AACD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,SAAS,KAAK;AAClB,UAAM,YAAY,WAAW,KAAK;AAClC,cAAU,MAAM;AACd,gBAAU,QAAQ;AAAA,IACpB,CAAC;AACD,gBAAY,MAAM;AAChB,yBAAW,EAAE,MAAM,oBAAoB,QAAQ,MAAM,SAAS,WAAW,QAAQ,8DAA8D;AAC/I,yBAAW,EAAE,eAAe,UAAU,UAAa,MAAM,oBAAoB,OAAO,QAAQ,0FAA0F;AAAA,IAMxL,CAAC;AACD,UAAM,aAAa,IAAI,CAAC,CAAC;AACzB,UAAM,qBAAqB,IAAI,CAAC,CAAC;AACjC,UAAM,cAAc,IAAI,CAAC,CAAC;AAC1B,UAAM,OAAO,MAAM;AACjB,YAAM,iBAAiB,CAAC;AACxB,iBAAW,YAAY,MAAM,MAAM,OAAO,GAAG;AAC3C,uBAAe,SAAS,GAAG,IAAI;AAAA,MACjC;AACA,kBAAY,QAAQ;AAAA,IACtB,GAAG;AAAA,MACD,OAAO;AAAA,IACT,CAAC;AACD,gBAAY,MAAM;AAChB,UAAI,MAAM,cAAc,QAAW;AACjC,YAAI,OAAO,CAAC;AACZ,cAAM,WAAW,MAAM,YAAY,YAAY,MAAM,MAAM,SAAS,IAAI;AACxE,YAAI,YAAY,MAAM,cAAc,QAAW;AAC7C,iBAAO,aAAK,CAAC,EAAE,OAAO,MAAM,SAAS,UAAU,GAAG,MAAM,SAAS,CAAC;AAAA,QACpE,OAAO;AACL,iBAAO,CAAC;AAAA,QACV;AACA,YAAI,CAAC,qBAAa,WAAW,OAAO,IAAI,GAAG;AACzC,qBAAW,QAAQ;AAAA,QACrB;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,MAAM,MAAM,cAAc,kBAAgB;AAC9C,UAAI,cAAc;AAChB,2BAAmB,QAAQ,aAAa,MAAM;AAAA,MAChD;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,UAAM,sBAAsB,IAAI,CAAC,CAAC;AAClC,UAAM,CAAC,aAAa,kBAAkB,GAAG,MAAM;AAC7C,UAAI,oBAAoB,CAAC;AACzB,yBAAmB,MAAM,QAAQ,SAAO;AACtC,cAAM,WAAW,YAAY,MAAM,GAAG;AACtC,YAAI,UAAU;AACZ,8BAAoB,kBAAkB,OAAO,MAAM,SAAS,UAAU,CAAC;AAAA,QACzE;AAAA,MACF,CAAC;AACD,0BAAoB,aAAK,iBAAiB;AAC1C,UAAI,CAAC,qBAAa,oBAAoB,OAAO,iBAAiB,GAAG;AAC/D,4BAAoB,QAAQ;AAAA,MAC9B;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AAED,UAAM,mBAAmB,UAAQ;AAC/B,UAAI,MAAM,YAAY;AAEpB,cAAM;AAAA,UACJ,KAAK;AAAA,QACP,IAAI;AACJ,cAAM,QAAQ,mBAAmB,MAAM,SAAS,SAAS;AACzD,YAAI;AACJ,YAAI,MAAM,UAAU;AAClB,cAAI,OAAO;AACT,8BAAkB,mBAAmB,MAAM,OAAO,SAAO,QAAQ,SAAS;AAAA,UAC5E,OAAO;AACL,8BAAkB,CAAC,GAAG,mBAAmB,OAAO,SAAS;AAAA,UAC3D;AAAA,QACF,OAAO;AACL,4BAAkB,CAAC,SAAS;AAAA,QAC9B;AAEA,cAAM,aAAa,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG;AAAA,UAC9C,cAAc;AAAA,QAChB,CAAC;AACD,YAAI,CAAC,qBAAa,iBAAiB,mBAAmB,KAAK,GAAG;AAC5D,cAAI,MAAM,iBAAiB,QAAW;AACpC,+BAAmB,QAAQ;AAAA,UAC7B;AACA,eAAK,uBAAuB,eAAe;AAC3C,cAAI,SAAS,MAAM,UAAU;AAC3B,iBAAK,YAAY,UAAU;AAAA,UAC7B,OAAO;AACL,iBAAK,UAAU,UAAU;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAEA,UAAI,WAAW,UAAU,YAAY,CAAC,MAAM,YAAY,eAAe,MAAM,QAAQ;AACnF,wBAAgB,UAAU;AAAA,MAC5B;AAAA,IACF;AACA,UAAM,iBAAiB,IAAI,CAAC,CAAC;AAC7B,UAAM,MAAM,MAAM,UAAU,WAAY;AACtC,UAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,eAAe;AAClG,UAAI,CAAC,qBAAa,eAAe,OAAO,QAAQ,GAAG;AACjD,uBAAe,QAAQ,SAAS,MAAM;AAAA,MACxC;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,QAAI;AACJ,UAAM,mBAAmB,UAAQ;AAC/B,mBAAa,OAAO;AACpB,gBAAU,WAAW,MAAM;AACzB,YAAI,MAAM,cAAc,QAAW;AACjC,qBAAW,QAAQ;AAAA,QACrB;AACA,aAAK,oBAAoB,KAAK,KAAK,SAAS,CAAC,CAAC;AAAA,MAChD,CAAC;AAAA,IACH;AACA,UAAM,WAAW,SAAS,MAAM,CAAC,CAAC,MAAM,QAAQ;AAChD,UAAM,QAAQ,SAAS,MAAM,UAAU,UAAU,KAAK;AACtD,UAAM,aAAa,IAAI,UAAU;AACjC,UAAM,wBAAwB,WAAW,KAAK;AAC9C,gBAAY,MAAM;AAChB,UAAI;AACJ,WAAK,MAAM,SAAS,YAAY,MAAM,SAAS,eAAe,gBAAgB,OAAO;AACnF,mBAAW,QAAQ;AACnB,8BAAsB,QAAQ,gBAAgB;AAAA,MAChD,OAAO;AACL,mBAAW,QAAQ,MAAM;AACzB,8BAAsB,QAAQ;AAAA,MAChC;AACA,WAAK,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAC1H,mBAAW,QAAQ,SAAS,KAAK;AAAA,MACnC;AAAA,IACF,CAAC;AACD,UAAM,eAAe,SAAS,MAAM,WAAW,UAAU,QAAQ;AACjE,UAAM,kBAAkB,UAAQ;AAC9B,qBAAe,QAAQ;AACvB,WAAK,mBAAmB,IAAI;AAC5B,WAAK,cAAc,IAAI;AAAA,IACzB;AAEA,UAAM,sBAAsB,IAAI,eAAe,KAAK;AACpD,UAAM,WAAW,WAAW,KAAK;AAEjC,UAAM,gBAAgB,MAAM;AAC1B,UAAI,aAAa,OAAO;AACtB,4BAAoB,QAAQ,eAAe;AAAA,MAC7C;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AAED,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,SAAS,OAAO;AACnB,iBAAS,QAAQ;AACjB;AAAA,MACF;AACA,UAAI,aAAa,OAAO;AACtB,uBAAe,QAAQ,oBAAoB;AAAA,MAC7C,OAAO;AAEL,wBAAgB,UAAU;AAAA,MAC5B;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AACD,UAAM,YAAY,SAAS,MAAM;AAC/B,aAAO;AAAA,QACL,CAAC,GAAG,UAAU,KAAK,EAAE,GAAG;AAAA,QACxB,CAAC,GAAG,UAAU,KAAK,OAAO,GAAG;AAAA,QAC7B,CAAC,GAAG,UAAU,KAAK,IAAI,WAAW,KAAK,EAAE,GAAG;AAAA,QAC5C,CAAC,GAAG,UAAU,KAAK,mBAAmB,GAAG,sBAAsB;AAAA,QAC/D,CAAC,GAAG,UAAU,KAAK,MAAM,GAAG,MAAM;AAAA,QAClC,CAAC,GAAG,UAAU,KAAK,IAAI,MAAM,KAAK,EAAE,GAAG;AAAA,MACzC;AAAA,IACF,CAAC;AACD,UAAM,gBAAgB,SAAS,MAAM,aAAa,CAAC;AACnD,UAAM,iBAAiB,SAAS,OAAO;AAAA,MACrC,YAAY;AAAA,QACV,MAAM,GAAG,cAAc,KAAK;AAAA,MAC9B;AAAA,MACA,QAAQ,uBAAe,GAAG,cAAc,KAAK,kBAAkB;AAAA,MAC/D,OAAO;AAAA,QACL,MAAM,GAAG,cAAc,KAAK;AAAA,MAC9B;AAAA,IACF,EAAE;AACF,yBAAqB,IAAI;AACzB,UAAM,kBAAkB,WAAY;AAClC,UAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACrF,YAAM,OAAO,CAAC;AACd,YAAM,aAAa,MAAM;AACzB,gBAAU,QAAQ,cAAY;AAC5B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,WAAW,IAAI,QAAQ;AAC3B,aAAK,KAAK,KAAK,GAAG,gBAAgB,MAAM,iBAAiB,CAAC,CAAC;AAAA,MAC7D,CAAC;AACD,aAAO;AAAA,IACT;AAKA,UAAM,kBAAkB,UAAQ;AAC9B,UAAI;AACJ,WAAK,SAAS,IAAI;AAClB,uBAAiB,IAAI;AACrB,OAAC,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ;AAAA,IACnI;AACA,UAAM,uBAAuB,CAAC,KAAK,SAAS;AAC1C,UAAI;AACJ,YAAM,sBAAsB,KAAK,YAAY,MAAM,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,sBAAsB,CAAC;AACxH,UAAI,cAAc,eAAe,MAAM,OAAO,OAAK,MAAM,GAAG;AAC5D,UAAI,MAAM;AACR,oBAAY,KAAK,GAAG;AAAA,MACtB,WAAW,WAAW,UAAU,UAAU;AAExC,cAAM,cAAc,gBAAgB,MAAM,iBAAiB,CAAC;AAC5D,sBAAc,aAAK,YAAY,OAAO,OAAK,CAAC,YAAY,SAAS,CAAC,CAAC,CAAC;AAAA,MACtE;AACA,UAAI,CAAC,qBAAa,gBAAgB,WAAW,GAAG;AAC9C,wBAAgB,WAAW;AAAA,MAC7B;AAAA,IACF;AACA,UAAM,mBAAmB,CAAC,KAAK,SAAS;AACtC,YAAM,MAAM,IAAI,KAAK,IAAI;AACzB,YAAM,QAAQ,IAAI,IAAI,MAAM,KAAK;AAAA,IACnC;AACA,UAAM,qBAAqB,SAAO;AAChC,YAAM,MAAM,OAAO,GAAG;AACtB,YAAM,QAAQ,IAAI,IAAI,MAAM,KAAK;AAAA,IACnC;AACA,UAAM,mBAAmB,IAAI,CAAC;AAC9B,UAAM,aAAa,SAAS,MAAM;AAChC,UAAI;AACJ,aAAO,MAAM,cAAc,MAAM,gBAAgB,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,SAAO;AACrL,YAAI,OAAO,MAAM,cAAc,MAAM;AACrC,eAAO,OAAO,SAAS,aAAa,KAAK,GAAG,IAAI;AAChD,eAAO,aAAa,MAAM;AAAA,UACxB,OAAO,GAAG,UAAU,KAAK;AAAA,QAC3B,GAAG,KAAK;AAAA,MACV,IAAI;AAAA,IACN,CAAC;AACD,2BAAe;AAAA,MACb;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,cAAc;AAAA,MACd;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,MAAM;AAAA,MACN,cAAc,SAAS,MAAM,MAAM,YAAY;AAAA,MAC/C,mBAAmB,SAAS,MAAM,MAAM,iBAAiB;AAAA,MACzD,kBAAkB,SAAS,MAAM,MAAM,gBAAgB;AAAA,MACvD,mBAAmB,SAAS,MAAM,MAAM,iBAAiB;AAAA,MACzD,sBAAsB,SAAS,MAAM,MAAM,oBAAoB;AAAA,MAC/D,mBAAmB,SAAS,MAAM,MAAM,iBAAiB;AAAA,MACzD,iBAAiB;AAAA,MACjB,OAAO,SAAS,MAAM,MAAM,KAAK;AAAA,MACjC;AAAA,MACA,gBAAgB,SAAS,MAAM,UAAU,QAAQ,eAAe,QAAQ,IAAI;AAAA,MAC5E,QAAQ,SAAS,MAAM,UAAU,QAAQ,MAAM,SAAS,IAAI;AAAA,MAC5D,kBAAkB,WAAW,MAAS;AAAA,MACtC,cAAc;AAAA,MACd,aAAa;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,oBAAoB,SAAS,MAAM,MAAM,kBAAkB;AAAA,MAC3D,eAAe;AAAA,IACjB,CAAC;AACD,UAAM,kBAAkB,MAAM;AAC5B,UAAI;AACJ,aAAO,WAAW,SAAS,iBAAiB,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC;AAAA,IACrH;AACA,WAAO,MAAM;AACX,UAAI;AACJ,YAAM,YAAY,gBAAgB;AAClC,YAAM,aAAa,iBAAiB,SAAS,UAAU,SAAS,KAAK,WAAW,UAAU,gBAAgB,MAAM;AAEhH,YAAM,iBAAiB,CAAAC,eAAa;AAClC,eAAO,WAAW,UAAU,gBAAgB,MAAM,mBAAmBA;AAAA;AAAA,UAErEA,WAAU,IAAI,CAAC,OAAO;AAAA;AAAA,YACtB,YAAa,qBAAqB;AAAA,cAChC,OAAO,MAAM;AAAA,cACb,oBAAoB,QAAQ,iBAAiB;AAAA,YAC/C,GAAG;AAAA,cACD,SAAS,MAAM;AAAA,YACjB,CAAC;AAAA,WAAC;AAAA;AAAA,MACJ;AACA,YAAM,wBAAwB,KAAK,MAAM,yBAAyB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,MAAM,YAAa,0BAAkB,MAAM,IAAI;AAC/J,aAAO,QAAQ,YAAa,qBAAU,eAAc,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QAChF,eAAe,MAAM;AAAA,QACrB,aAAa,GAAG,UAAU,KAAK;AAAA,QAC/B,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,SAAS,CAAC,UAAU,OAAO,MAAM,OAAO,OAAO,KAAK;AAAA,QACpD,QAAQ;AAAA,QACR,MAAM,MAAM;AAAA,QACZ,QAAQ,eAAe,SAAS;AAAA,QAChC,iBAAiB,UAAQ;AAAA,QACzB,iBAAiB,eAAa;AAE5B,gBAAM,MAAM,UAAU;AACtB,gBAAM,kBAAkB,MAAM,UAAU,MAAM,CAAC,GAAG,IAAI;AACtD,iBAAO,YAAa,UAAW,MAAM,CAAC,YAAa,iBAAS;AAAA,YAC1D,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,sBAAsB,QAAQ;AAAA,UAChC,GAAG;AAAA,YACD,SAAS,MAAM;AAAA,UACjB,CAAC,GAAG,YAAa,aAAa,MAAM;AAAA,YAClC,SAAS,MAAM,CAAC,YAAa,iBAAS;AAAA,cACpC,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,sBAAsB,QAAQ;AAAA,YAChC,GAAG;AAAA,cACD,SAAS,MAAM;AAAA,YACjB,CAAC,CAAC;AAAA,UACJ,CAAC,CAAC,CAAC;AAAA,QACL;AAAA,QACA,YAAY,WAAW,UAAU,gBAAgB,MAAM,mBAAmB,oBAAS,aAAa,oBAAS;AAAA,QACzG,OAAO;AAAA,QACP,kBAAkB;AAAA,QAClB,mBAAmB,kBAAgB;AACjC,2BAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF,CAAC,GAAG;AAAA,QACF,SAAS,MAAM,CAAC,YAAa,UAAU;AAAA,UACrC,MAAM;AAAA,QACR,GAAG;AAAA,UACD,SAAS,MAAM,CAAC,YAAa,OAAO;AAAA,YAClC,SAAS;AAAA,cACP,SAAS;AAAA,YACX;AAAA,YACA,eAAe;AAAA,UACjB,GAAG,CAAC,YAAa,aAAa,MAAM;AAAA,YAClC,SAAS,MAAM,CAAC,eAAe,gBAAgB,CAAC,CAAC;AAAA,UACnD,CAAC,CAAC,CAAC,CAAC;AAAA,QACN,CAAC,CAAC;AAAA,MACJ,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACF,CAAC;;;AC1dD,aAAK,UAAU,SAAU,KAAK;AAC5B,MAAI,UAAU,aAAK,MAAM,YAAI;AAC7B,MAAI,UAAU,iBAAS,MAAM,gBAAQ;AACrC,MAAI,UAAU,gBAAQ,MAAM,eAAO;AACnC,MAAI,UAAU,gBAAQ,MAAM,eAAO;AACnC,MAAI,UAAU,kBAAU,MAAM,iBAAS;AACvC,SAAO;AACT;AACA,aAAK,OAAO;AACZ,aAAK,UAAU;AACf,aAAK,UAAU;AACf,aAAK,YAAY;AAEjB,IAAO,eAAQ;", "names": ["targetOffset", "autoAdjustOverflow", "Tooltip_default", "autoAdjustOverflow", "placements", "Tooltip_default", "tooltipProps", "autoAdjustOverflow", "placements", "placements", "indexGuid", "_a", "_b", "__rest", "childrenNodes", "style_default", "_a", "_b", "style_default", "childList"]}