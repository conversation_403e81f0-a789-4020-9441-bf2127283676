{"version": 3, "sources": ["../../.pnpm/@antv+x6-plugin-snapline@2.1.7_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-snapline/src/snapline.ts", "../../.pnpm/@antv+x6-plugin-snapline@2.1.7_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-snapline/src/style/raw.ts", "../../.pnpm/@antv+x6-plugin-snapline@2.1.7_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-snapline/src/api.ts", "../../.pnpm/@antv+x6-plugin-snapline@2.1.7_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-snapline/src/index.ts"], "sourcesContent": ["import {\n  IDisablable,\n  ArrayExt,\n  FunctionExt,\n  Vector,\n  <PERSON>le,\n  Point,\n  Rectangle,\n  Graph,\n  EventArgs,\n  Model,\n  Node,\n  CellView,\n  NodeView,\n  View,\n} from '@antv/x6'\n\nexport class SnaplineImpl extends View implements IDisablable {\n  public readonly options: SnaplineImpl.Options\n  protected readonly graph: Graph\n  protected offset: Point.PointLike\n  protected timer: number | null\n\n  public container: SVGElement\n  protected containerWrapper: Vector\n  protected horizontal: Vector\n  protected vertical: Vector\n\n  protected get model() {\n    return this.graph.model\n  }\n\n  protected get containerClassName() {\n    return this.prefixClassName('widget-snapline')\n  }\n\n  protected get verticalClassName() {\n    return `${this.containerClassName}-vertical`\n  }\n\n  protected get horizontalClassName() {\n    return `${this.containerClassName}-horizontal`\n  }\n\n  constructor(options: SnaplineImpl.Options & { graph: Graph }) {\n    super()\n\n    const { graph, ...others } = options\n    this.graph = graph\n    this.options = { ...others }\n    this.offset = { x: 0, y: 0 }\n    this.render()\n    if (!this.disabled) {\n      this.startListening()\n    }\n  }\n\n  public get disabled() {\n    return this.options.enabled !== true\n  }\n\n  enable() {\n    if (this.disabled) {\n      this.options.enabled = true\n      this.startListening()\n    }\n  }\n\n  disable() {\n    if (!this.disabled) {\n      this.options.enabled = false\n      this.stopListening()\n    }\n  }\n\n  setFilter(filter?: SnaplineImpl.Filter) {\n    this.options.filter = filter\n  }\n\n  protected render() {\n    const container = (this.containerWrapper = new Vector('svg'))\n    const horizontal = (this.horizontal = new Vector('line'))\n    const vertical = (this.vertical = new Vector('line'))\n\n    container.addClass(this.containerClassName)\n    horizontal.addClass(this.horizontalClassName)\n    vertical.addClass(this.verticalClassName)\n\n    container.setAttribute('width', '100%')\n    container.setAttribute('height', '100%')\n\n    horizontal.setAttribute('display', 'none')\n    vertical.setAttribute('display', 'none')\n\n    container.append([horizontal, vertical])\n\n    if (this.options.className) {\n      container.addClass(this.options.className)\n    }\n\n    this.container = this.containerWrapper.node\n  }\n\n  protected startListening() {\n    this.stopListening()\n    this.graph.on('node:mousedown', this.captureCursorOffset, this)\n    this.graph.on('node:mousemove', this.snapOnMoving, this)\n    this.model.on('batch:stop', this.onBatchStop, this)\n    this.delegateDocumentEvents({\n      mouseup: 'hide',\n      touchend: 'hide',\n    })\n  }\n\n  protected stopListening() {\n    this.graph.off('node:mousedown', this.captureCursorOffset, this)\n    this.graph.off('node:mousemove', this.snapOnMoving, this)\n    this.model.off('batch:stop', this.onBatchStop, this)\n    this.undelegateDocumentEvents()\n  }\n\n  protected onBatchStop({ name, data }: Model.EventArgs['batch:stop']) {\n    if (name === 'resize') {\n      this.snapOnResizing(data.cell, data as Node.ResizeOptions)\n    }\n  }\n\n  captureCursorOffset({ view, x, y }: EventArgs['node:mousedown']) {\n    const targetView = view.getDelegatedView()\n    if (targetView && this.isNodeMovable(targetView)) {\n      const pos = view.cell.getPosition()\n      this.offset = {\n        x: x - pos.x,\n        y: y - pos.y,\n      }\n    }\n  }\n\n  protected isNodeMovable(view: CellView) {\n    return view && view.cell.isNode() && view.can('nodeMovable')\n  }\n\n  protected getRestrictArea(view?: NodeView): Rectangle.RectangleLike | null {\n    const restrict = this.graph.options.translating.restrict\n    const area =\n      typeof restrict === 'function'\n        ? FunctionExt.call(restrict, this.graph, view!)\n        : restrict\n\n    if (typeof area === 'number') {\n      return this.graph.transform.getGraphArea().inflate(area)\n    }\n\n    if (area === true) {\n      return this.graph.transform.getGraphArea()\n    }\n\n    return area || null\n  }\n\n  protected snapOnResizing(node: Node, options: Node.ResizeOptions) {\n    if (\n      this.options.resizing &&\n      !options.snapped &&\n      options.ui &&\n      options.direction &&\n      options.trueDirection\n    ) {\n      const view = this.graph.renderer.findViewByCell(node) as NodeView\n      if (view && view.cell.isNode()) {\n        const nodeBbox = node.getBBox()\n        const nodeBBoxRotated = nodeBbox.bbox(node.getAngle())\n        const nodeTopLeft = nodeBBoxRotated.getTopLeft()\n        const nodeBottomRight = nodeBBoxRotated.getBottomRight()\n        const angle = Angle.normalize(node.getAngle())\n        const tolerance = this.options.tolerance || 0\n        let verticalLeft: number | undefined\n        let verticalTop: number | undefined\n        let verticalHeight: number | undefined\n        let horizontalTop: number | undefined\n        let horizontalLeft: number | undefined\n        let horizontalWidth: number | undefined\n\n        const snapOrigin = {\n          vertical: 0,\n          horizontal: 0,\n        }\n\n        const direction = options.direction\n        const trueDirection = options.trueDirection\n        const relativeDirection = options.relativeDirection\n\n        if (trueDirection.indexOf('right') !== -1) {\n          snapOrigin.vertical = nodeBottomRight.x\n        } else {\n          snapOrigin.vertical = nodeTopLeft.x\n        }\n\n        if (trueDirection.indexOf('bottom') !== -1) {\n          snapOrigin.horizontal = nodeBottomRight.y\n        } else {\n          snapOrigin.horizontal = nodeTopLeft.y\n        }\n\n        this.model.getNodes().some((cell) => {\n          if (this.isIgnored(node, cell)) {\n            return false\n          }\n\n          const snapBBox = cell.getBBox().bbox(cell.getAngle())\n          const snapTopLeft = snapBBox.getTopLeft()\n          const snapBottomRight = snapBBox.getBottomRight()\n          const groups = {\n            vertical: [snapTopLeft.x, snapBottomRight.x],\n            horizontal: [snapTopLeft.y, snapBottomRight.y],\n          }\n\n          const distances = {} as {\n            vertical: { position: number; distance: number }[]\n            horizontal: { position: number; distance: number }[]\n          }\n\n          Object.keys(groups).forEach((k) => {\n            const key = k as 'vertical' | 'horizontal'\n            const list = groups[key]\n              .map((value) => ({\n                position: value,\n                distance: Math.abs(value - snapOrigin[key]),\n              }))\n              .filter((item) => item.distance <= tolerance)\n\n            distances[key] = ArrayExt.sortBy(list, (item) => item.distance)\n          })\n\n          if (verticalLeft == null && distances.vertical.length > 0) {\n            verticalLeft = distances.vertical[0].position\n            verticalTop = Math.min(nodeBBoxRotated.y, snapBBox.y)\n            verticalHeight =\n              Math.max(nodeBottomRight.y, snapBottomRight.y) - verticalTop\n          }\n\n          if (horizontalTop == null && distances.horizontal.length > 0) {\n            horizontalTop = distances.horizontal[0].position\n            horizontalLeft = Math.min(nodeBBoxRotated.x, snapBBox.x)\n            horizontalWidth =\n              Math.max(nodeBottomRight.x, snapBottomRight.x) - horizontalLeft\n          }\n\n          return verticalLeft != null && horizontalTop != null\n        })\n\n        this.hide()\n\n        let dx = 0\n        let dy = 0\n        if (horizontalTop != null || verticalLeft != null) {\n          if (verticalLeft != null) {\n            dx =\n              trueDirection.indexOf('right') !== -1\n                ? verticalLeft - nodeBottomRight.x\n                : nodeTopLeft.x - verticalLeft\n          }\n\n          if (horizontalTop != null) {\n            dy =\n              trueDirection.indexOf('bottom') !== -1\n                ? horizontalTop - nodeBottomRight.y\n                : nodeTopLeft.y - horizontalTop\n          }\n        }\n\n        let dWidth = 0\n        let dHeight = 0\n        if (angle % 90 === 0) {\n          if (angle === 90 || angle === 270) {\n            dWidth = dy\n            dHeight = dx\n          } else {\n            dWidth = dx\n            dHeight = dy\n          }\n        } else {\n          const quadrant =\n            angle >= 0 && angle < 90\n              ? 1\n              : angle >= 90 && angle < 180\n              ? 4\n              : angle >= 180 && angle < 270\n              ? 3\n              : 2\n\n          if (horizontalTop != null && verticalLeft != null) {\n            if (dx < dy) {\n              dy = 0\n              horizontalTop = undefined\n            } else {\n              dx = 0\n              verticalLeft = undefined\n            }\n          }\n\n          const rad = Angle.toRad(angle % 90)\n          if (dx) {\n            dWidth = quadrant === 3 ? dx / Math.cos(rad) : dx / Math.sin(rad)\n          }\n          if (dy) {\n            dHeight = quadrant === 3 ? dy / Math.cos(rad) : dy / Math.sin(rad)\n          }\n\n          const quadrant13 = quadrant === 1 || quadrant === 3\n          switch (relativeDirection) {\n            case 'top':\n            case 'bottom':\n              dHeight = dy\n                ? dy / (quadrant13 ? Math.cos(rad) : Math.sin(rad))\n                : dx / (quadrant13 ? Math.sin(rad) : Math.cos(rad))\n              break\n            case 'left':\n            case 'right':\n              dWidth = dx\n                ? dx / (quadrant13 ? Math.cos(rad) : Math.sin(rad))\n                : dy / (quadrant13 ? Math.sin(rad) : Math.cos(rad))\n              break\n            default:\n              break\n          }\n        }\n\n        switch (relativeDirection) {\n          case 'top':\n          case 'bottom':\n            dWidth = 0\n            break\n          case 'left':\n          case 'right':\n            dHeight = 0\n            break\n          default:\n            break\n        }\n\n        const gridSize = this.graph.getGridSize()\n        let newWidth = Math.max(nodeBbox.width + dWidth, gridSize)\n        let newHeight = Math.max(nodeBbox.height + dHeight, gridSize)\n\n        if (options.minWidth && options.minWidth > gridSize) {\n          newWidth = Math.max(newWidth, options.minWidth)\n        }\n\n        if (options.minHeight && options.minHeight > gridSize) {\n          newHeight = Math.max(newHeight, options.minHeight)\n        }\n\n        if (options.maxWidth) {\n          newWidth = Math.min(newWidth, options.maxWidth)\n        }\n\n        if (options.maxHeight) {\n          newHeight = Math.min(newHeight, options.maxHeight)\n        }\n\n        if (options.preserveAspectRatio) {\n          if (dHeight < dWidth) {\n            newHeight = newWidth * (nodeBbox.height / nodeBbox.width)\n          } else {\n            newWidth = newHeight * (nodeBbox.width / nodeBbox.height)\n          }\n        }\n\n        if (newWidth !== nodeBbox.width || newHeight !== nodeBbox.height) {\n          node.resize(newWidth, newHeight, {\n            direction,\n            relativeDirection,\n            trueDirection,\n            snapped: true,\n            snaplines: this.cid,\n            restrict: this.getRestrictArea(view),\n          })\n\n          if (verticalHeight) {\n            verticalHeight += newHeight - nodeBbox.height\n          }\n\n          if (horizontalWidth) {\n            horizontalWidth += newWidth - nodeBbox.width\n          }\n        }\n\n        const newRotatedBBox = node.getBBox().bbox(angle)\n        if (\n          verticalLeft &&\n          Math.abs(newRotatedBBox.x - verticalLeft) > 1 &&\n          Math.abs(newRotatedBBox.width + newRotatedBBox.x - verticalLeft) > 1\n        ) {\n          verticalLeft = undefined\n        }\n\n        if (\n          horizontalTop &&\n          Math.abs(newRotatedBBox.y - horizontalTop) > 1 &&\n          Math.abs(newRotatedBBox.height + newRotatedBBox.y - horizontalTop) > 1\n        ) {\n          horizontalTop = undefined\n        }\n\n        this.update({\n          verticalLeft,\n          verticalTop,\n          verticalHeight,\n          horizontalTop,\n          horizontalLeft,\n          horizontalWidth,\n        })\n      }\n    }\n  }\n\n  snapOnMoving({ view, e, x, y }: EventArgs['node:mousemove']) {\n    const targetView: NodeView = view.getEventData(e).delegatedView || view\n    if (!this.isNodeMovable(targetView)) {\n      return\n    }\n\n    const node = targetView.cell\n    const size = node.getSize()\n    const position = node.getPosition()\n    const cellBBox = new Rectangle(\n      x - this.offset.x,\n      y - this.offset.y,\n      size.width,\n      size.height,\n    )\n    const angle = node.getAngle()\n    const nodeCenter = cellBBox.getCenter()\n    const nodeBBoxRotated = cellBBox.bbox(angle)\n    const nodeTopLeft = nodeBBoxRotated.getTopLeft()\n    const nodeBottomRight = nodeBBoxRotated.getBottomRight()\n\n    const distance = this.options.tolerance || 0\n    let verticalLeft: number | undefined\n    let verticalTop: number | undefined\n    let verticalHeight: number | undefined\n    let horizontalTop: number | undefined\n    let horizontalLeft: number | undefined\n    let horizontalWidth: number | undefined\n    let verticalFix = 0\n    let horizontalFix = 0\n\n    this.model.getNodes().some((targetNode) => {\n      if (this.isIgnored(node, targetNode)) {\n        return false\n      }\n\n      const snapBBox = targetNode.getBBox().bbox(targetNode.getAngle())\n      const snapCenter = snapBBox.getCenter()\n      const snapTopLeft = snapBBox.getTopLeft()\n      const snapBottomRight = snapBBox.getBottomRight()\n\n      if (verticalLeft == null) {\n        if (Math.abs(snapCenter.x - nodeCenter.x) < distance) {\n          verticalLeft = snapCenter.x\n          verticalFix = 0.5\n        } else if (Math.abs(snapTopLeft.x - nodeTopLeft.x) < distance) {\n          verticalLeft = snapTopLeft.x\n          verticalFix = 0\n        } else if (Math.abs(snapTopLeft.x - nodeBottomRight.x) < distance) {\n          verticalLeft = snapTopLeft.x\n          verticalFix = 1\n        } else if (Math.abs(snapBottomRight.x - nodeBottomRight.x) < distance) {\n          verticalLeft = snapBottomRight.x\n          verticalFix = 1\n        } else if (Math.abs(snapBottomRight.x - nodeTopLeft.x) < distance) {\n          verticalLeft = snapBottomRight.x\n        }\n\n        if (verticalLeft != null) {\n          verticalTop = Math.min(nodeBBoxRotated.y, snapBBox.y)\n          verticalHeight =\n            Math.max(nodeBottomRight.y, snapBottomRight.y) - verticalTop\n        }\n      }\n\n      if (horizontalTop == null) {\n        if (Math.abs(snapCenter.y - nodeCenter.y) < distance) {\n          horizontalTop = snapCenter.y\n          horizontalFix = 0.5\n        } else if (Math.abs(snapTopLeft.y - nodeTopLeft.y) < distance) {\n          horizontalTop = snapTopLeft.y\n        } else if (Math.abs(snapTopLeft.y - nodeBottomRight.y) < distance) {\n          horizontalTop = snapTopLeft.y\n          horizontalFix = 1\n        } else if (Math.abs(snapBottomRight.y - nodeBottomRight.y) < distance) {\n          horizontalTop = snapBottomRight.y\n          horizontalFix = 1\n        } else if (Math.abs(snapBottomRight.y - nodeTopLeft.y) < distance) {\n          horizontalTop = snapBottomRight.y\n        }\n\n        if (horizontalTop != null) {\n          horizontalLeft = Math.min(nodeBBoxRotated.x, snapBBox.x)\n          horizontalWidth =\n            Math.max(nodeBottomRight.x, snapBottomRight.x) - horizontalLeft\n        }\n      }\n\n      return verticalLeft != null && horizontalTop != null\n    })\n\n    this.hide()\n\n    if (horizontalTop != null || verticalLeft != null) {\n      if (horizontalTop != null) {\n        nodeBBoxRotated.y =\n          horizontalTop - horizontalFix * nodeBBoxRotated.height\n      }\n\n      if (verticalLeft != null) {\n        nodeBBoxRotated.x = verticalLeft - verticalFix * nodeBBoxRotated.width\n      }\n\n      const newCenter = nodeBBoxRotated.getCenter()\n      const newX = newCenter.x - cellBBox.width / 2\n      const newY = newCenter.y - cellBBox.height / 2\n      const dx = newX - position.x\n      const dy = newY - position.y\n\n      if (dx !== 0 || dy !== 0) {\n        node.translate(dx, dy, {\n          snapped: true,\n          restrict: this.getRestrictArea(targetView),\n        })\n\n        if (horizontalWidth) {\n          horizontalWidth += dx\n        }\n\n        if (verticalHeight) {\n          verticalHeight += dy\n        }\n      }\n\n      this.update({\n        verticalLeft,\n        verticalTop,\n        verticalHeight,\n        horizontalTop,\n        horizontalLeft,\n        horizontalWidth,\n      })\n    }\n  }\n\n  protected isIgnored(snapNode: Node, targetNode: Node) {\n    return (\n      targetNode.id === snapNode.id ||\n      targetNode.isDescendantOf(snapNode) ||\n      !this.filter(targetNode)\n    )\n  }\n\n  protected filter(node: Node) {\n    const filter = this.options.filter\n    if (Array.isArray(filter)) {\n      return filter.some((item) => {\n        if (typeof item === 'string') {\n          return node.shape === item\n        }\n        return node.id === item.id\n      })\n    }\n    if (typeof filter === 'function') {\n      return FunctionExt.call(filter, this.graph, node)\n    }\n\n    return true\n  }\n\n  protected update(metadata: {\n    verticalLeft?: number\n    verticalTop?: number\n    verticalHeight?: number\n    horizontalTop?: number\n    horizontalLeft?: number\n    horizontalWidth?: number\n  }) {\n    // https://en.wikipedia.org/wiki/Transformation_matrix#Affine_transformations\n    if (metadata.horizontalTop) {\n      const start = this.graph.localToGraph(\n        new Point(metadata.horizontalLeft, metadata.horizontalTop),\n      )\n      const end = this.graph.localToGraph(\n        new Point(\n          metadata.horizontalLeft! + metadata.horizontalWidth!,\n          metadata.horizontalTop,\n        ),\n      )\n      this.horizontal.setAttributes({\n        x1: this.options.sharp ? `${start.x}` : '0',\n        y1: `${start.y}`,\n        x2: this.options.sharp ? `${end.x}` : '100%',\n        y2: `${end.y}`,\n        display: 'inherit',\n      })\n    } else {\n      this.horizontal.setAttribute('display', 'none')\n    }\n\n    if (metadata.verticalLeft) {\n      const start = this.graph.localToGraph(\n        new Point(metadata.verticalLeft, metadata.verticalTop),\n      )\n      const end = this.graph.localToGraph(\n        new Point(\n          metadata.verticalLeft,\n          metadata.verticalTop! + metadata.verticalHeight!,\n        ),\n      )\n      this.vertical.setAttributes({\n        x1: `${start.x}`,\n        y1: this.options.sharp ? `${start.y}` : '0',\n        x2: `${end.x}`,\n        y2: this.options.sharp ? `${end.y}` : '100%',\n        display: 'inherit',\n      })\n    } else {\n      this.vertical.setAttribute('display', 'none')\n    }\n\n    this.show()\n  }\n\n  protected resetTimer() {\n    if (this.timer) {\n      clearTimeout(this.timer)\n      this.timer = null\n    }\n  }\n\n  show() {\n    this.resetTimer()\n    if (this.container.parentNode == null) {\n      this.graph.container.appendChild(this.container)\n    }\n    return this\n  }\n\n  hide() {\n    this.resetTimer()\n    this.vertical.setAttribute('display', 'none')\n    this.horizontal.setAttribute('display', 'none')\n    const clean = this.options.clean\n    const delay = typeof clean === 'number' ? clean : clean !== false ? 3000 : 0\n    if (delay > 0) {\n      this.timer = window.setTimeout(() => {\n        if (this.container.parentNode !== null) {\n          this.unmount()\n        }\n      }, delay)\n    }\n    return this\n  }\n\n  protected onRemove() {\n    this.stopListening()\n    this.hide()\n  }\n\n  @View.dispose()\n  dispose() {\n    this.remove()\n  }\n}\n\nexport namespace SnaplineImpl {\n  export interface Options {\n    enabled?: boolean\n    className?: string\n    tolerance?: number\n    sharp?: boolean\n    /**\n     * Specify if snap on node resizing or not.\n     */\n    resizing?: boolean\n    clean?: boolean | number\n    filter?: Filter\n  }\n\n  export type Filter = null | (string | { id: string })[] | FilterFunction\n\n  export type FilterFunction = (this: Graph, node: Node) => boolean\n}\n", "/* eslint-disable */\n\n/**\n * Auto generated file, do not modify it!\n */\n\nexport const content = `.x6-widget-snapline {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  pointer-events: none;\n}\n.x6-widget-snapline-vertical,\n.x6-widget-snapline-horizontal {\n  stroke: #2ecc71;\n  stroke-width: 1px;\n}\n`\n", "import { Graph } from '@antv/x6'\nimport { Snapline } from './index'\n\ndeclare module '@antv/x6/lib/graph/graph' {\n  interface Graph {\n    isSnaplineEnabled: () => boolean\n    enableSnapline: () => Graph\n    disableSnapline: () => Graph\n    toggleSnapline: (enabled?: boolean) => Graph\n    hideSnapline: () => Graph\n    setSnaplineFilter: (filter?: Snapline.Filter) => Graph\n    isSnaplineOnResizingEnabled: () => boolean\n    enableSnaplineOnResizing: () => Graph\n    disableSnaplineOnResizing: () => Graph\n    toggleSnaplineOnResizing: (enableOnResizing?: boolean) => Graph\n    isSharpSnapline: () => boolean\n    enableSharpSnapline: () => Graph\n    disableSharpSnapline: () => Graph\n    toggleSharpSnapline: (sharp?: boolean) => Graph\n    getSnaplineTolerance: () => number | undefined\n    setSnaplineTolerance: (tolerance: number) => Graph\n  }\n}\n\nGraph.prototype.isSnaplineEnabled = function () {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    return snapline.isEnabled()\n  }\n  return false\n}\n\nGraph.prototype.enableSnapline = function () {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    snapline.enable()\n  }\n  return this\n}\n\nGraph.prototype.disableSnapline = function () {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    snapline.disable()\n  }\n  return this\n}\n\nGraph.prototype.toggleSnapline = function () {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    snapline.toggleEnabled()\n  }\n  return this\n}\n\nGraph.prototype.hideSnapline = function () {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    snapline.hide()\n  }\n  return this\n}\n\nGraph.prototype.setSnaplineFilter = function (filter?: Snapline.Filter) {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    snapline.setFilter(filter)\n  }\n  return this\n}\n\nGraph.prototype.isSnaplineOnResizingEnabled = function () {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    return snapline.isOnResizingEnabled()\n  }\n  return false\n}\n\nGraph.prototype.enableSnaplineOnResizing = function () {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    snapline.enableOnResizing()\n  }\n  return this\n}\n\nGraph.prototype.disableSnaplineOnResizing = function () {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    snapline.disableOnResizing()\n  }\n  return this\n}\n\nGraph.prototype.toggleSnaplineOnResizing = function (\n  enableOnResizing?: boolean,\n) {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    snapline.toggleOnResizing(enableOnResizing)\n  }\n  return this\n}\n\nGraph.prototype.isSharpSnapline = function () {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    return snapline.isSharp()\n  }\n  return false\n}\n\nGraph.prototype.enableSharpSnapline = function () {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    snapline.enableSharp()\n  }\n  return this\n}\n\nGraph.prototype.disableSharpSnapline = function () {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    snapline.disableSharp()\n  }\n  return this\n}\n\nGraph.prototype.toggleSharpSnapline = function (sharp?: boolean) {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    snapline.toggleSharp(sharp)\n  }\n  return this\n}\n\nGraph.prototype.getSnaplineTolerance = function () {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    return snapline.getTolerance()\n  }\n}\n\nGraph.prototype.setSnaplineTolerance = function (tolerance: number) {\n  const snapline = this.getPlugin('snapline') as Snapline\n  if (snapline) {\n    snapline.setTolerance(tolerance)\n  }\n  return this\n}\n", "import { Disposable, <PERSON><PERSON><PERSON>oa<PERSON>, Graph, EventArgs } from '@antv/x6'\nimport { SnaplineImpl } from './snapline'\nimport { content } from './style/raw'\nimport './api'\n\nexport class Snapline extends Disposable implements Graph.Plugin {\n  public name = 'snapline'\n  private snaplineImpl: SnaplineImpl\n  public options: Snapline.Options\n\n  constructor(options: Snapline.Options = {}) {\n    super()\n    this.options = { enabled: true, tolerance: 10, ...options }\n    CssLoader.ensure(this.name, content)\n  }\n\n  public init(graph: Graph) {\n    this.snaplineImpl = new SnaplineImpl({\n      ...this.options,\n      graph,\n    })\n  }\n\n  // #region api\n\n  isEnabled() {\n    return !this.snaplineImpl.disabled\n  }\n\n  enable() {\n    this.snaplineImpl.enable()\n  }\n\n  disable() {\n    this.snaplineImpl.disable()\n  }\n\n  toggleEnabled(enabled?: boolean) {\n    if (enabled != null) {\n      if (enabled !== this.isEnabled()) {\n        if (enabled) {\n          this.enable()\n        } else {\n          this.disable()\n        }\n      }\n    } else {\n      if (this.isEnabled()) {\n        this.disable()\n      } else {\n        this.enable()\n      }\n      return this\n    }\n  }\n\n  hide() {\n    this.snaplineImpl.hide()\n    return this\n  }\n\n  setFilter(filter?: SnaplineImpl.Filter) {\n    this.snaplineImpl.setFilter(filter)\n    return this\n  }\n\n  isOnResizingEnabled() {\n    return this.snaplineImpl.options.resizing === true\n  }\n\n  enableOnResizing() {\n    this.snaplineImpl.options.resizing = true\n    return this\n  }\n\n  disableOnResizing() {\n    this.snaplineImpl.options.resizing = false\n    return this\n  }\n\n  toggleOnResizing(enableOnResizing?: boolean) {\n    if (enableOnResizing != null) {\n      if (enableOnResizing !== this.isOnResizingEnabled()) {\n        if (enableOnResizing) {\n          this.enableOnResizing()\n        } else {\n          this.disableOnResizing()\n        }\n      }\n    } else if (this.isOnResizingEnabled()) {\n      this.disableOnResizing()\n    } else {\n      this.enableOnResizing()\n    }\n    return this\n  }\n\n  isSharp() {\n    return this.snaplineImpl.options.sharp === true\n  }\n\n  enableSharp() {\n    this.snaplineImpl.options.sharp = true\n    return this\n  }\n\n  disableSharp() {\n    this.snaplineImpl.options.sharp = false\n    return this\n  }\n\n  toggleSharp(sharp?: boolean) {\n    if (sharp != null) {\n      if (sharp !== this.isSharp()) {\n        if (sharp) {\n          this.enableSharp()\n        } else {\n          this.disableSharp()\n        }\n      }\n    } else if (this.isSharp()) {\n      this.disableSharp()\n    } else {\n      this.enableSharp()\n    }\n    return this\n  }\n\n  getTolerance() {\n    return this.snaplineImpl.options.tolerance\n  }\n\n  setTolerance(tolerance: number) {\n    this.snaplineImpl.options.tolerance = tolerance\n    return this\n  }\n\n  captureCursorOffset(e: EventArgs['node:mousedown']) {\n    this.snaplineImpl.captureCursorOffset(e)\n  }\n\n  snapOnMoving(args: EventArgs['node:mousemove']) {\n    this.snaplineImpl.snapOnMoving(args)\n  }\n\n  // #endregion\n\n  @Disposable.dispose()\n  dispose() {\n    this.snaplineImpl.dispose()\n    CssLoader.clean(this.name)\n  }\n}\n\nexport namespace Snapline {\n  export interface Options extends SnaplineImpl.Options {}\n  export type Filter = SnaplineImpl.Filter\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBM,IAAO,eAAP,cAA4B,KAAI;EAWpC,IAAc,QAAK;AACjB,WAAO,KAAK,MAAM;EACpB;EAEA,IAAc,qBAAkB;AAC9B,WAAO,KAAK,gBAAgB,iBAAiB;EAC/C;EAEA,IAAc,oBAAiB;AAC7B,WAAO,GAAG,KAAK,kBAAkB;EACnC;EAEA,IAAc,sBAAmB;AAC/B,WAAO,GAAG,KAAK,kBAAkB;EACnC;EAEA,YAAY,SAAgD;AAC1D,UAAK;AAEL,UAAM,EAAE,MAAK,IAAgB,SAAX,SAAM,OAAK,SAAvB,CAAA,OAAA,CAAoB;AAC1B,SAAK,QAAQ;AACb,SAAK,UAAO,OAAA,OAAA,CAAA,GAAQ,MAAM;AAC1B,SAAK,SAAS,EAAE,GAAG,GAAG,GAAG,EAAC;AAC1B,SAAK,OAAM;AACX,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,eAAc;;EAEvB;EAEA,IAAW,WAAQ;AACjB,WAAO,KAAK,QAAQ,YAAY;EAClC;EAEA,SAAM;AACJ,QAAI,KAAK,UAAU;AACjB,WAAK,QAAQ,UAAU;AACvB,WAAK,eAAc;;EAEvB;EAEA,UAAO;AACL,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,QAAQ,UAAU;AACvB,WAAK,cAAa;;EAEtB;EAEA,UAAU,QAA4B;AACpC,SAAK,QAAQ,SAAS;EACxB;EAEU,SAAM;AACd,UAAM,YAAa,KAAK,mBAAmB,IAAI,OAAO,KAAK;AAC3D,UAAM,aAAc,KAAK,aAAa,IAAI,OAAO,MAAM;AACvD,UAAM,WAAY,KAAK,WAAW,IAAI,OAAO,MAAM;AAEnD,cAAU,SAAS,KAAK,kBAAkB;AAC1C,eAAW,SAAS,KAAK,mBAAmB;AAC5C,aAAS,SAAS,KAAK,iBAAiB;AAExC,cAAU,aAAa,SAAS,MAAM;AACtC,cAAU,aAAa,UAAU,MAAM;AAEvC,eAAW,aAAa,WAAW,MAAM;AACzC,aAAS,aAAa,WAAW,MAAM;AAEvC,cAAU,OAAO,CAAC,YAAY,QAAQ,CAAC;AAEvC,QAAI,KAAK,QAAQ,WAAW;AAC1B,gBAAU,SAAS,KAAK,QAAQ,SAAS;;AAG3C,SAAK,YAAY,KAAK,iBAAiB;EACzC;EAEU,iBAAc;AACtB,SAAK,cAAa;AAClB,SAAK,MAAM,GAAG,kBAAkB,KAAK,qBAAqB,IAAI;AAC9D,SAAK,MAAM,GAAG,kBAAkB,KAAK,cAAc,IAAI;AACvD,SAAK,MAAM,GAAG,cAAc,KAAK,aAAa,IAAI;AAClD,SAAK,uBAAuB;MAC1B,SAAS;MACT,UAAU;KACX;EACH;EAEU,gBAAa;AACrB,SAAK,MAAM,IAAI,kBAAkB,KAAK,qBAAqB,IAAI;AAC/D,SAAK,MAAM,IAAI,kBAAkB,KAAK,cAAc,IAAI;AACxD,SAAK,MAAM,IAAI,cAAc,KAAK,aAAa,IAAI;AACnD,SAAK,yBAAwB;EAC/B;EAEU,YAAY,EAAE,MAAM,KAAI,GAAiC;AACjE,QAAI,SAAS,UAAU;AACrB,WAAK,eAAe,KAAK,MAAM,IAA0B;;EAE7D;EAEA,oBAAoB,EAAE,MAAM,GAAG,EAAC,GAA+B;AAC7D,UAAM,aAAa,KAAK,iBAAgB;AACxC,QAAI,cAAc,KAAK,cAAc,UAAU,GAAG;AAChD,YAAM,MAAM,KAAK,KAAK,YAAW;AACjC,WAAK,SAAS;QACZ,GAAG,IAAI,IAAI;QACX,GAAG,IAAI,IAAI;;;EAGjB;EAEU,cAAc,MAAc;AACpC,WAAO,QAAQ,KAAK,KAAK,OAAM,KAAM,KAAK,IAAI,aAAa;EAC7D;EAEU,gBAAgB,MAAe;AACvC,UAAM,WAAW,KAAK,MAAM,QAAQ,YAAY;AAChD,UAAM,OACJ,OAAO,aAAa,aAChB,aAAY,KAAK,UAAU,KAAK,OAAO,IAAK,IAC5C;AAEN,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,KAAK,MAAM,UAAU,aAAY,EAAG,QAAQ,IAAI;;AAGzD,QAAI,SAAS,MAAM;AACjB,aAAO,KAAK,MAAM,UAAU,aAAY;;AAG1C,WAAO,QAAQ;EACjB;EAEU,eAAe,MAAY,SAA2B;AAC9D,QACE,KAAK,QAAQ,YACb,CAAC,QAAQ,WACT,QAAQ,MACR,QAAQ,aACR,QAAQ,eACR;AACA,YAAM,OAAO,KAAK,MAAM,SAAS,eAAe,IAAI;AACpD,UAAI,QAAQ,KAAK,KAAK,OAAM,GAAI;AAC9B,cAAM,WAAW,KAAK,QAAO;AAC7B,cAAM,kBAAkB,SAAS,KAAK,KAAK,SAAQ,CAAE;AACrD,cAAM,cAAc,gBAAgB,WAAU;AAC9C,cAAM,kBAAkB,gBAAgB,eAAc;AACtD,cAAM,QAAQ,MAAM,UAAU,KAAK,SAAQ,CAAE;AAC7C,cAAM,YAAY,KAAK,QAAQ,aAAa;AAC5C,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AAEJ,cAAM,aAAa;UACjB,UAAU;UACV,YAAY;;AAGd,cAAM,YAAY,QAAQ;AAC1B,cAAM,gBAAgB,QAAQ;AAC9B,cAAM,oBAAoB,QAAQ;AAElC,YAAI,cAAc,QAAQ,OAAO,MAAM,IAAI;AACzC,qBAAW,WAAW,gBAAgB;eACjC;AACL,qBAAW,WAAW,YAAY;;AAGpC,YAAI,cAAc,QAAQ,QAAQ,MAAM,IAAI;AAC1C,qBAAW,aAAa,gBAAgB;eACnC;AACL,qBAAW,aAAa,YAAY;;AAGtC,aAAK,MAAM,SAAQ,EAAG,KAAK,CAAC,SAAQ;AAClC,cAAI,KAAK,UAAU,MAAM,IAAI,GAAG;AAC9B,mBAAO;;AAGT,gBAAM,WAAW,KAAK,QAAO,EAAG,KAAK,KAAK,SAAQ,CAAE;AACpD,gBAAM,cAAc,SAAS,WAAU;AACvC,gBAAM,kBAAkB,SAAS,eAAc;AAC/C,gBAAM,SAAS;YACb,UAAU,CAAC,YAAY,GAAG,gBAAgB,CAAC;YAC3C,YAAY,CAAC,YAAY,GAAG,gBAAgB,CAAC;;AAG/C,gBAAM,YAAY,CAAA;AAKlB,iBAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAK;AAChC,kBAAM,MAAM;AACZ,kBAAM,OAAO,OAAO,GAAG,EACpB,IAAI,CAAC,WAAW;cACf,UAAU;cACV,UAAU,KAAK,IAAI,QAAQ,WAAW,GAAG,CAAC;cAC1C,EACD,OAAO,CAAC,SAAS,KAAK,YAAY,SAAS;AAE9C,sBAAU,GAAG,IAAI,cAAS,OAAO,MAAM,CAAC,SAAS,KAAK,QAAQ;UAChE,CAAC;AAED,cAAI,gBAAgB,QAAQ,UAAU,SAAS,SAAS,GAAG;AACzD,2BAAe,UAAU,SAAS,CAAC,EAAE;AACrC,0BAAc,KAAK,IAAI,gBAAgB,GAAG,SAAS,CAAC;AACpD,6BACE,KAAK,IAAI,gBAAgB,GAAG,gBAAgB,CAAC,IAAI;;AAGrD,cAAI,iBAAiB,QAAQ,UAAU,WAAW,SAAS,GAAG;AAC5D,4BAAgB,UAAU,WAAW,CAAC,EAAE;AACxC,6BAAiB,KAAK,IAAI,gBAAgB,GAAG,SAAS,CAAC;AACvD,8BACE,KAAK,IAAI,gBAAgB,GAAG,gBAAgB,CAAC,IAAI;;AAGrD,iBAAO,gBAAgB,QAAQ,iBAAiB;QAClD,CAAC;AAED,aAAK,KAAI;AAET,YAAI,KAAK;AACT,YAAI,KAAK;AACT,YAAI,iBAAiB,QAAQ,gBAAgB,MAAM;AACjD,cAAI,gBAAgB,MAAM;AACxB,iBACE,cAAc,QAAQ,OAAO,MAAM,KAC/B,eAAe,gBAAgB,IAC/B,YAAY,IAAI;;AAGxB,cAAI,iBAAiB,MAAM;AACzB,iBACE,cAAc,QAAQ,QAAQ,MAAM,KAChC,gBAAgB,gBAAgB,IAChC,YAAY,IAAI;;;AAI1B,YAAI,SAAS;AACb,YAAI,UAAU;AACd,YAAI,QAAQ,OAAO,GAAG;AACpB,cAAI,UAAU,MAAM,UAAU,KAAK;AACjC,qBAAS;AACT,sBAAU;iBACL;AACL,qBAAS;AACT,sBAAU;;eAEP;AACL,gBAAM,WACJ,SAAS,KAAK,QAAQ,KAClB,IACA,SAAS,MAAM,QAAQ,MACvB,IACA,SAAS,OAAO,QAAQ,MACxB,IACA;AAEN,cAAI,iBAAiB,QAAQ,gBAAgB,MAAM;AACjD,gBAAI,KAAK,IAAI;AACX,mBAAK;AACL,8BAAgB;mBACX;AACL,mBAAK;AACL,6BAAe;;;AAInB,gBAAM,MAAM,MAAM,MAAM,QAAQ,EAAE;AAClC,cAAI,IAAI;AACN,qBAAS,aAAa,IAAI,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,GAAG;;AAElE,cAAI,IAAI;AACN,sBAAU,aAAa,IAAI,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,GAAG;;AAGnE,gBAAM,aAAa,aAAa,KAAK,aAAa;AAClD,kBAAQ,mBAAmB;YACzB,KAAK;YACL,KAAK;AACH,wBAAU,KACN,MAAM,aAAa,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,KAC/C,MAAM,aAAa,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;AACnD;YACF,KAAK;YACL,KAAK;AACH,uBAAS,KACL,MAAM,aAAa,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,KAC/C,MAAM,aAAa,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;AACnD;YACF;AACE;;;AAIN,gBAAQ,mBAAmB;UACzB,KAAK;UACL,KAAK;AACH,qBAAS;AACT;UACF,KAAK;UACL,KAAK;AACH,sBAAU;AACV;UACF;AACE;;AAGJ,cAAM,WAAW,KAAK,MAAM,YAAW;AACvC,YAAI,WAAW,KAAK,IAAI,SAAS,QAAQ,QAAQ,QAAQ;AACzD,YAAI,YAAY,KAAK,IAAI,SAAS,SAAS,SAAS,QAAQ;AAE5D,YAAI,QAAQ,YAAY,QAAQ,WAAW,UAAU;AACnD,qBAAW,KAAK,IAAI,UAAU,QAAQ,QAAQ;;AAGhD,YAAI,QAAQ,aAAa,QAAQ,YAAY,UAAU;AACrD,sBAAY,KAAK,IAAI,WAAW,QAAQ,SAAS;;AAGnD,YAAI,QAAQ,UAAU;AACpB,qBAAW,KAAK,IAAI,UAAU,QAAQ,QAAQ;;AAGhD,YAAI,QAAQ,WAAW;AACrB,sBAAY,KAAK,IAAI,WAAW,QAAQ,SAAS;;AAGnD,YAAI,QAAQ,qBAAqB;AAC/B,cAAI,UAAU,QAAQ;AACpB,wBAAY,YAAY,SAAS,SAAS,SAAS;iBAC9C;AACL,uBAAW,aAAa,SAAS,QAAQ,SAAS;;;AAItD,YAAI,aAAa,SAAS,SAAS,cAAc,SAAS,QAAQ;AAChE,eAAK,OAAO,UAAU,WAAW;YAC/B;YACA;YACA;YACA,SAAS;YACT,WAAW,KAAK;YAChB,UAAU,KAAK,gBAAgB,IAAI;WACpC;AAED,cAAI,gBAAgB;AAClB,8BAAkB,YAAY,SAAS;;AAGzC,cAAI,iBAAiB;AACnB,+BAAmB,WAAW,SAAS;;;AAI3C,cAAM,iBAAiB,KAAK,QAAO,EAAG,KAAK,KAAK;AAChD,YACE,gBACA,KAAK,IAAI,eAAe,IAAI,YAAY,IAAI,KAC5C,KAAK,IAAI,eAAe,QAAQ,eAAe,IAAI,YAAY,IAAI,GACnE;AACA,yBAAe;;AAGjB,YACE,iBACA,KAAK,IAAI,eAAe,IAAI,aAAa,IAAI,KAC7C,KAAK,IAAI,eAAe,SAAS,eAAe,IAAI,aAAa,IAAI,GACrE;AACA,0BAAgB;;AAGlB,aAAK,OAAO;UACV;UACA;UACA;UACA;UACA;UACA;SACD;;;EAGP;EAEA,aAAa,EAAE,MAAM,GAAG,GAAG,EAAC,GAA+B;AACzD,UAAM,aAAuB,KAAK,aAAa,CAAC,EAAE,iBAAiB;AACnE,QAAI,CAAC,KAAK,cAAc,UAAU,GAAG;AACnC;;AAGF,UAAM,OAAO,WAAW;AACxB,UAAM,OAAO,KAAK,QAAO;AACzB,UAAM,WAAW,KAAK,YAAW;AACjC,UAAM,WAAW,IAAI,UACnB,IAAI,KAAK,OAAO,GAChB,IAAI,KAAK,OAAO,GAChB,KAAK,OACL,KAAK,MAAM;AAEb,UAAM,QAAQ,KAAK,SAAQ;AAC3B,UAAM,aAAa,SAAS,UAAS;AACrC,UAAM,kBAAkB,SAAS,KAAK,KAAK;AAC3C,UAAM,cAAc,gBAAgB,WAAU;AAC9C,UAAM,kBAAkB,gBAAgB,eAAc;AAEtD,UAAM,WAAW,KAAK,QAAQ,aAAa;AAC3C,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,cAAc;AAClB,QAAI,gBAAgB;AAEpB,SAAK,MAAM,SAAQ,EAAG,KAAK,CAAC,eAAc;AACxC,UAAI,KAAK,UAAU,MAAM,UAAU,GAAG;AACpC,eAAO;;AAGT,YAAM,WAAW,WAAW,QAAO,EAAG,KAAK,WAAW,SAAQ,CAAE;AAChE,YAAM,aAAa,SAAS,UAAS;AACrC,YAAM,cAAc,SAAS,WAAU;AACvC,YAAM,kBAAkB,SAAS,eAAc;AAE/C,UAAI,gBAAgB,MAAM;AACxB,YAAI,KAAK,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,UAAU;AACpD,yBAAe,WAAW;AAC1B,wBAAc;mBACL,KAAK,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,UAAU;AAC7D,yBAAe,YAAY;AAC3B,wBAAc;mBACL,KAAK,IAAI,YAAY,IAAI,gBAAgB,CAAC,IAAI,UAAU;AACjE,yBAAe,YAAY;AAC3B,wBAAc;mBACL,KAAK,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,IAAI,UAAU;AACrE,yBAAe,gBAAgB;AAC/B,wBAAc;mBACL,KAAK,IAAI,gBAAgB,IAAI,YAAY,CAAC,IAAI,UAAU;AACjE,yBAAe,gBAAgB;;AAGjC,YAAI,gBAAgB,MAAM;AACxB,wBAAc,KAAK,IAAI,gBAAgB,GAAG,SAAS,CAAC;AACpD,2BACE,KAAK,IAAI,gBAAgB,GAAG,gBAAgB,CAAC,IAAI;;;AAIvD,UAAI,iBAAiB,MAAM;AACzB,YAAI,KAAK,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,UAAU;AACpD,0BAAgB,WAAW;AAC3B,0BAAgB;mBACP,KAAK,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,UAAU;AAC7D,0BAAgB,YAAY;mBACnB,KAAK,IAAI,YAAY,IAAI,gBAAgB,CAAC,IAAI,UAAU;AACjE,0BAAgB,YAAY;AAC5B,0BAAgB;mBACP,KAAK,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,IAAI,UAAU;AACrE,0BAAgB,gBAAgB;AAChC,0BAAgB;mBACP,KAAK,IAAI,gBAAgB,IAAI,YAAY,CAAC,IAAI,UAAU;AACjE,0BAAgB,gBAAgB;;AAGlC,YAAI,iBAAiB,MAAM;AACzB,2BAAiB,KAAK,IAAI,gBAAgB,GAAG,SAAS,CAAC;AACvD,4BACE,KAAK,IAAI,gBAAgB,GAAG,gBAAgB,CAAC,IAAI;;;AAIvD,aAAO,gBAAgB,QAAQ,iBAAiB;IAClD,CAAC;AAED,SAAK,KAAI;AAET,QAAI,iBAAiB,QAAQ,gBAAgB,MAAM;AACjD,UAAI,iBAAiB,MAAM;AACzB,wBAAgB,IACd,gBAAgB,gBAAgB,gBAAgB;;AAGpD,UAAI,gBAAgB,MAAM;AACxB,wBAAgB,IAAI,eAAe,cAAc,gBAAgB;;AAGnE,YAAM,YAAY,gBAAgB,UAAS;AAC3C,YAAM,OAAO,UAAU,IAAI,SAAS,QAAQ;AAC5C,YAAM,OAAO,UAAU,IAAI,SAAS,SAAS;AAC7C,YAAM,KAAK,OAAO,SAAS;AAC3B,YAAM,KAAK,OAAO,SAAS;AAE3B,UAAI,OAAO,KAAK,OAAO,GAAG;AACxB,aAAK,UAAU,IAAI,IAAI;UACrB,SAAS;UACT,UAAU,KAAK,gBAAgB,UAAU;SAC1C;AAED,YAAI,iBAAiB;AACnB,6BAAmB;;AAGrB,YAAI,gBAAgB;AAClB,4BAAkB;;;AAItB,WAAK,OAAO;QACV;QACA;QACA;QACA;QACA;QACA;OACD;;EAEL;EAEU,UAAU,UAAgB,YAAgB;AAClD,WACE,WAAW,OAAO,SAAS,MAC3B,WAAW,eAAe,QAAQ,KAClC,CAAC,KAAK,OAAO,UAAU;EAE3B;EAEU,OAAO,MAAU;AACzB,UAAM,SAAS,KAAK,QAAQ;AAC5B,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAO,OAAO,KAAK,CAAC,SAAQ;AAC1B,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO,KAAK,UAAU;;AAExB,eAAO,KAAK,OAAO,KAAK;MAC1B,CAAC;;AAEH,QAAI,OAAO,WAAW,YAAY;AAChC,aAAO,aAAY,KAAK,QAAQ,KAAK,OAAO,IAAI;;AAGlD,WAAO;EACT;EAEU,OAAO,UAOhB;AAEC,QAAI,SAAS,eAAe;AAC1B,YAAM,QAAQ,KAAK,MAAM,aACvB,IAAI,MAAM,SAAS,gBAAgB,SAAS,aAAa,CAAC;AAE5D,YAAM,MAAM,KAAK,MAAM,aACrB,IAAI,MACF,SAAS,iBAAkB,SAAS,iBACpC,SAAS,aAAa,CACvB;AAEH,WAAK,WAAW,cAAc;QAC5B,IAAI,KAAK,QAAQ,QAAQ,GAAG,MAAM,CAAC,KAAK;QACxC,IAAI,GAAG,MAAM,CAAC;QACd,IAAI,KAAK,QAAQ,QAAQ,GAAG,IAAI,CAAC,KAAK;QACtC,IAAI,GAAG,IAAI,CAAC;QACZ,SAAS;OACV;WACI;AACL,WAAK,WAAW,aAAa,WAAW,MAAM;;AAGhD,QAAI,SAAS,cAAc;AACzB,YAAM,QAAQ,KAAK,MAAM,aACvB,IAAI,MAAM,SAAS,cAAc,SAAS,WAAW,CAAC;AAExD,YAAM,MAAM,KAAK,MAAM,aACrB,IAAI,MACF,SAAS,cACT,SAAS,cAAe,SAAS,cAAe,CACjD;AAEH,WAAK,SAAS,cAAc;QAC1B,IAAI,GAAG,MAAM,CAAC;QACd,IAAI,KAAK,QAAQ,QAAQ,GAAG,MAAM,CAAC,KAAK;QACxC,IAAI,GAAG,IAAI,CAAC;QACZ,IAAI,KAAK,QAAQ,QAAQ,GAAG,IAAI,CAAC,KAAK;QACtC,SAAS;OACV;WACI;AACL,WAAK,SAAS,aAAa,WAAW,MAAM;;AAG9C,SAAK,KAAI;EACX;EAEU,aAAU;AAClB,QAAI,KAAK,OAAO;AACd,mBAAa,KAAK,KAAK;AACvB,WAAK,QAAQ;;EAEjB;EAEA,OAAI;AACF,SAAK,WAAU;AACf,QAAI,KAAK,UAAU,cAAc,MAAM;AACrC,WAAK,MAAM,UAAU,YAAY,KAAK,SAAS;;AAEjD,WAAO;EACT;EAEA,OAAI;AACF,SAAK,WAAU;AACf,SAAK,SAAS,aAAa,WAAW,MAAM;AAC5C,SAAK,WAAW,aAAa,WAAW,MAAM;AAC9C,UAAM,QAAQ,KAAK,QAAQ;AAC3B,UAAM,QAAQ,OAAO,UAAU,WAAW,QAAQ,UAAU,QAAQ,MAAO;AAC3E,QAAI,QAAQ,GAAG;AACb,WAAK,QAAQ,OAAO,WAAW,MAAK;AAClC,YAAI,KAAK,UAAU,eAAe,MAAM;AACtC,eAAK,QAAO;;MAEhB,GAAG,KAAK;;AAEV,WAAO;EACT;EAEU,WAAQ;AAChB,SAAK,cAAa;AAClB,SAAK,KAAI;EACX;EAGA,UAAO;AACL,SAAK,OAAM;EACb;;AAFA,WAAA;EADC,KAAK,QAAO;;;;ACrpBR,IAAM,UAAU;;;;;;;;;;;;;;;;ACkBvB,MAAM,UAAU,oBAAoB,WAAA;AAClC,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,WAAO,SAAS,UAAS;;AAE3B,SAAO;AACT;AAEA,MAAM,UAAU,iBAAiB,WAAA;AAC/B,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,aAAS,OAAM;;AAEjB,SAAO;AACT;AAEA,MAAM,UAAU,kBAAkB,WAAA;AAChC,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,aAAS,QAAO;;AAElB,SAAO;AACT;AAEA,MAAM,UAAU,iBAAiB,WAAA;AAC/B,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,aAAS,cAAa;;AAExB,SAAO;AACT;AAEA,MAAM,UAAU,eAAe,WAAA;AAC7B,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,aAAS,KAAI;;AAEf,SAAO;AACT;AAEA,MAAM,UAAU,oBAAoB,SAAU,QAAwB;AACpE,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,aAAS,UAAU,MAAM;;AAE3B,SAAO;AACT;AAEA,MAAM,UAAU,8BAA8B,WAAA;AAC5C,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,WAAO,SAAS,oBAAmB;;AAErC,SAAO;AACT;AAEA,MAAM,UAAU,2BAA2B,WAAA;AACzC,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,aAAS,iBAAgB;;AAE3B,SAAO;AACT;AAEA,MAAM,UAAU,4BAA4B,WAAA;AAC1C,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,aAAS,kBAAiB;;AAE5B,SAAO;AACT;AAEA,MAAM,UAAU,2BAA2B,SACzC,kBAA0B;AAE1B,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,aAAS,iBAAiB,gBAAgB;;AAE5C,SAAO;AACT;AAEA,MAAM,UAAU,kBAAkB,WAAA;AAChC,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,WAAO,SAAS,QAAO;;AAEzB,SAAO;AACT;AAEA,MAAM,UAAU,sBAAsB,WAAA;AACpC,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,aAAS,YAAW;;AAEtB,SAAO;AACT;AAEA,MAAM,UAAU,uBAAuB,WAAA;AACrC,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,aAAS,aAAY;;AAEvB,SAAO;AACT;AAEA,MAAM,UAAU,sBAAsB,SAAU,OAAe;AAC7D,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,aAAS,YAAY,KAAK;;AAE5B,SAAO;AACT;AAEA,MAAM,UAAU,uBAAuB,WAAA;AACrC,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,WAAO,SAAS,aAAY;;AAEhC;AAEA,MAAM,UAAU,uBAAuB,SAAU,WAAiB;AAChE,QAAM,WAAW,KAAK,UAAU,UAAU;AAC1C,MAAI,UAAU;AACZ,aAAS,aAAa,SAAS;;AAEjC,SAAO;AACT;;;;;;;;;AClJM,IAAO,WAAP,cAAwB,WAAU;EAKtC,YAAY,UAA4B,CAAA,GAAE;AACxC,UAAK;AALA,SAAA,OAAO;AAMZ,SAAK,UAAO,OAAA,OAAA,EAAK,SAAS,MAAM,WAAW,GAAE,GAAK,OAAO;AACzD,mBAAU,OAAO,KAAK,MAAM,OAAO;EACrC;EAEO,KAAK,OAAY;AACtB,SAAK,eAAe,IAAI,aAAY,OAAA,OAAA,OAAA,OAAA,CAAA,GAC/B,KAAK,OAAO,GAAA,EACf,MAAK,CAAA,CAAA;EAET;;EAIA,YAAS;AACP,WAAO,CAAC,KAAK,aAAa;EAC5B;EAEA,SAAM;AACJ,SAAK,aAAa,OAAM;EAC1B;EAEA,UAAO;AACL,SAAK,aAAa,QAAO;EAC3B;EAEA,cAAc,SAAiB;AAC7B,QAAI,WAAW,MAAM;AACnB,UAAI,YAAY,KAAK,UAAS,GAAI;AAChC,YAAI,SAAS;AACX,eAAK,OAAM;eACN;AACL,eAAK,QAAO;;;WAGX;AACL,UAAI,KAAK,UAAS,GAAI;AACpB,aAAK,QAAO;aACP;AACL,aAAK,OAAM;;AAEb,aAAO;;EAEX;EAEA,OAAI;AACF,SAAK,aAAa,KAAI;AACtB,WAAO;EACT;EAEA,UAAU,QAA4B;AACpC,SAAK,aAAa,UAAU,MAAM;AAClC,WAAO;EACT;EAEA,sBAAmB;AACjB,WAAO,KAAK,aAAa,QAAQ,aAAa;EAChD;EAEA,mBAAgB;AACd,SAAK,aAAa,QAAQ,WAAW;AACrC,WAAO;EACT;EAEA,oBAAiB;AACf,SAAK,aAAa,QAAQ,WAAW;AACrC,WAAO;EACT;EAEA,iBAAiB,kBAA0B;AACzC,QAAI,oBAAoB,MAAM;AAC5B,UAAI,qBAAqB,KAAK,oBAAmB,GAAI;AACnD,YAAI,kBAAkB;AACpB,eAAK,iBAAgB;eAChB;AACL,eAAK,kBAAiB;;;eAGjB,KAAK,oBAAmB,GAAI;AACrC,WAAK,kBAAiB;WACjB;AACL,WAAK,iBAAgB;;AAEvB,WAAO;EACT;EAEA,UAAO;AACL,WAAO,KAAK,aAAa,QAAQ,UAAU;EAC7C;EAEA,cAAW;AACT,SAAK,aAAa,QAAQ,QAAQ;AAClC,WAAO;EACT;EAEA,eAAY;AACV,SAAK,aAAa,QAAQ,QAAQ;AAClC,WAAO;EACT;EAEA,YAAY,OAAe;AACzB,QAAI,SAAS,MAAM;AACjB,UAAI,UAAU,KAAK,QAAO,GAAI;AAC5B,YAAI,OAAO;AACT,eAAK,YAAW;eACX;AACL,eAAK,aAAY;;;eAGZ,KAAK,QAAO,GAAI;AACzB,WAAK,aAAY;WACZ;AACL,WAAK,YAAW;;AAElB,WAAO;EACT;EAEA,eAAY;AACV,WAAO,KAAK,aAAa,QAAQ;EACnC;EAEA,aAAa,WAAiB;AAC5B,SAAK,aAAa,QAAQ,YAAY;AACtC,WAAO;EACT;EAEA,oBAAoB,GAA8B;AAChD,SAAK,aAAa,oBAAoB,CAAC;EACzC;EAEA,aAAa,MAAiC;AAC5C,SAAK,aAAa,aAAa,IAAI;EACrC;;EAKA,UAAO;AACL,SAAK,aAAa,QAAO;AACzB,mBAAU,MAAM,KAAK,IAAI;EAC3B;;AAHAA,YAAA;EADC,WAAW,QAAO;;", "names": ["__decorate"]}