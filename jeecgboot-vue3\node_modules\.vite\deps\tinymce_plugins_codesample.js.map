{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/codesample/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/codesample/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n\n    const noop = () => {\n    };\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const get$1 = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    const head = xs => get$1(xs, 0);\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    const Global = typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const prismjs = function (global, module, exports) {\n      const oldprism = window.Prism;\n      window.Prism = { manual: true };\n      var _self = typeof window !== 'undefined' ? window : typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope ? self : {};\n      var Prism = function (_self) {\n        var lang = /(?:^|\\s)lang(?:uage)?-([\\w-]+)(?=\\s|$)/i;\n        var uniqueId = 0;\n        var plainTextGrammar = {};\n        var _ = {\n          manual: _self.Prism && _self.Prism.manual,\n          disableWorkerMessageHandler: _self.Prism && _self.Prism.disableWorkerMessageHandler,\n          util: {\n            encode: function encode(tokens) {\n              if (tokens instanceof Token) {\n                return new Token(tokens.type, encode(tokens.content), tokens.alias);\n              } else if (Array.isArray(tokens)) {\n                return tokens.map(encode);\n              } else {\n                return tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\\u00a0/g, ' ');\n              }\n            },\n            type: function (o) {\n              return Object.prototype.toString.call(o).slice(8, -1);\n            },\n            objId: function (obj) {\n              if (!obj['__id']) {\n                Object.defineProperty(obj, '__id', { value: ++uniqueId });\n              }\n              return obj['__id'];\n            },\n            clone: function deepClone(o, visited) {\n              visited = visited || {};\n              var clone;\n              var id;\n              switch (_.util.type(o)) {\n              case 'Object':\n                id = _.util.objId(o);\n                if (visited[id]) {\n                  return visited[id];\n                }\n                clone = {};\n                visited[id] = clone;\n                for (var key in o) {\n                  if (o.hasOwnProperty(key)) {\n                    clone[key] = deepClone(o[key], visited);\n                  }\n                }\n                return clone;\n              case 'Array':\n                id = _.util.objId(o);\n                if (visited[id]) {\n                  return visited[id];\n                }\n                clone = [];\n                visited[id] = clone;\n                o.forEach(function (v, i) {\n                  clone[i] = deepClone(v, visited);\n                });\n                return clone;\n              default:\n                return o;\n              }\n            },\n            getLanguage: function (element) {\n              while (element) {\n                var m = lang.exec(element.className);\n                if (m) {\n                  return m[1].toLowerCase();\n                }\n                element = element.parentElement;\n              }\n              return 'none';\n            },\n            setLanguage: function (element, language) {\n              element.className = element.className.replace(RegExp(lang, 'gi'), '');\n              element.classList.add('language-' + language);\n            },\n            currentScript: function () {\n              if (typeof document === 'undefined') {\n                return null;\n              }\n              if ('currentScript' in document && 1 < 2) {\n                return document.currentScript;\n              }\n              try {\n                throw new Error();\n              } catch (err) {\n                var src = (/at [^(\\r\\n]*\\((.*):[^:]+:[^:]+\\)$/i.exec(err.stack) || [])[1];\n                if (src) {\n                  var scripts = document.getElementsByTagName('script');\n                  for (var i in scripts) {\n                    if (scripts[i].src == src) {\n                      return scripts[i];\n                    }\n                  }\n                }\n                return null;\n              }\n            },\n            isActive: function (element, className, defaultActivation) {\n              var no = 'no-' + className;\n              while (element) {\n                var classList = element.classList;\n                if (classList.contains(className)) {\n                  return true;\n                }\n                if (classList.contains(no)) {\n                  return false;\n                }\n                element = element.parentElement;\n              }\n              return !!defaultActivation;\n            }\n          },\n          languages: {\n            plain: plainTextGrammar,\n            plaintext: plainTextGrammar,\n            text: plainTextGrammar,\n            txt: plainTextGrammar,\n            extend: function (id, redef) {\n              var lang = _.util.clone(_.languages[id]);\n              for (var key in redef) {\n                lang[key] = redef[key];\n              }\n              return lang;\n            },\n            insertBefore: function (inside, before, insert, root) {\n              root = root || _.languages;\n              var grammar = root[inside];\n              var ret = {};\n              for (var token in grammar) {\n                if (grammar.hasOwnProperty(token)) {\n                  if (token == before) {\n                    for (var newToken in insert) {\n                      if (insert.hasOwnProperty(newToken)) {\n                        ret[newToken] = insert[newToken];\n                      }\n                    }\n                  }\n                  if (!insert.hasOwnProperty(token)) {\n                    ret[token] = grammar[token];\n                  }\n                }\n              }\n              var old = root[inside];\n              root[inside] = ret;\n              _.languages.DFS(_.languages, function (key, value) {\n                if (value === old && key != inside) {\n                  this[key] = ret;\n                }\n              });\n              return ret;\n            },\n            DFS: function DFS(o, callback, type, visited) {\n              visited = visited || {};\n              var objId = _.util.objId;\n              for (var i in o) {\n                if (o.hasOwnProperty(i)) {\n                  callback.call(o, i, o[i], type || i);\n                  var property = o[i];\n                  var propertyType = _.util.type(property);\n                  if (propertyType === 'Object' && !visited[objId(property)]) {\n                    visited[objId(property)] = true;\n                    DFS(property, callback, null, visited);\n                  } else if (propertyType === 'Array' && !visited[objId(property)]) {\n                    visited[objId(property)] = true;\n                    DFS(property, callback, i, visited);\n                  }\n                }\n              }\n            }\n          },\n          plugins: {},\n          highlightAll: function (async, callback) {\n            _.highlightAllUnder(document, async, callback);\n          },\n          highlightAllUnder: function (container, async, callback) {\n            var env = {\n              callback: callback,\n              container: container,\n              selector: 'code[class*=\"language-\"], [class*=\"language-\"] code, code[class*=\"lang-\"], [class*=\"lang-\"] code'\n            };\n            _.hooks.run('before-highlightall', env);\n            env.elements = Array.prototype.slice.apply(env.container.querySelectorAll(env.selector));\n            _.hooks.run('before-all-elements-highlight', env);\n            for (var i = 0, element; element = env.elements[i++];) {\n              _.highlightElement(element, async === true, env.callback);\n            }\n          },\n          highlightElement: function (element, async, callback) {\n            var language = _.util.getLanguage(element);\n            var grammar = _.languages[language];\n            _.util.setLanguage(element, language);\n            var parent = element.parentElement;\n            if (parent && parent.nodeName.toLowerCase() === 'pre') {\n              _.util.setLanguage(parent, language);\n            }\n            var code = element.textContent;\n            var env = {\n              element: element,\n              language: language,\n              grammar: grammar,\n              code: code\n            };\n            function insertHighlightedCode(highlightedCode) {\n              env.highlightedCode = highlightedCode;\n              _.hooks.run('before-insert', env);\n              env.element.innerHTML = env.highlightedCode;\n              _.hooks.run('after-highlight', env);\n              _.hooks.run('complete', env);\n              callback && callback.call(env.element);\n            }\n            _.hooks.run('before-sanity-check', env);\n            parent = env.element.parentElement;\n            if (parent && parent.nodeName.toLowerCase() === 'pre' && !parent.hasAttribute('tabindex')) {\n              parent.setAttribute('tabindex', '0');\n            }\n            if (!env.code) {\n              _.hooks.run('complete', env);\n              callback && callback.call(env.element);\n              return;\n            }\n            _.hooks.run('before-highlight', env);\n            if (!env.grammar) {\n              insertHighlightedCode(_.util.encode(env.code));\n              return;\n            }\n            if (async && _self.Worker) {\n              var worker = new Worker(_.filename);\n              worker.onmessage = function (evt) {\n                insertHighlightedCode(evt.data);\n              };\n              worker.postMessage(JSON.stringify({\n                language: env.language,\n                code: env.code,\n                immediateClose: true\n              }));\n            } else {\n              insertHighlightedCode(_.highlight(env.code, env.grammar, env.language));\n            }\n          },\n          highlight: function (text, grammar, language) {\n            var env = {\n              code: text,\n              grammar: grammar,\n              language: language\n            };\n            _.hooks.run('before-tokenize', env);\n            if (!env.grammar) {\n              throw new Error('The language \"' + env.language + '\" has no grammar.');\n            }\n            env.tokens = _.tokenize(env.code, env.grammar);\n            _.hooks.run('after-tokenize', env);\n            return Token.stringify(_.util.encode(env.tokens), env.language);\n          },\n          tokenize: function (text, grammar) {\n            var rest = grammar.rest;\n            if (rest) {\n              for (var token in rest) {\n                grammar[token] = rest[token];\n              }\n              delete grammar.rest;\n            }\n            var tokenList = new LinkedList();\n            addAfter(tokenList, tokenList.head, text);\n            matchGrammar(text, tokenList, grammar, tokenList.head, 0);\n            return toArray(tokenList);\n          },\n          hooks: {\n            all: {},\n            add: function (name, callback) {\n              var hooks = _.hooks.all;\n              hooks[name] = hooks[name] || [];\n              hooks[name].push(callback);\n            },\n            run: function (name, env) {\n              var callbacks = _.hooks.all[name];\n              if (!callbacks || !callbacks.length) {\n                return;\n              }\n              for (var i = 0, callback; callback = callbacks[i++];) {\n                callback(env);\n              }\n            }\n          },\n          Token: Token\n        };\n        _self.Prism = _;\n        function Token(type, content, alias, matchedStr) {\n          this.type = type;\n          this.content = content;\n          this.alias = alias;\n          this.length = (matchedStr || '').length | 0;\n        }\n        Token.stringify = function stringify(o, language) {\n          if (typeof o == 'string') {\n            return o;\n          }\n          if (Array.isArray(o)) {\n            var s = '';\n            o.forEach(function (e) {\n              s += stringify(e, language);\n            });\n            return s;\n          }\n          var env = {\n            type: o.type,\n            content: stringify(o.content, language),\n            tag: 'span',\n            classes: [\n              'token',\n              o.type\n            ],\n            attributes: {},\n            language: language\n          };\n          var aliases = o.alias;\n          if (aliases) {\n            if (Array.isArray(aliases)) {\n              Array.prototype.push.apply(env.classes, aliases);\n            } else {\n              env.classes.push(aliases);\n            }\n          }\n          _.hooks.run('wrap', env);\n          var attributes = '';\n          for (var name in env.attributes) {\n            attributes += ' ' + name + '=\"' + (env.attributes[name] || '').replace(/\"/g, '&quot;') + '\"';\n          }\n          return '<' + env.tag + ' class=\"' + env.classes.join(' ') + '\"' + attributes + '>' + env.content + '</' + env.tag + '>';\n        };\n        function matchPattern(pattern, pos, text, lookbehind) {\n          pattern.lastIndex = pos;\n          var match = pattern.exec(text);\n          if (match && lookbehind && match[1]) {\n            var lookbehindLength = match[1].length;\n            match.index += lookbehindLength;\n            match[0] = match[0].slice(lookbehindLength);\n          }\n          return match;\n        }\n        function matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n          for (var token in grammar) {\n            if (!grammar.hasOwnProperty(token) || !grammar[token]) {\n              continue;\n            }\n            var patterns = grammar[token];\n            patterns = Array.isArray(patterns) ? patterns : [patterns];\n            for (var j = 0; j < patterns.length; ++j) {\n              if (rematch && rematch.cause == token + ',' + j) {\n                return;\n              }\n              var patternObj = patterns[j];\n              var inside = patternObj.inside;\n              var lookbehind = !!patternObj.lookbehind;\n              var greedy = !!patternObj.greedy;\n              var alias = patternObj.alias;\n              if (greedy && !patternObj.pattern.global) {\n                var flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n                patternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');\n              }\n              var pattern = patternObj.pattern || patternObj;\n              for (var currentNode = startNode.next, pos = startPos; currentNode !== tokenList.tail; pos += currentNode.value.length, currentNode = currentNode.next) {\n                if (rematch && pos >= rematch.reach) {\n                  break;\n                }\n                var str = currentNode.value;\n                if (tokenList.length > text.length) {\n                  return;\n                }\n                if (str instanceof Token) {\n                  continue;\n                }\n                var removeCount = 1;\n                var match;\n                if (greedy) {\n                  match = matchPattern(pattern, pos, text, lookbehind);\n                  if (!match || match.index >= text.length) {\n                    break;\n                  }\n                  var from = match.index;\n                  var to = match.index + match[0].length;\n                  var p = pos;\n                  p += currentNode.value.length;\n                  while (from >= p) {\n                    currentNode = currentNode.next;\n                    p += currentNode.value.length;\n                  }\n                  p -= currentNode.value.length;\n                  pos = p;\n                  if (currentNode.value instanceof Token) {\n                    continue;\n                  }\n                  for (var k = currentNode; k !== tokenList.tail && (p < to || typeof k.value === 'string'); k = k.next) {\n                    removeCount++;\n                    p += k.value.length;\n                  }\n                  removeCount--;\n                  str = text.slice(pos, p);\n                  match.index -= pos;\n                } else {\n                  match = matchPattern(pattern, 0, str, lookbehind);\n                  if (!match) {\n                    continue;\n                  }\n                }\n                var from = match.index;\n                var matchStr = match[0];\n                var before = str.slice(0, from);\n                var after = str.slice(from + matchStr.length);\n                var reach = pos + str.length;\n                if (rematch && reach > rematch.reach) {\n                  rematch.reach = reach;\n                }\n                var removeFrom = currentNode.prev;\n                if (before) {\n                  removeFrom = addAfter(tokenList, removeFrom, before);\n                  pos += before.length;\n                }\n                removeRange(tokenList, removeFrom, removeCount);\n                var wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n                currentNode = addAfter(tokenList, removeFrom, wrapped);\n                if (after) {\n                  addAfter(tokenList, currentNode, after);\n                }\n                if (removeCount > 1) {\n                  var nestedRematch = {\n                    cause: token + ',' + j,\n                    reach: reach\n                  };\n                  matchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n                  if (rematch && nestedRematch.reach > rematch.reach) {\n                    rematch.reach = nestedRematch.reach;\n                  }\n                }\n              }\n            }\n          }\n        }\n        function LinkedList() {\n          var head = {\n            value: null,\n            prev: null,\n            next: null\n          };\n          var tail = {\n            value: null,\n            prev: head,\n            next: null\n          };\n          head.next = tail;\n          this.head = head;\n          this.tail = tail;\n          this.length = 0;\n        }\n        function addAfter(list, node, value) {\n          var next = node.next;\n          var newNode = {\n            value: value,\n            prev: node,\n            next: next\n          };\n          node.next = newNode;\n          next.prev = newNode;\n          list.length++;\n          return newNode;\n        }\n        function removeRange(list, node, count) {\n          var next = node.next;\n          for (var i = 0; i < count && next !== list.tail; i++) {\n            next = next.next;\n          }\n          node.next = next;\n          next.prev = node;\n          list.length -= i;\n        }\n        function toArray(list) {\n          var array = [];\n          var node = list.head.next;\n          while (node !== list.tail) {\n            array.push(node.value);\n            node = node.next;\n          }\n          return array;\n        }\n        if (!_self.document) {\n          if (!_self.addEventListener) {\n            return _;\n          }\n          if (!_.disableWorkerMessageHandler) {\n            _self.addEventListener('message', function (evt) {\n              var message = JSON.parse(evt.data);\n              var lang = message.language;\n              var code = message.code;\n              var immediateClose = message.immediateClose;\n              _self.postMessage(_.highlight(code, _.languages[lang], lang));\n              if (immediateClose) {\n                _self.close();\n              }\n            }, false);\n          }\n          return _;\n        }\n        var script = _.util.currentScript();\n        if (script) {\n          _.filename = script.src;\n          if (script.hasAttribute('data-manual')) {\n            _.manual = true;\n          }\n        }\n        function highlightAutomaticallyCallback() {\n          if (!_.manual) {\n            _.highlightAll();\n          }\n        }\n        if (!_.manual) {\n          var readyState = document.readyState;\n          if (readyState === 'loading' || readyState === 'interactive' && script && script.defer) {\n            document.addEventListener('DOMContentLoaded', highlightAutomaticallyCallback);\n          } else {\n            if (window.requestAnimationFrame) {\n              window.requestAnimationFrame(highlightAutomaticallyCallback);\n            } else {\n              window.setTimeout(highlightAutomaticallyCallback, 16);\n            }\n          }\n        }\n        return _;\n      }(_self);\n      if (typeof module !== 'undefined' && module.exports) {\n        module.exports = Prism;\n      }\n      if (typeof global !== 'undefined') {\n        global.Prism = Prism;\n      }\n      Prism.languages.clike = {\n        'comment': [\n          {\n            pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n            lookbehind: true,\n            greedy: true\n          },\n          {\n            pattern: /(^|[^\\\\:])\\/\\/.*/,\n            lookbehind: true,\n            greedy: true\n          }\n        ],\n        'string': {\n          pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n          greedy: true\n        },\n        'class-name': {\n          pattern: /(\\b(?:class|extends|implements|instanceof|interface|new|trait)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i,\n          lookbehind: true,\n          inside: { 'punctuation': /[.\\\\]/ }\n        },\n        'keyword': /\\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\\b/,\n        'boolean': /\\b(?:false|true)\\b/,\n        'function': /\\b\\w+(?=\\()/,\n        'number': /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n        'operator': /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/,\n        'punctuation': /[{}[\\];(),.:]/\n      };\n      (function (Prism) {\n        function getPlaceholder(language, index) {\n          return '___' + language.toUpperCase() + index + '___';\n        }\n        Object.defineProperties(Prism.languages['markup-templating'] = {}, {\n          buildPlaceholders: {\n            value: function (env, language, placeholderPattern, replaceFilter) {\n              if (env.language !== language) {\n                return;\n              }\n              var tokenStack = env.tokenStack = [];\n              env.code = env.code.replace(placeholderPattern, function (match) {\n                if (typeof replaceFilter === 'function' && !replaceFilter(match)) {\n                  return match;\n                }\n                var i = tokenStack.length;\n                var placeholder;\n                while (env.code.indexOf(placeholder = getPlaceholder(language, i)) !== -1) {\n                  ++i;\n                }\n                tokenStack[i] = match;\n                return placeholder;\n              });\n              env.grammar = Prism.languages.markup;\n            }\n          },\n          tokenizePlaceholders: {\n            value: function (env, language) {\n              if (env.language !== language || !env.tokenStack) {\n                return;\n              }\n              env.grammar = Prism.languages[language];\n              var j = 0;\n              var keys = Object.keys(env.tokenStack);\n              function walkTokens(tokens) {\n                for (var i = 0; i < tokens.length; i++) {\n                  if (j >= keys.length) {\n                    break;\n                  }\n                  var token = tokens[i];\n                  if (typeof token === 'string' || token.content && typeof token.content === 'string') {\n                    var k = keys[j];\n                    var t = env.tokenStack[k];\n                    var s = typeof token === 'string' ? token : token.content;\n                    var placeholder = getPlaceholder(language, k);\n                    var index = s.indexOf(placeholder);\n                    if (index > -1) {\n                      ++j;\n                      var before = s.substring(0, index);\n                      var middle = new Prism.Token(language, Prism.tokenize(t, env.grammar), 'language-' + language, t);\n                      var after = s.substring(index + placeholder.length);\n                      var replacement = [];\n                      if (before) {\n                        replacement.push.apply(replacement, walkTokens([before]));\n                      }\n                      replacement.push(middle);\n                      if (after) {\n                        replacement.push.apply(replacement, walkTokens([after]));\n                      }\n                      if (typeof token === 'string') {\n                        tokens.splice.apply(tokens, [\n                          i,\n                          1\n                        ].concat(replacement));\n                      } else {\n                        token.content = replacement;\n                      }\n                    }\n                  } else if (token.content) {\n                    walkTokens(token.content);\n                  }\n                }\n                return tokens;\n              }\n              walkTokens(env.tokens);\n            }\n          }\n        });\n      }(Prism));\n      Prism.languages.c = Prism.languages.extend('clike', {\n        'comment': {\n          pattern: /\\/\\/(?:[^\\r\\n\\\\]|\\\\(?:\\r\\n?|\\n|(?![\\r\\n])))*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n          greedy: true\n        },\n        'string': {\n          pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n          greedy: true\n        },\n        'class-name': {\n          pattern: /(\\b(?:enum|struct)\\s+(?:__attribute__\\s*\\(\\([\\s\\S]*?\\)\\)\\s*)?)\\w+|\\b[a-z]\\w*_t\\b/,\n          lookbehind: true\n        },\n        'keyword': /\\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b/,\n        'function': /\\b[a-z_]\\w*(?=\\s*\\()/i,\n        'number': /(?:\\b0x(?:[\\da-f]+(?:\\.[\\da-f]*)?|\\.[\\da-f]+)(?:p[+-]?\\d+)?|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)[ful]{0,4}/i,\n        'operator': />>=?|<<=?|->|([-+&|:])\\1|[?:~]|[-+*/%&|^!=<>]=?/\n      });\n      Prism.languages.insertBefore('c', 'string', {\n        'char': {\n          pattern: /'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n]){0,32}'/,\n          greedy: true\n        }\n      });\n      Prism.languages.insertBefore('c', 'string', {\n        'macro': {\n          pattern: /(^[\\t ]*)#\\s*[a-z](?:[^\\r\\n\\\\/]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\\\\(?:\\r\\n|[\\s\\S]))*/im,\n          lookbehind: true,\n          greedy: true,\n          alias: 'property',\n          inside: {\n            'string': [\n              {\n                pattern: /^(#\\s*include\\s*)<[^>]+>/,\n                lookbehind: true\n              },\n              Prism.languages.c['string']\n            ],\n            'char': Prism.languages.c['char'],\n            'comment': Prism.languages.c['comment'],\n            'macro-name': [\n              {\n                pattern: /(^#\\s*define\\s+)\\w+\\b(?!\\()/i,\n                lookbehind: true\n              },\n              {\n                pattern: /(^#\\s*define\\s+)\\w+\\b(?=\\()/i,\n                lookbehind: true,\n                alias: 'function'\n              }\n            ],\n            'directive': {\n              pattern: /^(#\\s*)[a-z]+/,\n              lookbehind: true,\n              alias: 'keyword'\n            },\n            'directive-hash': /^#/,\n            'punctuation': /##|\\\\(?=[\\r\\n])/,\n            'expression': {\n              pattern: /\\S[\\s\\S]*/,\n              inside: Prism.languages.c\n            }\n          }\n        }\n      });\n      Prism.languages.insertBefore('c', 'function', { 'constant': /\\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\\b/ });\n      delete Prism.languages.c['boolean'];\n      (function (Prism) {\n        var keyword = /\\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\\b/;\n        var modName = /\\b(?!<keyword>)\\w+(?:\\s*\\.\\s*\\w+)*\\b/.source.replace(/<keyword>/g, function () {\n          return keyword.source;\n        });\n        Prism.languages.cpp = Prism.languages.extend('c', {\n          'class-name': [\n            {\n              pattern: RegExp(/(\\b(?:class|concept|enum|struct|typename)\\s+)(?!<keyword>)\\w+/.source.replace(/<keyword>/g, function () {\n                return keyword.source;\n              })),\n              lookbehind: true\n            },\n            /\\b[A-Z]\\w*(?=\\s*::\\s*\\w+\\s*\\()/,\n            /\\b[A-Z_]\\w*(?=\\s*::\\s*~\\w+\\s*\\()/i,\n            /\\b\\w+(?=\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\\s*::\\s*\\w+\\s*\\()/\n          ],\n          'keyword': keyword,\n          'number': {\n            pattern: /(?:\\b0b[01']+|\\b0x(?:[\\da-f']+(?:\\.[\\da-f']*)?|\\.[\\da-f']+)(?:p[+-]?[\\d']+)?|(?:\\b[\\d']+(?:\\.[\\d']*)?|\\B\\.[\\d']+)(?:e[+-]?[\\d']+)?)[ful]{0,4}/i,\n            greedy: true\n          },\n          'operator': />>=?|<<=?|->|--|\\+\\+|&&|\\|\\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\\b/,\n          'boolean': /\\b(?:false|true)\\b/\n        });\n        Prism.languages.insertBefore('cpp', 'string', {\n          'module': {\n            pattern: RegExp(/(\\b(?:import|module)\\s+)/.source + '(?:' + /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|<[^<>\\r\\n]*>/.source + '|' + /<mod-name>(?:\\s*:\\s*<mod-name>)?|:\\s*<mod-name>/.source.replace(/<mod-name>/g, function () {\n              return modName;\n            }) + ')'),\n            lookbehind: true,\n            greedy: true,\n            inside: {\n              'string': /^[<\"][\\s\\S]+/,\n              'operator': /:/,\n              'punctuation': /\\./\n            }\n          },\n          'raw-string': {\n            pattern: /R\"([^()\\\\ ]{0,16})\\([\\s\\S]*?\\)\\1\"/,\n            alias: 'string',\n            greedy: true\n          }\n        });\n        Prism.languages.insertBefore('cpp', 'keyword', {\n          'generic-function': {\n            pattern: /\\b(?!operator\\b)[a-z_]\\w*\\s*<(?:[^<>]|<[^<>]*>)*>(?=\\s*\\()/i,\n            inside: {\n              'function': /^\\w+/,\n              'generic': {\n                pattern: /<[\\s\\S]+/,\n                alias: 'class-name',\n                inside: Prism.languages.cpp\n              }\n            }\n          }\n        });\n        Prism.languages.insertBefore('cpp', 'operator', {\n          'double-colon': {\n            pattern: /::/,\n            alias: 'punctuation'\n          }\n        });\n        Prism.languages.insertBefore('cpp', 'class-name', {\n          'base-clause': {\n            pattern: /(\\b(?:class|struct)\\s+\\w+\\s*:\\s*)[^;{}\"'\\s]+(?:\\s+[^;{}\"'\\s]+)*(?=\\s*[;{])/,\n            lookbehind: true,\n            greedy: true,\n            inside: Prism.languages.extend('cpp', {})\n          }\n        });\n        Prism.languages.insertBefore('inside', 'double-colon', { 'class-name': /\\b[a-z_]\\w*\\b(?!\\s*::)/i }, Prism.languages.cpp['base-clause']);\n      }(Prism));\n      (function (Prism) {\n        function replace(pattern, replacements) {\n          return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n            return '(?:' + replacements[+index] + ')';\n          });\n        }\n        function re(pattern, replacements, flags) {\n          return RegExp(replace(pattern, replacements), flags || '');\n        }\n        function nested(pattern, depthLog2) {\n          for (var i = 0; i < depthLog2; i++) {\n            pattern = pattern.replace(/<<self>>/g, function () {\n              return '(?:' + pattern + ')';\n            });\n          }\n          return pattern.replace(/<<self>>/g, '[^\\\\s\\\\S]');\n        }\n        var keywordKinds = {\n          type: 'bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void',\n          typeDeclaration: 'class enum interface record struct',\n          contextual: 'add alias and ascending async await by descending from(?=\\\\s*(?:\\\\w|$)) get global group into init(?=\\\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\\\s*{)',\n          other: 'abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield'\n        };\n        function keywordsToPattern(words) {\n          return '\\\\b(?:' + words.trim().replace(/ /g, '|') + ')\\\\b';\n        }\n        var typeDeclarationKeywords = keywordsToPattern(keywordKinds.typeDeclaration);\n        var keywords = RegExp(keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.typeDeclaration + ' ' + keywordKinds.contextual + ' ' + keywordKinds.other));\n        var nonTypeKeywords = keywordsToPattern(keywordKinds.typeDeclaration + ' ' + keywordKinds.contextual + ' ' + keywordKinds.other);\n        var nonContextualKeywords = keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.typeDeclaration + ' ' + keywordKinds.other);\n        var generic = nested(/<(?:[^<>;=+\\-*/%&|^]|<<self>>)*>/.source, 2);\n        var nestedRound = nested(/\\((?:[^()]|<<self>>)*\\)/.source, 2);\n        var name = /@?\\b[A-Za-z_]\\w*\\b/.source;\n        var genericName = replace(/<<0>>(?:\\s*<<1>>)?/.source, [\n          name,\n          generic\n        ]);\n        var identifier = replace(/(?!<<0>>)<<1>>(?:\\s*\\.\\s*<<1>>)*/.source, [\n          nonTypeKeywords,\n          genericName\n        ]);\n        var array = /\\[\\s*(?:,\\s*)*\\]/.source;\n        var typeExpressionWithoutTuple = replace(/<<0>>(?:\\s*(?:\\?\\s*)?<<1>>)*(?:\\s*\\?)?/.source, [\n          identifier,\n          array\n        ]);\n        var tupleElement = replace(/[^,()<>[\\];=+\\-*/%&|^]|<<0>>|<<1>>|<<2>>/.source, [\n          generic,\n          nestedRound,\n          array\n        ]);\n        var tuple = replace(/\\(<<0>>+(?:,<<0>>+)+\\)/.source, [tupleElement]);\n        var typeExpression = replace(/(?:<<0>>|<<1>>)(?:\\s*(?:\\?\\s*)?<<2>>)*(?:\\s*\\?)?/.source, [\n          tuple,\n          identifier,\n          array\n        ]);\n        var typeInside = {\n          'keyword': keywords,\n          'punctuation': /[<>()?,.:[\\]]/\n        };\n        var character = /'(?:[^\\r\\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'/.source;\n        var regularString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/.source;\n        var verbatimString = /@\"(?:\"\"|\\\\[\\s\\S]|[^\\\\\"])*\"(?!\")/.source;\n        Prism.languages.csharp = Prism.languages.extend('clike', {\n          'string': [\n            {\n              pattern: re(/(^|[^$\\\\])<<0>>/.source, [verbatimString]),\n              lookbehind: true,\n              greedy: true\n            },\n            {\n              pattern: re(/(^|[^@$\\\\])<<0>>/.source, [regularString]),\n              lookbehind: true,\n              greedy: true\n            }\n          ],\n          'class-name': [\n            {\n              pattern: re(/(\\busing\\s+static\\s+)<<0>>(?=\\s*;)/.source, [identifier]),\n              lookbehind: true,\n              inside: typeInside\n            },\n            {\n              pattern: re(/(\\busing\\s+<<0>>\\s*=\\s*)<<1>>(?=\\s*;)/.source, [\n                name,\n                typeExpression\n              ]),\n              lookbehind: true,\n              inside: typeInside\n            },\n            {\n              pattern: re(/(\\busing\\s+)<<0>>(?=\\s*=)/.source, [name]),\n              lookbehind: true\n            },\n            {\n              pattern: re(/(\\b<<0>>\\s+)<<1>>/.source, [\n                typeDeclarationKeywords,\n                genericName\n              ]),\n              lookbehind: true,\n              inside: typeInside\n            },\n            {\n              pattern: re(/(\\bcatch\\s*\\(\\s*)<<0>>/.source, [identifier]),\n              lookbehind: true,\n              inside: typeInside\n            },\n            {\n              pattern: re(/(\\bwhere\\s+)<<0>>/.source, [name]),\n              lookbehind: true\n            },\n            {\n              pattern: re(/(\\b(?:is(?:\\s+not)?|as)\\s+)<<0>>/.source, [typeExpressionWithoutTuple]),\n              lookbehind: true,\n              inside: typeInside\n            },\n            {\n              pattern: re(/\\b<<0>>(?=\\s+(?!<<1>>|with\\s*\\{)<<2>>(?:\\s*[=,;:{)\\]]|\\s+(?:in|when)\\b))/.source, [\n                typeExpression,\n                nonContextualKeywords,\n                name\n              ]),\n              inside: typeInside\n            }\n          ],\n          'keyword': keywords,\n          'number': /(?:\\b0(?:x[\\da-f_]*[\\da-f]|b[01_]*[01])|(?:\\B\\.\\d+(?:_+\\d+)*|\\b\\d+(?:_+\\d+)*(?:\\.\\d+(?:_+\\d+)*)?)(?:e[-+]?\\d+(?:_+\\d+)*)?)(?:[dflmu]|lu|ul)?\\b/i,\n          'operator': />>=?|<<=?|[-=]>|([-+&|])\\1|~|\\?\\?=?|[-+*/%&|^!=<>]=?/,\n          'punctuation': /\\?\\.?|::|[{}[\\];(),.:]/\n        });\n        Prism.languages.insertBefore('csharp', 'number', {\n          'range': {\n            pattern: /\\.\\./,\n            alias: 'operator'\n          }\n        });\n        Prism.languages.insertBefore('csharp', 'punctuation', {\n          'named-parameter': {\n            pattern: re(/([(,]\\s*)<<0>>(?=\\s*:)/.source, [name]),\n            lookbehind: true,\n            alias: 'punctuation'\n          }\n        });\n        Prism.languages.insertBefore('csharp', 'class-name', {\n          'namespace': {\n            pattern: re(/(\\b(?:namespace|using)\\s+)<<0>>(?:\\s*\\.\\s*<<0>>)*(?=\\s*[;{])/.source, [name]),\n            lookbehind: true,\n            inside: { 'punctuation': /\\./ }\n          },\n          'type-expression': {\n            pattern: re(/(\\b(?:default|sizeof|typeof)\\s*\\(\\s*(?!\\s))(?:[^()\\s]|\\s(?!\\s)|<<0>>)*(?=\\s*\\))/.source, [nestedRound]),\n            lookbehind: true,\n            alias: 'class-name',\n            inside: typeInside\n          },\n          'return-type': {\n            pattern: re(/<<0>>(?=\\s+(?:<<1>>\\s*(?:=>|[({]|\\.\\s*this\\s*\\[)|this\\s*\\[))/.source, [\n              typeExpression,\n              identifier\n            ]),\n            inside: typeInside,\n            alias: 'class-name'\n          },\n          'constructor-invocation': {\n            pattern: re(/(\\bnew\\s+)<<0>>(?=\\s*[[({])/.source, [typeExpression]),\n            lookbehind: true,\n            inside: typeInside,\n            alias: 'class-name'\n          },\n          'generic-method': {\n            pattern: re(/<<0>>\\s*<<1>>(?=\\s*\\()/.source, [\n              name,\n              generic\n            ]),\n            inside: {\n              'function': re(/^<<0>>/.source, [name]),\n              'generic': {\n                pattern: RegExp(generic),\n                alias: 'class-name',\n                inside: typeInside\n              }\n            }\n          },\n          'type-list': {\n            pattern: re(/\\b((?:<<0>>\\s+<<1>>|record\\s+<<1>>\\s*<<5>>|where\\s+<<2>>)\\s*:\\s*)(?:<<3>>|<<4>>|<<1>>\\s*<<5>>|<<6>>)(?:\\s*,\\s*(?:<<3>>|<<4>>|<<6>>))*(?=\\s*(?:where|[{;]|=>|$))/.source, [\n              typeDeclarationKeywords,\n              genericName,\n              name,\n              typeExpression,\n              keywords.source,\n              nestedRound,\n              /\\bnew\\s*\\(\\s*\\)/.source\n            ]),\n            lookbehind: true,\n            inside: {\n              'record-arguments': {\n                pattern: re(/(^(?!new\\s*\\()<<0>>\\s*)<<1>>/.source, [\n                  genericName,\n                  nestedRound\n                ]),\n                lookbehind: true,\n                greedy: true,\n                inside: Prism.languages.csharp\n              },\n              'keyword': keywords,\n              'class-name': {\n                pattern: RegExp(typeExpression),\n                greedy: true,\n                inside: typeInside\n              },\n              'punctuation': /[,()]/\n            }\n          },\n          'preprocessor': {\n            pattern: /(^[\\t ]*)#.*/m,\n            lookbehind: true,\n            alias: 'property',\n            inside: {\n              'directive': {\n                pattern: /(#)\\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\\b/,\n                lookbehind: true,\n                alias: 'keyword'\n              }\n            }\n          }\n        });\n        var regularStringOrCharacter = regularString + '|' + character;\n        var regularStringCharacterOrComment = replace(/\\/(?![*/])|\\/\\/[^\\r\\n]*[\\r\\n]|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>/.source, [regularStringOrCharacter]);\n        var roundExpression = nested(replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [regularStringCharacterOrComment]), 2);\n        var attrTarget = /\\b(?:assembly|event|field|method|module|param|property|return|type)\\b/.source;\n        var attr = replace(/<<0>>(?:\\s*\\(<<1>>*\\))?/.source, [\n          identifier,\n          roundExpression\n        ]);\n        Prism.languages.insertBefore('csharp', 'class-name', {\n          'attribute': {\n            pattern: re(/((?:^|[^\\s\\w>)?])\\s*\\[\\s*)(?:<<0>>\\s*:\\s*)?<<1>>(?:\\s*,\\s*<<1>>)*(?=\\s*\\])/.source, [\n              attrTarget,\n              attr\n            ]),\n            lookbehind: true,\n            greedy: true,\n            inside: {\n              'target': {\n                pattern: re(/^<<0>>(?=\\s*:)/.source, [attrTarget]),\n                alias: 'keyword'\n              },\n              'attribute-arguments': {\n                pattern: re(/\\(<<0>>*\\)/.source, [roundExpression]),\n                inside: Prism.languages.csharp\n              },\n              'class-name': {\n                pattern: RegExp(identifier),\n                inside: { 'punctuation': /\\./ }\n              },\n              'punctuation': /[:,]/\n            }\n          }\n        });\n        var formatString = /:[^}\\r\\n]+/.source;\n        var mInterpolationRound = nested(replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [regularStringCharacterOrComment]), 2);\n        var mInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [\n          mInterpolationRound,\n          formatString\n        ]);\n        var sInterpolationRound = nested(replace(/[^\"'/()]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>|\\(<<self>>*\\)/.source, [regularStringOrCharacter]), 2);\n        var sInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [\n          sInterpolationRound,\n          formatString\n        ]);\n        function createInterpolationInside(interpolation, interpolationRound) {\n          return {\n            'interpolation': {\n              pattern: re(/((?:^|[^{])(?:\\{\\{)*)<<0>>/.source, [interpolation]),\n              lookbehind: true,\n              inside: {\n                'format-string': {\n                  pattern: re(/(^\\{(?:(?![}:])<<0>>)*)<<1>>(?=\\}$)/.source, [\n                    interpolationRound,\n                    formatString\n                  ]),\n                  lookbehind: true,\n                  inside: { 'punctuation': /^:/ }\n                },\n                'punctuation': /^\\{|\\}$/,\n                'expression': {\n                  pattern: /[\\s\\S]+/,\n                  alias: 'language-csharp',\n                  inside: Prism.languages.csharp\n                }\n              }\n            },\n            'string': /[\\s\\S]+/\n          };\n        }\n        Prism.languages.insertBefore('csharp', 'string', {\n          'interpolation-string': [\n            {\n              pattern: re(/(^|[^\\\\])(?:\\$@|@\\$)\"(?:\"\"|\\\\[\\s\\S]|\\{\\{|<<0>>|[^\\\\{\"])*\"/.source, [mInterpolation]),\n              lookbehind: true,\n              greedy: true,\n              inside: createInterpolationInside(mInterpolation, mInterpolationRound)\n            },\n            {\n              pattern: re(/(^|[^@\\\\])\\$\"(?:\\\\.|\\{\\{|<<0>>|[^\\\\\"{])*\"/.source, [sInterpolation]),\n              lookbehind: true,\n              greedy: true,\n              inside: createInterpolationInside(sInterpolation, sInterpolationRound)\n            }\n          ],\n          'char': {\n            pattern: RegExp(character),\n            greedy: true\n          }\n        });\n        Prism.languages.dotnet = Prism.languages.cs = Prism.languages.csharp;\n      }(Prism));\n      (function (Prism) {\n        var string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n        Prism.languages.css = {\n          'comment': /\\/\\*[\\s\\S]*?\\*\\//,\n          'atrule': {\n            pattern: RegExp('@[\\\\w-](?:' + /[^;{\\s\"']|\\s+(?!\\s)/.source + '|' + string.source + ')*?' + /(?:;|(?=\\s*\\{))/.source),\n            inside: {\n              'rule': /^@[\\w-]+/,\n              'selector-function-argument': {\n                pattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n                lookbehind: true,\n                alias: 'selector'\n              },\n              'keyword': {\n                pattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n                lookbehind: true\n              }\n            }\n          },\n          'url': {\n            pattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n            greedy: true,\n            inside: {\n              'function': /^url/i,\n              'punctuation': /^\\(|\\)$/,\n              'string': {\n                pattern: RegExp('^' + string.source + '$'),\n                alias: 'url'\n              }\n            }\n          },\n          'selector': {\n            pattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n            lookbehind: true\n          },\n          'string': {\n            pattern: string,\n            greedy: true\n          },\n          'property': {\n            pattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n            lookbehind: true\n          },\n          'important': /!important\\b/i,\n          'function': {\n            pattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n            lookbehind: true\n          },\n          'punctuation': /[(){};:,]/\n        };\n        Prism.languages.css['atrule'].inside.rest = Prism.languages.css;\n        var markup = Prism.languages.markup;\n        if (markup) {\n          markup.tag.addInlined('style', 'css');\n          markup.tag.addAttribute('style', 'css');\n        }\n      }(Prism));\n      (function (Prism) {\n        var keywords = /\\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record(?!\\s*[(){}[\\]<>=%~.:,;?+\\-*/&|^])|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\\b/;\n        var classNamePrefix = /(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/.source;\n        var className = {\n          pattern: RegExp(/(^|[^\\w.])/.source + classNamePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n          lookbehind: true,\n          inside: {\n            'namespace': {\n              pattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n              inside: { 'punctuation': /\\./ }\n            },\n            'punctuation': /\\./\n          }\n        };\n        Prism.languages.java = Prism.languages.extend('clike', {\n          'string': {\n            pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n            lookbehind: true,\n            greedy: true\n          },\n          'class-name': [\n            className,\n            {\n              pattern: RegExp(/(^|[^\\w.])/.source + classNamePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()]|\\s*(?:\\[[\\s,]*\\]\\s*)?::\\s*new\\b)/.source),\n              lookbehind: true,\n              inside: className.inside\n            },\n            {\n              pattern: RegExp(/(\\b(?:class|enum|extends|implements|instanceof|interface|new|record|throws)\\s+)/.source + classNamePrefix + /[A-Z]\\w*\\b/.source),\n              lookbehind: true,\n              inside: className.inside\n            }\n          ],\n          'keyword': keywords,\n          'function': [\n            Prism.languages.clike.function,\n            {\n              pattern: /(::\\s*)[a-z_]\\w*/,\n              lookbehind: true\n            }\n          ],\n          'number': /\\b0b[01][01_]*L?\\b|\\b0x(?:\\.[\\da-f_p+-]+|[\\da-f_]+(?:\\.[\\da-f_p+-]+)?)\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?\\d[\\d_]*)?[dfl]?/i,\n          'operator': {\n            pattern: /(^|[^.])(?:<<=?|>>>?=?|->|--|\\+\\+|&&|\\|\\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,\n            lookbehind: true\n          },\n          'constant': /\\b[A-Z][A-Z_\\d]+\\b/\n        });\n        Prism.languages.insertBefore('java', 'string', {\n          'triple-quoted-string': {\n            pattern: /\"\"\"[ \\t]*[\\r\\n](?:(?:\"|\"\")?(?:\\\\.|[^\"\\\\]))*\"\"\"/,\n            greedy: true,\n            alias: 'string'\n          },\n          'char': {\n            pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){1,6}'/,\n            greedy: true\n          }\n        });\n        Prism.languages.insertBefore('java', 'class-name', {\n          'annotation': {\n            pattern: /(^|[^.])@\\w+(?:\\s*\\.\\s*\\w+)*/,\n            lookbehind: true,\n            alias: 'punctuation'\n          },\n          'generics': {\n            pattern: /<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&))*>)*>)*>)*>/,\n            inside: {\n              'class-name': className,\n              'keyword': keywords,\n              'punctuation': /[<>(),.:]/,\n              'operator': /[?&|]/\n            }\n          },\n          'import': [\n            {\n              pattern: RegExp(/(\\bimport\\s+)/.source + classNamePrefix + /(?:[A-Z]\\w*|\\*)(?=\\s*;)/.source),\n              lookbehind: true,\n              inside: {\n                'namespace': className.inside.namespace,\n                'punctuation': /\\./,\n                'operator': /\\*/,\n                'class-name': /\\w+/\n              }\n            },\n            {\n              pattern: RegExp(/(\\bimport\\s+static\\s+)/.source + classNamePrefix + /(?:\\w+|\\*)(?=\\s*;)/.source),\n              lookbehind: true,\n              alias: 'static',\n              inside: {\n                'namespace': className.inside.namespace,\n                'static': /\\b\\w+$/,\n                'punctuation': /\\./,\n                'operator': /\\*/,\n                'class-name': /\\w+/\n              }\n            }\n          ],\n          'namespace': {\n            pattern: RegExp(/(\\b(?:exports|import(?:\\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\\s+)(?!<keyword>)[a-z]\\w*(?:\\.[a-z]\\w*)*\\.?/.source.replace(/<keyword>/g, function () {\n              return keywords.source;\n            })),\n            lookbehind: true,\n            inside: { 'punctuation': /\\./ }\n          }\n        });\n      }(Prism));\n      Prism.languages.javascript = Prism.languages.extend('clike', {\n        'class-name': [\n          Prism.languages.clike['class-name'],\n          {\n            pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,\n            lookbehind: true\n          }\n        ],\n        'keyword': [\n          {\n            pattern: /((?:^|\\})\\s*)catch\\b/,\n            lookbehind: true\n          },\n          {\n            pattern: /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n            lookbehind: true\n          }\n        ],\n        'function': /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n        'number': {\n          pattern: RegExp(/(^|[^\\w$])/.source + '(?:' + (/NaN|Infinity/.source + '|' + /0[bB][01]+(?:_[01]+)*n?/.source + '|' + /0[oO][0-7]+(?:_[0-7]+)*n?/.source + '|' + /0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source + '|' + /\\d+(?:_\\d+)*n/.source + '|' + /(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/.source) + ')' + /(?![\\w$])/.source),\n          lookbehind: true\n        },\n        'operator': /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n      });\n      Prism.languages.javascript['class-name'][0].pattern = /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/;\n      Prism.languages.insertBefore('javascript', 'keyword', {\n        'regex': {\n          pattern: RegExp(/((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)/.source + /\\//.source + '(?:' + /(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}/.source + '|' + /(?:\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.)*\\])*\\])*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source + ')' + /(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/.source),\n          lookbehind: true,\n          greedy: true,\n          inside: {\n            'regex-source': {\n              pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n              lookbehind: true,\n              alias: 'language-regex',\n              inside: Prism.languages.regex\n            },\n            'regex-delimiter': /^\\/|\\/$/,\n            'regex-flags': /^[a-z]+$/\n          }\n        },\n        'function-variable': {\n          pattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n          alias: 'function'\n        },\n        'parameter': [\n          {\n            pattern: /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n            lookbehind: true,\n            inside: Prism.languages.javascript\n          },\n          {\n            pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n            lookbehind: true,\n            inside: Prism.languages.javascript\n          },\n          {\n            pattern: /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n            lookbehind: true,\n            inside: Prism.languages.javascript\n          },\n          {\n            pattern: /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n            lookbehind: true,\n            inside: Prism.languages.javascript\n          }\n        ],\n        'constant': /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n      });\n      Prism.languages.insertBefore('javascript', 'string', {\n        'hashbang': {\n          pattern: /^#!.*/,\n          greedy: true,\n          alias: 'comment'\n        },\n        'template-string': {\n          pattern: /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n          greedy: true,\n          inside: {\n            'template-punctuation': {\n              pattern: /^`|`$/,\n              alias: 'string'\n            },\n            'interpolation': {\n              pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n              lookbehind: true,\n              inside: {\n                'interpolation-punctuation': {\n                  pattern: /^\\$\\{|\\}$/,\n                  alias: 'punctuation'\n                },\n                rest: Prism.languages.javascript\n              }\n            },\n            'string': /[\\s\\S]+/\n          }\n        },\n        'string-property': {\n          pattern: /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,\n          lookbehind: true,\n          greedy: true,\n          alias: 'property'\n        }\n      });\n      Prism.languages.insertBefore('javascript', 'operator', {\n        'literal-property': {\n          pattern: /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,\n          lookbehind: true,\n          alias: 'property'\n        }\n      });\n      if (Prism.languages.markup) {\n        Prism.languages.markup.tag.addInlined('script', 'javascript');\n        Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source, 'javascript');\n      }\n      Prism.languages.js = Prism.languages.javascript;\n      Prism.languages.markup = {\n        'comment': {\n          pattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n          greedy: true\n        },\n        'prolog': {\n          pattern: /<\\?[\\s\\S]+?\\?>/,\n          greedy: true\n        },\n        'doctype': {\n          pattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n          greedy: true,\n          inside: {\n            'internal-subset': {\n              pattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n              lookbehind: true,\n              greedy: true,\n              inside: null\n            },\n            'string': {\n              pattern: /\"[^\"]*\"|'[^']*'/,\n              greedy: true\n            },\n            'punctuation': /^<!|>$|[[\\]]/,\n            'doctype-tag': /^DOCTYPE/i,\n            'name': /[^\\s<>'\"]+/\n          }\n        },\n        'cdata': {\n          pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n          greedy: true\n        },\n        'tag': {\n          pattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n          greedy: true,\n          inside: {\n            'tag': {\n              pattern: /^<\\/?[^\\s>\\/]+/,\n              inside: {\n                'punctuation': /^<\\/?/,\n                'namespace': /^[^\\s>\\/:]+:/\n              }\n            },\n            'special-attr': [],\n            'attr-value': {\n              pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n              inside: {\n                'punctuation': [\n                  {\n                    pattern: /^=/,\n                    alias: 'attr-equals'\n                  },\n                  {\n                    pattern: /^(\\s*)[\"']|[\"']$/,\n                    lookbehind: true\n                  }\n                ]\n              }\n            },\n            'punctuation': /\\/?>/,\n            'attr-name': {\n              pattern: /[^\\s>\\/]+/,\n              inside: { 'namespace': /^[^\\s>\\/:]+:/ }\n            }\n          }\n        },\n        'entity': [\n          {\n            pattern: /&[\\da-z]{1,8};/i,\n            alias: 'named-entity'\n          },\n          /&#x?[\\da-f]{1,8};/i\n        ]\n      };\n      Prism.languages.markup['tag'].inside['attr-value'].inside['entity'] = Prism.languages.markup['entity'];\n      Prism.languages.markup['doctype'].inside['internal-subset'].inside = Prism.languages.markup;\n      Prism.hooks.add('wrap', function (env) {\n        if (env.type === 'entity') {\n          env.attributes['title'] = env.content.replace(/&amp;/, '&');\n        }\n      });\n      Object.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n        value: function addInlined(tagName, lang) {\n          var includedCdataInside = {};\n          includedCdataInside['language-' + lang] = {\n            pattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n            lookbehind: true,\n            inside: Prism.languages[lang]\n          };\n          includedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i;\n          var inside = {\n            'included-cdata': {\n              pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n              inside: includedCdataInside\n            }\n          };\n          inside['language-' + lang] = {\n            pattern: /[\\s\\S]+/,\n            inside: Prism.languages[lang]\n          };\n          var def = {};\n          def[tagName] = {\n            pattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function () {\n              return tagName;\n            }), 'i'),\n            lookbehind: true,\n            greedy: true,\n            inside: inside\n          };\n          Prism.languages.insertBefore('markup', 'cdata', def);\n        }\n      });\n      Object.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n        value: function (attrName, lang) {\n          Prism.languages.markup.tag.inside['special-attr'].push({\n            pattern: RegExp(/(^|[\"'\\s])/.source + '(?:' + attrName + ')' + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source, 'i'),\n            lookbehind: true,\n            inside: {\n              'attr-name': /^[^\\s=]+/,\n              'attr-value': {\n                pattern: /=[\\s\\S]+/,\n                inside: {\n                  'value': {\n                    pattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n                    lookbehind: true,\n                    alias: [\n                      lang,\n                      'language-' + lang\n                    ],\n                    inside: Prism.languages[lang]\n                  },\n                  'punctuation': [\n                    {\n                      pattern: /^=/,\n                      alias: 'attr-equals'\n                    },\n                    /\"|'/\n                  ]\n                }\n              }\n            }\n          });\n        }\n      });\n      Prism.languages.html = Prism.languages.markup;\n      Prism.languages.mathml = Prism.languages.markup;\n      Prism.languages.svg = Prism.languages.markup;\n      Prism.languages.xml = Prism.languages.extend('markup', {});\n      Prism.languages.ssml = Prism.languages.xml;\n      Prism.languages.atom = Prism.languages.xml;\n      Prism.languages.rss = Prism.languages.xml;\n      (function (Prism) {\n        var comment = /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*|#(?!\\[).*/;\n        var constant = [\n          {\n            pattern: /\\b(?:false|true)\\b/i,\n            alias: 'boolean'\n          },\n          {\n            pattern: /(::\\s*)\\b[a-z_]\\w*\\b(?!\\s*\\()/i,\n            greedy: true,\n            lookbehind: true\n          },\n          {\n            pattern: /(\\b(?:case|const)\\s+)\\b[a-z_]\\w*(?=\\s*[;=])/i,\n            greedy: true,\n            lookbehind: true\n          },\n          /\\b(?:null)\\b/i,\n          /\\b[A-Z_][A-Z0-9_]*\\b(?!\\s*\\()/\n        ];\n        var number = /\\b0b[01]+(?:_[01]+)*\\b|\\b0o[0-7]+(?:_[0-7]+)*\\b|\\b0x[\\da-f]+(?:_[\\da-f]+)*\\b|(?:\\b\\d+(?:_\\d+)*\\.?(?:\\d+(?:_\\d+)*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i;\n        var operator = /<?=>|\\?\\?=?|\\.{3}|\\??->|[!=]=?=?|::|\\*\\*=?|--|\\+\\+|&&|\\|\\||<<|>>|[?~]|[/^|%*&<>.+-]=?/;\n        var punctuation = /[{}\\[\\](),:;]/;\n        Prism.languages.php = {\n          'delimiter': {\n            pattern: /\\?>$|^<\\?(?:php(?=\\s)|=)?/i,\n            alias: 'important'\n          },\n          'comment': comment,\n          'variable': /\\$+(?:\\w+\\b|(?=\\{))/,\n          'package': {\n            pattern: /(namespace\\s+|use\\s+(?:function\\s+)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n            lookbehind: true,\n            inside: { 'punctuation': /\\\\/ }\n          },\n          'class-name-definition': {\n            pattern: /(\\b(?:class|enum|interface|trait)\\s+)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n            lookbehind: true,\n            alias: 'class-name'\n          },\n          'function-definition': {\n            pattern: /(\\bfunction\\s+)[a-z_]\\w*(?=\\s*\\()/i,\n            lookbehind: true,\n            alias: 'function'\n          },\n          'keyword': [\n            {\n              pattern: /(\\(\\s*)\\b(?:array|bool|boolean|float|int|integer|object|string)\\b(?=\\s*\\))/i,\n              alias: 'type-casting',\n              greedy: true,\n              lookbehind: true\n            },\n            {\n              pattern: /([(,?]\\s*)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|object|self|static|string)\\b(?=\\s*\\$)/i,\n              alias: 'type-hint',\n              greedy: true,\n              lookbehind: true\n            },\n            {\n              pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|never|object|self|static|string|void)\\b/i,\n              alias: 'return-type',\n              greedy: true,\n              lookbehind: true\n            },\n            {\n              pattern: /\\b(?:array(?!\\s*\\()|bool|float|int|iterable|mixed|object|string|void)\\b/i,\n              alias: 'type-declaration',\n              greedy: true\n            },\n            {\n              pattern: /(\\|\\s*)(?:false|null)\\b|\\b(?:false|null)(?=\\s*\\|)/i,\n              alias: 'type-declaration',\n              greedy: true,\n              lookbehind: true\n            },\n            {\n              pattern: /\\b(?:parent|self|static)(?=\\s*::)/i,\n              alias: 'static-context',\n              greedy: true\n            },\n            {\n              pattern: /(\\byield\\s+)from\\b/i,\n              lookbehind: true\n            },\n            /\\bclass\\b/i,\n            {\n              pattern: /((?:^|[^\\s>:]|(?:^|[^-])>|(?:^|[^:]):)\\s*)\\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|never|new|or|parent|print|private|protected|public|readonly|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\\b/i,\n              lookbehind: true\n            }\n          ],\n          'argument-name': {\n            pattern: /([(,]\\s*)\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n            lookbehind: true\n          },\n          'class-name': [\n            {\n              pattern: /(\\b(?:extends|implements|instanceof|new(?!\\s+self|\\s+static))\\s+|\\bcatch\\s*\\()\\b[a-z_]\\w*(?!\\\\)\\b/i,\n              greedy: true,\n              lookbehind: true\n            },\n            {\n              pattern: /(\\|\\s*)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n              greedy: true,\n              lookbehind: true\n            },\n            {\n              pattern: /\\b[a-z_]\\w*(?!\\\\)\\b(?=\\s*\\|)/i,\n              greedy: true\n            },\n            {\n              pattern: /(\\|\\s*)(?:\\\\?\\b[a-z_]\\w*)+\\b/i,\n              alias: 'class-name-fully-qualified',\n              greedy: true,\n              lookbehind: true,\n              inside: { 'punctuation': /\\\\/ }\n            },\n            {\n              pattern: /(?:\\\\?\\b[a-z_]\\w*)+\\b(?=\\s*\\|)/i,\n              alias: 'class-name-fully-qualified',\n              greedy: true,\n              inside: { 'punctuation': /\\\\/ }\n            },\n            {\n              pattern: /(\\b(?:extends|implements|instanceof|new(?!\\s+self\\b|\\s+static\\b))\\s+|\\bcatch\\s*\\()(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n              alias: 'class-name-fully-qualified',\n              greedy: true,\n              lookbehind: true,\n              inside: { 'punctuation': /\\\\/ }\n            },\n            {\n              pattern: /\\b[a-z_]\\w*(?=\\s*\\$)/i,\n              alias: 'type-declaration',\n              greedy: true\n            },\n            {\n              pattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n              alias: [\n                'class-name-fully-qualified',\n                'type-declaration'\n              ],\n              greedy: true,\n              inside: { 'punctuation': /\\\\/ }\n            },\n            {\n              pattern: /\\b[a-z_]\\w*(?=\\s*::)/i,\n              alias: 'static-context',\n              greedy: true\n            },\n            {\n              pattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*::)/i,\n              alias: [\n                'class-name-fully-qualified',\n                'static-context'\n              ],\n              greedy: true,\n              inside: { 'punctuation': /\\\\/ }\n            },\n            {\n              pattern: /([(,?]\\s*)[a-z_]\\w*(?=\\s*\\$)/i,\n              alias: 'type-hint',\n              greedy: true,\n              lookbehind: true\n            },\n            {\n              pattern: /([(,?]\\s*)(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n              alias: [\n                'class-name-fully-qualified',\n                'type-hint'\n              ],\n              greedy: true,\n              lookbehind: true,\n              inside: { 'punctuation': /\\\\/ }\n            },\n            {\n              pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n              alias: 'return-type',\n              greedy: true,\n              lookbehind: true\n            },\n            {\n              pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n              alias: [\n                'class-name-fully-qualified',\n                'return-type'\n              ],\n              greedy: true,\n              lookbehind: true,\n              inside: { 'punctuation': /\\\\/ }\n            }\n          ],\n          'constant': constant,\n          'function': {\n            pattern: /(^|[^\\\\\\w])\\\\?[a-z_](?:[\\w\\\\]*\\w)?(?=\\s*\\()/i,\n            lookbehind: true,\n            inside: { 'punctuation': /\\\\/ }\n          },\n          'property': {\n            pattern: /(->\\s*)\\w+/,\n            lookbehind: true\n          },\n          'number': number,\n          'operator': operator,\n          'punctuation': punctuation\n        };\n        var string_interpolation = {\n          pattern: /\\{\\$(?:\\{(?:\\{[^{}]+\\}|[^{}]+)\\}|[^{}])+\\}|(^|[^\\\\{])\\$+(?:\\w+(?:\\[[^\\r\\n\\[\\]]+\\]|->\\w+)?)/,\n          lookbehind: true,\n          inside: Prism.languages.php\n        };\n        var string = [\n          {\n            pattern: /<<<'([^']+)'[\\r\\n](?:.*[\\r\\n])*?\\1;/,\n            alias: 'nowdoc-string',\n            greedy: true,\n            inside: {\n              'delimiter': {\n                pattern: /^<<<'[^']+'|[a-z_]\\w*;$/i,\n                alias: 'symbol',\n                inside: { 'punctuation': /^<<<'?|[';]$/ }\n              }\n            }\n          },\n          {\n            pattern: /<<<(?:\"([^\"]+)\"[\\r\\n](?:.*[\\r\\n])*?\\1;|([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?\\2;)/i,\n            alias: 'heredoc-string',\n            greedy: true,\n            inside: {\n              'delimiter': {\n                pattern: /^<<<(?:\"[^\"]+\"|[a-z_]\\w*)|[a-z_]\\w*;$/i,\n                alias: 'symbol',\n                inside: { 'punctuation': /^<<<\"?|[\";]$/ }\n              },\n              'interpolation': string_interpolation\n            }\n          },\n          {\n            pattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n            alias: 'backtick-quoted-string',\n            greedy: true\n          },\n          {\n            pattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n            alias: 'single-quoted-string',\n            greedy: true\n          },\n          {\n            pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n            alias: 'double-quoted-string',\n            greedy: true,\n            inside: { 'interpolation': string_interpolation }\n          }\n        ];\n        Prism.languages.insertBefore('php', 'variable', {\n          'string': string,\n          'attribute': {\n            pattern: /#\\[(?:[^\"'\\/#]|\\/(?![*/])|\\/\\/.*$|#(?!\\[).*$|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*')+\\](?=\\s*[a-z$#])/im,\n            greedy: true,\n            inside: {\n              'attribute-content': {\n                pattern: /^(#\\[)[\\s\\S]+(?=\\]$)/,\n                lookbehind: true,\n                inside: {\n                  'comment': comment,\n                  'string': string,\n                  'attribute-class-name': [\n                    {\n                      pattern: /([^:]|^)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n                      alias: 'class-name',\n                      greedy: true,\n                      lookbehind: true\n                    },\n                    {\n                      pattern: /([^:]|^)(?:\\\\?\\b[a-z_]\\w*)+/i,\n                      alias: [\n                        'class-name',\n                        'class-name-fully-qualified'\n                      ],\n                      greedy: true,\n                      lookbehind: true,\n                      inside: { 'punctuation': /\\\\/ }\n                    }\n                  ],\n                  'constant': constant,\n                  'number': number,\n                  'operator': operator,\n                  'punctuation': punctuation\n                }\n              },\n              'delimiter': {\n                pattern: /^#\\[|\\]$/,\n                alias: 'punctuation'\n              }\n            }\n          }\n        });\n        Prism.hooks.add('before-tokenize', function (env) {\n          if (!/<\\?/.test(env.code)) {\n            return;\n          }\n          var phpPattern = /<\\?(?:[^\"'/#]|\\/(?![*/])|(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|(?:\\/\\/|#(?!\\[))(?:[^?\\n\\r]|\\?(?!>))*(?=$|\\?>|[\\r\\n])|#\\[|\\/\\*(?:[^*]|\\*(?!\\/))*(?:\\*\\/|$))*?(?:\\?>|$)/g;\n          Prism.languages['markup-templating'].buildPlaceholders(env, 'php', phpPattern);\n        });\n        Prism.hooks.add('after-tokenize', function (env) {\n          Prism.languages['markup-templating'].tokenizePlaceholders(env, 'php');\n        });\n      }(Prism));\n      Prism.languages.python = {\n        'comment': {\n          pattern: /(^|[^\\\\])#.*/,\n          lookbehind: true,\n          greedy: true\n        },\n        'string-interpolation': {\n          pattern: /(?:f|fr|rf)(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2)/i,\n          greedy: true,\n          inside: {\n            'interpolation': {\n              pattern: /((?:^|[^{])(?:\\{\\{)*)\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}])+\\})+\\})+\\}/,\n              lookbehind: true,\n              inside: {\n                'format-spec': {\n                  pattern: /(:)[^:(){}]+(?=\\}$)/,\n                  lookbehind: true\n                },\n                'conversion-option': {\n                  pattern: /![sra](?=[:}]$)/,\n                  alias: 'punctuation'\n                },\n                rest: null\n              }\n            },\n            'string': /[\\s\\S]+/\n          }\n        },\n        'triple-quoted-string': {\n          pattern: /(?:[rub]|br|rb)?(\"\"\"|''')[\\s\\S]*?\\1/i,\n          greedy: true,\n          alias: 'string'\n        },\n        'string': {\n          pattern: /(?:[rub]|br|rb)?(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/i,\n          greedy: true\n        },\n        'function': {\n          pattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/g,\n          lookbehind: true\n        },\n        'class-name': {\n          pattern: /(\\bclass\\s+)\\w+/i,\n          lookbehind: true\n        },\n        'decorator': {\n          pattern: /(^[\\t ]*)@\\w+(?:\\.\\w+)*/m,\n          lookbehind: true,\n          alias: [\n            'annotation',\n            'punctuation'\n          ],\n          inside: { 'punctuation': /\\./ }\n        },\n        'keyword': /\\b(?:_(?=\\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\\b/,\n        'builtin': /\\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\\b/,\n        'boolean': /\\b(?:False|None|True)\\b/,\n        'number': /\\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\\b|(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:e[+-]?\\d+(?:_\\d+)*)?j?(?!\\w)/i,\n        'operator': /[-+%=]=?|!=|:=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n        'punctuation': /[{}[\\];(),.:]/\n      };\n      Prism.languages.python['string-interpolation'].inside['interpolation'].inside.rest = Prism.languages.python;\n      Prism.languages.py = Prism.languages.python;\n      (function (Prism) {\n        Prism.languages.ruby = Prism.languages.extend('clike', {\n          'comment': {\n            pattern: /#.*|^=begin\\s[\\s\\S]*?^=end/m,\n            greedy: true\n          },\n          'class-name': {\n            pattern: /(\\b(?:class|module)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+|\\b[A-Z_]\\w*(?=\\s*\\.\\s*new\\b)/,\n            lookbehind: true,\n            inside: { 'punctuation': /[.\\\\]/ }\n          },\n          'keyword': /\\b(?:BEGIN|END|alias|and|begin|break|case|class|def|define_method|defined|do|each|else|elsif|end|ensure|extend|for|if|in|include|module|new|next|nil|not|or|prepend|private|protected|public|raise|redo|require|rescue|retry|return|self|super|then|throw|undef|unless|until|when|while|yield)\\b/,\n          'operator': /\\.{2,3}|&\\.|===|<?=>|[!=]?~|(?:&&|\\|\\||<<|>>|\\*\\*|[+\\-*/%<>!^&|=])=?|[?:]/,\n          'punctuation': /[(){}[\\].,;]/\n        });\n        Prism.languages.insertBefore('ruby', 'operator', {\n          'double-colon': {\n            pattern: /::/,\n            alias: 'punctuation'\n          }\n        });\n        var interpolation = {\n          pattern: /((?:^|[^\\\\])(?:\\\\{2})*)#\\{(?:[^{}]|\\{[^{}]*\\})*\\}/,\n          lookbehind: true,\n          inside: {\n            'content': {\n              pattern: /^(#\\{)[\\s\\S]+(?=\\}$)/,\n              lookbehind: true,\n              inside: Prism.languages.ruby\n            },\n            'delimiter': {\n              pattern: /^#\\{|\\}$/,\n              alias: 'punctuation'\n            }\n          }\n        };\n        delete Prism.languages.ruby.function;\n        var percentExpression = '(?:' + [\n          /([^a-zA-Z0-9\\s{(\\[<=])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source,\n          /\\((?:[^()\\\\]|\\\\[\\s\\S]|\\((?:[^()\\\\]|\\\\[\\s\\S])*\\))*\\)/.source,\n          /\\{(?:[^{}\\\\]|\\\\[\\s\\S]|\\{(?:[^{}\\\\]|\\\\[\\s\\S])*\\})*\\}/.source,\n          /\\[(?:[^\\[\\]\\\\]|\\\\[\\s\\S]|\\[(?:[^\\[\\]\\\\]|\\\\[\\s\\S])*\\])*\\]/.source,\n          /<(?:[^<>\\\\]|\\\\[\\s\\S]|<(?:[^<>\\\\]|\\\\[\\s\\S])*>)*>/.source\n        ].join('|') + ')';\n        var symbolName = /(?:\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|(?:\\b[a-zA-Z_]\\w*|[^\\s\\0-\\x7F]+)[?!]?|\\$.)/.source;\n        Prism.languages.insertBefore('ruby', 'keyword', {\n          'regex-literal': [\n            {\n              pattern: RegExp(/%r/.source + percentExpression + /[egimnosux]{0,6}/.source),\n              greedy: true,\n              inside: {\n                'interpolation': interpolation,\n                'regex': /[\\s\\S]+/\n              }\n            },\n            {\n              pattern: /(^|[^/])\\/(?!\\/)(?:\\[[^\\r\\n\\]]+\\]|\\\\.|[^[/\\\\\\r\\n])+\\/[egimnosux]{0,6}(?=\\s*(?:$|[\\r\\n,.;})#]))/,\n              lookbehind: true,\n              greedy: true,\n              inside: {\n                'interpolation': interpolation,\n                'regex': /[\\s\\S]+/\n              }\n            }\n          ],\n          'variable': /[@$]+[a-zA-Z_]\\w*(?:[?!]|\\b)/,\n          'symbol': [\n            {\n              pattern: RegExp(/(^|[^:]):/.source + symbolName),\n              lookbehind: true,\n              greedy: true\n            },\n            {\n              pattern: RegExp(/([\\r\\n{(,][ \\t]*)/.source + symbolName + /(?=:(?!:))/.source),\n              lookbehind: true,\n              greedy: true\n            }\n          ],\n          'method-definition': {\n            pattern: /(\\bdef\\s+)\\w+(?:\\s*\\.\\s*\\w+)?/,\n            lookbehind: true,\n            inside: {\n              'function': /\\b\\w+$/,\n              'keyword': /^self\\b/,\n              'class-name': /^\\w+/,\n              'punctuation': /\\./\n            }\n          }\n        });\n        Prism.languages.insertBefore('ruby', 'string', {\n          'string-literal': [\n            {\n              pattern: RegExp(/%[qQiIwWs]?/.source + percentExpression),\n              greedy: true,\n              inside: {\n                'interpolation': interpolation,\n                'string': /[\\s\\S]+/\n              }\n            },\n            {\n              pattern: /(\"|')(?:#\\{[^}]+\\}|#(?!\\{)|\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\#\\r\\n])*\\1/,\n              greedy: true,\n              inside: {\n                'interpolation': interpolation,\n                'string': /[\\s\\S]+/\n              }\n            },\n            {\n              pattern: /<<[-~]?([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?[\\t ]*\\1/i,\n              alias: 'heredoc-string',\n              greedy: true,\n              inside: {\n                'delimiter': {\n                  pattern: /^<<[-~]?[a-z_]\\w*|\\b[a-z_]\\w*$/i,\n                  inside: {\n                    'symbol': /\\b\\w+/,\n                    'punctuation': /^<<[-~]?/\n                  }\n                },\n                'interpolation': interpolation,\n                'string': /[\\s\\S]+/\n              }\n            },\n            {\n              pattern: /<<[-~]?'([a-z_]\\w*)'[\\r\\n](?:.*[\\r\\n])*?[\\t ]*\\1/i,\n              alias: 'heredoc-string',\n              greedy: true,\n              inside: {\n                'delimiter': {\n                  pattern: /^<<[-~]?'[a-z_]\\w*'|\\b[a-z_]\\w*$/i,\n                  inside: {\n                    'symbol': /\\b\\w+/,\n                    'punctuation': /^<<[-~]?'|'$/\n                  }\n                },\n                'string': /[\\s\\S]+/\n              }\n            }\n          ],\n          'command-literal': [\n            {\n              pattern: RegExp(/%x/.source + percentExpression),\n              greedy: true,\n              inside: {\n                'interpolation': interpolation,\n                'command': {\n                  pattern: /[\\s\\S]+/,\n                  alias: 'string'\n                }\n              }\n            },\n            {\n              pattern: /`(?:#\\{[^}]+\\}|#(?!\\{)|\\\\(?:\\r\\n|[\\s\\S])|[^\\\\`#\\r\\n])*`/,\n              greedy: true,\n              inside: {\n                'interpolation': interpolation,\n                'command': {\n                  pattern: /[\\s\\S]+/,\n                  alias: 'string'\n                }\n              }\n            }\n          ]\n        });\n        delete Prism.languages.ruby.string;\n        Prism.languages.insertBefore('ruby', 'number', {\n          'builtin': /\\b(?:Array|Bignum|Binding|Class|Continuation|Dir|Exception|FalseClass|File|Fixnum|Float|Hash|IO|Integer|MatchData|Method|Module|NilClass|Numeric|Object|Proc|Range|Regexp|Stat|String|Struct|Symbol|TMS|Thread|ThreadGroup|Time|TrueClass)\\b/,\n          'constant': /\\b[A-Z][A-Z0-9_]*(?:[?!]|\\b)/\n        });\n        Prism.languages.rb = Prism.languages.ruby;\n      }(Prism));\n      window.Prism = oldprism;\n      return Prism;\n    }(undefined, undefined);\n\n    const option = name => editor => editor.options.get(name);\n    const register$2 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('codesample_languages', { processor: 'object[]' });\n      registerOption('codesample_global_prismjs', {\n        processor: 'boolean',\n        default: false\n      });\n    };\n    const getLanguages$1 = option('codesample_languages');\n    const useGlobalPrismJS = option('codesample_global_prismjs');\n\n    const get = editor => Global.Prism && useGlobalPrismJS(editor) ? Global.Prism : prismjs;\n\n    const isCodeSample = elm => {\n      return isNonNullable(elm) && elm.nodeName === 'PRE' && elm.className.indexOf('language-') !== -1;\n    };\n\n    const getSelectedCodeSample = editor => {\n      const node = editor.selection ? editor.selection.getNode() : null;\n      return isCodeSample(node) ? Optional.some(node) : Optional.none();\n    };\n    const insertCodeSample = (editor, language, code) => {\n      const dom = editor.dom;\n      editor.undoManager.transact(() => {\n        const node = getSelectedCodeSample(editor);\n        code = global$1.DOM.encode(code);\n        return node.fold(() => {\n          editor.insertContent('<pre id=\"__new\" class=\"language-' + language + '\">' + code + '</pre>');\n          const newPre = dom.select('#__new')[0];\n          dom.setAttrib(newPre, 'id', null);\n          editor.selection.select(newPre);\n        }, n => {\n          dom.setAttrib(n, 'class', 'language-' + language);\n          n.innerHTML = code;\n          get(editor).highlightElement(n);\n          editor.selection.select(n);\n        });\n      });\n    };\n    const getCurrentCode = editor => {\n      const node = getSelectedCodeSample(editor);\n      return node.bind(n => Optional.from(n.textContent)).getOr('');\n    };\n\n    const getLanguages = editor => {\n      const defaultLanguages = [\n        {\n          text: 'HTML/XML',\n          value: 'markup'\n        },\n        {\n          text: 'JavaScript',\n          value: 'javascript'\n        },\n        {\n          text: 'CSS',\n          value: 'css'\n        },\n        {\n          text: 'PHP',\n          value: 'php'\n        },\n        {\n          text: 'Ruby',\n          value: 'ruby'\n        },\n        {\n          text: 'Python',\n          value: 'python'\n        },\n        {\n          text: 'Java',\n          value: 'java'\n        },\n        {\n          text: 'C',\n          value: 'c'\n        },\n        {\n          text: 'C#',\n          value: 'csharp'\n        },\n        {\n          text: 'C++',\n          value: 'cpp'\n        }\n      ];\n      const customLanguages = getLanguages$1(editor);\n      return customLanguages ? customLanguages : defaultLanguages;\n    };\n    const getCurrentLanguage = (editor, fallback) => {\n      const node = getSelectedCodeSample(editor);\n      return node.fold(() => fallback, n => {\n        const matches = n.className.match(/language-(\\w+)/);\n        return matches ? matches[1] : fallback;\n      });\n    };\n\n    const open = editor => {\n      const languages = getLanguages(editor);\n      const defaultLanguage = head(languages).fold(constant(''), l => l.value);\n      const currentLanguage = getCurrentLanguage(editor, defaultLanguage);\n      const currentCode = getCurrentCode(editor);\n      editor.windowManager.open({\n        title: 'Insert/Edit Code Sample',\n        size: 'large',\n        body: {\n          type: 'panel',\n          items: [\n            {\n              type: 'listbox',\n              name: 'language',\n              label: 'Language',\n              items: languages\n            },\n            {\n              type: 'textarea',\n              name: 'code',\n              label: 'Code view'\n            }\n          ]\n        },\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: {\n          language: currentLanguage,\n          code: currentCode\n        },\n        onSubmit: api => {\n          const data = api.getData();\n          insertCodeSample(editor, data.language, data.code);\n          api.close();\n        }\n      });\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('codesample', () => {\n        const node = editor.selection.getNode();\n        if (editor.selection.isCollapsed() || isCodeSample(node)) {\n          open(editor);\n        } else {\n          editor.formatter.toggle('code');\n        }\n      });\n    };\n\n    const blank = r => s => s.replace(r, '');\n    const trim = blank(/^\\s+|\\s+$/g);\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const setup = editor => {\n      editor.on('PreProcess', e => {\n        const dom = editor.dom;\n        const pres = dom.select('pre[contenteditable=false]', e.node);\n        global.each(global.grep(pres, isCodeSample), elm => {\n          const code = elm.textContent;\n          dom.setAttrib(elm, 'class', trim(dom.getAttrib(elm, 'class')));\n          dom.setAttrib(elm, 'contentEditable', null);\n          dom.setAttrib(elm, 'data-mce-highlighted', null);\n          let child;\n          while (child = elm.firstChild) {\n            elm.removeChild(child);\n          }\n          const codeElm = dom.add(elm, 'code');\n          codeElm.textContent = code;\n        });\n      });\n      editor.on('SetContent', () => {\n        const dom = editor.dom;\n        const unprocessedCodeSamples = global.grep(dom.select('pre'), elm => {\n          return isCodeSample(elm) && dom.getAttrib(elm, 'data-mce-highlighted') !== 'true';\n        });\n        if (unprocessedCodeSamples.length) {\n          editor.undoManager.transact(() => {\n            global.each(unprocessedCodeSamples, elm => {\n              var _a;\n              global.each(dom.select('br', elm), elm => {\n                dom.replace(editor.getDoc().createTextNode('\\n'), elm);\n              });\n              elm.innerHTML = dom.encode((_a = elm.textContent) !== null && _a !== void 0 ? _a : '');\n              get(editor).highlightElement(elm);\n              dom.setAttrib(elm, 'data-mce-highlighted', true);\n              elm.className = trim(elm.className);\n            });\n          });\n        }\n      });\n      editor.on('PreInit', () => {\n        editor.parser.addNodeFilter('pre', nodes => {\n          var _a;\n          for (let i = 0, l = nodes.length; i < l; i++) {\n            const node = nodes[i];\n            const isCodeSample = ((_a = node.attr('class')) !== null && _a !== void 0 ? _a : '').indexOf('language-') !== -1;\n            if (isCodeSample) {\n              node.attr('contenteditable', 'false');\n              node.attr('data-mce-highlighted', 'false');\n            }\n          }\n        });\n      });\n    };\n\n    const onSetupEditable = (editor, onChanged = noop) => api => {\n      const nodeChanged = () => {\n        api.setEnabled(editor.selection.isEditable());\n        onChanged(api);\n      };\n      editor.on('NodeChange', nodeChanged);\n      nodeChanged();\n      return () => {\n        editor.off('NodeChange', nodeChanged);\n      };\n    };\n    const isCodeSampleSelection = editor => {\n      const node = editor.selection.getStart();\n      return editor.dom.is(node, 'pre[class*=\"language-\"]');\n    };\n    const register = editor => {\n      const onAction = () => editor.execCommand('codesample');\n      editor.ui.registry.addToggleButton('codesample', {\n        icon: 'code-sample',\n        tooltip: 'Insert/edit code sample',\n        onAction,\n        onSetup: onSetupEditable(editor, api => {\n          api.setActive(isCodeSampleSelection(editor));\n        })\n      });\n      editor.ui.registry.addMenuItem('codesample', {\n        text: 'Code sample...',\n        icon: 'code-sample',\n        onAction,\n        onSetup: onSetupEditable(editor)\n      });\n    };\n\n    var Plugin = () => {\n      global$2.add('codesample', editor => {\n        register$2(editor);\n        setup(editor);\n        register(editor);\n        register$1(editor);\n        editor.on('dblclick', ev => {\n          if (isCodeSample(ev.target)) {\n            open(editor);\n          }\n        });\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"codesample\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/codesample')\n//   ES2015:\n//     import 'tinymce/plugins/codesample'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,YAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AAExC,YAAM,OAAO,MAAM;AAAA,MACnB;AACA,YAAM,WAAW,WAAS;AACxB,eAAO,MAAM;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MAEA,MAAM,SAAS;AAAA,QACb,YAAY,KAAK,OAAO;AACtB,eAAK,MAAM;AACX,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,IAAI,SAAS,MAAM,KAAK;AAAA,QACjC;AAAA,QACA,OAAO,OAAO;AACZ,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,KAAK,QAAQ,QAAQ;AACnB,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,QACA,SAAS;AACP,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,SAAS;AACP,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA,IAAI,QAAQ;AACV,cAAI,KAAK,KAAK;AACZ,mBAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,UACzC,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QACzC;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QAC1C;AAAA,QACA,OAAO,WAAW;AAChB,cAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,MAAM,aAAa;AACjB,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,GAAG,aAAa;AACd,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B;AAAA,QACA,WAAW,OAAO;AAChB,iBAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,QACvC;AAAA,QACA,QAAQ,OAAO;AACb,iBAAO,KAAK,MAAM,OAAO,MAAM;AAAA,QACjC;AAAA,QACA,SAAS,SAAS;AAChB,cAAI,CAAC,KAAK,KAAK;AACb,kBAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,UAC9F,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,cAAc,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,QACrE;AAAA,QACA,YAAY;AACV,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,iBAAiB;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AACR,iBAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,YAAM,QAAQ,CAAC,IAAI,MAAM,KAAK,KAAK,IAAI,GAAG,SAAS,SAAS,KAAK,GAAG,CAAC,CAAC,IAAI,SAAS,KAAK;AACxF,YAAM,OAAO,QAAM,MAAM,IAAI,CAAC;AAE9B,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,sBAAsB;AAEhE,YAAM,SAAS,OAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAEjF,YAAM,UAAU,SAAUA,SAAQC,SAAQC,UAAS;AACjD,cAAM,WAAW,OAAO;AACxB,eAAO,QAAQ,EAAE,QAAQ,KAAK;AAC9B,YAAI,QAAQ,OAAO,WAAW,cAAc,SAAS,OAAO,sBAAsB,eAAe,gBAAgB,oBAAoB,OAAO,CAAC;AAC7I,YAAI,QAAQ,SAAUC,QAAO;AAC3B,cAAI,OAAO;AACX,cAAI,WAAW;AACf,cAAI,mBAAmB,CAAC;AACxB,cAAI,IAAI;AAAA,YACN,QAAQA,OAAM,SAASA,OAAM,MAAM;AAAA,YACnC,6BAA6BA,OAAM,SAASA,OAAM,MAAM;AAAA,YACxD,MAAM;AAAA,cACJ,QAAQ,SAAS,OAAO,QAAQ;AAC9B,oBAAI,kBAAkB,OAAO;AAC3B,yBAAO,IAAI,MAAM,OAAO,MAAM,OAAO,OAAO,OAAO,GAAG,OAAO,KAAK;AAAA,gBACpE,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,yBAAO,OAAO,IAAI,MAAM;AAAA,gBAC1B,OAAO;AACL,yBAAO,OAAO,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,WAAW,GAAG;AAAA,gBACnF;AAAA,cACF;AAAA,cACA,MAAM,SAAU,GAAG;AACjB,uBAAO,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAA,cACtD;AAAA,cACA,OAAO,SAAU,KAAK;AACpB,oBAAI,CAAC,IAAI,MAAM,GAAG;AAChB,yBAAO,eAAe,KAAK,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;AAAA,gBAC1D;AACA,uBAAO,IAAI,MAAM;AAAA,cACnB;AAAA,cACA,OAAO,SAAS,UAAU,GAAG,SAAS;AACpC,0BAAU,WAAW,CAAC;AACtB,oBAAI;AACJ,oBAAI;AACJ,wBAAQ,EAAE,KAAK,KAAK,CAAC,GAAG;AAAA,kBACxB,KAAK;AACH,yBAAK,EAAE,KAAK,MAAM,CAAC;AACnB,wBAAI,QAAQ,EAAE,GAAG;AACf,6BAAO,QAAQ,EAAE;AAAA,oBACnB;AACA,4BAAQ,CAAC;AACT,4BAAQ,EAAE,IAAI;AACd,6BAAS,OAAO,GAAG;AACjB,0BAAI,EAAE,eAAe,GAAG,GAAG;AACzB,8BAAM,GAAG,IAAI,UAAU,EAAE,GAAG,GAAG,OAAO;AAAA,sBACxC;AAAA,oBACF;AACA,2BAAO;AAAA,kBACT,KAAK;AACH,yBAAK,EAAE,KAAK,MAAM,CAAC;AACnB,wBAAI,QAAQ,EAAE,GAAG;AACf,6BAAO,QAAQ,EAAE;AAAA,oBACnB;AACA,4BAAQ,CAAC;AACT,4BAAQ,EAAE,IAAI;AACd,sBAAE,QAAQ,SAAU,GAAG,GAAG;AACxB,4BAAM,CAAC,IAAI,UAAU,GAAG,OAAO;AAAA,oBACjC,CAAC;AACD,2BAAO;AAAA,kBACT;AACE,2BAAO;AAAA,gBACT;AAAA,cACF;AAAA,cACA,aAAa,SAAU,SAAS;AAC9B,uBAAO,SAAS;AACd,sBAAI,IAAI,KAAK,KAAK,QAAQ,SAAS;AACnC,sBAAI,GAAG;AACL,2BAAO,EAAE,CAAC,EAAE,YAAY;AAAA,kBAC1B;AACA,4BAAU,QAAQ;AAAA,gBACpB;AACA,uBAAO;AAAA,cACT;AAAA,cACA,aAAa,SAAU,SAAS,UAAU;AACxC,wBAAQ,YAAY,QAAQ,UAAU,QAAQ,OAAO,MAAM,IAAI,GAAG,EAAE;AACpE,wBAAQ,UAAU,IAAI,cAAc,QAAQ;AAAA,cAC9C;AAAA,cACA,eAAe,WAAY;AACzB,oBAAI,OAAO,aAAa,aAAa;AACnC,yBAAO;AAAA,gBACT;AACA,oBAAI,mBAAmB,YAAY,IAAI,GAAG;AACxC,yBAAO,SAAS;AAAA,gBAClB;AACA,oBAAI;AACF,wBAAM,IAAI,MAAM;AAAA,gBAClB,SAAS,KAAK;AACZ,sBAAI,OAAO,qCAAqC,KAAK,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC;AACxE,sBAAI,KAAK;AACP,wBAAI,UAAU,SAAS,qBAAqB,QAAQ;AACpD,6BAAS,KAAK,SAAS;AACrB,0BAAI,QAAQ,CAAC,EAAE,OAAO,KAAK;AACzB,+BAAO,QAAQ,CAAC;AAAA,sBAClB;AAAA,oBACF;AAAA,kBACF;AACA,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,cACA,UAAU,SAAU,SAAS,WAAW,mBAAmB;AACzD,oBAAI,KAAK,QAAQ;AACjB,uBAAO,SAAS;AACd,sBAAI,YAAY,QAAQ;AACxB,sBAAI,UAAU,SAAS,SAAS,GAAG;AACjC,2BAAO;AAAA,kBACT;AACA,sBAAI,UAAU,SAAS,EAAE,GAAG;AAC1B,2BAAO;AAAA,kBACT;AACA,4BAAU,QAAQ;AAAA,gBACpB;AACA,uBAAO,CAAC,CAAC;AAAA,cACX;AAAA,YACF;AAAA,YACA,WAAW;AAAA,cACT,OAAO;AAAA,cACP,WAAW;AAAA,cACX,MAAM;AAAA,cACN,KAAK;AAAA,cACL,QAAQ,SAAU,IAAI,OAAO;AAC3B,oBAAIC,QAAO,EAAE,KAAK,MAAM,EAAE,UAAU,EAAE,CAAC;AACvC,yBAAS,OAAO,OAAO;AACrB,kBAAAA,MAAK,GAAG,IAAI,MAAM,GAAG;AAAA,gBACvB;AACA,uBAAOA;AAAA,cACT;AAAA,cACA,cAAc,SAAU,QAAQ,QAAQ,QAAQ,MAAM;AACpD,uBAAO,QAAQ,EAAE;AACjB,oBAAI,UAAU,KAAK,MAAM;AACzB,oBAAI,MAAM,CAAC;AACX,yBAAS,SAAS,SAAS;AACzB,sBAAI,QAAQ,eAAe,KAAK,GAAG;AACjC,wBAAI,SAAS,QAAQ;AACnB,+BAAS,YAAY,QAAQ;AAC3B,4BAAI,OAAO,eAAe,QAAQ,GAAG;AACnC,8BAAI,QAAQ,IAAI,OAAO,QAAQ;AAAA,wBACjC;AAAA,sBACF;AAAA,oBACF;AACA,wBAAI,CAAC,OAAO,eAAe,KAAK,GAAG;AACjC,0BAAI,KAAK,IAAI,QAAQ,KAAK;AAAA,oBAC5B;AAAA,kBACF;AAAA,gBACF;AACA,oBAAI,MAAM,KAAK,MAAM;AACrB,qBAAK,MAAM,IAAI;AACf,kBAAE,UAAU,IAAI,EAAE,WAAW,SAAU,KAAK,OAAO;AACjD,sBAAI,UAAU,OAAO,OAAO,QAAQ;AAClC,yBAAK,GAAG,IAAI;AAAA,kBACd;AAAA,gBACF,CAAC;AACD,uBAAO;AAAA,cACT;AAAA,cACA,KAAK,SAAS,IAAI,GAAG,UAAU,MAAM,SAAS;AAC5C,0BAAU,WAAW,CAAC;AACtB,oBAAI,QAAQ,EAAE,KAAK;AACnB,yBAAS,KAAK,GAAG;AACf,sBAAI,EAAE,eAAe,CAAC,GAAG;AACvB,6BAAS,KAAK,GAAG,GAAG,EAAE,CAAC,GAAG,QAAQ,CAAC;AACnC,wBAAI,WAAW,EAAE,CAAC;AAClB,wBAAI,eAAe,EAAE,KAAK,KAAK,QAAQ;AACvC,wBAAI,iBAAiB,YAAY,CAAC,QAAQ,MAAM,QAAQ,CAAC,GAAG;AAC1D,8BAAQ,MAAM,QAAQ,CAAC,IAAI;AAC3B,0BAAI,UAAU,UAAU,MAAM,OAAO;AAAA,oBACvC,WAAW,iBAAiB,WAAW,CAAC,QAAQ,MAAM,QAAQ,CAAC,GAAG;AAChE,8BAAQ,MAAM,QAAQ,CAAC,IAAI;AAC3B,0BAAI,UAAU,UAAU,GAAG,OAAO;AAAA,oBACpC;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,YACA,SAAS,CAAC;AAAA,YACV,cAAc,SAAU,OAAO,UAAU;AACvC,gBAAE,kBAAkB,UAAU,OAAO,QAAQ;AAAA,YAC/C;AAAA,YACA,mBAAmB,SAAU,WAAW,OAAO,UAAU;AACvD,kBAAI,MAAM;AAAA,gBACR;AAAA,gBACA;AAAA,gBACA,UAAU;AAAA,cACZ;AACA,gBAAE,MAAM,IAAI,uBAAuB,GAAG;AACtC,kBAAI,WAAW,MAAM,UAAU,MAAM,MAAM,IAAI,UAAU,iBAAiB,IAAI,QAAQ,CAAC;AACvF,gBAAE,MAAM,IAAI,iCAAiC,GAAG;AAChD,uBAAS,IAAI,GAAG,SAAS,UAAU,IAAI,SAAS,GAAG,KAAI;AACrD,kBAAE,iBAAiB,SAAS,UAAU,MAAM,IAAI,QAAQ;AAAA,cAC1D;AAAA,YACF;AAAA,YACA,kBAAkB,SAAU,SAAS,OAAO,UAAU;AACpD,kBAAI,WAAW,EAAE,KAAK,YAAY,OAAO;AACzC,kBAAI,UAAU,EAAE,UAAU,QAAQ;AAClC,gBAAE,KAAK,YAAY,SAAS,QAAQ;AACpC,kBAAI,SAAS,QAAQ;AACrB,kBAAI,UAAU,OAAO,SAAS,YAAY,MAAM,OAAO;AACrD,kBAAE,KAAK,YAAY,QAAQ,QAAQ;AAAA,cACrC;AACA,kBAAI,OAAO,QAAQ;AACnB,kBAAI,MAAM;AAAA,gBACR;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF;AACA,uBAAS,sBAAsB,iBAAiB;AAC9C,oBAAI,kBAAkB;AACtB,kBAAE,MAAM,IAAI,iBAAiB,GAAG;AAChC,oBAAI,QAAQ,YAAY,IAAI;AAC5B,kBAAE,MAAM,IAAI,mBAAmB,GAAG;AAClC,kBAAE,MAAM,IAAI,YAAY,GAAG;AAC3B,4BAAY,SAAS,KAAK,IAAI,OAAO;AAAA,cACvC;AACA,gBAAE,MAAM,IAAI,uBAAuB,GAAG;AACtC,uBAAS,IAAI,QAAQ;AACrB,kBAAI,UAAU,OAAO,SAAS,YAAY,MAAM,SAAS,CAAC,OAAO,aAAa,UAAU,GAAG;AACzF,uBAAO,aAAa,YAAY,GAAG;AAAA,cACrC;AACA,kBAAI,CAAC,IAAI,MAAM;AACb,kBAAE,MAAM,IAAI,YAAY,GAAG;AAC3B,4BAAY,SAAS,KAAK,IAAI,OAAO;AACrC;AAAA,cACF;AACA,gBAAE,MAAM,IAAI,oBAAoB,GAAG;AACnC,kBAAI,CAAC,IAAI,SAAS;AAChB,sCAAsB,EAAE,KAAK,OAAO,IAAI,IAAI,CAAC;AAC7C;AAAA,cACF;AACA,kBAAI,SAASD,OAAM,QAAQ;AACzB,oBAAI,SAAS,IAAI,OAAO,EAAE,QAAQ;AAClC,uBAAO,YAAY,SAAU,KAAK;AAChC,wCAAsB,IAAI,IAAI;AAAA,gBAChC;AACA,uBAAO,YAAY,KAAK,UAAU;AAAA,kBAChC,UAAU,IAAI;AAAA,kBACd,MAAM,IAAI;AAAA,kBACV,gBAAgB;AAAA,gBAClB,CAAC,CAAC;AAAA,cACJ,OAAO;AACL,sCAAsB,EAAE,UAAU,IAAI,MAAM,IAAI,SAAS,IAAI,QAAQ,CAAC;AAAA,cACxE;AAAA,YACF;AAAA,YACA,WAAW,SAAU,MAAM,SAAS,UAAU;AAC5C,kBAAI,MAAM;AAAA,gBACR,MAAM;AAAA,gBACN;AAAA,gBACA;AAAA,cACF;AACA,gBAAE,MAAM,IAAI,mBAAmB,GAAG;AAClC,kBAAI,CAAC,IAAI,SAAS;AAChB,sBAAM,IAAI,MAAM,mBAAmB,IAAI,WAAW,mBAAmB;AAAA,cACvE;AACA,kBAAI,SAAS,EAAE,SAAS,IAAI,MAAM,IAAI,OAAO;AAC7C,gBAAE,MAAM,IAAI,kBAAkB,GAAG;AACjC,qBAAO,MAAM,UAAU,EAAE,KAAK,OAAO,IAAI,MAAM,GAAG,IAAI,QAAQ;AAAA,YAChE;AAAA,YACA,UAAU,SAAU,MAAM,SAAS;AACjC,kBAAI,OAAO,QAAQ;AACnB,kBAAI,MAAM;AACR,yBAAS,SAAS,MAAM;AACtB,0BAAQ,KAAK,IAAI,KAAK,KAAK;AAAA,gBAC7B;AACA,uBAAO,QAAQ;AAAA,cACjB;AACA,kBAAI,YAAY,IAAI,WAAW;AAC/B,uBAAS,WAAW,UAAU,MAAM,IAAI;AACxC,2BAAa,MAAM,WAAW,SAAS,UAAU,MAAM,CAAC;AACxD,qBAAO,QAAQ,SAAS;AAAA,YAC1B;AAAA,YACA,OAAO;AAAA,cACL,KAAK,CAAC;AAAA,cACN,KAAK,SAAU,MAAM,UAAU;AAC7B,oBAAI,QAAQ,EAAE,MAAM;AACpB,sBAAM,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;AAC9B,sBAAM,IAAI,EAAE,KAAK,QAAQ;AAAA,cAC3B;AAAA,cACA,KAAK,SAAU,MAAM,KAAK;AACxB,oBAAI,YAAY,EAAE,MAAM,IAAI,IAAI;AAChC,oBAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AACnC;AAAA,gBACF;AACA,yBAAS,IAAI,GAAG,UAAU,WAAW,UAAU,GAAG,KAAI;AACpD,2BAAS,GAAG;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,UACF;AACA,UAAAA,OAAM,QAAQ;AACd,mBAAS,MAAM,MAAM,SAAS,OAAO,YAAY;AAC/C,iBAAK,OAAO;AACZ,iBAAK,UAAU;AACf,iBAAK,QAAQ;AACb,iBAAK,UAAU,cAAc,IAAI,SAAS;AAAA,UAC5C;AACA,gBAAM,YAAY,SAAS,UAAU,GAAG,UAAU;AAChD,gBAAI,OAAO,KAAK,UAAU;AACxB,qBAAO;AAAA,YACT;AACA,gBAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,kBAAI,IAAI;AACR,gBAAE,QAAQ,SAAU,GAAG;AACrB,qBAAK,UAAU,GAAG,QAAQ;AAAA,cAC5B,CAAC;AACD,qBAAO;AAAA,YACT;AACA,gBAAI,MAAM;AAAA,cACR,MAAM,EAAE;AAAA,cACR,SAAS,UAAU,EAAE,SAAS,QAAQ;AAAA,cACtC,KAAK;AAAA,cACL,SAAS;AAAA,gBACP;AAAA,gBACA,EAAE;AAAA,cACJ;AAAA,cACA,YAAY,CAAC;AAAA,cACb;AAAA,YACF;AACA,gBAAI,UAAU,EAAE;AAChB,gBAAI,SAAS;AACX,kBAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,sBAAM,UAAU,KAAK,MAAM,IAAI,SAAS,OAAO;AAAA,cACjD,OAAO;AACL,oBAAI,QAAQ,KAAK,OAAO;AAAA,cAC1B;AAAA,YACF;AACA,cAAE,MAAM,IAAI,QAAQ,GAAG;AACvB,gBAAI,aAAa;AACjB,qBAAS,QAAQ,IAAI,YAAY;AAC/B,4BAAc,MAAM,OAAO,QAAQ,IAAI,WAAW,IAAI,KAAK,IAAI,QAAQ,MAAM,QAAQ,IAAI;AAAA,YAC3F;AACA,mBAAO,MAAM,IAAI,MAAM,aAAa,IAAI,QAAQ,KAAK,GAAG,IAAI,MAAM,aAAa,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM;AAAA,UACtH;AACA,mBAAS,aAAa,SAAS,KAAK,MAAM,YAAY;AACpD,oBAAQ,YAAY;AACpB,gBAAI,QAAQ,QAAQ,KAAK,IAAI;AAC7B,gBAAI,SAAS,cAAc,MAAM,CAAC,GAAG;AACnC,kBAAI,mBAAmB,MAAM,CAAC,EAAE;AAChC,oBAAM,SAAS;AACf,oBAAM,CAAC,IAAI,MAAM,CAAC,EAAE,MAAM,gBAAgB;AAAA,YAC5C;AACA,mBAAO;AAAA,UACT;AACA,mBAAS,aAAa,MAAM,WAAW,SAAS,WAAW,UAAU,SAAS;AAC5E,qBAAS,SAAS,SAAS;AACzB,kBAAI,CAAC,QAAQ,eAAe,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG;AACrD;AAAA,cACF;AACA,kBAAI,WAAW,QAAQ,KAAK;AAC5B,yBAAW,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AACzD,uBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACxC,oBAAI,WAAW,QAAQ,SAAS,QAAQ,MAAM,GAAG;AAC/C;AAAA,gBACF;AACA,oBAAI,aAAa,SAAS,CAAC;AAC3B,oBAAI,SAAS,WAAW;AACxB,oBAAI,aAAa,CAAC,CAAC,WAAW;AAC9B,oBAAI,SAAS,CAAC,CAAC,WAAW;AAC1B,oBAAI,QAAQ,WAAW;AACvB,oBAAI,UAAU,CAAC,WAAW,QAAQ,QAAQ;AACxC,sBAAI,QAAQ,WAAW,QAAQ,SAAS,EAAE,MAAM,WAAW,EAAE,CAAC;AAC9D,6BAAW,UAAU,OAAO,WAAW,QAAQ,QAAQ,QAAQ,GAAG;AAAA,gBACpE;AACA,oBAAI,UAAU,WAAW,WAAW;AACpC,yBAAS,cAAc,UAAU,MAAM,MAAM,UAAU,gBAAgB,UAAU,MAAM,OAAO,YAAY,MAAM,QAAQ,cAAc,YAAY,MAAM;AACtJ,sBAAI,WAAW,OAAO,QAAQ,OAAO;AACnC;AAAA,kBACF;AACA,sBAAI,MAAM,YAAY;AACtB,sBAAI,UAAU,SAAS,KAAK,QAAQ;AAClC;AAAA,kBACF;AACA,sBAAI,eAAe,OAAO;AACxB;AAAA,kBACF;AACA,sBAAI,cAAc;AAClB,sBAAI;AACJ,sBAAI,QAAQ;AACV,4BAAQ,aAAa,SAAS,KAAK,MAAM,UAAU;AACnD,wBAAI,CAAC,SAAS,MAAM,SAAS,KAAK,QAAQ;AACxC;AAAA,oBACF;AACA,wBAAI,OAAO,MAAM;AACjB,wBAAI,KAAK,MAAM,QAAQ,MAAM,CAAC,EAAE;AAChC,wBAAI,IAAI;AACR,yBAAK,YAAY,MAAM;AACvB,2BAAO,QAAQ,GAAG;AAChB,oCAAc,YAAY;AAC1B,2BAAK,YAAY,MAAM;AAAA,oBACzB;AACA,yBAAK,YAAY,MAAM;AACvB,0BAAM;AACN,wBAAI,YAAY,iBAAiB,OAAO;AACtC;AAAA,oBACF;AACA,6BAAS,IAAI,aAAa,MAAM,UAAU,SAAS,IAAI,MAAM,OAAO,EAAE,UAAU,WAAW,IAAI,EAAE,MAAM;AACrG;AACA,2BAAK,EAAE,MAAM;AAAA,oBACf;AACA;AACA,0BAAM,KAAK,MAAM,KAAK,CAAC;AACvB,0BAAM,SAAS;AAAA,kBACjB,OAAO;AACL,4BAAQ,aAAa,SAAS,GAAG,KAAK,UAAU;AAChD,wBAAI,CAAC,OAAO;AACV;AAAA,oBACF;AAAA,kBACF;AACA,sBAAI,OAAO,MAAM;AACjB,sBAAI,WAAW,MAAM,CAAC;AACtB,sBAAI,SAAS,IAAI,MAAM,GAAG,IAAI;AAC9B,sBAAI,QAAQ,IAAI,MAAM,OAAO,SAAS,MAAM;AAC5C,sBAAI,QAAQ,MAAM,IAAI;AACtB,sBAAI,WAAW,QAAQ,QAAQ,OAAO;AACpC,4BAAQ,QAAQ;AAAA,kBAClB;AACA,sBAAI,aAAa,YAAY;AAC7B,sBAAI,QAAQ;AACV,iCAAa,SAAS,WAAW,YAAY,MAAM;AACnD,2BAAO,OAAO;AAAA,kBAChB;AACA,8BAAY,WAAW,YAAY,WAAW;AAC9C,sBAAI,UAAU,IAAI,MAAM,OAAO,SAAS,EAAE,SAAS,UAAU,MAAM,IAAI,UAAU,OAAO,QAAQ;AAChG,gCAAc,SAAS,WAAW,YAAY,OAAO;AACrD,sBAAI,OAAO;AACT,6BAAS,WAAW,aAAa,KAAK;AAAA,kBACxC;AACA,sBAAI,cAAc,GAAG;AACnB,wBAAI,gBAAgB;AAAA,sBAClB,OAAO,QAAQ,MAAM;AAAA,sBACrB;AAAA,oBACF;AACA,iCAAa,MAAM,WAAW,SAAS,YAAY,MAAM,KAAK,aAAa;AAC3E,wBAAI,WAAW,cAAc,QAAQ,QAAQ,OAAO;AAClD,8BAAQ,QAAQ,cAAc;AAAA,oBAChC;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,mBAAS,aAAa;AACpB,gBAAIE,QAAO;AAAA,cACT,OAAO;AAAA,cACP,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AACA,gBAAI,OAAO;AAAA,cACT,OAAO;AAAA,cACP,MAAMA;AAAA,cACN,MAAM;AAAA,YACR;AACA,YAAAA,MAAK,OAAO;AACZ,iBAAK,OAAOA;AACZ,iBAAK,OAAO;AACZ,iBAAK,SAAS;AAAA,UAChB;AACA,mBAAS,SAAS,MAAM,MAAM,OAAO;AACnC,gBAAI,OAAO,KAAK;AAChB,gBAAI,UAAU;AAAA,cACZ;AAAA,cACA,MAAM;AAAA,cACN;AAAA,YACF;AACA,iBAAK,OAAO;AACZ,iBAAK,OAAO;AACZ,iBAAK;AACL,mBAAO;AAAA,UACT;AACA,mBAAS,YAAY,MAAM,MAAM,OAAO;AACtC,gBAAI,OAAO,KAAK;AAChB,qBAAS,IAAI,GAAG,IAAI,SAAS,SAAS,KAAK,MAAM,KAAK;AACpD,qBAAO,KAAK;AAAA,YACd;AACA,iBAAK,OAAO;AACZ,iBAAK,OAAO;AACZ,iBAAK,UAAU;AAAA,UACjB;AACA,mBAAS,QAAQ,MAAM;AACrB,gBAAI,QAAQ,CAAC;AACb,gBAAI,OAAO,KAAK,KAAK;AACrB,mBAAO,SAAS,KAAK,MAAM;AACzB,oBAAM,KAAK,KAAK,KAAK;AACrB,qBAAO,KAAK;AAAA,YACd;AACA,mBAAO;AAAA,UACT;AACA,cAAI,CAACF,OAAM,UAAU;AACnB,gBAAI,CAACA,OAAM,kBAAkB;AAC3B,qBAAO;AAAA,YACT;AACA,gBAAI,CAAC,EAAE,6BAA6B;AAClC,cAAAA,OAAM,iBAAiB,WAAW,SAAU,KAAK;AAC/C,oBAAI,UAAU,KAAK,MAAM,IAAI,IAAI;AACjC,oBAAIC,QAAO,QAAQ;AACnB,oBAAI,OAAO,QAAQ;AACnB,oBAAI,iBAAiB,QAAQ;AAC7B,gBAAAD,OAAM,YAAY,EAAE,UAAU,MAAM,EAAE,UAAUC,KAAI,GAAGA,KAAI,CAAC;AAC5D,oBAAI,gBAAgB;AAClB,kBAAAD,OAAM,MAAM;AAAA,gBACd;AAAA,cACF,GAAG,KAAK;AAAA,YACV;AACA,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,EAAE,KAAK,cAAc;AAClC,cAAI,QAAQ;AACV,cAAE,WAAW,OAAO;AACpB,gBAAI,OAAO,aAAa,aAAa,GAAG;AACtC,gBAAE,SAAS;AAAA,YACb;AAAA,UACF;AACA,mBAAS,iCAAiC;AACxC,gBAAI,CAAC,EAAE,QAAQ;AACb,gBAAE,aAAa;AAAA,YACjB;AAAA,UACF;AACA,cAAI,CAAC,EAAE,QAAQ;AACb,gBAAI,aAAa,SAAS;AAC1B,gBAAI,eAAe,aAAa,eAAe,iBAAiB,UAAU,OAAO,OAAO;AACtF,uBAAS,iBAAiB,oBAAoB,8BAA8B;AAAA,YAC9E,OAAO;AACL,kBAAI,OAAO,uBAAuB;AAChC,uBAAO,sBAAsB,8BAA8B;AAAA,cAC7D,OAAO;AACL,uBAAO,WAAW,gCAAgC,EAAE;AAAA,cACtD;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT,EAAE,KAAK;AACP,YAAI,OAAOF,YAAW,eAAeA,QAAO,SAAS;AACnD,UAAAA,QAAO,UAAU;AAAA,QACnB;AACA,YAAI,OAAOD,YAAW,aAAa;AACjC,UAAAA,QAAO,QAAQ;AAAA,QACjB;AACA,cAAM,UAAU,QAAQ;AAAA,UACtB,WAAW;AAAA,YACT;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ,EAAE,eAAe,QAAQ;AAAA,UACnC;AAAA,UACA,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,eAAe;AAAA,QACjB;AACA,SAAC,SAAUM,QAAO;AAChB,mBAAS,eAAe,UAAU,OAAO;AACvC,mBAAO,QAAQ,SAAS,YAAY,IAAI,QAAQ;AAAA,UAClD;AACA,iBAAO,iBAAiBA,OAAM,UAAU,mBAAmB,IAAI,CAAC,GAAG;AAAA,YACjE,mBAAmB;AAAA,cACjB,OAAO,SAAU,KAAK,UAAU,oBAAoB,eAAe;AACjE,oBAAI,IAAI,aAAa,UAAU;AAC7B;AAAA,gBACF;AACA,oBAAI,aAAa,IAAI,aAAa,CAAC;AACnC,oBAAI,OAAO,IAAI,KAAK,QAAQ,oBAAoB,SAAU,OAAO;AAC/D,sBAAI,OAAO,kBAAkB,cAAc,CAAC,cAAc,KAAK,GAAG;AAChE,2BAAO;AAAA,kBACT;AACA,sBAAI,IAAI,WAAW;AACnB,sBAAI;AACJ,yBAAO,IAAI,KAAK,QAAQ,cAAc,eAAe,UAAU,CAAC,CAAC,MAAM,IAAI;AACzE,sBAAE;AAAA,kBACJ;AACA,6BAAW,CAAC,IAAI;AAChB,yBAAO;AAAA,gBACT,CAAC;AACD,oBAAI,UAAUA,OAAM,UAAU;AAAA,cAChC;AAAA,YACF;AAAA,YACA,sBAAsB;AAAA,cACpB,OAAO,SAAU,KAAK,UAAU;AAC9B,oBAAI,IAAI,aAAa,YAAY,CAAC,IAAI,YAAY;AAChD;AAAA,gBACF;AACA,oBAAI,UAAUA,OAAM,UAAU,QAAQ;AACtC,oBAAI,IAAI;AACR,oBAAI,OAAO,OAAO,KAAK,IAAI,UAAU;AACrC,yBAAS,WAAW,QAAQ;AAC1B,2BAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,wBAAI,KAAK,KAAK,QAAQ;AACpB;AAAA,oBACF;AACA,wBAAI,QAAQ,OAAO,CAAC;AACpB,wBAAI,OAAO,UAAU,YAAY,MAAM,WAAW,OAAO,MAAM,YAAY,UAAU;AACnF,0BAAI,IAAI,KAAK,CAAC;AACd,0BAAI,IAAI,IAAI,WAAW,CAAC;AACxB,0BAAI,IAAI,OAAO,UAAU,WAAW,QAAQ,MAAM;AAClD,0BAAI,cAAc,eAAe,UAAU,CAAC;AAC5C,0BAAI,QAAQ,EAAE,QAAQ,WAAW;AACjC,0BAAI,QAAQ,IAAI;AACd,0BAAE;AACF,4BAAI,SAAS,EAAE,UAAU,GAAG,KAAK;AACjC,4BAAI,SAAS,IAAIA,OAAM,MAAM,UAAUA,OAAM,SAAS,GAAG,IAAI,OAAO,GAAG,cAAc,UAAU,CAAC;AAChG,4BAAI,QAAQ,EAAE,UAAU,QAAQ,YAAY,MAAM;AAClD,4BAAI,cAAc,CAAC;AACnB,4BAAI,QAAQ;AACV,sCAAY,KAAK,MAAM,aAAa,WAAW,CAAC,MAAM,CAAC,CAAC;AAAA,wBAC1D;AACA,oCAAY,KAAK,MAAM;AACvB,4BAAI,OAAO;AACT,sCAAY,KAAK,MAAM,aAAa,WAAW,CAAC,KAAK,CAAC,CAAC;AAAA,wBACzD;AACA,4BAAI,OAAO,UAAU,UAAU;AAC7B,iCAAO,OAAO,MAAM,QAAQ;AAAA,4BAC1B;AAAA,4BACA;AAAA,0BACF,EAAE,OAAO,WAAW,CAAC;AAAA,wBACvB,OAAO;AACL,gCAAM,UAAU;AAAA,wBAClB;AAAA,sBACF;AAAA,oBACF,WAAW,MAAM,SAAS;AACxB,iCAAW,MAAM,OAAO;AAAA,oBAC1B;AAAA,kBACF;AACA,yBAAO;AAAA,gBACT;AACA,2BAAW,IAAI,MAAM;AAAA,cACvB;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH,GAAE,KAAK;AACP,cAAM,UAAU,IAAI,MAAM,UAAU,OAAO,SAAS;AAAA,UAClD,WAAW;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,YAAY;AAAA,QACd,CAAC;AACD,cAAM,UAAU,aAAa,KAAK,UAAU;AAAA,UAC1C,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,QACF,CAAC;AACD,cAAM,UAAU,aAAa,KAAK,UAAU;AAAA,UAC1C,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,UAAU;AAAA,gBACR;AAAA,kBACE,SAAS;AAAA,kBACT,YAAY;AAAA,gBACd;AAAA,gBACA,MAAM,UAAU,EAAE,QAAQ;AAAA,cAC5B;AAAA,cACA,QAAQ,MAAM,UAAU,EAAE,MAAM;AAAA,cAChC,WAAW,MAAM,UAAU,EAAE,SAAS;AAAA,cACtC,cAAc;AAAA,gBACZ;AAAA,kBACE,SAAS;AAAA,kBACT,YAAY;AAAA,gBACd;AAAA,gBACA;AAAA,kBACE,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,cACA,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,cACA,kBAAkB;AAAA,cAClB,eAAe;AAAA,cACf,cAAc;AAAA,gBACZ,SAAS;AAAA,gBACT,QAAQ,MAAM,UAAU;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,cAAM,UAAU,aAAa,KAAK,YAAY,EAAE,YAAY,6HAA6H,CAAC;AAC1L,eAAO,MAAM,UAAU,EAAE,SAAS;AAClC,SAAC,SAAUA,QAAO;AAChB,cAAI,UAAU;AACd,cAAI,UAAU,uCAAuC,OAAO,QAAQ,cAAc,WAAY;AAC5F,mBAAO,QAAQ;AAAA,UACjB,CAAC;AACD,UAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,KAAK;AAAA,YAChD,cAAc;AAAA,cACZ;AAAA,gBACE,SAAS,OAAO,gEAAgE,OAAO,QAAQ,cAAc,WAAY;AACvH,yBAAO,QAAQ;AAAA,gBACjB,CAAC,CAAC;AAAA,gBACF,YAAY;AAAA,cACd;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,YACA,WAAW;AAAA,YACX,UAAU;AAAA,cACR,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,YACA,YAAY;AAAA,YACZ,WAAW;AAAA,UACb,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,OAAO,UAAU;AAAA,YAC5C,UAAU;AAAA,cACR,SAAS,OAAO,2BAA2B,SAAS,QAAQ,mDAAmD,SAAS,MAAM,kDAAkD,OAAO,QAAQ,eAAe,WAAY;AACxN,uBAAO;AAAA,cACT,CAAC,IAAI,GAAG;AAAA,cACR,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,eAAe;AAAA,cACjB;AAAA,YACF;AAAA,YACA,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,YACV;AAAA,UACF,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,OAAO,WAAW;AAAA,YAC7C,oBAAoB;AAAA,cAClB,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,YAAY;AAAA,gBACZ,WAAW;AAAA,kBACT,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQA,OAAM,UAAU;AAAA,gBAC1B;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,OAAO,YAAY;AAAA,YAC9C,gBAAgB;AAAA,cACd,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,UACF,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,OAAO,cAAc;AAAA,YAChD,eAAe;AAAA,cACb,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,QAAQA,OAAM,UAAU,OAAO,OAAO,CAAC,CAAC;AAAA,YAC1C;AAAA,UACF,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,UAAU,gBAAgB,EAAE,cAAc,0BAA0B,GAAGA,OAAM,UAAU,IAAI,aAAa,CAAC;AAAA,QACxI,GAAE,KAAK;AACP,SAAC,SAAUA,QAAO;AAChB,mBAAS,QAAQ,SAAS,cAAc;AACtC,mBAAO,QAAQ,QAAQ,cAAc,SAAU,GAAG,OAAO;AACvD,qBAAO,QAAQ,aAAa,CAAC,KAAK,IAAI;AAAA,YACxC,CAAC;AAAA,UACH;AACA,mBAAS,GAAG,SAAS,cAAc,OAAO;AACxC,mBAAO,OAAO,QAAQ,SAAS,YAAY,GAAG,SAAS,EAAE;AAAA,UAC3D;AACA,mBAAS,OAAO,SAAS,WAAW;AAClC,qBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,wBAAU,QAAQ,QAAQ,aAAa,WAAY;AACjD,uBAAO,QAAQ,UAAU;AAAA,cAC3B,CAAC;AAAA,YACH;AACA,mBAAO,QAAQ,QAAQ,aAAa,WAAW;AAAA,UACjD;AACA,cAAI,eAAe;AAAA,YACjB,MAAM;AAAA,YACN,iBAAiB;AAAA,YACjB,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AACA,mBAAS,kBAAkB,OAAO;AAChC,mBAAO,WAAW,MAAM,KAAK,EAAE,QAAQ,MAAM,GAAG,IAAI;AAAA,UACtD;AACA,cAAI,0BAA0B,kBAAkB,aAAa,eAAe;AAC5E,cAAI,WAAW,OAAO,kBAAkB,aAAa,OAAO,MAAM,aAAa,kBAAkB,MAAM,aAAa,aAAa,MAAM,aAAa,KAAK,CAAC;AAC1J,cAAI,kBAAkB,kBAAkB,aAAa,kBAAkB,MAAM,aAAa,aAAa,MAAM,aAAa,KAAK;AAC/H,cAAI,wBAAwB,kBAAkB,aAAa,OAAO,MAAM,aAAa,kBAAkB,MAAM,aAAa,KAAK;AAC/H,cAAI,UAAU,OAAO,mCAAmC,QAAQ,CAAC;AACjE,cAAI,cAAc,OAAO,0BAA0B,QAAQ,CAAC;AAC5D,cAAI,OAAO,qBAAqB;AAChC,cAAI,cAAc,QAAQ,qBAAqB,QAAQ;AAAA,YACrD;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,aAAa,QAAQ,mCAAmC,QAAQ;AAAA,YAClE;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,QAAQ,mBAAmB;AAC/B,cAAI,6BAA6B,QAAQ,yCAAyC,QAAQ;AAAA,YACxF;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,eAAe,QAAQ,2CAA2C,QAAQ;AAAA,YAC5E;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,QAAQ,QAAQ,yBAAyB,QAAQ,CAAC,YAAY,CAAC;AACnE,cAAI,iBAAiB,QAAQ,mDAAmD,QAAQ;AAAA,YACtF;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,aAAa;AAAA,YACf,WAAW;AAAA,YACX,eAAe;AAAA,UACjB;AACA,cAAI,YAAY,8CAA8C;AAC9D,cAAI,gBAAgB,wBAAwB;AAC5C,cAAI,iBAAiB,kCAAkC;AACvD,UAAAA,OAAM,UAAU,SAASA,OAAM,UAAU,OAAO,SAAS;AAAA,YACvD,UAAU;AAAA,cACR;AAAA,gBACE,SAAS,GAAG,kBAAkB,QAAQ,CAAC,cAAc,CAAC;AAAA,gBACtD,YAAY;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,gBACE,SAAS,GAAG,mBAAmB,QAAQ,CAAC,aAAa,CAAC;AAAA,gBACtD,YAAY;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,YACA,cAAc;AAAA,cACZ;AAAA,gBACE,SAAS,GAAG,qCAAqC,QAAQ,CAAC,UAAU,CAAC;AAAA,gBACrE,YAAY;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,gBACE,SAAS,GAAG,wCAAwC,QAAQ;AAAA,kBAC1D;AAAA,kBACA;AAAA,gBACF,CAAC;AAAA,gBACD,YAAY;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,gBACE,SAAS,GAAG,4BAA4B,QAAQ,CAAC,IAAI,CAAC;AAAA,gBACtD,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,SAAS,GAAG,oBAAoB,QAAQ;AAAA,kBACtC;AAAA,kBACA;AAAA,gBACF,CAAC;AAAA,gBACD,YAAY;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,gBACE,SAAS,GAAG,yBAAyB,QAAQ,CAAC,UAAU,CAAC;AAAA,gBACzD,YAAY;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,gBACE,SAAS,GAAG,oBAAoB,QAAQ,CAAC,IAAI,CAAC;AAAA,gBAC9C,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,SAAS,GAAG,mCAAmC,QAAQ,CAAC,0BAA0B,CAAC;AAAA,gBACnF,YAAY;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,gBACE,SAAS,GAAG,2EAA2E,QAAQ;AAAA,kBAC7F;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF,CAAC;AAAA,gBACD,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,YACA,WAAW;AAAA,YACX,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,eAAe;AAAA,UACjB,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,UAAU,UAAU;AAAA,YAC/C,SAAS;AAAA,cACP,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,UACF,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,UAAU,eAAe;AAAA,YACpD,mBAAmB;AAAA,cACjB,SAAS,GAAG,yBAAyB,QAAQ,CAAC,IAAI,CAAC;AAAA,cACnD,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,UACF,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,UAAU,cAAc;AAAA,YACnD,aAAa;AAAA,cACX,SAAS,GAAG,+DAA+D,QAAQ,CAAC,IAAI,CAAC;AAAA,cACzF,YAAY;AAAA,cACZ,QAAQ,EAAE,eAAe,KAAK;AAAA,YAChC;AAAA,YACA,mBAAmB;AAAA,cACjB,SAAS,GAAG,kFAAkF,QAAQ,CAAC,WAAW,CAAC;AAAA,cACnH,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ;AAAA,YACV;AAAA,YACA,eAAe;AAAA,cACb,SAAS,GAAG,+DAA+D,QAAQ;AAAA,gBACjF;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,cACD,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,YACA,0BAA0B;AAAA,cACxB,SAAS,GAAG,8BAA8B,QAAQ,CAAC,cAAc,CAAC;AAAA,cAClE,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,YACA,kBAAkB;AAAA,cAChB,SAAS,GAAG,yBAAyB,QAAQ;AAAA,gBAC3C;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,cACD,QAAQ;AAAA,gBACN,YAAY,GAAG,SAAS,QAAQ,CAAC,IAAI,CAAC;AAAA,gBACtC,WAAW;AAAA,kBACT,SAAS,OAAO,OAAO;AAAA,kBACvB,OAAO;AAAA,kBACP,QAAQ;AAAA,gBACV;AAAA,cACF;AAAA,YACF;AAAA,YACA,aAAa;AAAA,cACX,SAAS,GAAG,kKAAkK,QAAQ;AAAA,gBACpL;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,SAAS;AAAA,gBACT;AAAA,gBACA,kBAAkB;AAAA,cACpB,CAAC;AAAA,cACD,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,oBAAoB;AAAA,kBAClB,SAAS,GAAG,+BAA+B,QAAQ;AAAA,oBACjD;AAAA,oBACA;AAAA,kBACF,CAAC;AAAA,kBACD,YAAY;AAAA,kBACZ,QAAQ;AAAA,kBACR,QAAQA,OAAM,UAAU;AAAA,gBAC1B;AAAA,gBACA,WAAW;AAAA,gBACX,cAAc;AAAA,kBACZ,SAAS,OAAO,cAAc;AAAA,kBAC9B,QAAQ;AAAA,kBACR,QAAQ;AAAA,gBACV;AAAA,gBACA,eAAe;AAAA,cACjB;AAAA,YACF;AAAA,YACA,gBAAgB;AAAA,cACd,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,aAAa;AAAA,kBACX,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AACD,cAAI,2BAA2B,gBAAgB,MAAM;AACrD,cAAI,kCAAkC,QAAQ,iEAAiE,QAAQ,CAAC,wBAAwB,CAAC;AACjJ,cAAI,kBAAkB,OAAO,QAAQ,+BAA+B,QAAQ,CAAC,+BAA+B,CAAC,GAAG,CAAC;AACjH,cAAI,aAAa,wEAAwE;AACzF,cAAI,OAAO,QAAQ,0BAA0B,QAAQ;AAAA,YACnD;AAAA,YACA;AAAA,UACF,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,UAAU,cAAc;AAAA,YACnD,aAAa;AAAA,cACX,SAAS,GAAG,6EAA6E,QAAQ;AAAA,gBAC/F;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,cACD,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,UAAU;AAAA,kBACR,SAAS,GAAG,iBAAiB,QAAQ,CAAC,UAAU,CAAC;AAAA,kBACjD,OAAO;AAAA,gBACT;AAAA,gBACA,uBAAuB;AAAA,kBACrB,SAAS,GAAG,aAAa,QAAQ,CAAC,eAAe,CAAC;AAAA,kBAClD,QAAQA,OAAM,UAAU;AAAA,gBAC1B;AAAA,gBACA,cAAc;AAAA,kBACZ,SAAS,OAAO,UAAU;AAAA,kBAC1B,QAAQ,EAAE,eAAe,KAAK;AAAA,gBAChC;AAAA,gBACA,eAAe;AAAA,cACjB;AAAA,YACF;AAAA,UACF,CAAC;AACD,cAAI,eAAe,aAAa;AAChC,cAAI,sBAAsB,OAAO,QAAQ,+BAA+B,QAAQ,CAAC,+BAA+B,CAAC,GAAG,CAAC;AACrH,cAAI,iBAAiB,QAAQ,qCAAqC,QAAQ;AAAA,YACxE;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,sBAAsB,OAAO,QAAQ,mEAAmE,QAAQ,CAAC,wBAAwB,CAAC,GAAG,CAAC;AAClJ,cAAI,iBAAiB,QAAQ,qCAAqC,QAAQ;AAAA,YACxE;AAAA,YACA;AAAA,UACF,CAAC;AACD,mBAAS,0BAA0B,eAAe,oBAAoB;AACpE,mBAAO;AAAA,cACL,iBAAiB;AAAA,gBACf,SAAS,GAAG,6BAA6B,QAAQ,CAAC,aAAa,CAAC;AAAA,gBAChE,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,iBAAiB;AAAA,oBACf,SAAS,GAAG,sCAAsC,QAAQ;AAAA,sBACxD;AAAA,sBACA;AAAA,oBACF,CAAC;AAAA,oBACD,YAAY;AAAA,oBACZ,QAAQ,EAAE,eAAe,KAAK;AAAA,kBAChC;AAAA,kBACA,eAAe;AAAA,kBACf,cAAc;AAAA,oBACZ,SAAS;AAAA,oBACT,OAAO;AAAA,oBACP,QAAQA,OAAM,UAAU;AAAA,kBAC1B;AAAA,gBACF;AAAA,cACF;AAAA,cACA,UAAU;AAAA,YACZ;AAAA,UACF;AACA,UAAAA,OAAM,UAAU,aAAa,UAAU,UAAU;AAAA,YAC/C,wBAAwB;AAAA,cACtB;AAAA,gBACE,SAAS,GAAG,4DAA4D,QAAQ,CAAC,cAAc,CAAC;AAAA,gBAChG,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,QAAQ,0BAA0B,gBAAgB,mBAAmB;AAAA,cACvE;AAAA,cACA;AAAA,gBACE,SAAS,GAAG,4CAA4C,QAAQ,CAAC,cAAc,CAAC;AAAA,gBAChF,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,QAAQ,0BAA0B,gBAAgB,mBAAmB;AAAA,cACvE;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,cACN,SAAS,OAAO,SAAS;AAAA,cACzB,QAAQ;AAAA,YACV;AAAA,UACF,CAAC;AACD,UAAAA,OAAM,UAAU,SAASA,OAAM,UAAU,KAAKA,OAAM,UAAU;AAAA,QAChE,GAAE,KAAK;AACP,SAAC,SAAUA,QAAO;AAChB,cAAI,SAAS;AACb,UAAAA,OAAM,UAAU,MAAM;AAAA,YACpB,WAAW;AAAA,YACX,UAAU;AAAA,cACR,SAAS,OAAO,eAAe,sBAAsB,SAAS,MAAM,OAAO,SAAS,QAAQ,kBAAkB,MAAM;AAAA,cACpH,QAAQ;AAAA,gBACN,QAAQ;AAAA,gBACR,8BAA8B;AAAA,kBAC5B,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,OAAO;AAAA,gBACT;AAAA,gBACA,WAAW;AAAA,kBACT,SAAS;AAAA,kBACT,YAAY;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAAA,YACA,OAAO;AAAA,cACL,SAAS,OAAO,iBAAiB,OAAO,SAAS,MAAM,8BAA8B,SAAS,QAAQ,GAAG;AAAA,cACzG,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,YAAY;AAAA,gBACZ,eAAe;AAAA,gBACf,UAAU;AAAA,kBACR,SAAS,OAAO,MAAM,OAAO,SAAS,GAAG;AAAA,kBACzC,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,YACA,YAAY;AAAA,cACV,SAAS,OAAO,sDAAuD,OAAO,SAAS,eAAe;AAAA,cACtG,YAAY;AAAA,YACd;AAAA,YACA,UAAU;AAAA,cACR,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,YACA,YAAY;AAAA,cACV,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,aAAa;AAAA,YACb,YAAY;AAAA,cACV,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,eAAe;AAAA,UACjB;AACA,UAAAA,OAAM,UAAU,IAAI,QAAQ,EAAE,OAAO,OAAOA,OAAM,UAAU;AAC5D,cAAI,SAASA,OAAM,UAAU;AAC7B,cAAI,QAAQ;AACV,mBAAO,IAAI,WAAW,SAAS,KAAK;AACpC,mBAAO,IAAI,aAAa,SAAS,KAAK;AAAA,UACxC;AAAA,QACF,GAAE,KAAK;AACP,SAAC,SAAUA,QAAO;AAChB,cAAI,WAAW;AACf,cAAI,kBAAkB,6CAA6C;AACnE,cAAI,YAAY;AAAA,YACd,SAAS,OAAO,aAAa,SAAS,kBAAkB,gCAAgC,MAAM;AAAA,YAC9F,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,QAAQ,EAAE,eAAe,KAAK;AAAA,cAChC;AAAA,cACA,eAAe;AAAA,YACjB;AAAA,UACF;AACA,UAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,OAAO,SAAS;AAAA,YACrD,UAAU;AAAA,cACR,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA,cAAc;AAAA,cACZ;AAAA,cACA;AAAA,gBACE,SAAS,OAAO,aAAa,SAAS,kBAAkB,+DAA+D,MAAM;AAAA,gBAC7H,YAAY;AAAA,gBACZ,QAAQ,UAAU;AAAA,cACpB;AAAA,cACA;AAAA,gBACE,SAAS,OAAO,kFAAkF,SAAS,kBAAkB,aAAa,MAAM;AAAA,gBAChJ,YAAY;AAAA,gBACZ,QAAQ,UAAU;AAAA,cACpB;AAAA,YACF;AAAA,YACA,WAAW;AAAA,YACX,YAAY;AAAA,cACVA,OAAM,UAAU,MAAM;AAAA,cACtB;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,YACF;AAAA,YACA,UAAU;AAAA,YACV,YAAY;AAAA,cACV,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,YAAY;AAAA,UACd,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,YAC7C,wBAAwB;AAAA,cACtB,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,YACA,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,UACF,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,QAAQ,cAAc;AAAA,YACjD,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,YAAY;AAAA,cACV,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,cAAc;AAAA,gBACd,WAAW;AAAA,gBACX,eAAe;AAAA,gBACf,YAAY;AAAA,cACd;AAAA,YACF;AAAA,YACA,UAAU;AAAA,cACR;AAAA,gBACE,SAAS,OAAO,gBAAgB,SAAS,kBAAkB,0BAA0B,MAAM;AAAA,gBAC3F,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,aAAa,UAAU,OAAO;AAAA,kBAC9B,eAAe;AAAA,kBACf,YAAY;AAAA,kBACZ,cAAc;AAAA,gBAChB;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS,OAAO,yBAAyB,SAAS,kBAAkB,qBAAqB,MAAM;AAAA,gBAC/F,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,QAAQ;AAAA,kBACN,aAAa,UAAU,OAAO;AAAA,kBAC9B,UAAU;AAAA,kBACV,eAAe;AAAA,kBACf,YAAY;AAAA,kBACZ,cAAc;AAAA,gBAChB;AAAA,cACF;AAAA,YACF;AAAA,YACA,aAAa;AAAA,cACX,SAAS,OAAO,qJAAqJ,OAAO,QAAQ,cAAc,WAAY;AAC5M,uBAAO,SAAS;AAAA,cAClB,CAAC,CAAC;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ,EAAE,eAAe,KAAK;AAAA,YAChC;AAAA,UACF,CAAC;AAAA,QACH,GAAE,KAAK;AACP,cAAM,UAAU,aAAa,MAAM,UAAU,OAAO,SAAS;AAAA,UAC3D,cAAc;AAAA,YACZ,MAAM,UAAU,MAAM,YAAY;AAAA,YAClC;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,WAAW;AAAA,YACT;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,YAAY;AAAA,UACZ,UAAU;AAAA,YACR,SAAS,OAAO,aAAa,SAAS,SAAS,eAAe,SAAS,MAAM,0BAA0B,SAAS,MAAM,4BAA4B,SAAS,MAAM,sCAAsC,SAAS,MAAM,gBAAgB,SAAS,MAAM,oFAAoF,UAAU,MAAM,YAAY,MAAM;AAAA,YAC3W,YAAY;AAAA,UACd;AAAA,UACA,YAAY;AAAA,QACd,CAAC;AACD,cAAM,UAAU,WAAW,YAAY,EAAE,CAAC,EAAE,UAAU;AACtD,cAAM,UAAU,aAAa,cAAc,WAAW;AAAA,UACpD,SAAS;AAAA,YACP,SAAS,OAAO,0DAA0D,SAAS,KAAK,SAAS,QAAQ,iEAAiE,SAAS,MAAM,qIAAqI,SAAS,MAAM,kEAAkE,MAAM;AAAA,YACrZ,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,gBAAgB;AAAA,gBACd,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,QAAQ,MAAM,UAAU;AAAA,cAC1B;AAAA,cACA,mBAAmB;AAAA,cACnB,eAAe;AAAA,YACjB;AAAA,UACF;AAAA,UACA,qBAAqB;AAAA,YACnB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,aAAa;AAAA,YACX;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ,MAAM,UAAU;AAAA,YAC1B;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ,MAAM,UAAU;AAAA,YAC1B;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ,MAAM,UAAU;AAAA,YAC1B;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ,MAAM,UAAU;AAAA,YAC1B;AAAA,UACF;AAAA,UACA,YAAY;AAAA,QACd,CAAC;AACD,cAAM,UAAU,aAAa,cAAc,UAAU;AAAA,UACnD,YAAY;AAAA,YACV,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,mBAAmB;AAAA,YACjB,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,wBAAwB;AAAA,gBACtB,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,iBAAiB;AAAA,gBACf,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,6BAA6B;AAAA,oBAC3B,SAAS;AAAA,oBACT,OAAO;AAAA,kBACT;AAAA,kBACA,MAAM,MAAM,UAAU;AAAA,gBACxB;AAAA,cACF;AAAA,cACA,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,mBAAmB;AAAA,YACjB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,cAAM,UAAU,aAAa,cAAc,YAAY;AAAA,UACrD,oBAAoB;AAAA,YAClB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,YAAI,MAAM,UAAU,QAAQ;AAC1B,gBAAM,UAAU,OAAO,IAAI,WAAW,UAAU,YAAY;AAC5D,gBAAM,UAAU,OAAO,IAAI,aAAa,yNAAyN,QAAQ,YAAY;AAAA,QACvR;AACA,cAAM,UAAU,KAAK,MAAM,UAAU;AACrC,cAAM,UAAU,SAAS;AAAA,UACvB,WAAW;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,WAAW;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,mBAAmB;AAAA,gBACjB,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,QAAQ;AAAA,cACV;AAAA,cACA,UAAU;AAAA,gBACR,SAAS;AAAA,gBACT,QAAQ;AAAA,cACV;AAAA,cACA,eAAe;AAAA,cACf,eAAe;AAAA,cACf,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,OAAO;AAAA,YACL,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,OAAO;AAAA,gBACL,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,eAAe;AAAA,kBACf,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,gBAAgB,CAAC;AAAA,cACjB,cAAc;AAAA,gBACZ,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,eAAe;AAAA,oBACb;AAAA,sBACE,SAAS;AAAA,sBACT,OAAO;AAAA,oBACT;AAAA,oBACA;AAAA,sBACE,SAAS;AAAA,sBACT,YAAY;AAAA,oBACd;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA,eAAe;AAAA,cACf,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,QAAQ,EAAE,aAAa,eAAe;AAAA,cACxC;AAAA,YACF;AAAA,UACF;AAAA,UACA,UAAU;AAAA,YACR;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM,UAAU,OAAO,KAAK,EAAE,OAAO,YAAY,EAAE,OAAO,QAAQ,IAAI,MAAM,UAAU,OAAO,QAAQ;AACrG,cAAM,UAAU,OAAO,SAAS,EAAE,OAAO,iBAAiB,EAAE,SAAS,MAAM,UAAU;AACrF,cAAM,MAAM,IAAI,QAAQ,SAAU,KAAK;AACrC,cAAI,IAAI,SAAS,UAAU;AACzB,gBAAI,WAAW,OAAO,IAAI,IAAI,QAAQ,QAAQ,SAAS,GAAG;AAAA,UAC5D;AAAA,QACF,CAAC;AACD,eAAO,eAAe,MAAM,UAAU,OAAO,KAAK,cAAc;AAAA,UAC9D,OAAO,SAAS,WAAW,SAAS,MAAM;AACxC,gBAAI,sBAAsB,CAAC;AAC3B,gCAAoB,cAAc,IAAI,IAAI;AAAA,cACxC,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ,MAAM,UAAU,IAAI;AAAA,YAC9B;AACA,gCAAoB,OAAO,IAAI;AAC/B,gBAAI,SAAS;AAAA,cACX,kBAAkB;AAAA,gBAChB,SAAS;AAAA,gBACT,QAAQ;AAAA,cACV;AAAA,YACF;AACA,mBAAO,cAAc,IAAI,IAAI;AAAA,cAC3B,SAAS;AAAA,cACT,QAAQ,MAAM,UAAU,IAAI;AAAA,YAC9B;AACA,gBAAI,MAAM,CAAC;AACX,gBAAI,OAAO,IAAI;AAAA,cACb,SAAS,OAAO,wFAAwF,OAAO,QAAQ,OAAO,WAAY;AACxI,uBAAO;AAAA,cACT,CAAC,GAAG,GAAG;AAAA,cACP,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR;AAAA,YACF;AACA,kBAAM,UAAU,aAAa,UAAU,SAAS,GAAG;AAAA,UACrD;AAAA,QACF,CAAC;AACD,eAAO,eAAe,MAAM,UAAU,OAAO,KAAK,gBAAgB;AAAA,UAChE,OAAO,SAAU,UAAU,MAAM;AAC/B,kBAAM,UAAU,OAAO,IAAI,OAAO,cAAc,EAAE,KAAK;AAAA,cACrD,SAAS,OAAO,aAAa,SAAS,QAAQ,WAAW,MAAM,iDAAiD,QAAQ,GAAG;AAAA,cAC3H,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,aAAa;AAAA,gBACb,cAAc;AAAA,kBACZ,SAAS;AAAA,kBACT,QAAQ;AAAA,oBACN,SAAS;AAAA,sBACP,SAAS;AAAA,sBACT,YAAY;AAAA,sBACZ,OAAO;AAAA,wBACL;AAAA,wBACA,cAAc;AAAA,sBAChB;AAAA,sBACA,QAAQ,MAAM,UAAU,IAAI;AAAA,oBAC9B;AAAA,oBACA,eAAe;AAAA,sBACb;AAAA,wBACE,SAAS;AAAA,wBACT,OAAO;AAAA,sBACT;AAAA,sBACA;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AACD,cAAM,UAAU,OAAO,MAAM,UAAU;AACvC,cAAM,UAAU,SAAS,MAAM,UAAU;AACzC,cAAM,UAAU,MAAM,MAAM,UAAU;AACtC,cAAM,UAAU,MAAM,MAAM,UAAU,OAAO,UAAU,CAAC,CAAC;AACzD,cAAM,UAAU,OAAO,MAAM,UAAU;AACvC,cAAM,UAAU,OAAO,MAAM,UAAU;AACvC,cAAM,UAAU,MAAM,MAAM,UAAU;AACtC,SAAC,SAAUA,QAAO;AAChB,cAAI,UAAU;AACd,cAAIC,YAAW;AAAA,YACb;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,cAAI,SAAS;AACb,cAAI,WAAW;AACf,cAAI,cAAc;AAClB,UAAAD,OAAM,UAAU,MAAM;AAAA,YACpB,aAAa;AAAA,cACX,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,WAAW;AAAA,cACT,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ,EAAE,eAAe,KAAK;AAAA,YAChC;AAAA,YACA,yBAAyB;AAAA,cACvB,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,uBAAuB;AAAA,cACrB,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,WAAW;AAAA,cACT;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,YACF;AAAA,YACA,iBAAiB;AAAA,cACf,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,cAAc;AAAA,cACZ;AAAA,gBACE,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,YAAY;AAAA,gBACZ,QAAQ,EAAE,eAAe,KAAK;AAAA,cAChC;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,QAAQ,EAAE,eAAe,KAAK;AAAA,cAChC;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,YAAY;AAAA,gBACZ,QAAQ,EAAE,eAAe,KAAK;AAAA,cAChC;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,kBACL;AAAA,kBACA;AAAA,gBACF;AAAA,gBACA,QAAQ;AAAA,gBACR,QAAQ,EAAE,eAAe,KAAK;AAAA,cAChC;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,kBACL;AAAA,kBACA;AAAA,gBACF;AAAA,gBACA,QAAQ;AAAA,gBACR,QAAQ,EAAE,eAAe,KAAK;AAAA,cAChC;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,kBACL;AAAA,kBACA;AAAA,gBACF;AAAA,gBACA,QAAQ;AAAA,gBACR,YAAY;AAAA,gBACZ,QAAQ,EAAE,eAAe,KAAK;AAAA,cAChC;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,kBACL;AAAA,kBACA;AAAA,gBACF;AAAA,gBACA,QAAQ;AAAA,gBACR,YAAY;AAAA,gBACZ,QAAQ,EAAE,eAAe,KAAK;AAAA,cAChC;AAAA,YACF;AAAA,YACA,YAAYC;AAAA,YACZ,YAAY;AAAA,cACV,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ,EAAE,eAAe,KAAK;AAAA,YAChC;AAAA,YACA,YAAY;AAAA,cACV,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,eAAe;AAAA,UACjB;AACA,cAAI,uBAAuB;AAAA,YACzB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQD,OAAM,UAAU;AAAA,UAC1B;AACA,cAAI,SAAS;AAAA,YACX;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,aAAa;AAAA,kBACX,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ,EAAE,eAAe,eAAe;AAAA,gBAC1C;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,aAAa;AAAA,kBACX,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ,EAAE,eAAe,eAAe;AAAA,gBAC1C;AAAA,gBACA,iBAAiB;AAAA,cACnB;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,QAAQ,EAAE,iBAAiB,qBAAqB;AAAA,YAClD;AAAA,UACF;AACA,UAAAA,OAAM,UAAU,aAAa,OAAO,YAAY;AAAA,YAC9C,UAAU;AAAA,YACV,aAAa;AAAA,cACX,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,qBAAqB;AAAA,kBACnB,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQ;AAAA,oBACN,WAAW;AAAA,oBACX,UAAU;AAAA,oBACV,wBAAwB;AAAA,sBACtB;AAAA,wBACE,SAAS;AAAA,wBACT,OAAO;AAAA,wBACP,QAAQ;AAAA,wBACR,YAAY;AAAA,sBACd;AAAA,sBACA;AAAA,wBACE,SAAS;AAAA,wBACT,OAAO;AAAA,0BACL;AAAA,0BACA;AAAA,wBACF;AAAA,wBACA,QAAQ;AAAA,wBACR,YAAY;AAAA,wBACZ,QAAQ,EAAE,eAAe,KAAK;AAAA,sBAChC;AAAA,oBACF;AAAA,oBACA,YAAYC;AAAA,oBACZ,UAAU;AAAA,oBACV,YAAY;AAAA,oBACZ,eAAe;AAAA,kBACjB;AAAA,gBACF;AAAA,gBACA,aAAa;AAAA,kBACX,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AACD,UAAAD,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,gBAAI,CAAC,MAAM,KAAK,IAAI,IAAI,GAAG;AACzB;AAAA,YACF;AACA,gBAAI,aAAa;AACjB,YAAAA,OAAM,UAAU,mBAAmB,EAAE,kBAAkB,KAAK,OAAO,UAAU;AAAA,UAC/E,CAAC;AACD,UAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,YAAAA,OAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,KAAK;AAAA,UACtE,CAAC;AAAA,QACH,GAAE,KAAK;AACP,cAAM,UAAU,SAAS;AAAA,UACvB,WAAW;AAAA,YACT,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,UACA,wBAAwB;AAAA,YACtB,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,iBAAiB;AAAA,gBACf,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,eAAe;AAAA,oBACb,SAAS;AAAA,oBACT,YAAY;AAAA,kBACd;AAAA,kBACA,qBAAqB;AAAA,oBACnB,SAAS;AAAA,oBACT,OAAO;AAAA,kBACT;AAAA,kBACA,MAAM;AAAA,gBACR;AAAA,cACF;AAAA,cACA,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,wBAAwB;AAAA,YACtB,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,YAAY;AAAA,YACV,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,aAAa;AAAA,YACX,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,YACA,QAAQ,EAAE,eAAe,KAAK;AAAA,UAChC;AAAA,UACA,WAAW;AAAA,UACX,WAAW;AAAA,UACX,WAAW;AAAA,UACX,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,eAAe;AAAA,QACjB;AACA,cAAM,UAAU,OAAO,sBAAsB,EAAE,OAAO,eAAe,EAAE,OAAO,OAAO,MAAM,UAAU;AACrG,cAAM,UAAU,KAAK,MAAM,UAAU;AACrC,SAAC,SAAUA,QAAO;AAChB,UAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,OAAO,SAAS;AAAA,YACrD,WAAW;AAAA,cACT,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,YACA,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ,EAAE,eAAe,QAAQ;AAAA,YACnC;AAAA,YACA,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,eAAe;AAAA,UACjB,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,QAAQ,YAAY;AAAA,YAC/C,gBAAgB;AAAA,cACd,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,UACF,CAAC;AACD,cAAI,gBAAgB;AAAA,YAClB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQA,OAAM,UAAU;AAAA,cAC1B;AAAA,cACA,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAOA,OAAM,UAAU,KAAK;AAC5B,cAAI,oBAAoB,QAAQ;AAAA,YAC9B,oDAAoD;AAAA,YACpD,sDAAsD;AAAA,YACtD,sDAAsD;AAAA,YACtD,0DAA0D;AAAA,YAC1D,kDAAkD;AAAA,UACpD,EAAE,KAAK,GAAG,IAAI;AACd,cAAI,aAAa,sEAAsE;AACvF,UAAAA,OAAM,UAAU,aAAa,QAAQ,WAAW;AAAA,YAC9C,iBAAiB;AAAA,cACf;AAAA,gBACE,SAAS,OAAO,KAAK,SAAS,oBAAoB,mBAAmB,MAAM;AAAA,gBAC3E,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,iBAAiB;AAAA,kBACjB,SAAS;AAAA,gBACX;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,iBAAiB;AAAA,kBACjB,SAAS;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,UAAU;AAAA,cACR;AAAA,gBACE,SAAS,OAAO,YAAY,SAAS,UAAU;AAAA,gBAC/C,YAAY;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,cACA;AAAA,gBACE,SAAS,OAAO,oBAAoB,SAAS,aAAa,aAAa,MAAM;AAAA,gBAC7E,YAAY;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,YACA,qBAAqB;AAAA,cACnB,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,YAAY;AAAA,gBACZ,WAAW;AAAA,gBACX,cAAc;AAAA,gBACd,eAAe;AAAA,cACjB;AAAA,YACF;AAAA,UACF,CAAC;AACD,UAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,YAC7C,kBAAkB;AAAA,cAChB;AAAA,gBACE,SAAS,OAAO,cAAc,SAAS,iBAAiB;AAAA,gBACxD,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,iBAAiB;AAAA,kBACjB,UAAU;AAAA,gBACZ;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,iBAAiB;AAAA,kBACjB,UAAU;AAAA,gBACZ;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,aAAa;AAAA,oBACX,SAAS;AAAA,oBACT,QAAQ;AAAA,sBACN,UAAU;AAAA,sBACV,eAAe;AAAA,oBACjB;AAAA,kBACF;AAAA,kBACA,iBAAiB;AAAA,kBACjB,UAAU;AAAA,gBACZ;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,aAAa;AAAA,oBACX,SAAS;AAAA,oBACT,QAAQ;AAAA,sBACN,UAAU;AAAA,sBACV,eAAe;AAAA,oBACjB;AAAA,kBACF;AAAA,kBACA,UAAU;AAAA,gBACZ;AAAA,cACF;AAAA,YACF;AAAA,YACA,mBAAmB;AAAA,cACjB;AAAA,gBACE,SAAS,OAAO,KAAK,SAAS,iBAAiB;AAAA,gBAC/C,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,iBAAiB;AAAA,kBACjB,WAAW;AAAA,oBACT,SAAS;AAAA,oBACT,OAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,iBAAiB;AAAA,kBACjB,WAAW;AAAA,oBACT,SAAS;AAAA,oBACT,OAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AACD,iBAAOA,OAAM,UAAU,KAAK;AAC5B,UAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,YAC7C,WAAW;AAAA,YACX,YAAY;AAAA,UACd,CAAC;AACD,UAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AAAA,QACvC,GAAE,KAAK;AACP,eAAO,QAAQ;AACf,eAAO;AAAA,MACT,EAAE,QAAW,MAAS;AAEtB,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,aAAa,YAAU;AAC3B,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,wBAAwB,EAAE,WAAW,WAAW,CAAC;AAChE,uBAAe,6BAA6B;AAAA,UAC1C,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,iBAAiB,OAAO,sBAAsB;AACpD,YAAM,mBAAmB,OAAO,2BAA2B;AAE3D,YAAM,MAAM,YAAU,OAAO,SAAS,iBAAiB,MAAM,IAAI,OAAO,QAAQ;AAEhF,YAAM,eAAe,SAAO;AAC1B,eAAO,cAAc,GAAG,KAAK,IAAI,aAAa,SAAS,IAAI,UAAU,QAAQ,WAAW,MAAM;AAAA,MAChG;AAEA,YAAM,wBAAwB,YAAU;AACtC,cAAM,OAAO,OAAO,YAAY,OAAO,UAAU,QAAQ,IAAI;AAC7D,eAAO,aAAa,IAAI,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK;AAAA,MAClE;AACA,YAAM,mBAAmB,CAAC,QAAQ,UAAU,SAAS;AACnD,cAAM,MAAM,OAAO;AACnB,eAAO,YAAY,SAAS,MAAM;AAChC,gBAAM,OAAO,sBAAsB,MAAM;AACzC,iBAAO,SAAS,IAAI,OAAO,IAAI;AAC/B,iBAAO,KAAK,KAAK,MAAM;AACrB,mBAAO,cAAc,qCAAqC,WAAW,OAAO,OAAO,QAAQ;AAC3F,kBAAM,SAAS,IAAI,OAAO,QAAQ,EAAE,CAAC;AACrC,gBAAI,UAAU,QAAQ,MAAM,IAAI;AAChC,mBAAO,UAAU,OAAO,MAAM;AAAA,UAChC,GAAG,OAAK;AACN,gBAAI,UAAU,GAAG,SAAS,cAAc,QAAQ;AAChD,cAAE,YAAY;AACd,gBAAI,MAAM,EAAE,iBAAiB,CAAC;AAC9B,mBAAO,UAAU,OAAO,CAAC;AAAA,UAC3B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,iBAAiB,YAAU;AAC/B,cAAM,OAAO,sBAAsB,MAAM;AACzC,eAAO,KAAK,KAAK,OAAK,SAAS,KAAK,EAAE,WAAW,CAAC,EAAE,MAAM,EAAE;AAAA,MAC9D;AAEA,YAAM,eAAe,YAAU;AAC7B,cAAM,mBAAmB;AAAA,UACvB;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,kBAAkB,eAAe,MAAM;AAC7C,eAAO,kBAAkB,kBAAkB;AAAA,MAC7C;AACA,YAAM,qBAAqB,CAAC,QAAQ,aAAa;AAC/C,cAAM,OAAO,sBAAsB,MAAM;AACzC,eAAO,KAAK,KAAK,MAAM,UAAU,OAAK;AACpC,gBAAM,UAAU,EAAE,UAAU,MAAM,gBAAgB;AAClD,iBAAO,UAAU,QAAQ,CAAC,IAAI;AAAA,QAChC,CAAC;AAAA,MACH;AAEA,YAAM,OAAO,YAAU;AACrB,cAAM,YAAY,aAAa,MAAM;AACrC,cAAM,kBAAkB,KAAK,SAAS,EAAE,KAAK,SAAS,EAAE,GAAG,OAAK,EAAE,KAAK;AACvE,cAAM,kBAAkB,mBAAmB,QAAQ,eAAe;AAClE,cAAM,cAAc,eAAe,MAAM;AACzC,eAAO,cAAc,KAAK;AAAA,UACxB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,cACL;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,OAAO;AAAA,gBACP,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA,aAAa;AAAA,YACX,UAAU;AAAA,YACV,MAAM;AAAA,UACR;AAAA,UACA,UAAU,SAAO;AACf,kBAAM,OAAO,IAAI,QAAQ;AACzB,6BAAiB,QAAQ,KAAK,UAAU,KAAK,IAAI;AACjD,gBAAI,MAAM;AAAA,UACZ;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,aAAa,YAAU;AAC3B,eAAO,WAAW,cAAc,MAAM;AACpC,gBAAM,OAAO,OAAO,UAAU,QAAQ;AACtC,cAAI,OAAO,UAAU,YAAY,KAAK,aAAa,IAAI,GAAG;AACxD,iBAAK,MAAM;AAAA,UACb,OAAO;AACL,mBAAO,UAAU,OAAO,MAAM;AAAA,UAChC;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,QAAQ,OAAK,OAAK,EAAE,QAAQ,GAAG,EAAE;AACvC,YAAM,OAAO,MAAM,YAAY;AAE/B,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE5D,YAAM,QAAQ,YAAU;AACtB,eAAO,GAAG,cAAc,OAAK;AAC3B,gBAAM,MAAM,OAAO;AACnB,gBAAM,OAAO,IAAI,OAAO,8BAA8B,EAAE,IAAI;AAC5D,iBAAO,KAAK,OAAO,KAAK,MAAM,YAAY,GAAG,SAAO;AAClD,kBAAM,OAAO,IAAI;AACjB,gBAAI,UAAU,KAAK,SAAS,KAAK,IAAI,UAAU,KAAK,OAAO,CAAC,CAAC;AAC7D,gBAAI,UAAU,KAAK,mBAAmB,IAAI;AAC1C,gBAAI,UAAU,KAAK,wBAAwB,IAAI;AAC/C,gBAAI;AACJ,mBAAO,QAAQ,IAAI,YAAY;AAC7B,kBAAI,YAAY,KAAK;AAAA,YACvB;AACA,kBAAM,UAAU,IAAI,IAAI,KAAK,MAAM;AACnC,oBAAQ,cAAc;AAAA,UACxB,CAAC;AAAA,QACH,CAAC;AACD,eAAO,GAAG,cAAc,MAAM;AAC5B,gBAAM,MAAM,OAAO;AACnB,gBAAM,yBAAyB,OAAO,KAAK,IAAI,OAAO,KAAK,GAAG,SAAO;AACnE,mBAAO,aAAa,GAAG,KAAK,IAAI,UAAU,KAAK,sBAAsB,MAAM;AAAA,UAC7E,CAAC;AACD,cAAI,uBAAuB,QAAQ;AACjC,mBAAO,YAAY,SAAS,MAAM;AAChC,qBAAO,KAAK,wBAAwB,SAAO;AACzC,oBAAI;AACJ,uBAAO,KAAK,IAAI,OAAO,MAAM,GAAG,GAAG,CAAAE,SAAO;AACxC,sBAAI,QAAQ,OAAO,OAAO,EAAE,eAAe,IAAI,GAAGA,IAAG;AAAA,gBACvD,CAAC;AACD,oBAAI,YAAY,IAAI,QAAQ,KAAK,IAAI,iBAAiB,QAAQ,OAAO,SAAS,KAAK,EAAE;AACrF,oBAAI,MAAM,EAAE,iBAAiB,GAAG;AAChC,oBAAI,UAAU,KAAK,wBAAwB,IAAI;AAC/C,oBAAI,YAAY,KAAK,IAAI,SAAS;AAAA,cACpC,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AACD,eAAO,GAAG,WAAW,MAAM;AACzB,iBAAO,OAAO,cAAc,OAAO,WAAS;AAC1C,gBAAI;AACJ,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,oBAAM,OAAO,MAAM,CAAC;AACpB,oBAAMC,kBAAiB,KAAK,KAAK,KAAK,OAAO,OAAO,QAAQ,OAAO,SAAS,KAAK,IAAI,QAAQ,WAAW,MAAM;AAC9G,kBAAIA,eAAc;AAChB,qBAAK,KAAK,mBAAmB,OAAO;AACpC,qBAAK,KAAK,wBAAwB,OAAO;AAAA,cAC3C;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,YAAM,kBAAkB,CAAC,QAAQ,YAAY,SAAS,SAAO;AAC3D,cAAM,cAAc,MAAM;AACxB,cAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAC5C,oBAAU,GAAG;AAAA,QACf;AACA,eAAO,GAAG,cAAc,WAAW;AACnC,oBAAY;AACZ,eAAO,MAAM;AACX,iBAAO,IAAI,cAAc,WAAW;AAAA,QACtC;AAAA,MACF;AACA,YAAM,wBAAwB,YAAU;AACtC,cAAM,OAAO,OAAO,UAAU,SAAS;AACvC,eAAO,OAAO,IAAI,GAAG,MAAM,yBAAyB;AAAA,MACtD;AACA,YAAM,WAAW,YAAU;AACzB,cAAM,WAAW,MAAM,OAAO,YAAY,YAAY;AACtD,eAAO,GAAG,SAAS,gBAAgB,cAAc;AAAA,UAC/C,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA,UACA,SAAS,gBAAgB,QAAQ,SAAO;AACtC,gBAAI,UAAU,sBAAsB,MAAM,CAAC;AAAA,UAC7C,CAAC;AAAA,QACH,CAAC;AACD,eAAO,GAAG,SAAS,YAAY,cAAc;AAAA,UAC3C,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,SAAS,gBAAgB,MAAM;AAAA,QACjC,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,cAAc,YAAU;AACnC,qBAAW,MAAM;AACjB,gBAAM,MAAM;AACZ,mBAAS,MAAM;AACf,qBAAW,MAAM;AACjB,iBAAO,GAAG,YAAY,QAAM;AAC1B,gBAAI,aAAa,GAAG,MAAM,GAAG;AAC3B,mBAAK,MAAM;AAAA,YACb;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;ACx5EH;", "names": ["global", "module", "exports", "_self", "lang", "head", "Prism", "constant", "elm", "isCodeSample"]}