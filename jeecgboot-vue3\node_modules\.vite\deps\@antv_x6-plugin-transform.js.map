{"version": 3, "sources": ["../../.pnpm/@antv+x6-plugin-transform@2.1.8_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-transform/src/transform.ts", "../../.pnpm/@antv+x6-plugin-transform@2.1.8_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-transform/src/style/raw.ts", "../../.pnpm/@antv+x6-plugin-transform@2.1.8_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-transform/src/api.ts", "../../.pnpm/@antv+x6-plugin-transform@2.1.8_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-transform/src/index.ts"], "sourcesContent": ["import {\n  Geometry<PERSON><PERSON>,\n  <PERSON><PERSON>,\n  Point,\n  Dom,\n  KeyV<PERSON>ue,\n  NumberExt,\n  Node,\n  Graph,\n  View,\n  NodeView,\n} from '@antv/x6'\n\nexport class TransformImpl extends View<TransformImpl.EventArgs> {\n  private node: Node\n  private graph: Graph\n  private options: TransformImpl.Options\n  protected handle: Element | null\n  protected prevShift: number\n  public container: HTMLElement\n\n  protected get model() {\n    return this.graph.model\n  }\n\n  protected get view() {\n    return this.graph.renderer.findViewByCell(this.node)!\n  }\n\n  protected get containerClassName() {\n    return this.prefixClassName('widget-transform')\n  }\n\n  protected get resizeClassName() {\n    return `${this.containerClassName}-resize`\n  }\n\n  protected get rotateClassName() {\n    return `${this.containerClassName}-rotate`\n  }\n\n  constructor(options: TransformImpl.Options, node: Node, graph: Graph) {\n    super()\n\n    this.node = node\n    this.graph = graph\n\n    this.options = {\n      ...Private.defaultOptions,\n      ...options,\n    }\n\n    this.render()\n    this.startListening()\n  }\n\n  protected startListening() {\n    this.delegateEvents({\n      [`mousedown .${this.resizeClassName}`]: 'startResizing',\n      [`touchstart .${this.resizeClassName}`]: 'startResizing',\n      [`mousedown .${this.rotateClassName}`]: 'startRotating',\n      [`touchstart .${this.rotateClassName}`]: 'startRotating',\n    })\n\n    this.model.on('*', this.update, this)\n    this.graph.on('scale', this.update, this)\n    this.graph.on('translate', this.update, this)\n\n    this.node.on('removed', this.remove, this)\n    this.model.on('reseted', this.remove, this)\n\n    this.view.on('cell:knob:mousedown', this.onKnobMouseDown, this)\n    this.view.on('cell:knob:mouseup', this.onKnobMouseUp, this)\n  }\n\n  protected stopListening() {\n    this.undelegateEvents()\n\n    this.model.off('*', this.update, this)\n    this.graph.off('scale', this.update, this)\n    this.graph.off('translate', this.update, this)\n\n    this.node.off('removed', this.remove, this)\n    this.model.off('reseted', this.remove, this)\n\n    this.view.off('cell:knob:mousedown', this.onKnobMouseDown, this)\n    this.view.off('cell:knob:mouseup', this.onKnobMouseUp, this)\n  }\n\n  protected renderHandles() {\n    this.container = document.createElement('div')\n\n    const knob = document.createElement('div')\n    Dom.attr(knob, 'draggable', 'false')\n    const rotate = knob.cloneNode(true) as Element\n    Dom.addClass(rotate, this.rotateClassName)\n\n    const resizes = Private.POSITIONS.map((pos) => {\n      const elem = knob.cloneNode(true) as Element\n      Dom.addClass(elem, this.resizeClassName)\n      Dom.attr(elem, 'data-position', pos)\n      return elem\n    })\n    this.empty()\n    Dom.append(this.container, [...resizes, rotate])\n  }\n\n  render() {\n    this.renderHandles()\n\n    if (this.view) {\n      this.view.addClass(Private.NODE_CLS)\n    }\n\n    Dom.addClass(this.container, this.containerClassName)\n    Dom.toggleClass(\n      this.container,\n      'no-orth-resize',\n      this.options.preserveAspectRatio || !this.options.orthogonalResizing,\n    )\n    Dom.toggleClass(this.container, 'no-resize', !this.options.resizable)\n    Dom.toggleClass(this.container, 'no-rotate', !this.options.rotatable)\n\n    if (this.options.className) {\n      Dom.addClass(this.container, this.options.className)\n    }\n\n    this.graph.container.appendChild(this.container)\n\n    return this.update()\n  }\n\n  update() {\n    const ctm = this.graph.matrix()\n    const bbox = this.node.getBBox()\n\n    bbox.x *= ctm.a\n    bbox.x += ctm.e\n    bbox.y *= ctm.d\n    bbox.y += ctm.f\n    bbox.width *= ctm.a\n    bbox.height *= ctm.d\n\n    const angle = Angle.normalize(this.node.getAngle())\n    const transform = angle !== 0 ? `rotate(${angle}deg)` : ''\n    Dom.css(this.container, {\n      transform,\n      width: bbox.width,\n      height: bbox.height,\n      left: bbox.x,\n      top: bbox.y,\n    })\n\n    this.updateResizerDirections()\n\n    return this\n  }\n\n  remove() {\n    if (this.view) {\n      this.view.removeClass(Private.NODE_CLS)\n    }\n    return super.remove()\n  }\n\n  protected onKnobMouseDown() {\n    this.startHandle()\n  }\n\n  protected onKnobMouseUp() {\n    this.stopHandle()\n  }\n\n  protected updateResizerDirections() {\n    // Update the directions on the resizer divs while the node being rotated.\n    // The directions are represented by cardinal points (N,S,E,W). For example\n    // the div originally pointed to north needs to be changed to point to south\n    // if the node was rotated by 180 degrees.\n    const angle = Angle.normalize(this.node.getAngle())\n    const shift = Math.floor(angle * (Private.DIRECTIONS.length / 360))\n    if (shift !== this.prevShift) {\n      // Create the current directions array based on the calculated shift.\n      const directions = Private.DIRECTIONS.slice(shift).concat(\n        Private.DIRECTIONS.slice(0, shift),\n      )\n\n      const className = (dir: string) =>\n        `${this.containerClassName}-cursor-${dir}`\n\n      const resizes = this.container.querySelectorAll(\n        `.${this.resizeClassName}`,\n      )\n      resizes.forEach((resize, index) => {\n        Dom.removeClass(\n          resize,\n          Private.DIRECTIONS.map((dir) => className(dir)).join(' '),\n        )\n        Dom.addClass(resize, className(directions[index]))\n      })\n\n      this.prevShift = shift\n    }\n  }\n\n  protected getTrueDirection(dir: Node.ResizeDirection) {\n    const angle = Angle.normalize(this.node.getAngle())\n    let index = Private.POSITIONS.indexOf(dir)\n\n    index += Math.floor(angle * (Private.POSITIONS.length / 360))\n    index %= Private.POSITIONS.length\n\n    return Private.POSITIONS[index]\n  }\n\n  protected toValidResizeDirection(dir: string): Node.ResizeDirection {\n    return (\n      (\n        {\n          top: 'top-left',\n          bottom: 'bottom-right',\n          left: 'bottom-left',\n          right: 'top-right',\n        } as KeyValue\n      )[dir] || dir\n    )\n  }\n\n  protected startResizing(evt: Dom.MouseDownEvent) {\n    evt.stopPropagation()\n    this.model.startBatch('resize', { cid: this.cid })\n    const dir = Dom.attr(evt.target, 'data-position') as Node.ResizeDirection\n    this.prepareResizing(evt, dir)\n    this.startAction(evt)\n  }\n\n  protected prepareResizing(\n    evt: Dom.EventObject,\n    relativeDirection: Node.ResizeDirection,\n  ) {\n    const trueDirection = this.getTrueDirection(relativeDirection)\n    let rx = 0\n    let ry = 0\n    relativeDirection.split('-').forEach((direction) => {\n      rx = ({ left: -1, right: 1 } as KeyValue)[direction] || rx\n      ry = ({ top: -1, bottom: 1 } as KeyValue)[direction] || ry\n    })\n\n    const direction = this.toValidResizeDirection(relativeDirection)\n    const selector = (\n      {\n        'top-right': 'bottomLeft',\n        'top-left': 'bottomRight',\n        'bottom-left': 'topRight',\n        'bottom-right': 'topLeft',\n      } as KeyValue\n    )[direction]\n    const angle = Angle.normalize(this.node.getAngle())\n\n    this.setEventData<EventData.Resizing>(evt, {\n      selector,\n      direction,\n      trueDirection,\n      relativeDirection,\n      angle,\n      resizeX: rx,\n      resizeY: ry,\n      action: 'resizing',\n    })\n  }\n\n  protected startRotating(evt: Dom.MouseDownEvent) {\n    evt.stopPropagation()\n\n    this.model.startBatch('rotate', { cid: this.cid })\n\n    const center = this.node.getBBox().getCenter()\n    const e = this.normalizeEvent(evt)\n    const client = this.graph.snapToGrid(e.clientX, e.clientY)\n    this.setEventData<EventData.Rotating>(evt, {\n      center,\n      action: 'rotating',\n      angle: Angle.normalize(this.node.getAngle()),\n      start: Point.create(client).theta(center),\n    })\n    this.startAction(evt)\n  }\n\n  protected onMouseMove(evt: Dom.MouseMoveEvent) {\n    const view = this.graph.findViewByCell(this.node) as NodeView\n    let data = this.getEventData<EventData.Resizing | EventData.Rotating>(evt)\n    if (data.action) {\n      const e = this.normalizeEvent(evt)\n      let clientX = e.clientX\n      let clientY = e.clientY\n\n      const scroller = this.graph.getPlugin<any>('scroller')\n      const restrict = this.options.restrictedResizing\n\n      if (restrict === true || typeof restrict === 'number') {\n        const factor = restrict === true ? 0 : restrict\n        const fix = scroller ? Math.max(factor, 8) : factor\n        const rect = this.graph.container.getBoundingClientRect()\n        clientX = NumberExt.clamp(clientX, rect.left + fix, rect.right - fix)\n        clientY = NumberExt.clamp(clientY, rect.top + fix, rect.bottom - fix)\n      } else if (this.options.autoScrollOnResizing && scroller) {\n        scroller.autoScroll(clientX, clientY)\n      }\n\n      const pos = this.graph.snapToGrid(clientX, clientY)\n      const gridSize = this.graph.getGridSize()\n      const node = this.node\n      const options = this.options\n\n      if (data.action === 'resizing') {\n        data = data as EventData.Resizing\n        if (!data.resized) {\n          if (view) {\n            view.addClass('node-resizing')\n            this.notify('node:resize', evt, view)\n          }\n          data.resized = true\n        }\n\n        const currentBBox = node.getBBox()\n        const requestedSize = Point.create(pos)\n          .rotate(data.angle, currentBBox.getCenter())\n          .diff(currentBBox[data.selector])\n\n        let width = data.resizeX\n          ? requestedSize.x * data.resizeX\n          : currentBBox.width\n\n        let height = data.resizeY\n          ? requestedSize.y * data.resizeY\n          : currentBBox.height\n\n        const rawWidth = width\n        const rawHeight = height\n\n        width = GeometryUtil.snapToGrid(width, gridSize)\n        height = GeometryUtil.snapToGrid(height, gridSize)\n        width = Math.max(width, options.minWidth || gridSize)\n        height = Math.max(height, options.minHeight || gridSize)\n        width = Math.min(width, options.maxWidth || Infinity)\n        height = Math.min(height, options.maxHeight || Infinity)\n\n        if (options.preserveAspectRatio) {\n          const candidateWidth =\n            (currentBBox.width * height) / currentBBox.height\n          const candidateHeight =\n            (currentBBox.height * width) / currentBBox.width\n\n          if (width < candidateWidth) {\n            height = candidateHeight\n          } else {\n            width = candidateWidth\n          }\n        }\n\n        const relativeDirection = data.relativeDirection\n        if (\n          options.allowReverse &&\n          (rawWidth <= -width || rawHeight <= -height)\n        ) {\n          let reverted: Node.ResizeDirection\n\n          if (relativeDirection === 'left') {\n            if (rawWidth <= -width) {\n              reverted = 'right'\n            }\n          } else if (relativeDirection === 'right') {\n            if (rawWidth <= -width) {\n              reverted = 'left'\n            }\n          } else if (relativeDirection === 'top') {\n            if (rawHeight <= -height) {\n              reverted = 'bottom'\n            }\n          } else if (relativeDirection === 'bottom') {\n            if (rawHeight <= -height) {\n              reverted = 'top'\n            }\n          } else if (relativeDirection === 'top-left') {\n            if (rawWidth <= -width && rawHeight <= -height) {\n              reverted = 'bottom-right'\n            } else if (rawWidth <= -width) {\n              reverted = 'top-right'\n            } else if (rawHeight <= -height) {\n              reverted = 'bottom-left'\n            }\n          } else if (relativeDirection === 'top-right') {\n            if (rawWidth <= -width && rawHeight <= -height) {\n              reverted = 'bottom-left'\n            } else if (rawWidth <= -width) {\n              reverted = 'top-left'\n            } else if (rawHeight <= -height) {\n              reverted = 'bottom-right'\n            }\n          } else if (relativeDirection === 'bottom-left') {\n            if (rawWidth <= -width && rawHeight <= -height) {\n              reverted = 'top-right'\n            } else if (rawWidth <= -width) {\n              reverted = 'bottom-right'\n            } else if (rawHeight <= -height) {\n              reverted = 'top-left'\n            }\n          } else if (relativeDirection === 'bottom-right') {\n            if (rawWidth <= -width && rawHeight <= -height) {\n              reverted = 'top-left'\n            } else if (rawWidth <= -width) {\n              reverted = 'bottom-left'\n            } else if (rawHeight <= -height) {\n              reverted = 'top-right'\n            }\n          }\n\n          const revertedDir = reverted!\n          this.stopHandle()\n          const handle = this.container.querySelector(\n            `.${this.resizeClassName}[data-position=\"${revertedDir}\"]`,\n          )\n          this.startHandle(handle)\n          this.prepareResizing(evt, revertedDir)\n          this.onMouseMove(evt)\n        }\n\n        if (currentBBox.width !== width || currentBBox.height !== height) {\n          const resizeOptions: Node.ResizeOptions = {\n            ui: true,\n            direction: data.direction,\n            relativeDirection: data.relativeDirection,\n            trueDirection: data.trueDirection,\n            minWidth: options.minWidth!,\n            minHeight: options.minHeight!,\n            maxWidth: options.maxWidth!,\n            maxHeight: options.maxHeight!,\n            preserveAspectRatio: options.preserveAspectRatio === true,\n          }\n          node.resize(width, height, resizeOptions)\n          this.notify('node:resizing', evt, view)\n        }\n      } else if (data.action === 'rotating') {\n        data = data as EventData.Rotating\n        if (!data.rotated) {\n          if (view) {\n            view.addClass('node-rotating')\n            this.notify('node:rotate', evt, view)\n          }\n          data.rotated = true\n        }\n\n        const currentAngle = node.getAngle()\n        const theta = data.start - Point.create(pos).theta(data.center)\n        let target = data.angle + theta\n        if (options.rotateGrid) {\n          target = GeometryUtil.snapToGrid(target, options.rotateGrid)\n        }\n        target = Angle.normalize(target)\n\n        if (currentAngle !== target) {\n          node.rotate(target, { absolute: true })\n          this.notify('node:rotating', evt, view)\n        }\n      }\n    }\n  }\n\n  protected onMouseUp(evt: Dom.MouseUpEvent) {\n    const data = this.getEventData<EventData.Resizing | EventData.Rotating>(evt)\n    if (data.action) {\n      this.stopAction(evt)\n      this.model.stopBatch(data.action === 'resizing' ? 'resize' : 'rotate', {\n        cid: this.cid,\n      })\n    }\n  }\n\n  protected startHandle(handle?: Element | null) {\n    this.handle = handle || null\n    Dom.addClass(this.container, `${this.containerClassName}-active`)\n    if (handle) {\n      Dom.addClass(handle, `${this.containerClassName}-active-handle`)\n\n      const pos = handle.getAttribute('data-position') as Node.ResizeDirection\n      if (pos) {\n        const dir = Private.DIRECTIONS[Private.POSITIONS.indexOf(pos)]\n        Dom.addClass(this.container, `${this.containerClassName}-cursor-${dir}`)\n      }\n    }\n  }\n\n  protected stopHandle() {\n    Dom.removeClass(this.container, `${this.containerClassName}-active`)\n\n    if (this.handle) {\n      Dom.removeClass(this.handle, `${this.containerClassName}-active-handle`)\n\n      const pos = this.handle.getAttribute(\n        'data-position',\n      ) as Node.ResizeDirection\n      if (pos) {\n        const dir = Private.DIRECTIONS[Private.POSITIONS.indexOf(pos)]\n        Dom.removeClass(\n          this.container,\n          `${this.containerClassName}-cursor-${dir}`,\n        )\n      }\n\n      this.handle = null\n    }\n  }\n\n  protected startAction(evt: Dom.MouseDownEvent) {\n    this.startHandle(evt.target)\n    this.graph.view.undelegateEvents()\n    this.delegateDocumentEvents(Private.documentEvents, evt.data)\n  }\n\n  protected stopAction(evt: Dom.MouseUpEvent) {\n    this.stopHandle()\n    this.undelegateDocumentEvents()\n    this.graph.view.delegateEvents()\n\n    const view = this.graph.findViewByCell(this.node) as NodeView\n    const data = this.getEventData<EventData.Resizing | EventData.Rotating>(evt)\n\n    if (view) {\n      view.removeClass(`node-${data.action}`)\n      if (data.action === 'resizing' && data.resized) {\n        this.notify('node:resized', evt, view)\n      } else if (data.action === 'rotating' && data.rotated) {\n        this.notify('node:rotated', evt, view)\n      }\n    }\n  }\n\n  protected notify<\n    K extends keyof TransformImpl.EventArgs,\n    T extends Dom.EventObject,\n  >(name: K, evt: T, view: NodeView, args: KeyValue = {}) {\n    if (view) {\n      const graph = view.graph\n      const e = graph.view.normalizeEvent(evt) as any\n      const localPoint = graph.snapToGrid(e.clientX, e.clientY)\n\n      this.trigger(name, {\n        e,\n        view,\n        node: view.cell,\n        cell: view.cell,\n        x: localPoint.x,\n        y: localPoint.y,\n        ...args,\n      })\n    }\n  }\n\n  @View.dispose()\n  dispose() {\n    this.stopListening()\n    this.remove()\n    this.off()\n  }\n}\n\nexport namespace TransformImpl {\n  interface ResizeEventArgs<E> extends NodeView.PositionEventArgs<E> {}\n  interface RotateEventArgs<E> extends NodeView.PositionEventArgs<E> {}\n  export interface EventArgs {\n    'node:resize': ResizeEventArgs<Dom.MouseDownEvent>\n    'node:resizing': ResizeEventArgs<Dom.MouseMoveEvent>\n    'node:resized': ResizeEventArgs<Dom.MouseUpEvent>\n    'node:rotate': RotateEventArgs<Dom.MouseDownEvent>\n    'node:rotating': RotateEventArgs<Dom.MouseMoveEvent>\n    'node:rotated': RotateEventArgs<Dom.MouseUpEvent>\n  }\n\n  export type Direction = 'nw' | 'n' | 'ne' | 'e' | 'se' | 's' | 'sw' | 'w'\n\n  export interface Options {\n    className?: string\n\n    minWidth?: number\n    maxWidth?: number\n    minHeight?: number\n    maxHeight?: number\n    resizable?: boolean\n\n    rotatable?: boolean\n    rotateGrid?: number\n    orthogonalResizing?: boolean\n    restrictedResizing?: boolean | number\n    autoScrollOnResizing?: boolean\n\n    /**\n     * Set to `true` if you want the resizing to preserve the\n     * aspect ratio of the node. Default is `false`.\n     */\n    preserveAspectRatio?: boolean\n    /**\n     * Reaching the minimum width or height is whether to allow control points to reverse\n     */\n    allowReverse?: boolean\n  }\n}\n\nnamespace Private {\n  export const NODE_CLS = 'has-widget-transform'\n  export const DIRECTIONS = ['nw', 'n', 'ne', 'e', 'se', 's', 'sw', 'w']\n  export const POSITIONS: Node.ResizeDirection[] = [\n    'top-left',\n    'top',\n    'top-right',\n    'right',\n    'bottom-right',\n    'bottom',\n    'bottom-left',\n    'left',\n  ]\n\n  export const documentEvents = {\n    mousemove: 'onMouseMove',\n    touchmove: 'onMouseMove',\n    mouseup: 'onMouseUp',\n    touchend: 'onMouseUp',\n  }\n\n  export const defaultOptions: TransformImpl.Options = {\n    minWidth: 0,\n    minHeight: 0,\n    maxWidth: Infinity,\n    maxHeight: Infinity,\n    rotateGrid: 15,\n    rotatable: true,\n    preserveAspectRatio: false,\n    orthogonalResizing: true,\n    restrictedResizing: false,\n    autoScrollOnResizing: true,\n    allowReverse: true,\n  }\n}\n\nnamespace EventData {\n  export interface Resizing {\n    action: 'resizing'\n    selector: 'bottomLeft' | 'bottomRight' | 'topRight' | 'topLeft'\n    direction: Node.ResizeDirection\n    trueDirection: Node.ResizeDirection\n    relativeDirection: Node.ResizeDirection\n    resizeX: number\n    resizeY: number\n    angle: number\n    resized?: boolean\n  }\n\n  export interface Rotating {\n    action: 'rotating'\n    center: Point.PointLike\n    angle: number\n    start: number\n    rotated?: boolean\n  }\n}\n", "/* eslint-disable */\n\n/**\n * Auto generated file, do not modify it!\n */\n\nexport const content = `.x6-widget-transform {\n  position: absolute;\n  box-sizing: content-box !important;\n  margin: -5px 0 0 -5px;\n  padding: 4px;\n  border: 1px dashed #000;\n  border-radius: 5px;\n  user-select: none;\n  pointer-events: none;\n}\n.x6-widget-transform > div {\n  position: absolute;\n  box-sizing: border-box;\n  background-color: #fff;\n  border: 1px solid #000;\n  transition: background-color 0.2s;\n  pointer-events: auto;\n  -webkit-user-drag: none;\n  user-drag: none;\n  /* stylelint-disable-line */\n}\n.x6-widget-transform > div:hover {\n  background-color: #d3d3d3;\n}\n.x6-widget-transform-cursor-n {\n  cursor: n-resize;\n}\n.x6-widget-transform-cursor-s {\n  cursor: s-resize;\n}\n.x6-widget-transform-cursor-e {\n  cursor: e-resize;\n}\n.x6-widget-transform-cursor-w {\n  cursor: w-resize;\n}\n.x6-widget-transform-cursor-ne {\n  cursor: ne-resize;\n}\n.x6-widget-transform-cursor-nw {\n  cursor: nw-resize;\n}\n.x6-widget-transform-cursor-se {\n  cursor: se-resize;\n}\n.x6-widget-transform-cursor-sw {\n  cursor: sw-resize;\n}\n.x6-widget-transform-resize {\n  width: 10px;\n  height: 10px;\n  border-radius: 6px;\n}\n.x6-widget-transform-resize[data-position='top-left'] {\n  top: -5px;\n  left: -5px;\n}\n.x6-widget-transform-resize[data-position='top-right'] {\n  top: -5px;\n  right: -5px;\n}\n.x6-widget-transform-resize[data-position='bottom-left'] {\n  bottom: -5px;\n  left: -5px;\n}\n.x6-widget-transform-resize[data-position='bottom-right'] {\n  right: -5px;\n  bottom: -5px;\n}\n.x6-widget-transform-resize[data-position='top'] {\n  top: -5px;\n  left: 50%;\n  margin-left: -5px;\n}\n.x6-widget-transform-resize[data-position='bottom'] {\n  bottom: -5px;\n  left: 50%;\n  margin-left: -5px;\n}\n.x6-widget-transform-resize[data-position='left'] {\n  top: 50%;\n  left: -5px;\n  margin-top: -5px;\n}\n.x6-widget-transform-resize[data-position='right'] {\n  top: 50%;\n  right: -5px;\n  margin-top: -5px;\n}\n.x6-widget-transform.prevent-aspect-ratio .x6-widget-transform-resize[data-position='top'],\n.x6-widget-transform.prevent-aspect-ratio .x6-widget-transform-resize[data-position='bottom'],\n.x6-widget-transform.prevent-aspect-ratio .x6-widget-transform-resize[data-position='left'],\n.x6-widget-transform.prevent-aspect-ratio .x6-widget-transform-resize[data-position='right'] {\n  display: none;\n}\n.x6-widget-transform.no-orth-resize .x6-widget-transform-resize[data-position='bottom'],\n.x6-widget-transform.no-orth-resize .x6-widget-transform-resize[data-position='left'],\n.x6-widget-transform.no-orth-resize .x6-widget-transform-resize[data-position='right'],\n.x6-widget-transform.no-orth-resize .x6-widget-transform-resize[data-position='top'] {\n  display: none;\n}\n.x6-widget-transform.no-resize .x6-widget-transform-resize {\n  display: none;\n}\n.x6-widget-transform-rotate {\n  top: -20px;\n  left: -20px;\n  width: 12px;\n  height: 12px;\n  border-radius: 6px;\n  cursor: crosshair;\n}\n.x6-widget-transform.no-rotate .x6-widget-transform-rotate {\n  display: none;\n}\n.x6-widget-transform-active {\n  border-color: transparent;\n  pointer-events: all;\n}\n.x6-widget-transform-active > div {\n  display: none;\n}\n.x6-widget-transform-active > .x6-widget-transform-active-handle {\n  display: block;\n  background-color: #808080;\n}\n`\n", "import { Graph, Node } from '@antv/x6'\nimport { TransformImpl } from './transform'\nimport { Transform } from './index'\n\ndeclare module '@antv/x6/lib/graph/graph' {\n  interface Graph {\n    createTransformWidget: (node: Node) => Graph\n    clearTransformWidgets: () => Graph\n  }\n}\n\ndeclare module '@antv/x6/lib/graph/events' {\n  interface EventArgs extends TransformImpl.EventArgs {}\n}\n\nGraph.prototype.createTransformWidget = function (node) {\n  const transform = this.getPlugin('transform') as Transform\n  if (transform) {\n    transform.createWidget(node)\n  }\n  return this\n}\n\nGraph.prototype.clearTransformWidgets = function () {\n  const transform = this.getPlugin('transform') as Transform\n  if (transform) {\n    transform.clearWidgets()\n  }\n  return this\n}\n", "import { <PERSON><PERSON>, <PERSON>ss<PERSON><PERSON><PERSON>, Key<PERSON><PERSON>ue, Node, <PERSON>raph, EventArgs } from '@antv/x6'\nimport { TransformImpl } from './transform'\nimport { content } from './style/raw'\nimport './api'\n\nexport class Transform\n  extends Basecoat<Transform.EventArgs>\n  implements Graph.Plugin\n{\n  public name = 'transform'\n  public options: Transform.Options\n  private graph: Graph\n  protected widgets: Map<Node, TransformImpl> = new Map()\n  private disabled = false\n\n  constructor(options: Transform.Options = {}) {\n    super()\n    this.options = options\n    CssLoader.ensure(this.name, content)\n  }\n\n  init(graph: Graph) {\n    this.graph = graph\n    if (this.disabled) {\n      return\n    }\n    this.startListening()\n  }\n\n  protected startListening() {\n    this.graph.on('node:click', this.onNodeClick, this)\n    this.graph.on('blank:mousedown', this.onBlankMouseDown, this)\n  }\n\n  protected stopListening() {\n    this.graph.off('node:click', this.onNodeClick, this)\n    this.graph.off('blank:mousedown', this.onBlankMouseDown, this)\n  }\n\n  enable() {\n    if (this.disabled) {\n      this.disabled = false\n      this.startListening()\n    }\n  }\n\n  disable() {\n    if (!this.disabled) {\n      this.disabled = true\n      this.stopListening()\n    }\n  }\n\n  isEnabled() {\n    return !this.disabled\n  }\n\n  createWidget(node: Node) {\n    this.clearWidgets()\n    const widget = this.createTransform(node)\n    if (widget) {\n      this.widgets.set(node, widget)\n      widget.on('*', (name, args) => {\n        this.trigger(name, args)\n        this.graph.trigger(name, args)\n      })\n    }\n  }\n\n  protected onNodeClick({ node }: EventArgs['node:click']) {\n    this.createWidget(node)\n  }\n\n  protected onBlankMouseDown() {\n    this.clearWidgets()\n  }\n\n  protected createTransform(node: Node) {\n    const options = this.getTransformOptions(node)\n    if (options.resizable || options.rotatable) {\n      return new TransformImpl(options, node, this.graph)\n    }\n\n    return null\n  }\n\n  protected getTransformOptions(node: Node) {\n    if (!this.options.resizing) {\n      this.options.resizing = {\n        enabled: false,\n      }\n    }\n\n    if (!this.options.rotating) {\n      this.options.rotating = {\n        enabled: false,\n      }\n    }\n\n    if (typeof this.options.resizing === 'boolean') {\n      this.options.resizing = {\n        enabled: this.options.resizing,\n      }\n    }\n    if (typeof this.options.rotating === 'boolean') {\n      this.options.rotating = {\n        enabled: this.options.rotating,\n      }\n    }\n\n    const resizing = Transform.parseOptionGroup<Transform.ResizingRaw>(\n      this.graph,\n      node,\n      this.options.resizing,\n    )\n\n    const rotating = Transform.parseOptionGroup<Transform.RotatingRaw>(\n      this.graph,\n      node,\n      this.options.rotating,\n    )\n\n    const options: TransformImpl.Options = {\n      resizable: !!resizing.enabled,\n      minWidth: resizing.minWidth || 0,\n      maxWidth: resizing.maxWidth || Number.MAX_SAFE_INTEGER,\n      minHeight: resizing.minHeight || 0,\n      maxHeight: resizing.maxHeight || Number.MAX_SAFE_INTEGER,\n      orthogonalResizing:\n        typeof resizing.orthogonal === 'boolean' ? resizing.orthogonal : true,\n      restrictedResizing: !!resizing.restrict,\n      autoScrollOnResizing:\n        typeof resizing.autoScroll === 'boolean' ? resizing.autoScroll : true,\n      preserveAspectRatio: !!resizing.preserveAspectRatio,\n      allowReverse:\n        typeof resizing.allowReverse === 'boolean'\n          ? resizing.allowReverse\n          : true,\n\n      rotatable: !!rotating.enabled,\n      rotateGrid: rotating.grid || 15,\n    }\n\n    return options\n  }\n\n  clearWidgets() {\n    this.widgets.forEach((widget, node) => {\n      if (this.graph.getCellById(node.id)) {\n        widget.dispose()\n      }\n    })\n    this.widgets.clear()\n  }\n\n  @Basecoat.dispose()\n  dispose() {\n    this.clearWidgets()\n    this.stopListening()\n    this.off()\n    CssLoader.clean(this.name)\n  }\n}\n\nexport namespace Transform {\n  export interface EventArgs extends TransformImpl.EventArgs {}\n\n  type OptionItem<T, S> = S | ((this: Graph, arg: T) => S)\n\n  export interface ResizingRaw {\n    enabled?: boolean\n    minWidth?: number\n    maxWidth?: number\n    minHeight?: number\n    maxHeight?: number\n    orthogonal?: boolean\n    restrict?: boolean | number\n    autoScroll?: boolean\n    preserveAspectRatio?: boolean\n    allowReverse?: boolean\n  }\n\n  export interface RotatingRaw {\n    enabled?: boolean\n    grid?: number\n  }\n\n  export type Resizing = {\n    [K in keyof ResizingRaw]?: OptionItem<Node, ResizingRaw[K]>\n  }\n\n  export type Rotating = {\n    [K in keyof RotatingRaw]?: OptionItem<Node, RotatingRaw[K]>\n  }\n\n  export type Options = {\n    rotating?: boolean | Partial<Rotating>\n    resizing?: boolean | Partial<Resizing>\n  }\n\n  export function parseOptionGroup<\n    K extends KeyValue,\n    S extends KeyValue = KeyValue,\n    T = any,\n  >(graph: Graph, arg: T, options: S): K {\n    const result: any = {}\n    Object.keys(options || {}).forEach((key) => {\n      const val = options[key]\n      result[key] = typeof val === 'function' ? val.call(graph, arg) : val\n    })\n    return result\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAaM,IAAO,gBAAP,cAA6B,KAA6B;EAQ9D,IAAc,QAAK;AACjB,WAAO,KAAK,MAAM;EACpB;EAEA,IAAc,OAAI;AAChB,WAAO,KAAK,MAAM,SAAS,eAAe,KAAK,IAAI;EACrD;EAEA,IAAc,qBAAkB;AAC9B,WAAO,KAAK,gBAAgB,kBAAkB;EAChD;EAEA,IAAc,kBAAe;AAC3B,WAAO,GAAG,KAAK,kBAAkB;EACnC;EAEA,IAAc,kBAAe;AAC3B,WAAO,GAAG,KAAK,kBAAkB;EACnC;EAEA,YAAY,SAAgC,MAAY,OAAY;AAClE,UAAK;AAEL,SAAK,OAAO;AACZ,SAAK,QAAQ;AAEb,SAAK,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GACP,QAAQ,cAAc,GACtB,OAAO;AAGZ,SAAK,OAAM;AACX,SAAK,eAAc;EACrB;EAEU,iBAAc;AACtB,SAAK,eAAe;MAClB,CAAC,cAAc,KAAK,eAAe,EAAE,GAAG;MACxC,CAAC,eAAe,KAAK,eAAe,EAAE,GAAG;MACzC,CAAC,cAAc,KAAK,eAAe,EAAE,GAAG;MACxC,CAAC,eAAe,KAAK,eAAe,EAAE,GAAG;KAC1C;AAED,SAAK,MAAM,GAAG,KAAK,KAAK,QAAQ,IAAI;AACpC,SAAK,MAAM,GAAG,SAAS,KAAK,QAAQ,IAAI;AACxC,SAAK,MAAM,GAAG,aAAa,KAAK,QAAQ,IAAI;AAE5C,SAAK,KAAK,GAAG,WAAW,KAAK,QAAQ,IAAI;AACzC,SAAK,MAAM,GAAG,WAAW,KAAK,QAAQ,IAAI;AAE1C,SAAK,KAAK,GAAG,uBAAuB,KAAK,iBAAiB,IAAI;AAC9D,SAAK,KAAK,GAAG,qBAAqB,KAAK,eAAe,IAAI;EAC5D;EAEU,gBAAa;AACrB,SAAK,iBAAgB;AAErB,SAAK,MAAM,IAAI,KAAK,KAAK,QAAQ,IAAI;AACrC,SAAK,MAAM,IAAI,SAAS,KAAK,QAAQ,IAAI;AACzC,SAAK,MAAM,IAAI,aAAa,KAAK,QAAQ,IAAI;AAE7C,SAAK,KAAK,IAAI,WAAW,KAAK,QAAQ,IAAI;AAC1C,SAAK,MAAM,IAAI,WAAW,KAAK,QAAQ,IAAI;AAE3C,SAAK,KAAK,IAAI,uBAAuB,KAAK,iBAAiB,IAAI;AAC/D,SAAK,KAAK,IAAI,qBAAqB,KAAK,eAAe,IAAI;EAC7D;EAEU,gBAAa;AACrB,SAAK,YAAY,SAAS,cAAc,KAAK;AAE7C,UAAM,OAAO,SAAS,cAAc,KAAK;AACzC,iBAAI,KAAK,MAAM,aAAa,OAAO;AACnC,UAAM,SAAS,KAAK,UAAU,IAAI;AAClC,iBAAI,SAAS,QAAQ,KAAK,eAAe;AAEzC,UAAM,UAAU,QAAQ,UAAU,IAAI,CAAC,QAAO;AAC5C,YAAM,OAAO,KAAK,UAAU,IAAI;AAChC,mBAAI,SAAS,MAAM,KAAK,eAAe;AACvC,mBAAI,KAAK,MAAM,iBAAiB,GAAG;AACnC,aAAO;IACT,CAAC;AACD,SAAK,MAAK;AACV,iBAAI,OAAO,KAAK,WAAW,CAAC,GAAG,SAAS,MAAM,CAAC;EACjD;EAEA,SAAM;AACJ,SAAK,cAAa;AAElB,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,SAAS,QAAQ,QAAQ;;AAGrC,iBAAI,SAAS,KAAK,WAAW,KAAK,kBAAkB;AACpD,iBAAI,YACF,KAAK,WACL,kBACA,KAAK,QAAQ,uBAAuB,CAAC,KAAK,QAAQ,kBAAkB;AAEtE,iBAAI,YAAY,KAAK,WAAW,aAAa,CAAC,KAAK,QAAQ,SAAS;AACpE,iBAAI,YAAY,KAAK,WAAW,aAAa,CAAC,KAAK,QAAQ,SAAS;AAEpE,QAAI,KAAK,QAAQ,WAAW;AAC1B,mBAAI,SAAS,KAAK,WAAW,KAAK,QAAQ,SAAS;;AAGrD,SAAK,MAAM,UAAU,YAAY,KAAK,SAAS;AAE/C,WAAO,KAAK,OAAM;EACpB;EAEA,SAAM;AACJ,UAAM,MAAM,KAAK,MAAM,OAAM;AAC7B,UAAM,OAAO,KAAK,KAAK,QAAO;AAE9B,SAAK,KAAK,IAAI;AACd,SAAK,KAAK,IAAI;AACd,SAAK,KAAK,IAAI;AACd,SAAK,KAAK,IAAI;AACd,SAAK,SAAS,IAAI;AAClB,SAAK,UAAU,IAAI;AAEnB,UAAM,QAAQ,MAAM,UAAU,KAAK,KAAK,SAAQ,CAAE;AAClD,UAAM,YAAY,UAAU,IAAI,UAAU,KAAK,SAAS;AACxD,iBAAI,IAAI,KAAK,WAAW;MACtB;MACA,OAAO,KAAK;MACZ,QAAQ,KAAK;MACb,MAAM,KAAK;MACX,KAAK,KAAK;KACX;AAED,SAAK,wBAAuB;AAE5B,WAAO;EACT;EAEA,SAAM;AACJ,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,YAAY,QAAQ,QAAQ;;AAExC,WAAO,MAAM,OAAM;EACrB;EAEU,kBAAe;AACvB,SAAK,YAAW;EAClB;EAEU,gBAAa;AACrB,SAAK,WAAU;EACjB;EAEU,0BAAuB;AAK/B,UAAM,QAAQ,MAAM,UAAU,KAAK,KAAK,SAAQ,CAAE;AAClD,UAAM,QAAQ,KAAK,MAAM,SAAS,QAAQ,WAAW,SAAS,IAAI;AAClE,QAAI,UAAU,KAAK,WAAW;AAE5B,YAAM,aAAa,QAAQ,WAAW,MAAM,KAAK,EAAE,OACjD,QAAQ,WAAW,MAAM,GAAG,KAAK,CAAC;AAGpC,YAAM,YAAY,CAAC,QACjB,GAAG,KAAK,kBAAkB,WAAW,GAAG;AAE1C,YAAM,UAAU,KAAK,UAAU,iBAC7B,IAAI,KAAK,eAAe,EAAE;AAE5B,cAAQ,QAAQ,CAAC,QAAQ,UAAS;AAChC,qBAAI,YACF,QACA,QAAQ,WAAW,IAAI,CAAC,QAAQ,UAAU,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC;AAE3D,qBAAI,SAAS,QAAQ,UAAU,WAAW,KAAK,CAAC,CAAC;MACnD,CAAC;AAED,WAAK,YAAY;;EAErB;EAEU,iBAAiB,KAAyB;AAClD,UAAM,QAAQ,MAAM,UAAU,KAAK,KAAK,SAAQ,CAAE;AAClD,QAAI,QAAQ,QAAQ,UAAU,QAAQ,GAAG;AAEzC,aAAS,KAAK,MAAM,SAAS,QAAQ,UAAU,SAAS,IAAI;AAC5D,aAAS,QAAQ,UAAU;AAE3B,WAAO,QAAQ,UAAU,KAAK;EAChC;EAEU,uBAAuB,KAAW;AAC1C,WAEI;MACE,KAAK;MACL,QAAQ;MACR,MAAM;MACN,OAAO;MAET,GAAG,KAAK;EAEd;EAEU,cAAc,KAAuB;AAC7C,QAAI,gBAAe;AACnB,SAAK,MAAM,WAAW,UAAU,EAAE,KAAK,KAAK,IAAG,CAAE;AACjD,UAAM,MAAM,aAAI,KAAK,IAAI,QAAQ,eAAe;AAChD,SAAK,gBAAgB,KAAK,GAAG;AAC7B,SAAK,YAAY,GAAG;EACtB;EAEU,gBACR,KACA,mBAAuC;AAEvC,UAAM,gBAAgB,KAAK,iBAAiB,iBAAiB;AAC7D,QAAI,KAAK;AACT,QAAI,KAAK;AACT,sBAAkB,MAAM,GAAG,EAAE,QAAQ,CAACA,eAAa;AACjD,WAAM,EAAE,MAAM,IAAI,OAAO,EAAC,EAAgBA,UAAS,KAAK;AACxD,WAAM,EAAE,KAAK,IAAI,QAAQ,EAAC,EAAgBA,UAAS,KAAK;IAC1D,CAAC;AAED,UAAM,YAAY,KAAK,uBAAuB,iBAAiB;AAC/D,UAAM,WACJ;MACE,aAAa;MACb,YAAY;MACZ,eAAe;MACf,gBAAgB;MAElB,SAAS;AACX,UAAM,QAAQ,MAAM,UAAU,KAAK,KAAK,SAAQ,CAAE;AAElD,SAAK,aAAiC,KAAK;MACzC;MACA;MACA;MACA;MACA;MACA,SAAS;MACT,SAAS;MACT,QAAQ;KACT;EACH;EAEU,cAAc,KAAuB;AAC7C,QAAI,gBAAe;AAEnB,SAAK,MAAM,WAAW,UAAU,EAAE,KAAK,KAAK,IAAG,CAAE;AAEjD,UAAM,SAAS,KAAK,KAAK,QAAO,EAAG,UAAS;AAC5C,UAAM,IAAI,KAAK,eAAe,GAAG;AACjC,UAAM,SAAS,KAAK,MAAM,WAAW,EAAE,SAAS,EAAE,OAAO;AACzD,SAAK,aAAiC,KAAK;MACzC;MACA,QAAQ;MACR,OAAO,MAAM,UAAU,KAAK,KAAK,SAAQ,CAAE;MAC3C,OAAO,MAAM,OAAO,MAAM,EAAE,MAAM,MAAM;KACzC;AACD,SAAK,YAAY,GAAG;EACtB;EAEU,YAAY,KAAuB;AAC3C,UAAM,OAAO,KAAK,MAAM,eAAe,KAAK,IAAI;AAChD,QAAI,OAAO,KAAK,aAAsD,GAAG;AACzE,QAAI,KAAK,QAAQ;AACf,YAAM,IAAI,KAAK,eAAe,GAAG;AACjC,UAAI,UAAU,EAAE;AAChB,UAAI,UAAU,EAAE;AAEhB,YAAM,WAAW,KAAK,MAAM,UAAe,UAAU;AACrD,YAAM,WAAW,KAAK,QAAQ;AAE9B,UAAI,aAAa,QAAQ,OAAO,aAAa,UAAU;AACrD,cAAM,SAAS,aAAa,OAAO,IAAI;AACvC,cAAM,MAAM,WAAW,KAAK,IAAI,QAAQ,CAAC,IAAI;AAC7C,cAAM,OAAO,KAAK,MAAM,UAAU,sBAAqB;AACvD,kBAAU,eAAU,MAAM,SAAS,KAAK,OAAO,KAAK,KAAK,QAAQ,GAAG;AACpE,kBAAU,eAAU,MAAM,SAAS,KAAK,MAAM,KAAK,KAAK,SAAS,GAAG;iBAC3D,KAAK,QAAQ,wBAAwB,UAAU;AACxD,iBAAS,WAAW,SAAS,OAAO;;AAGtC,YAAM,MAAM,KAAK,MAAM,WAAW,SAAS,OAAO;AAClD,YAAM,WAAW,KAAK,MAAM,YAAW;AACvC,YAAM,OAAO,KAAK;AAClB,YAAM,UAAU,KAAK;AAErB,UAAI,KAAK,WAAW,YAAY;AAC9B,eAAO;AACP,YAAI,CAAC,KAAK,SAAS;AACjB,cAAI,MAAM;AACR,iBAAK,SAAS,eAAe;AAC7B,iBAAK,OAAO,eAAe,KAAK,IAAI;;AAEtC,eAAK,UAAU;;AAGjB,cAAM,cAAc,KAAK,QAAO;AAChC,cAAM,gBAAgB,MAAM,OAAO,GAAG,EACnC,OAAO,KAAK,OAAO,YAAY,UAAS,CAAE,EAC1C,KAAK,YAAY,KAAK,QAAQ,CAAC;AAElC,YAAI,QAAQ,KAAK,UACb,cAAc,IAAI,KAAK,UACvB,YAAY;AAEhB,YAAI,SAAS,KAAK,UACd,cAAc,IAAI,KAAK,UACvB,YAAY;AAEhB,cAAM,WAAW;AACjB,cAAM,YAAY;AAElB,gBAAQ,aAAa,WAAW,OAAO,QAAQ;AAC/C,iBAAS,aAAa,WAAW,QAAQ,QAAQ;AACjD,gBAAQ,KAAK,IAAI,OAAO,QAAQ,YAAY,QAAQ;AACpD,iBAAS,KAAK,IAAI,QAAQ,QAAQ,aAAa,QAAQ;AACvD,gBAAQ,KAAK,IAAI,OAAO,QAAQ,YAAY,QAAQ;AACpD,iBAAS,KAAK,IAAI,QAAQ,QAAQ,aAAa,QAAQ;AAEvD,YAAI,QAAQ,qBAAqB;AAC/B,gBAAM,iBACH,YAAY,QAAQ,SAAU,YAAY;AAC7C,gBAAM,kBACH,YAAY,SAAS,QAAS,YAAY;AAE7C,cAAI,QAAQ,gBAAgB;AAC1B,qBAAS;iBACJ;AACL,oBAAQ;;;AAIZ,cAAM,oBAAoB,KAAK;AAC/B,YACE,QAAQ,iBACP,YAAY,CAAC,SAAS,aAAa,CAAC,SACrC;AACA,cAAI;AAEJ,cAAI,sBAAsB,QAAQ;AAChC,gBAAI,YAAY,CAAC,OAAO;AACtB,yBAAW;;qBAEJ,sBAAsB,SAAS;AACxC,gBAAI,YAAY,CAAC,OAAO;AACtB,yBAAW;;qBAEJ,sBAAsB,OAAO;AACtC,gBAAI,aAAa,CAAC,QAAQ;AACxB,yBAAW;;qBAEJ,sBAAsB,UAAU;AACzC,gBAAI,aAAa,CAAC,QAAQ;AACxB,yBAAW;;qBAEJ,sBAAsB,YAAY;AAC3C,gBAAI,YAAY,CAAC,SAAS,aAAa,CAAC,QAAQ;AAC9C,yBAAW;uBACF,YAAY,CAAC,OAAO;AAC7B,yBAAW;uBACF,aAAa,CAAC,QAAQ;AAC/B,yBAAW;;qBAEJ,sBAAsB,aAAa;AAC5C,gBAAI,YAAY,CAAC,SAAS,aAAa,CAAC,QAAQ;AAC9C,yBAAW;uBACF,YAAY,CAAC,OAAO;AAC7B,yBAAW;uBACF,aAAa,CAAC,QAAQ;AAC/B,yBAAW;;qBAEJ,sBAAsB,eAAe;AAC9C,gBAAI,YAAY,CAAC,SAAS,aAAa,CAAC,QAAQ;AAC9C,yBAAW;uBACF,YAAY,CAAC,OAAO;AAC7B,yBAAW;uBACF,aAAa,CAAC,QAAQ;AAC/B,yBAAW;;qBAEJ,sBAAsB,gBAAgB;AAC/C,gBAAI,YAAY,CAAC,SAAS,aAAa,CAAC,QAAQ;AAC9C,yBAAW;uBACF,YAAY,CAAC,OAAO;AAC7B,yBAAW;uBACF,aAAa,CAAC,QAAQ;AAC/B,yBAAW;;;AAIf,gBAAM,cAAc;AACpB,eAAK,WAAU;AACf,gBAAM,SAAS,KAAK,UAAU,cAC5B,IAAI,KAAK,eAAe,mBAAmB,WAAW,IAAI;AAE5D,eAAK,YAAY,MAAM;AACvB,eAAK,gBAAgB,KAAK,WAAW;AACrC,eAAK,YAAY,GAAG;;AAGtB,YAAI,YAAY,UAAU,SAAS,YAAY,WAAW,QAAQ;AAChE,gBAAM,gBAAoC;YACxC,IAAI;YACJ,WAAW,KAAK;YAChB,mBAAmB,KAAK;YACxB,eAAe,KAAK;YACpB,UAAU,QAAQ;YAClB,WAAW,QAAQ;YACnB,UAAU,QAAQ;YAClB,WAAW,QAAQ;YACnB,qBAAqB,QAAQ,wBAAwB;;AAEvD,eAAK,OAAO,OAAO,QAAQ,aAAa;AACxC,eAAK,OAAO,iBAAiB,KAAK,IAAI;;iBAE/B,KAAK,WAAW,YAAY;AACrC,eAAO;AACP,YAAI,CAAC,KAAK,SAAS;AACjB,cAAI,MAAM;AACR,iBAAK,SAAS,eAAe;AAC7B,iBAAK,OAAO,eAAe,KAAK,IAAI;;AAEtC,eAAK,UAAU;;AAGjB,cAAM,eAAe,KAAK,SAAQ;AAClC,cAAM,QAAQ,KAAK,QAAQ,MAAM,OAAO,GAAG,EAAE,MAAM,KAAK,MAAM;AAC9D,YAAI,SAAS,KAAK,QAAQ;AAC1B,YAAI,QAAQ,YAAY;AACtB,mBAAS,aAAa,WAAW,QAAQ,QAAQ,UAAU;;AAE7D,iBAAS,MAAM,UAAU,MAAM;AAE/B,YAAI,iBAAiB,QAAQ;AAC3B,eAAK,OAAO,QAAQ,EAAE,UAAU,KAAI,CAAE;AACtC,eAAK,OAAO,iBAAiB,KAAK,IAAI;;;;EAI9C;EAEU,UAAU,KAAqB;AACvC,UAAM,OAAO,KAAK,aAAsD,GAAG;AAC3E,QAAI,KAAK,QAAQ;AACf,WAAK,WAAW,GAAG;AACnB,WAAK,MAAM,UAAU,KAAK,WAAW,aAAa,WAAW,UAAU;QACrE,KAAK,KAAK;OACX;;EAEL;EAEU,YAAY,QAAuB;AAC3C,SAAK,SAAS,UAAU;AACxB,iBAAI,SAAS,KAAK,WAAW,GAAG,KAAK,kBAAkB,SAAS;AAChE,QAAI,QAAQ;AACV,mBAAI,SAAS,QAAQ,GAAG,KAAK,kBAAkB,gBAAgB;AAE/D,YAAM,MAAM,OAAO,aAAa,eAAe;AAC/C,UAAI,KAAK;AACP,cAAM,MAAM,QAAQ,WAAW,QAAQ,UAAU,QAAQ,GAAG,CAAC;AAC7D,qBAAI,SAAS,KAAK,WAAW,GAAG,KAAK,kBAAkB,WAAW,GAAG,EAAE;;;EAG7E;EAEU,aAAU;AAClB,iBAAI,YAAY,KAAK,WAAW,GAAG,KAAK,kBAAkB,SAAS;AAEnE,QAAI,KAAK,QAAQ;AACf,mBAAI,YAAY,KAAK,QAAQ,GAAG,KAAK,kBAAkB,gBAAgB;AAEvE,YAAM,MAAM,KAAK,OAAO,aACtB,eAAe;AAEjB,UAAI,KAAK;AACP,cAAM,MAAM,QAAQ,WAAW,QAAQ,UAAU,QAAQ,GAAG,CAAC;AAC7D,qBAAI,YACF,KAAK,WACL,GAAG,KAAK,kBAAkB,WAAW,GAAG,EAAE;;AAI9C,WAAK,SAAS;;EAElB;EAEU,YAAY,KAAuB;AAC3C,SAAK,YAAY,IAAI,MAAM;AAC3B,SAAK,MAAM,KAAK,iBAAgB;AAChC,SAAK,uBAAuB,QAAQ,gBAAgB,IAAI,IAAI;EAC9D;EAEU,WAAW,KAAqB;AACxC,SAAK,WAAU;AACf,SAAK,yBAAwB;AAC7B,SAAK,MAAM,KAAK,eAAc;AAE9B,UAAM,OAAO,KAAK,MAAM,eAAe,KAAK,IAAI;AAChD,UAAM,OAAO,KAAK,aAAsD,GAAG;AAE3E,QAAI,MAAM;AACR,WAAK,YAAY,QAAQ,KAAK,MAAM,EAAE;AACtC,UAAI,KAAK,WAAW,cAAc,KAAK,SAAS;AAC9C,aAAK,OAAO,gBAAgB,KAAK,IAAI;iBAC5B,KAAK,WAAW,cAAc,KAAK,SAAS;AACrD,aAAK,OAAO,gBAAgB,KAAK,IAAI;;;EAG3C;EAEU,OAGR,MAAS,KAAQ,MAAgB,OAAiB,CAAA,GAAE;AACpD,QAAI,MAAM;AACR,YAAM,QAAQ,KAAK;AACnB,YAAM,IAAI,MAAM,KAAK,eAAe,GAAG;AACvC,YAAM,aAAa,MAAM,WAAW,EAAE,SAAS,EAAE,OAAO;AAExD,WAAK,QAAQ,MAAI,OAAA,OAAA;QACf;QACA;QACA,MAAM,KAAK;QACX,MAAM,KAAK;QACX,GAAG,WAAW;QACd,GAAG,WAAW;MAAC,GACZ,IAAI,CAAA;;EAGb;EAGA,UAAO;AACL,SAAK,cAAa;AAClB,SAAK,OAAM;AACX,SAAK,IAAG;EACV;;AAJA,WAAA;EADC,KAAK,QAAO;;AAiDf,IAAU;CAAV,SAAUC,UAAO;AACF,EAAAA,SAAA,WAAW;AACX,EAAAA,SAAA,aAAa,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG;AACxD,EAAAA,SAAA,YAAoC;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;AAGW,EAAAA,SAAA,iBAAiB;IAC5B,WAAW;IACX,WAAW;IACX,SAAS;IACT,UAAU;;AAGC,EAAAA,SAAA,iBAAwC;IACnD,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW;IACX,YAAY;IACZ,WAAW;IACX,qBAAqB;IACrB,oBAAoB;IACpB,oBAAoB;IACpB,sBAAsB;IACtB,cAAc;;AAElB,GAlCU,YAAA,UAAO,CAAA,EAAA;;;ACxlBV,IAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSvB,MAAM,UAAU,wBAAwB,SAAU,MAAI;AACpD,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,aAAa,IAAI;;AAE7B,SAAO;AACT;AAEA,MAAM,UAAU,wBAAwB,WAAA;AACtC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,aAAY;;AAExB,SAAO;AACT;;;;;;;;;ACxBM,IAAO,YAAP,MAAO,mBACH,SAA6B;EASrC,YAAY,UAA6B,CAAA,GAAE;AACzC,UAAK;AAPA,SAAA,OAAO;AAGJ,SAAA,UAAoC,oBAAI,IAAG;AAC7C,SAAA,WAAW;AAIjB,SAAK,UAAU;AACf,mBAAU,OAAO,KAAK,MAAM,OAAO;EACrC;EAEA,KAAK,OAAY;AACf,SAAK,QAAQ;AACb,QAAI,KAAK,UAAU;AACjB;;AAEF,SAAK,eAAc;EACrB;EAEU,iBAAc;AACtB,SAAK,MAAM,GAAG,cAAc,KAAK,aAAa,IAAI;AAClD,SAAK,MAAM,GAAG,mBAAmB,KAAK,kBAAkB,IAAI;EAC9D;EAEU,gBAAa;AACrB,SAAK,MAAM,IAAI,cAAc,KAAK,aAAa,IAAI;AACnD,SAAK,MAAM,IAAI,mBAAmB,KAAK,kBAAkB,IAAI;EAC/D;EAEA,SAAM;AACJ,QAAI,KAAK,UAAU;AACjB,WAAK,WAAW;AAChB,WAAK,eAAc;;EAEvB;EAEA,UAAO;AACL,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAChB,WAAK,cAAa;;EAEtB;EAEA,YAAS;AACP,WAAO,CAAC,KAAK;EACf;EAEA,aAAa,MAAU;AACrB,SAAK,aAAY;AACjB,UAAM,SAAS,KAAK,gBAAgB,IAAI;AACxC,QAAI,QAAQ;AACV,WAAK,QAAQ,IAAI,MAAM,MAAM;AAC7B,aAAO,GAAG,KAAK,CAAC,MAAM,SAAQ;AAC5B,aAAK,QAAQ,MAAM,IAAI;AACvB,aAAK,MAAM,QAAQ,MAAM,IAAI;MAC/B,CAAC;;EAEL;EAEU,YAAY,EAAE,KAAI,GAA2B;AACrD,SAAK,aAAa,IAAI;EACxB;EAEU,mBAAgB;AACxB,SAAK,aAAY;EACnB;EAEU,gBAAgB,MAAU;AAClC,UAAM,UAAU,KAAK,oBAAoB,IAAI;AAC7C,QAAI,QAAQ,aAAa,QAAQ,WAAW;AAC1C,aAAO,IAAI,cAAc,SAAS,MAAM,KAAK,KAAK;;AAGpD,WAAO;EACT;EAEU,oBAAoB,MAAU;AACtC,QAAI,CAAC,KAAK,QAAQ,UAAU;AAC1B,WAAK,QAAQ,WAAW;QACtB,SAAS;;;AAIb,QAAI,CAAC,KAAK,QAAQ,UAAU;AAC1B,WAAK,QAAQ,WAAW;QACtB,SAAS;;;AAIb,QAAI,OAAO,KAAK,QAAQ,aAAa,WAAW;AAC9C,WAAK,QAAQ,WAAW;QACtB,SAAS,KAAK,QAAQ;;;AAG1B,QAAI,OAAO,KAAK,QAAQ,aAAa,WAAW;AAC9C,WAAK,QAAQ,WAAW;QACtB,SAAS,KAAK,QAAQ;;;AAI1B,UAAM,WAAW,WAAU,iBACzB,KAAK,OACL,MACA,KAAK,QAAQ,QAAQ;AAGvB,UAAM,WAAW,WAAU,iBACzB,KAAK,OACL,MACA,KAAK,QAAQ,QAAQ;AAGvB,UAAM,UAAiC;MACrC,WAAW,CAAC,CAAC,SAAS;MACtB,UAAU,SAAS,YAAY;MAC/B,UAAU,SAAS,YAAY,OAAO;MACtC,WAAW,SAAS,aAAa;MACjC,WAAW,SAAS,aAAa,OAAO;MACxC,oBACE,OAAO,SAAS,eAAe,YAAY,SAAS,aAAa;MACnE,oBAAoB,CAAC,CAAC,SAAS;MAC/B,sBACE,OAAO,SAAS,eAAe,YAAY,SAAS,aAAa;MACnE,qBAAqB,CAAC,CAAC,SAAS;MAChC,cACE,OAAO,SAAS,iBAAiB,YAC7B,SAAS,eACT;MAEN,WAAW,CAAC,CAAC,SAAS;MACtB,YAAY,SAAS,QAAQ;;AAG/B,WAAO;EACT;EAEA,eAAY;AACV,SAAK,QAAQ,QAAQ,CAAC,QAAQ,SAAQ;AACpC,UAAI,KAAK,MAAM,YAAY,KAAK,EAAE,GAAG;AACnC,eAAO,QAAO;;IAElB,CAAC;AACD,SAAK,QAAQ,MAAK;EACpB;EAGA,UAAO;AACL,SAAK,aAAY;AACjB,SAAK,cAAa;AAClB,SAAK,IAAG;AACR,mBAAU,MAAM,KAAK,IAAI;EAC3B;;AALAC,YAAA;EADC,SAAS,QAAO;;CASnB,SAAiBC,YAAS;AAoCxB,WAAgB,iBAId,OAAc,KAAQ,SAAU;AAChC,UAAM,SAAc,CAAA;AACpB,WAAO,KAAK,WAAW,CAAA,CAAE,EAAE,QAAQ,CAAC,QAAO;AACzC,YAAM,MAAM,QAAQ,GAAG;AACvB,aAAO,GAAG,IAAI,OAAO,QAAQ,aAAa,IAAI,KAAK,OAAO,GAAG,IAAI;IACnE,CAAC;AACD,WAAO;EACT;AAXgB,EAAAA,WAAA,mBAAgB;AAYlC,GAhDiB,cAAA,YAAS,CAAA,EAAA;", "names": ["direction", "Private", "__decorate", "Transform"]}