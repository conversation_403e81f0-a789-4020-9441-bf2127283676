import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/searchreplace/plugin.js
var require_plugin = __commonJS({
  "node_modules/.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/searchreplace/plugin.js"() {
    (function() {
      "use strict";
      const Cell = (initial) => {
        let value2 = initial;
        const get2 = () => {
          return value2;
        };
        const set2 = (v) => {
          value2 = v;
        };
        return {
          get: get2,
          set: set2
        };
      };
      var global$3 = tinymce.util.Tools.resolve("tinymce.PluginManager");
      const hasProto = (v, constructor, predicate) => {
        var _a;
        if (predicate(v, constructor.prototype)) {
          return true;
        } else {
          return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;
        }
      };
      const typeOf = (x) => {
        const t = typeof x;
        if (x === null) {
          return "null";
        } else if (t === "object" && Array.isArray(x)) {
          return "array";
        } else if (t === "object" && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {
          return "string";
        } else {
          return t;
        }
      };
      const isType$1 = (type2) => (value2) => typeOf(value2) === type2;
      const isSimpleType = (type2) => (value2) => typeof value2 === type2;
      const isString = isType$1("string");
      const isArray = isType$1("array");
      const isBoolean = isSimpleType("boolean");
      const isNullable = (a) => a === null || a === void 0;
      const isNonNullable = (a) => !isNullable(a);
      const isNumber = isSimpleType("number");
      const noop = () => {
      };
      const constant = (value2) => {
        return () => {
          return value2;
        };
      };
      const always = constant(true);
      const punctuationStr = `[~№|!-*+-\\/:;?@\\[-\`{}¡«·»¿;·՚-՟։֊־׀׃׆׳״؉؊،؍؛؞؟٪-٭۔܀-܍߷-߹࠰-࠾࡞।॥॰෴๏๚๛༄-༒༺-༽྅࿐-࿔࿙࿚၊-၏჻፡-፨᐀᙭᙮᚛᚜᛫-᛭᜵᜶។-៖៘-៚᠀-᠊᥄᥅᨞᨟᪠-᪦᪨-᪭᭚-᭠᯼-᯿᰻-᰿᱾᱿᳓‐-‧‰-⁃⁅-⁑⁓-⁞⁽⁾₍₎〈〉❨-❵⟅⟆⟦-⟯⦃-⦘⧘-⧛⧼⧽⳹-⳼⳾⳿⵰⸀-⸮⸰⸱、-〃〈-】〔-〟〰〽゠・꓾꓿꘍-꘏꙳꙾꛲-꛷꡴-꡷꣎꣏꣸-꣺꤮꤯꥟꧁-꧍꧞꧟꩜-꩟꫞꫟꯫﴾﴿︐-︙︰-﹒﹔-﹡﹣﹨﹪﹫！-＃％-＊，-／：；？＠［-］＿｛｝｟-･]`;
      const punctuation$1 = constant(punctuationStr);
      class Optional {
        constructor(tag, value2) {
          this.tag = tag;
          this.value = value2;
        }
        static some(value2) {
          return new Optional(true, value2);
        }
        static none() {
          return Optional.singletonNone;
        }
        fold(onNone, onSome) {
          if (this.tag) {
            return onSome(this.value);
          } else {
            return onNone();
          }
        }
        isSome() {
          return this.tag;
        }
        isNone() {
          return !this.tag;
        }
        map(mapper) {
          if (this.tag) {
            return Optional.some(mapper(this.value));
          } else {
            return Optional.none();
          }
        }
        bind(binder) {
          if (this.tag) {
            return binder(this.value);
          } else {
            return Optional.none();
          }
        }
        exists(predicate) {
          return this.tag && predicate(this.value);
        }
        forall(predicate) {
          return !this.tag || predicate(this.value);
        }
        filter(predicate) {
          if (!this.tag || predicate(this.value)) {
            return this;
          } else {
            return Optional.none();
          }
        }
        getOr(replacement) {
          return this.tag ? this.value : replacement;
        }
        or(replacement) {
          return this.tag ? this : replacement;
        }
        getOrThunk(thunk) {
          return this.tag ? this.value : thunk();
        }
        orThunk(thunk) {
          return this.tag ? this : thunk();
        }
        getOrDie(message) {
          if (!this.tag) {
            throw new Error(message !== null && message !== void 0 ? message : "Called getOrDie on None");
          } else {
            return this.value;
          }
        }
        static from(value2) {
          return isNonNullable(value2) ? Optional.some(value2) : Optional.none();
        }
        getOrNull() {
          return this.tag ? this.value : null;
        }
        getOrUndefined() {
          return this.value;
        }
        each(worker) {
          if (this.tag) {
            worker(this.value);
          }
        }
        toArray() {
          return this.tag ? [this.value] : [];
        }
        toString() {
          return this.tag ? `some(${this.value})` : "none()";
        }
      }
      Optional.singletonNone = new Optional(false);
      const punctuation = punctuation$1;
      var global$2 = tinymce.util.Tools.resolve("tinymce.Env");
      var global$1 = tinymce.util.Tools.resolve("tinymce.util.Tools");
      const nativeSlice = Array.prototype.slice;
      const nativePush = Array.prototype.push;
      const map = (xs, f) => {
        const len = xs.length;
        const r = new Array(len);
        for (let i = 0; i < len; i++) {
          const x = xs[i];
          r[i] = f(x, i);
        }
        return r;
      };
      const each = (xs, f) => {
        for (let i = 0, len = xs.length; i < len; i++) {
          const x = xs[i];
          f(x, i);
        }
      };
      const eachr = (xs, f) => {
        for (let i = xs.length - 1; i >= 0; i--) {
          const x = xs[i];
          f(x, i);
        }
      };
      const groupBy = (xs, f) => {
        if (xs.length === 0) {
          return [];
        } else {
          let wasType = f(xs[0]);
          const r = [];
          let group = [];
          for (let i = 0, len = xs.length; i < len; i++) {
            const x = xs[i];
            const type2 = f(x);
            if (type2 !== wasType) {
              r.push(group);
              group = [];
            }
            wasType = type2;
            group.push(x);
          }
          if (group.length !== 0) {
            r.push(group);
          }
          return r;
        }
      };
      const foldl = (xs, f, acc) => {
        each(xs, (x, i) => {
          acc = f(acc, x, i);
        });
        return acc;
      };
      const flatten = (xs) => {
        const r = [];
        for (let i = 0, len = xs.length; i < len; ++i) {
          if (!isArray(xs[i])) {
            throw new Error("Arr.flatten item " + i + " was not an array, input: " + xs);
          }
          nativePush.apply(r, xs[i]);
        }
        return r;
      };
      const bind = (xs, f) => flatten(map(xs, f));
      const sort = (xs, comparator) => {
        const copy = nativeSlice.call(xs, 0);
        copy.sort(comparator);
        return copy;
      };
      const hasOwnProperty = Object.hasOwnProperty;
      const has = (obj, key) => hasOwnProperty.call(obj, key);
      typeof window !== "undefined" ? window : Function("return this;")();
      const DOCUMENT = 9;
      const DOCUMENT_FRAGMENT = 11;
      const ELEMENT = 1;
      const TEXT = 3;
      const type = (element) => element.dom.nodeType;
      const isType = (t) => (element) => type(element) === t;
      const isText$1 = isType(TEXT);
      const rawSet = (dom, key, value2) => {
        if (isString(value2) || isBoolean(value2) || isNumber(value2)) {
          dom.setAttribute(key, value2 + "");
        } else {
          console.error("Invalid call to Attribute.set. Key ", key, ":: Value ", value2, ":: Element ", dom);
          throw new Error("Attribute value was not simple");
        }
      };
      const set = (element, key, value2) => {
        rawSet(element.dom, key, value2);
      };
      const fromHtml = (html, scope) => {
        const doc = scope || document;
        const div = doc.createElement("div");
        div.innerHTML = html;
        if (!div.hasChildNodes() || div.childNodes.length > 1) {
          const message = "HTML does not have a single root node";
          console.error(message, html);
          throw new Error(message);
        }
        return fromDom(div.childNodes[0]);
      };
      const fromTag = (tag, scope) => {
        const doc = scope || document;
        const node = doc.createElement(tag);
        return fromDom(node);
      };
      const fromText = (text, scope) => {
        const doc = scope || document;
        const node = doc.createTextNode(text);
        return fromDom(node);
      };
      const fromDom = (node) => {
        if (node === null || node === void 0) {
          throw new Error("Node cannot be null or undefined");
        }
        return { dom: node };
      };
      const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);
      const SugarElement = {
        fromHtml,
        fromTag,
        fromText,
        fromDom,
        fromPoint
      };
      const bypassSelector = (dom) => dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT || dom.childElementCount === 0;
      const all = (selector, scope) => {
        const base = scope === void 0 ? document : scope.dom;
        return bypassSelector(base) ? [] : map(base.querySelectorAll(selector), SugarElement.fromDom);
      };
      const parent = (element) => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);
      const children = (element) => map(element.dom.childNodes, SugarElement.fromDom);
      const spot = (element, offset) => ({
        element,
        offset
      });
      const leaf = (element, offset) => {
        const cs = children(element);
        return cs.length > 0 && offset < cs.length ? spot(cs[offset], 0) : spot(element, offset);
      };
      const before = (marker, element) => {
        const parent$1 = parent(marker);
        parent$1.each((v) => {
          v.dom.insertBefore(element.dom, marker.dom);
        });
      };
      const append = (parent2, element) => {
        parent2.dom.appendChild(element.dom);
      };
      const wrap = (element, wrapper) => {
        before(element, wrapper);
        append(wrapper, element);
      };
      const NodeValue = (is, name) => {
        const get2 = (element) => {
          if (!is(element)) {
            throw new Error("Can only get " + name + " value of a " + name + " node");
          }
          return getOption(element).getOr("");
        };
        const getOption = (element) => is(element) ? Optional.from(element.dom.nodeValue) : Optional.none();
        const set2 = (element, value2) => {
          if (!is(element)) {
            throw new Error("Can only set raw " + name + " value of a " + name + " node");
          }
          element.dom.nodeValue = value2;
        };
        return {
          get: get2,
          getOption,
          set: set2
        };
      };
      const api = NodeValue(isText$1, "text");
      const get$1 = (element) => api.get(element);
      const compareDocumentPosition = (a, b, match) => {
        return (a.compareDocumentPosition(b) & match) !== 0;
      };
      const documentPositionPreceding = (a, b) => {
        return compareDocumentPosition(a, b, Node.DOCUMENT_POSITION_PRECEDING);
      };
      const descendants = (scope, selector) => all(selector, scope);
      var global = tinymce.util.Tools.resolve("tinymce.dom.TreeWalker");
      const isSimpleBoundary = (dom, node) => dom.isBlock(node) || has(dom.schema.getVoidElements(), node.nodeName);
      const isContentEditableFalse = (dom, node) => dom.getContentEditable(node) === "false";
      const isContentEditableTrueInCef = (dom, node) => dom.getContentEditable(node) === "true" && node.parentNode && dom.getContentEditableParent(node.parentNode) === "false";
      const isHidden = (dom, node) => !dom.isBlock(node) && has(dom.schema.getWhitespaceElements(), node.nodeName);
      const isBoundary = (dom, node) => isSimpleBoundary(dom, node) || isContentEditableFalse(dom, node) || isHidden(dom, node) || isContentEditableTrueInCef(dom, node);
      const isText = (node) => node.nodeType === 3;
      const nuSection = () => ({
        sOffset: 0,
        fOffset: 0,
        elements: []
      });
      const toLeaf = (node, offset) => leaf(SugarElement.fromDom(node), offset);
      const walk = (dom, walkerFn, startNode, callbacks, endNode, skipStart = true) => {
        let next2 = skipStart ? walkerFn(false) : startNode;
        while (next2) {
          const isCefNode = isContentEditableFalse(dom, next2);
          if (isCefNode || isHidden(dom, next2)) {
            const stopWalking = isCefNode ? callbacks.cef(next2) : callbacks.boundary(next2);
            if (stopWalking) {
              break;
            } else {
              next2 = walkerFn(true);
              continue;
            }
          } else if (isSimpleBoundary(dom, next2)) {
            if (callbacks.boundary(next2)) {
              break;
            }
          } else if (isText(next2)) {
            callbacks.text(next2);
          }
          if (next2 === endNode) {
            break;
          } else {
            next2 = walkerFn(false);
          }
        }
      };
      const collectTextToBoundary = (dom, section, node, rootNode, forwards) => {
        var _a;
        if (isBoundary(dom, node)) {
          return;
        }
        const rootBlock = (_a = dom.getParent(rootNode, dom.isBlock)) !== null && _a !== void 0 ? _a : dom.getRoot();
        const walker = new global(node, rootBlock);
        const walkerFn = forwards ? walker.next.bind(walker) : walker.prev.bind(walker);
        walk(dom, walkerFn, node, {
          boundary: always,
          cef: always,
          text: (next2) => {
            if (forwards) {
              section.fOffset += next2.length;
            } else {
              section.sOffset += next2.length;
            }
            section.elements.push(SugarElement.fromDom(next2));
          }
        });
      };
      const collect = (dom, rootNode, startNode, endNode, callbacks, skipStart = true) => {
        const walker = new global(startNode, rootNode);
        const sections = [];
        let current = nuSection();
        collectTextToBoundary(dom, current, startNode, rootNode, false);
        const finishSection = () => {
          if (current.elements.length > 0) {
            sections.push(current);
            current = nuSection();
          }
          return false;
        };
        walk(dom, walker.next.bind(walker), startNode, {
          boundary: finishSection,
          cef: (node) => {
            finishSection();
            if (callbacks) {
              sections.push(...callbacks.cef(node));
            }
            return false;
          },
          text: (next2) => {
            current.elements.push(SugarElement.fromDom(next2));
            if (callbacks) {
              callbacks.text(next2, current);
            }
          }
        }, endNode, skipStart);
        if (endNode) {
          collectTextToBoundary(dom, current, endNode, rootNode, true);
        }
        finishSection();
        return sections;
      };
      const collectRangeSections = (dom, rng) => {
        const start = toLeaf(rng.startContainer, rng.startOffset);
        const startNode = start.element.dom;
        const end = toLeaf(rng.endContainer, rng.endOffset);
        const endNode = end.element.dom;
        return collect(dom, rng.commonAncestorContainer, startNode, endNode, {
          text: (node, section) => {
            if (node === endNode) {
              section.fOffset += node.length - end.offset;
            } else if (node === startNode) {
              section.sOffset += start.offset;
            }
          },
          cef: (node) => {
            const sections = bind(descendants(SugarElement.fromDom(node), "*[contenteditable=true]"), (e) => {
              const ceTrueNode = e.dom;
              return collect(dom, ceTrueNode, ceTrueNode);
            });
            return sort(sections, (a, b) => documentPositionPreceding(a.elements[0].dom, b.elements[0].dom) ? 1 : -1);
          }
        }, false);
      };
      const fromRng = (dom, rng) => rng.collapsed ? [] : collectRangeSections(dom, rng);
      const fromNode = (dom, node) => {
        const rng = dom.createRng();
        rng.selectNode(node);
        return fromRng(dom, rng);
      };
      const fromNodes = (dom, nodes) => bind(nodes, (node) => fromNode(dom, node));
      const find$2 = (text, pattern, start = 0, finish = text.length) => {
        const regex = pattern.regex;
        regex.lastIndex = start;
        const results = [];
        let match;
        while (match = regex.exec(text)) {
          const matchedText = match[pattern.matchIndex];
          const matchStart = match.index + match[0].indexOf(matchedText);
          const matchFinish = matchStart + matchedText.length;
          if (matchFinish > finish) {
            break;
          }
          results.push({
            start: matchStart,
            finish: matchFinish
          });
          regex.lastIndex = matchFinish;
        }
        return results;
      };
      const extract = (elements, matches) => {
        const nodePositions = foldl(elements, (acc, element) => {
          const content = get$1(element);
          const start = acc.last;
          const finish = start + content.length;
          const positions = bind(matches, (match, matchIdx) => {
            if (match.start < finish && match.finish > start) {
              return [{
                element,
                start: Math.max(start, match.start) - start,
                finish: Math.min(finish, match.finish) - start,
                matchId: matchIdx
              }];
            } else {
              return [];
            }
          });
          return {
            results: acc.results.concat(positions),
            last: finish
          };
        }, {
          results: [],
          last: 0
        }).results;
        return groupBy(nodePositions, (position) => position.matchId);
      };
      const find$1 = (pattern, sections) => bind(sections, (section) => {
        const elements = section.elements;
        const content = map(elements, get$1).join("");
        const positions = find$2(content, pattern, section.sOffset, content.length - section.fOffset);
        return extract(elements, positions);
      });
      const mark = (matches, replacementNode) => {
        eachr(matches, (match, idx) => {
          eachr(match, (pos) => {
            const wrapper = SugarElement.fromDom(replacementNode.cloneNode(false));
            set(wrapper, "data-mce-index", idx);
            const textNode = pos.element.dom;
            if (textNode.length === pos.finish && pos.start === 0) {
              wrap(pos.element, wrapper);
            } else {
              if (textNode.length !== pos.finish) {
                textNode.splitText(pos.finish);
              }
              const matchNode = textNode.splitText(pos.start);
              wrap(SugarElement.fromDom(matchNode), wrapper);
            }
          });
        });
      };
      const findAndMark = (dom, pattern, node, replacementNode) => {
        const textSections = fromNode(dom, node);
        const matches = find$1(pattern, textSections);
        mark(matches, replacementNode);
        return matches.length;
      };
      const findAndMarkInSelection = (dom, pattern, selection, replacementNode) => {
        const bookmark = selection.getBookmark();
        const nodes = dom.select("td[data-mce-selected],th[data-mce-selected]");
        const textSections = nodes.length > 0 ? fromNodes(dom, nodes) : fromRng(dom, selection.getRng());
        const matches = find$1(pattern, textSections);
        mark(matches, replacementNode);
        selection.moveToBookmark(bookmark);
        return matches.length;
      };
      const getElmIndex = (elm) => {
        return elm.getAttribute("data-mce-index");
      };
      const markAllMatches = (editor, currentSearchState, pattern, inSelection) => {
        const marker = editor.dom.create("span", { "data-mce-bogus": 1 });
        marker.className = "mce-match-marker";
        const node = editor.getBody();
        done(editor, currentSearchState, false);
        if (inSelection) {
          return findAndMarkInSelection(editor.dom, pattern, editor.selection, marker);
        } else {
          return findAndMark(editor.dom, pattern, node, marker);
        }
      };
      const unwrap = (node) => {
        var _a;
        const parentNode = node.parentNode;
        if (node.firstChild) {
          parentNode.insertBefore(node.firstChild, node);
        }
        (_a = node.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(node);
      };
      const findSpansByIndex = (editor, index) => {
        const spans = [];
        const nodes = global$1.toArray(editor.getBody().getElementsByTagName("span"));
        if (nodes.length) {
          for (let i = 0; i < nodes.length; i++) {
            const nodeIndex = getElmIndex(nodes[i]);
            if (nodeIndex === null || !nodeIndex.length) {
              continue;
            }
            if (nodeIndex === index.toString()) {
              spans.push(nodes[i]);
            }
          }
        }
        return spans;
      };
      const moveSelection = (editor, currentSearchState, forward) => {
        const searchState = currentSearchState.get();
        let testIndex = searchState.index;
        const dom = editor.dom;
        if (forward) {
          if (testIndex + 1 === searchState.count) {
            testIndex = 0;
          } else {
            testIndex++;
          }
        } else {
          if (testIndex - 1 === -1) {
            testIndex = searchState.count - 1;
          } else {
            testIndex--;
          }
        }
        dom.removeClass(findSpansByIndex(editor, searchState.index), "mce-match-marker-selected");
        const spans = findSpansByIndex(editor, testIndex);
        if (spans.length) {
          dom.addClass(findSpansByIndex(editor, testIndex), "mce-match-marker-selected");
          editor.selection.scrollIntoView(spans[0]);
          return testIndex;
        }
        return -1;
      };
      const removeNode = (dom, node) => {
        const parent2 = node.parentNode;
        dom.remove(node);
        if (parent2 && dom.isEmpty(parent2)) {
          dom.remove(parent2);
        }
      };
      const escapeSearchText = (text, wholeWord) => {
        const escapedText = text.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, "\\$&").replace(/\s/g, "[^\\S\\r\\n\\uFEFF]");
        const wordRegex = "(" + escapedText + ")";
        return wholeWord ? `(?:^|\\s|${punctuation()})` + wordRegex + `(?=$|\\s|${punctuation()})` : wordRegex;
      };
      const find = (editor, currentSearchState, text, matchCase, wholeWord, inSelection) => {
        const selection = editor.selection;
        const escapedText = escapeSearchText(text, wholeWord);
        const isForwardSelection = selection.isForward();
        const pattern = {
          regex: new RegExp(escapedText, matchCase ? "g" : "gi"),
          matchIndex: 1
        };
        const count = markAllMatches(editor, currentSearchState, pattern, inSelection);
        if (global$2.browser.isSafari()) {
          selection.setRng(selection.getRng(), isForwardSelection);
        }
        if (count) {
          const newIndex = moveSelection(editor, currentSearchState, true);
          currentSearchState.set({
            index: newIndex,
            count,
            text,
            matchCase,
            wholeWord,
            inSelection
          });
        }
        return count;
      };
      const next = (editor, currentSearchState) => {
        const index = moveSelection(editor, currentSearchState, true);
        currentSearchState.set({
          ...currentSearchState.get(),
          index
        });
      };
      const prev = (editor, currentSearchState) => {
        const index = moveSelection(editor, currentSearchState, false);
        currentSearchState.set({
          ...currentSearchState.get(),
          index
        });
      };
      const isMatchSpan = (node) => {
        const matchIndex = getElmIndex(node);
        return matchIndex !== null && matchIndex.length > 0;
      };
      const replace = (editor, currentSearchState, text, forward, all2) => {
        const searchState = currentSearchState.get();
        const currentIndex = searchState.index;
        let currentMatchIndex, nextIndex = currentIndex;
        forward = forward !== false;
        const node = editor.getBody();
        const nodes = global$1.grep(global$1.toArray(node.getElementsByTagName("span")), isMatchSpan);
        for (let i = 0; i < nodes.length; i++) {
          const nodeIndex = getElmIndex(nodes[i]);
          let matchIndex = currentMatchIndex = parseInt(nodeIndex, 10);
          if (all2 || matchIndex === searchState.index) {
            if (text.length) {
              nodes[i].innerText = text;
              unwrap(nodes[i]);
            } else {
              removeNode(editor.dom, nodes[i]);
            }
            while (nodes[++i]) {
              matchIndex = parseInt(getElmIndex(nodes[i]), 10);
              if (matchIndex === currentMatchIndex) {
                removeNode(editor.dom, nodes[i]);
              } else {
                i--;
                break;
              }
            }
            if (forward) {
              nextIndex--;
            }
          } else if (currentMatchIndex > currentIndex) {
            nodes[i].setAttribute("data-mce-index", String(currentMatchIndex - 1));
          }
        }
        currentSearchState.set({
          ...searchState,
          count: all2 ? 0 : searchState.count - 1,
          index: nextIndex
        });
        if (forward) {
          next(editor, currentSearchState);
        } else {
          prev(editor, currentSearchState);
        }
        return !all2 && currentSearchState.get().count > 0;
      };
      const done = (editor, currentSearchState, keepEditorSelection) => {
        let startContainer;
        let endContainer;
        const searchState = currentSearchState.get();
        const nodes = global$1.toArray(editor.getBody().getElementsByTagName("span"));
        for (let i = 0; i < nodes.length; i++) {
          const nodeIndex = getElmIndex(nodes[i]);
          if (nodeIndex !== null && nodeIndex.length) {
            if (nodeIndex === searchState.index.toString()) {
              if (!startContainer) {
                startContainer = nodes[i].firstChild;
              }
              endContainer = nodes[i].firstChild;
            }
            unwrap(nodes[i]);
          }
        }
        currentSearchState.set({
          ...searchState,
          index: -1,
          count: 0,
          text: ""
        });
        if (startContainer && endContainer) {
          const rng = editor.dom.createRng();
          rng.setStart(startContainer, 0);
          rng.setEnd(endContainer, endContainer.data.length);
          if (keepEditorSelection !== false) {
            editor.selection.setRng(rng);
          }
          return rng;
        } else {
          return void 0;
        }
      };
      const hasNext = (editor, currentSearchState) => currentSearchState.get().count > 1;
      const hasPrev = (editor, currentSearchState) => currentSearchState.get().count > 1;
      const get = (editor, currentState) => {
        const done$1 = (keepEditorSelection) => {
          return done(editor, currentState, keepEditorSelection);
        };
        const find$12 = (text, matchCase, wholeWord, inSelection = false) => {
          return find(editor, currentState, text, matchCase, wholeWord, inSelection);
        };
        const next$1 = () => {
          return next(editor, currentState);
        };
        const prev$1 = () => {
          return prev(editor, currentState);
        };
        const replace$1 = (text, forward, all2) => {
          return replace(editor, currentState, text, forward, all2);
        };
        return {
          done: done$1,
          find: find$12,
          next: next$1,
          prev: prev$1,
          replace: replace$1
        };
      };
      const singleton = (doRevoke) => {
        const subject = Cell(Optional.none());
        const revoke = () => subject.get().each(doRevoke);
        const clear = () => {
          revoke();
          subject.set(Optional.none());
        };
        const isSet = () => subject.get().isSome();
        const get2 = () => subject.get();
        const set2 = (s) => {
          revoke();
          subject.set(Optional.some(s));
        };
        return {
          clear,
          isSet,
          get: get2,
          set: set2
        };
      };
      const value = () => {
        const subject = singleton(noop);
        const on = (f) => subject.get().each(f);
        return {
          ...subject,
          on
        };
      };
      const open = (editor, currentSearchState) => {
        const dialogApi = value();
        editor.undoManager.add();
        const selectedText = global$1.trim(editor.selection.getContent({ format: "text" }));
        const updateButtonStates = (api2) => {
          api2.setEnabled("next", hasNext(editor, currentSearchState));
          api2.setEnabled("prev", hasPrev(editor, currentSearchState));
        };
        const updateSearchState = (api2) => {
          const data = api2.getData();
          const current = currentSearchState.get();
          currentSearchState.set({
            ...current,
            matchCase: data.matchcase,
            wholeWord: data.wholewords,
            inSelection: data.inselection
          });
        };
        const disableAll = (api2, disable) => {
          const buttons = [
            "replace",
            "replaceall",
            "prev",
            "next"
          ];
          const toggle = (name) => api2.setEnabled(name, !disable);
          each(buttons, toggle);
        };
        const toggleNotFoundAlert = (isVisible, api2) => {
          api2.redial(getDialogSpec(isVisible, api2.getData()));
        };
        const focusButtonIfRequired = (api2, name) => {
          if (global$2.browser.isSafari() && global$2.deviceType.isTouch() && (name === "find" || name === "replace" || name === "replaceall")) {
            api2.focus(name);
          }
        };
        const reset = (api2) => {
          done(editor, currentSearchState, false);
          disableAll(api2, true);
          updateButtonStates(api2);
        };
        const doFind = (api2) => {
          const data = api2.getData();
          const last = currentSearchState.get();
          if (!data.findtext.length) {
            reset(api2);
            return;
          }
          if (last.text === data.findtext && last.matchCase === data.matchcase && last.wholeWord === data.wholewords) {
            next(editor, currentSearchState);
          } else {
            const count = find(editor, currentSearchState, data.findtext, data.matchcase, data.wholewords, data.inselection);
            if (count <= 0) {
              toggleNotFoundAlert(true, api2);
            }
            disableAll(api2, count === 0);
          }
          updateButtonStates(api2);
        };
        const initialState = currentSearchState.get();
        const initialData = {
          findtext: selectedText,
          replacetext: "",
          wholewords: initialState.wholeWord,
          matchcase: initialState.matchCase,
          inselection: initialState.inSelection
        };
        const getPanelItems = (error) => {
          const items = [
            {
              type: "bar",
              items: [
                {
                  type: "input",
                  name: "findtext",
                  placeholder: "Find",
                  maximized: true,
                  inputMode: "search"
                },
                {
                  type: "button",
                  name: "prev",
                  text: "Previous",
                  icon: "action-prev",
                  enabled: false,
                  borderless: true
                },
                {
                  type: "button",
                  name: "next",
                  text: "Next",
                  icon: "action-next",
                  enabled: false,
                  borderless: true
                }
              ]
            },
            {
              type: "input",
              name: "replacetext",
              placeholder: "Replace with",
              inputMode: "search"
            }
          ];
          if (error) {
            items.push({
              type: "alertbanner",
              level: "error",
              text: "Could not find the specified string.",
              icon: "warning"
            });
          }
          return items;
        };
        const getDialogSpec = (showNoMatchesAlertBanner, initialData2) => ({
          title: "Find and Replace",
          size: "normal",
          body: {
            type: "panel",
            items: getPanelItems(showNoMatchesAlertBanner)
          },
          buttons: [
            {
              type: "menu",
              name: "options",
              icon: "preferences",
              tooltip: "Preferences",
              align: "start",
              items: [
                {
                  type: "togglemenuitem",
                  name: "matchcase",
                  text: "Match case"
                },
                {
                  type: "togglemenuitem",
                  name: "wholewords",
                  text: "Find whole words only"
                },
                {
                  type: "togglemenuitem",
                  name: "inselection",
                  text: "Find in selection"
                }
              ]
            },
            {
              type: "custom",
              name: "find",
              text: "Find",
              primary: true
            },
            {
              type: "custom",
              name: "replace",
              text: "Replace",
              enabled: false
            },
            {
              type: "custom",
              name: "replaceall",
              text: "Replace all",
              enabled: false
            }
          ],
          initialData: initialData2,
          onChange: (api2, details) => {
            if (showNoMatchesAlertBanner) {
              toggleNotFoundAlert(false, api2);
            }
            if (details.name === "findtext" && currentSearchState.get().count > 0) {
              reset(api2);
            }
          },
          onAction: (api2, details) => {
            const data = api2.getData();
            switch (details.name) {
              case "find":
                doFind(api2);
                break;
              case "replace":
                if (!replace(editor, currentSearchState, data.replacetext)) {
                  reset(api2);
                } else {
                  updateButtonStates(api2);
                }
                break;
              case "replaceall":
                replace(editor, currentSearchState, data.replacetext, true, true);
                reset(api2);
                break;
              case "prev":
                prev(editor, currentSearchState);
                updateButtonStates(api2);
                break;
              case "next":
                next(editor, currentSearchState);
                updateButtonStates(api2);
                break;
              case "matchcase":
              case "wholewords":
              case "inselection":
                toggleNotFoundAlert(false, api2);
                updateSearchState(api2);
                reset(api2);
                break;
            }
            focusButtonIfRequired(api2, details.name);
          },
          onSubmit: (api2) => {
            doFind(api2);
            focusButtonIfRequired(api2, "find");
          },
          onClose: () => {
            editor.focus();
            done(editor, currentSearchState);
            editor.undoManager.add();
          }
        });
        dialogApi.set(editor.windowManager.open(getDialogSpec(false, initialData), { inline: "toolbar" }));
      };
      const register$1 = (editor, currentSearchState) => {
        editor.addCommand("SearchReplace", () => {
          open(editor, currentSearchState);
        });
      };
      const showDialog = (editor, currentSearchState) => () => {
        open(editor, currentSearchState);
      };
      const register = (editor, currentSearchState) => {
        editor.ui.registry.addMenuItem("searchreplace", {
          text: "Find and replace...",
          shortcut: "Meta+F",
          onAction: showDialog(editor, currentSearchState),
          icon: "search"
        });
        editor.ui.registry.addButton("searchreplace", {
          tooltip: "Find and replace",
          onAction: showDialog(editor, currentSearchState),
          icon: "search"
        });
        editor.shortcuts.add("Meta+F", "", showDialog(editor, currentSearchState));
      };
      var Plugin = () => {
        global$3.add("searchreplace", (editor) => {
          const currentSearchState = Cell({
            index: -1,
            count: 0,
            text: "",
            matchCase: false,
            wholeWord: false,
            inSelection: false
          });
          register$1(editor, currentSearchState);
          register(editor, currentSearchState);
          return get(editor, currentSearchState);
        });
      };
      Plugin();
    })();
  }
});

// node_modules/.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/searchreplace/index.js
require_plugin();
//# sourceMappingURL=tinymce_plugins_searchreplace.js.map
