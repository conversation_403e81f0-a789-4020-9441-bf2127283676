{"version": 3, "sources": ["../../.pnpm/codemirror@5.65.19/node_modules/codemirror/mode/shell/shell.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nCodeMirror.defineMode('shell', function() {\n\n  var words = {};\n  function define(style, dict) {\n    for(var i = 0; i < dict.length; i++) {\n      words[dict[i]] = style;\n    }\n  };\n\n  var commonAtoms = [\"true\", \"false\"];\n  var commonKeywords = [\"if\", \"then\", \"do\", \"else\", \"elif\", \"while\", \"until\", \"for\", \"in\", \"esac\", \"fi\",\n    \"fin\", \"fil\", \"done\", \"exit\", \"set\", \"unset\", \"export\", \"function\"];\n  var commonCommands = [\"ab\", \"awk\", \"bash\", \"beep\", \"cat\", \"cc\", \"cd\", \"chown\", \"chmod\", \"chroot\", \"clear\",\n    \"cp\", \"curl\", \"cut\", \"diff\", \"echo\", \"find\", \"gawk\", \"gcc\", \"get\", \"git\", \"grep\", \"hg\", \"kill\", \"killall\",\n    \"ln\", \"ls\", \"make\", \"mkdir\", \"openssl\", \"mv\", \"nc\", \"nl\", \"node\", \"npm\", \"ping\", \"ps\", \"restart\", \"rm\",\n    \"rmdir\", \"sed\", \"service\", \"sh\", \"shopt\", \"shred\", \"source\", \"sort\", \"sleep\", \"ssh\", \"start\", \"stop\",\n    \"su\", \"sudo\", \"svn\", \"tee\", \"telnet\", \"top\", \"touch\", \"vi\", \"vim\", \"wall\", \"wc\", \"wget\", \"who\", \"write\",\n    \"yes\", \"zsh\"];\n\n  CodeMirror.registerHelper(\"hintWords\", \"shell\", commonAtoms.concat(commonKeywords, commonCommands));\n\n  define('atom', commonAtoms);\n  define('keyword', commonKeywords);\n  define('builtin', commonCommands);\n\n  function tokenBase(stream, state) {\n    if (stream.eatSpace()) return null;\n\n    var sol = stream.sol();\n    var ch = stream.next();\n\n    if (ch === '\\\\') {\n      stream.next();\n      return null;\n    }\n    if (ch === '\\'' || ch === '\"' || ch === '`') {\n      state.tokens.unshift(tokenString(ch, ch === \"`\" ? \"quote\" : \"string\"));\n      return tokenize(stream, state);\n    }\n    if (ch === '#') {\n      if (sol && stream.eat('!')) {\n        stream.skipToEnd();\n        return 'meta'; // 'comment'?\n      }\n      stream.skipToEnd();\n      return 'comment';\n    }\n    if (ch === '$') {\n      state.tokens.unshift(tokenDollar);\n      return tokenize(stream, state);\n    }\n    if (ch === '+' || ch === '=') {\n      return 'operator';\n    }\n    if (ch === '-') {\n      stream.eat('-');\n      stream.eatWhile(/\\w/);\n      return 'attribute';\n    }\n    if (ch == \"<\") {\n      if (stream.match(\"<<\")) return \"operator\"\n      var heredoc = stream.match(/^<-?\\s*['\"]?([^'\"]*)['\"]?/)\n      if (heredoc) {\n        state.tokens.unshift(tokenHeredoc(heredoc[1]))\n        return 'string-2'\n      }\n    }\n    if (/\\d/.test(ch)) {\n      stream.eatWhile(/\\d/);\n      if(stream.eol() || !/\\w/.test(stream.peek())) {\n        return 'number';\n      }\n    }\n    stream.eatWhile(/[\\w-]/);\n    var cur = stream.current();\n    if (stream.peek() === '=' && /\\w+/.test(cur)) return 'def';\n    return words.hasOwnProperty(cur) ? words[cur] : null;\n  }\n\n  function tokenString(quote, style) {\n    var close = quote == \"(\" ? \")\" : quote == \"{\" ? \"}\" : quote\n    return function(stream, state) {\n      var next, escaped = false;\n      while ((next = stream.next()) != null) {\n        if (next === close && !escaped) {\n          state.tokens.shift();\n          break;\n        } else if (next === '$' && !escaped && quote !== \"'\" && stream.peek() != close) {\n          escaped = true;\n          stream.backUp(1);\n          state.tokens.unshift(tokenDollar);\n          break;\n        } else if (!escaped && quote !== close && next === quote) {\n          state.tokens.unshift(tokenString(quote, style))\n          return tokenize(stream, state)\n        } else if (!escaped && /['\"]/.test(next) && !/['\"]/.test(quote)) {\n          state.tokens.unshift(tokenStringStart(next, \"string\"));\n          stream.backUp(1);\n          break;\n        }\n        escaped = !escaped && next === '\\\\';\n      }\n      return style;\n    };\n  };\n\n  function tokenStringStart(quote, style) {\n    return function(stream, state) {\n      state.tokens[0] = tokenString(quote, style)\n      stream.next()\n      return tokenize(stream, state)\n    }\n  }\n\n  var tokenDollar = function(stream, state) {\n    if (state.tokens.length > 1) stream.eat('$');\n    var ch = stream.next()\n    if (/['\"({]/.test(ch)) {\n      state.tokens[0] = tokenString(ch, ch == \"(\" ? \"quote\" : ch == \"{\" ? \"def\" : \"string\");\n      return tokenize(stream, state);\n    }\n    if (!/\\d/.test(ch)) stream.eatWhile(/\\w/);\n    state.tokens.shift();\n    return 'def';\n  };\n\n  function tokenHeredoc(delim) {\n    return function(stream, state) {\n      if (stream.sol() && stream.string == delim) state.tokens.shift()\n      stream.skipToEnd()\n      return \"string-2\"\n    }\n  }\n\n  function tokenize(stream, state) {\n    return (state.tokens[0] || tokenBase) (stream, state);\n  };\n\n  return {\n    startState: function() {return {tokens:[]};},\n    token: function(stream, state) {\n      return tokenize(stream, state);\n    },\n    closeBrackets: \"()[]{}''\\\"\\\"``\",\n    lineComment: '#',\n    fold: \"brace\"\n  };\n});\n\nCodeMirror.defineMIME('text/x-sh', 'shell');\n// Apache uses a slightly different Media Type for Shell scripts\n// http://svn.apache.org/repos/asf/httpd/httpd/trunk/docs/conf/mime.types\nCodeMirror.defineMIME('application/x-sh', 'shell');\n\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACxB;AAEA,MAAAA,YAAW,WAAW,SAAS,WAAW;AAExC,YAAI,QAAQ,CAAC;AACb,iBAASC,QAAO,OAAO,MAAM;AAC3B,mBAAQ,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACnC,kBAAM,KAAK,CAAC,CAAC,IAAI;AAAA,UACnB;AAAA,QACF;AAAC;AAED,YAAI,cAAc,CAAC,QAAQ,OAAO;AAClC,YAAI,iBAAiB;AAAA,UAAC;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAS;AAAA,UAAO;AAAA,UAAM;AAAA,UAAQ;AAAA,UAC/F;AAAA,UAAO;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAS;AAAA,UAAU;AAAA,QAAU;AACpE,YAAI,iBAAiB;AAAA,UAAC;AAAA,UAAM;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAM;AAAA,UAAM;AAAA,UAAS;AAAA,UAAS;AAAA,UAAU;AAAA,UAChG;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAO;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAM;AAAA,UAAQ;AAAA,UAChG;AAAA,UAAM;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAW;AAAA,UAAM;AAAA,UAAM;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAM;AAAA,UAAW;AAAA,UAClG;AAAA,UAAS;AAAA,UAAO;AAAA,UAAW;AAAA,UAAM;AAAA,UAAS;AAAA,UAAS;AAAA,UAAU;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAO;AAAA,UAAS;AAAA,UAC9F;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAO;AAAA,UAAU;AAAA,UAAO;AAAA,UAAS;AAAA,UAAM;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAO;AAAA,UAChG;AAAA,UAAO;AAAA,QAAK;AAEd,QAAAD,YAAW,eAAe,aAAa,SAAS,YAAY,OAAO,gBAAgB,cAAc,CAAC;AAElG,QAAAC,QAAO,QAAQ,WAAW;AAC1B,QAAAA,QAAO,WAAW,cAAc;AAChC,QAAAA,QAAO,WAAW,cAAc;AAEhC,iBAAS,UAAU,QAAQ,OAAO;AAChC,cAAI,OAAO,SAAS,EAAG,QAAO;AAE9B,cAAI,MAAM,OAAO,IAAI;AACrB,cAAI,KAAK,OAAO,KAAK;AAErB,cAAI,OAAO,MAAM;AACf,mBAAO,KAAK;AACZ,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,OAAQ,OAAO,OAAO,OAAO,KAAK;AAC3C,kBAAM,OAAO,QAAQ,YAAY,IAAI,OAAO,MAAM,UAAU,QAAQ,CAAC;AACrE,mBAAO,SAAS,QAAQ,KAAK;AAAA,UAC/B;AACA,cAAI,OAAO,KAAK;AACd,gBAAI,OAAO,OAAO,IAAI,GAAG,GAAG;AAC1B,qBAAO,UAAU;AACjB,qBAAO;AAAA,YACT;AACA,mBAAO,UAAU;AACjB,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,KAAK;AACd,kBAAM,OAAO,QAAQ,WAAW;AAChC,mBAAO,SAAS,QAAQ,KAAK;AAAA,UAC/B;AACA,cAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,KAAK;AACd,mBAAO,IAAI,GAAG;AACd,mBAAO,SAAS,IAAI;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,MAAM,KAAK;AACb,gBAAI,OAAO,MAAM,IAAI,EAAG,QAAO;AAC/B,gBAAI,UAAU,OAAO,MAAM,2BAA2B;AACtD,gBAAI,SAAS;AACX,oBAAM,OAAO,QAAQ,aAAa,QAAQ,CAAC,CAAC,CAAC;AAC7C,qBAAO;AAAA,YACT;AAAA,UACF;AACA,cAAI,KAAK,KAAK,EAAE,GAAG;AACjB,mBAAO,SAAS,IAAI;AACpB,gBAAG,OAAO,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO,KAAK,CAAC,GAAG;AAC5C,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO,SAAS,OAAO;AACvB,cAAI,MAAM,OAAO,QAAQ;AACzB,cAAI,OAAO,KAAK,MAAM,OAAO,MAAM,KAAK,GAAG,EAAG,QAAO;AACrD,iBAAO,MAAM,eAAe,GAAG,IAAI,MAAM,GAAG,IAAI;AAAA,QAClD;AAEA,iBAAS,YAAY,OAAO,OAAO;AACjC,cAAI,QAAQ,SAAS,MAAM,MAAM,SAAS,MAAM,MAAM;AACtD,iBAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,MAAM,UAAU;AACpB,oBAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,kBAAI,SAAS,SAAS,CAAC,SAAS;AAC9B,sBAAM,OAAO,MAAM;AACnB;AAAA,cACF,WAAW,SAAS,OAAO,CAAC,WAAW,UAAU,OAAO,OAAO,KAAK,KAAK,OAAO;AAC9E,0BAAU;AACV,uBAAO,OAAO,CAAC;AACf,sBAAM,OAAO,QAAQ,WAAW;AAChC;AAAA,cACF,WAAW,CAAC,WAAW,UAAU,SAAS,SAAS,OAAO;AACxD,sBAAM,OAAO,QAAQ,YAAY,OAAO,KAAK,CAAC;AAC9C,uBAAO,SAAS,QAAQ,KAAK;AAAA,cAC/B,WAAW,CAAC,WAAW,OAAO,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,GAAG;AAC/D,sBAAM,OAAO,QAAQ,iBAAiB,MAAM,QAAQ,CAAC;AACrD,uBAAO,OAAO,CAAC;AACf;AAAA,cACF;AACA,wBAAU,CAAC,WAAW,SAAS;AAAA,YACjC;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAAC;AAED,iBAAS,iBAAiB,OAAO,OAAO;AACtC,iBAAO,SAAS,QAAQ,OAAO;AAC7B,kBAAM,OAAO,CAAC,IAAI,YAAY,OAAO,KAAK;AAC1C,mBAAO,KAAK;AACZ,mBAAO,SAAS,QAAQ,KAAK;AAAA,UAC/B;AAAA,QACF;AAEA,YAAI,cAAc,SAAS,QAAQ,OAAO;AACxC,cAAI,MAAM,OAAO,SAAS,EAAG,QAAO,IAAI,GAAG;AAC3C,cAAI,KAAK,OAAO,KAAK;AACrB,cAAI,SAAS,KAAK,EAAE,GAAG;AACrB,kBAAM,OAAO,CAAC,IAAI,YAAY,IAAI,MAAM,MAAM,UAAU,MAAM,MAAM,QAAQ,QAAQ;AACpF,mBAAO,SAAS,QAAQ,KAAK;AAAA,UAC/B;AACA,cAAI,CAAC,KAAK,KAAK,EAAE,EAAG,QAAO,SAAS,IAAI;AACxC,gBAAM,OAAO,MAAM;AACnB,iBAAO;AAAA,QACT;AAEA,iBAAS,aAAa,OAAO;AAC3B,iBAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,OAAO,IAAI,KAAK,OAAO,UAAU,MAAO,OAAM,OAAO,MAAM;AAC/D,mBAAO,UAAU;AACjB,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,SAAS,QAAQ,OAAO;AAC/B,kBAAQ,MAAM,OAAO,CAAC,KAAK,WAAY,QAAQ,KAAK;AAAA,QACtD;AAAC;AAED,eAAO;AAAA,UACL,YAAY,WAAW;AAAC,mBAAO,EAAC,QAAO,CAAC,EAAC;AAAA,UAAE;AAAA,UAC3C,OAAO,SAAS,QAAQ,OAAO;AAC7B,mBAAO,SAAS,QAAQ,KAAK;AAAA,UAC/B;AAAA,UACA,eAAe;AAAA,UACf,aAAa;AAAA,UACb,MAAM;AAAA,QACR;AAAA,MACF,CAAC;AAED,MAAAD,YAAW,WAAW,aAAa,OAAO;AAG1C,MAAAA,YAAW,WAAW,oBAAoB,OAAO;AAAA,IAEjD,CAAC;AAAA;AAAA;", "names": ["CodeMirror", "define"]}