import { ref, onUnmounted, Ref, watch } from 'vue';
import { Graph, Node, Edge } from '@antv/x6';
import type { WorkflowEventHandlers } from '../types/workflow';

/**
 * 工作流事件处理 Hook
 */
export function useWorkflowEvents(graphRef: Ref<Graph | null>) {
  const selectedNode = ref<Node | null>(null);
  const selectedEdge = ref<Edge | null>(null);
  const showPropertyPanel = ref(false);

  // 存储事件处理器和容器的引用，用于在Graph实例变化时重新绑定
  let currentContainer: HTMLElement | null = null;
  let currentHandlers: Partial<WorkflowEventHandlers> | undefined = undefined;

  /**
   * 防抖函数（保留以备将来使用）
   */
  const _debounce = <T extends (...args: any[]) => any>(func: T, wait: number): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  };

  /**
   * 强制显示所有连接桩（用于持续显示模式）
   */
  const forceShowAllPorts = (container: HTMLElement | null) => {
    if (!container) return;

    const ports = container.querySelectorAll('.x6-port-body') as NodeListOf<SVGElement>;
    ports.forEach((port) => {
      port.style.visibility = 'visible';
      port.style.opacity = '1';
      // 添加一个自定义属性标记，表示这是强制显示的连接桩
      port.setAttribute('data-force-visible', 'true');
    });
  };

  /**
   * 初始化连接桩可见性控制
   */
  const initPortVisibility = (container: HTMLElement | null) => {
    const graph = graphRef.value;
    if (!graph || !container) return;

    // 修改为始终显示连接桩
    // 在图形初始化完成后，设置所有连接桩为可见状态
    const showAllPorts = () => {
      forceShowAllPorts(container);
    };

    // 监听节点添加事件，确保新添加的节点连接桩也是可见的
    graph.on('node:added', () => {
      // 延迟执行，确保DOM已更新
      setTimeout(() => {
        showAllPorts();
      }, 100);
    });

    // 监听图形数据加载完成事件
    graph.on('render:done', () => {
      showAllPorts();
    });

    // 监听画布重绘事件，确保连接桩始终可见
    graph.on('cell:changed', () => {
      setTimeout(() => {
        showAllPorts();
      }, 50);
    });

    // 立即显示当前所有连接桩
    showAllPorts();

    // 可选：如果需要在特定条件下切换连接桩显示状态，可以保留鼠标事件
    // 但默认情况下连接桩始终可见
    // graph.on('node:mouseenter', () => {
    //   // 可以在这里添加其他鼠标悬停效果，但不控制连接桩显示
    // });

    // graph.on('node:mouseleave', () => {
    //   // 可以在这里添加其他鼠标离开效果，但不隐藏连接桩
    // });
  };

  /**
   * 默认事件处理器
   */
  const defaultEventHandlers: WorkflowEventHandlers = {
    onCellClick: ({ cell: _cell }) => {
      // 处理单元格点击
    },

    onNodeClick: ({ node }) => {
      selectedNode.value = node;
      selectedEdge.value = null;
      showPropertyPanel.value = true;
    },

    onEdgeClick: ({ edge }) => {
      selectedEdge.value = edge;
      selectedNode.value = null;
      showPropertyPanel.value = true;
    },

    onBlankClick: () => {
      selectedNode.value = null;
      selectedEdge.value = null;
      showPropertyPanel.value = false;
    },

    onNodeMouseEnter: ({ node: _node }) => {
      // 处理节点鼠标进入
    },

    onNodeMouseLeave: ({ node: _node }) => {
      // 处理节点鼠标离开
    },
  };

  /**
   * 绑定图形事件
   */
  const bindGraphEvents = (container: HTMLElement | null, customHandlers?: Partial<WorkflowEventHandlers>) => {
    // 存储参数以便在Graph实例变化时重新绑定
    currentContainer = container;
    currentHandlers = customHandlers;

    const graph = graphRef.value;
    if (!graph) {
      return;
    }

    const handlers = { ...defaultEventHandlers, ...customHandlers };

    // 绑定点击事件
    graph.on('cell:click', handlers.onCellClick);
    graph.on('node:click', handlers.onNodeClick);
    graph.on('edge:click', handlers.onEdgeClick);
    graph.on('blank:click', handlers.onBlankClick);

    // 绑定鼠标事件
    graph.on('node:mouseenter', handlers.onNodeMouseEnter);
    graph.on('node:mouseleave', handlers.onNodeMouseLeave);

    // 初始化连接桩可见性
    initPortVisibility(container);
  };

  /**
   * 解绑图形事件
   */
  const unbindGraphEvents = () => {
    const graph = graphRef.value;
    if (!graph) return;

    graph.off('cell:click');
    graph.off('node:click');
    graph.off('edge:click');
    graph.off('blank:click');
    graph.off('node:mouseenter');
    graph.off('node:mouseleave');
  };

  /**
   * 初始化快捷键
   */
  const initKeyboardShortcuts = (callbacks: {
    onUndo?: () => void;
    onRedo?: () => void;
    onSave?: () => void;
    onDelete?: () => void;
    onCopy?: () => void;
    onPaste?: () => void;
  }) => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'z':
            e.preventDefault();
            if (e.shiftKey) {
              callbacks.onRedo?.();
            } else {
              callbacks.onUndo?.();
            }
            break;
          case 'y':
            e.preventDefault();
            callbacks.onRedo?.();
            break;
          case 's':
            e.preventDefault();
            callbacks.onSave?.();
            break;
          case 'c':
            e.preventDefault();
            callbacks.onCopy?.();
            break;
          case 'v':
            e.preventDefault();
            callbacks.onPaste?.();
            break;
          case 'd':
            e.preventDefault();
            callbacks.onDelete?.();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    // 返回清理函数
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  };

  /**
   * 获取选中的节点数据
   */
  const getSelectedNodeData = () => {
    if (!selectedNode.value) return null;

    return {
      id: selectedNode.value.id,
      shape: selectedNode.value.shape,
      position: selectedNode.value.getPosition(),
      size: selectedNode.value.getSize(),
      attrs: selectedNode.value.getAttrs(),
      data: selectedNode.value.getData(),
    };
  };

  /**
   * 更新选中节点的属性
   */
  const updateSelectedNodeData = (data: Record<string, any>) => {
    if (!selectedNode.value) return;

    // 获取现有数据并合并新数据
    const existingData = selectedNode.value.getData() || {};
    const mergedData = { ...existingData, ...data };

    selectedNode.value.setData(mergedData);

    // 如果有名称属性，更新节点文本
    if (data.name) {
      if (selectedNode.value.shape === 'lane') {
        selectedNode.value.attr('name-text/text', data.name);
      } else {
        selectedNode.value.attr('text/text', data.name);
      }
      // 同时更新label属性以保持一致性
      selectedNode.value.prop('label', data.name);
    }
  };

  /**
   * 获取选中的边数据
   */
  const getSelectedEdgeData = () => {
    if (!selectedEdge.value) return null;

    return {
      id: selectedEdge.value.id,
      source: selectedEdge.value.getSource(),
      target: selectedEdge.value.getTarget(),
      attrs: selectedEdge.value.getAttrs(),
      labels: selectedEdge.value.getLabels(),
      data: selectedEdge.value.getData(),
    };
  };

  /**
   * 更新选中边的属性
   */
  const updateSelectedEdgeData = (data: Record<string, any>) => {
    if (!selectedEdge.value) return;

    // 获取现有数据并合并新数据
    const existingData = selectedEdge.value.getData() || {};
    const mergedData = { ...existingData, ...data };

    selectedEdge.value.setData(mergedData);

    // 如果有标签属性，更新边标签
    if (data.label) {
      selectedEdge.value.setLabels([{ attrs: { text: { text: data.label } } }]);
    }
  };

  // 组件卸载时清理事件
  // 监听Graph实例变化，自动重新绑定事件
  watch(
    graphRef,
    (newGraph, oldGraph) => {
      if (oldGraph) {
        // 解绑旧的事件
        unbindGraphEvents();
      }

      if (newGraph && currentContainer !== null) {
        // 重新绑定事件
        bindGraphEvents(currentContainer, currentHandlers);
      }
    },
    { immediate: false }
  );

  onUnmounted(() => {
    unbindGraphEvents();
  });

  return {
    selectedNode,
    selectedEdge,
    showPropertyPanel,
    bindGraphEvents,
    unbindGraphEvents,
    initKeyboardShortcuts,
    forceShowAllPorts,
    getSelectedNodeData,
    updateSelectedNodeData,
    getSelectedEdgeData,
    updateSelectedEdgeData,
  };
}
