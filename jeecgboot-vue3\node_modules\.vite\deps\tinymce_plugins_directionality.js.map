{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/directionality/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/directionality/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType$1 = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const isString = isType$1('string');\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n\n    const compose1 = (fbc, fab) => a => fbc(fab(a));\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    const never = constant(false);\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const filter = (xs, pred) => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const fromHtml = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        const message = 'HTML does not have a single root node';\n        console.error(message, html);\n        throw new Error(message);\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n      const doc = scope || document;\n      const node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    const fromText = (text, scope) => {\n      const doc = scope || document;\n      const node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    const fromDom = node => {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    const SugarElement = {\n      fromHtml,\n      fromTag,\n      fromText,\n      fromDom,\n      fromPoint\n    };\n\n    const is = (element, selector) => {\n      const dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        const elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const name = element => {\n      const r = element.dom.nodeName;\n      return r.toLowerCase();\n    };\n    const type = element => element.dom.nodeType;\n    const isType = t => element => type(element) === t;\n    const isElement = isType(ELEMENT);\n    const isText = isType(TEXT);\n    const isDocument = isType(DOCUMENT);\n    const isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n    const isTag = tag => e => isElement(e) && name(e) === tag;\n\n    const owner = element => SugarElement.fromDom(element.dom.ownerDocument);\n    const documentOrOwner = dos => isDocument(dos) ? dos : owner(dos);\n    const parent = element => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const children$2 = element => map(element.dom.childNodes, SugarElement.fromDom);\n\n    const rawSet = (dom, key, value) => {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    const set = (element, key, value) => {\n      rawSet(element.dom, key, value);\n    };\n    const remove = (element, key) => {\n      element.dom.removeAttribute(key);\n    };\n\n    const isShadowRoot = dos => isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    const supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);\n    const getRootNode = supported ? e => SugarElement.fromDom(e.dom.getRootNode()) : documentOrOwner;\n    const getShadowRoot = e => {\n      const r = getRootNode(e);\n      return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    const getShadowHost = e => SugarElement.fromDom(e.dom.host);\n\n    const inBody = element => {\n      const dom = isText(element) ? element.dom.parentNode : element.dom;\n      if (dom === undefined || dom === null || dom.ownerDocument === null) {\n        return false;\n      }\n      const doc = dom.ownerDocument;\n      return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));\n    };\n\n    const ancestor$1 = (scope, predicate, isRoot) => {\n      let element = scope.dom;\n      const stop = isFunction(isRoot) ? isRoot : never;\n      while (element.parentNode) {\n        element = element.parentNode;\n        const el = SugarElement.fromDom(element);\n        if (predicate(el)) {\n          return Optional.some(el);\n        } else if (stop(el)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n\n    const ancestor = (scope, selector, isRoot) => ancestor$1(scope, e => is(e, selector), isRoot);\n\n    const isSupported = dom => dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n\n    const get = (element, property) => {\n      const dom = element.dom;\n      const styles = window.getComputedStyle(dom);\n      const r = styles.getPropertyValue(property);\n      return r === '' && !inBody(element) ? getUnsafeProperty(dom, property) : r;\n    };\n    const getUnsafeProperty = (dom, property) => isSupported(dom) ? dom.style.getPropertyValue(property) : '';\n\n    const getDirection = element => get(element, 'direction') === 'rtl' ? 'rtl' : 'ltr';\n\n    const children$1 = (scope, predicate) => filter(children$2(scope), predicate);\n\n    const children = (scope, selector) => children$1(scope, e => is(e, selector));\n\n    const getParentElement = element => parent(element).filter(isElement);\n    const getNormalizedBlock = (element, isListItem) => {\n      const normalizedElement = isListItem ? ancestor(element, 'ol,ul') : Optional.some(element);\n      return normalizedElement.getOr(element);\n    };\n    const isListItem = isTag('li');\n    const setDirOnElements = (dom, blocks, dir) => {\n      each(blocks, block => {\n        const blockElement = SugarElement.fromDom(block);\n        const isBlockElementListItem = isListItem(blockElement);\n        const normalizedBlock = getNormalizedBlock(blockElement, isBlockElementListItem);\n        const normalizedBlockParent = getParentElement(normalizedBlock);\n        normalizedBlockParent.each(parent => {\n          dom.setStyle(normalizedBlock.dom, 'direction', null);\n          const parentDirection = getDirection(parent);\n          if (parentDirection === dir) {\n            remove(normalizedBlock, 'dir');\n          } else {\n            set(normalizedBlock, 'dir', dir);\n          }\n          if (getDirection(normalizedBlock) !== dir) {\n            dom.setStyle(normalizedBlock.dom, 'direction', dir);\n          }\n          if (isBlockElementListItem) {\n            const listItems = children(normalizedBlock, 'li[dir],li[style]');\n            each(listItems, listItem => {\n              remove(listItem, 'dir');\n              dom.setStyle(listItem.dom, 'direction', null);\n            });\n          }\n        });\n      });\n    };\n    const setDir = (editor, dir) => {\n      if (editor.selection.isEditable()) {\n        setDirOnElements(editor.dom, editor.selection.getSelectedBlocks(), dir);\n        editor.nodeChanged();\n      }\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('mceDirectionLTR', () => {\n        setDir(editor, 'ltr');\n      });\n      editor.addCommand('mceDirectionRTL', () => {\n        setDir(editor, 'rtl');\n      });\n    };\n\n    const getNodeChangeHandler = (editor, dir) => api => {\n      const nodeChangeHandler = e => {\n        const element = SugarElement.fromDom(e.element);\n        api.setActive(getDirection(element) === dir);\n        api.setEnabled(editor.selection.isEditable());\n      };\n      editor.on('NodeChange', nodeChangeHandler);\n      api.setEnabled(editor.selection.isEditable());\n      return () => editor.off('NodeChange', nodeChangeHandler);\n    };\n    const register = editor => {\n      editor.ui.registry.addToggleButton('ltr', {\n        tooltip: 'Left to right',\n        icon: 'ltr',\n        onAction: () => editor.execCommand('mceDirectionLTR'),\n        onSetup: getNodeChangeHandler(editor, 'ltr')\n      });\n      editor.ui.registry.addToggleButton('rtl', {\n        tooltip: 'Right to left',\n        icon: 'rtl',\n        onAction: () => editor.execCommand('mceDirectionRTL'),\n        onSetup: getNodeChangeHandler(editor, 'rtl')\n      });\n    };\n\n    var Plugin = () => {\n      global.add('directionality', editor => {\n        register$1(editor);\n        register(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"directionality\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/directionality')\n//   ES2015:\n//     import 'tinymce/plugins/directionality'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAE/D,YAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,YAAI;AACJ,YAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,iBAAO;AAAA,QACT,OAAO;AACL,mBAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,QAC7F;AAAA,MACF;AACA,YAAM,SAAS,OAAK;AAClB,cAAM,IAAI,OAAO;AACjB,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,WAAW,CAAAA,UAAQ,WAAS,OAAO,KAAK,MAAMA;AACpD,YAAM,eAAe,CAAAA,UAAQ,WAAS,OAAO,UAAUA;AACvD,YAAM,WAAW,SAAS,QAAQ;AAClC,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,YAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,YAAM,aAAa,aAAa,UAAU;AAC1C,YAAM,WAAW,aAAa,QAAQ;AAEtC,YAAM,WAAW,CAAC,KAAK,QAAQ,OAAK,IAAI,IAAI,CAAC,CAAC;AAC9C,YAAM,WAAW,WAAS;AACxB,eAAO,MAAM;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,QAAQ,SAAS,KAAK;AAAA,MAE5B,MAAM,SAAS;AAAA,QACb,YAAY,KAAK,OAAO;AACtB,eAAK,MAAM;AACX,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,IAAI,SAAS,MAAM,KAAK;AAAA,QACjC;AAAA,QACA,OAAO,OAAO;AACZ,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,KAAK,QAAQ,QAAQ;AACnB,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,QACA,SAAS;AACP,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,SAAS;AACP,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA,IAAI,QAAQ;AACV,cAAI,KAAK,KAAK;AACZ,mBAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,UACzC,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QACzC;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QAC1C;AAAA,QACA,OAAO,WAAW;AAChB,cAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,MAAM,aAAa;AACjB,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,GAAG,aAAa;AACd,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B;AAAA,QACA,WAAW,OAAO;AAChB,iBAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,QACvC;AAAA,QACA,QAAQ,OAAO;AACb,iBAAO,KAAK,MAAM,OAAO,MAAM;AAAA,QACjC;AAAA,QACA,SAAS,SAAS;AAChB,cAAI,CAAC,KAAK,KAAK;AACb,kBAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,UAC9F,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,cAAc,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,QACrE;AAAA,QACA,YAAY;AACV,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,iBAAiB;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AACR,iBAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,YAAM,MAAM,CAAC,IAAI,MAAM;AACrB,cAAM,MAAM,GAAG;AACf,cAAM,IAAI,IAAI,MAAM,GAAG;AACvB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,QACf;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,CAAC,IAAI,MAAM;AACtB,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,cAAM,IAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,cAAE,KAAK,CAAC;AAAA,UACV;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,YAAM,WAAW;AACjB,YAAM,oBAAoB;AAC1B,YAAM,UAAU;AAChB,YAAM,OAAO;AAEb,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,IAAI,cAAc,KAAK;AACnC,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,cAAc,KAAK,IAAI,WAAW,SAAS,GAAG;AACrD,gBAAM,UAAU;AAChB,kBAAQ,MAAM,SAAS,IAAI;AAC3B,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB;AACA,eAAO,QAAQ,IAAI,WAAW,CAAC,CAAC;AAAA,MAClC;AACA,YAAM,UAAU,CAAC,KAAK,UAAU;AAC9B,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,cAAc,GAAG;AAClC,eAAO,QAAQ,IAAI;AAAA,MACrB;AACA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,eAAe,IAAI;AACpC,eAAO,QAAQ,IAAI;AAAA,MACrB;AACA,YAAM,UAAU,UAAQ;AACtB,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AACA,eAAO,EAAE,KAAK,KAAK;AAAA,MACrB;AACA,YAAM,YAAY,CAAC,QAAQ,GAAG,MAAM,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,OAAO;AAChG,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,KAAK,CAAC,SAAS,aAAa;AAChC,cAAM,MAAM,QAAQ;AACpB,YAAI,IAAI,aAAa,SAAS;AAC5B,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM,OAAO;AACb,cAAI,KAAK,YAAY,QAAW;AAC9B,mBAAO,KAAK,QAAQ,QAAQ;AAAA,UAC9B,WAAW,KAAK,sBAAsB,QAAW;AAC/C,mBAAO,KAAK,kBAAkB,QAAQ;AAAA,UACxC,WAAW,KAAK,0BAA0B,QAAW;AACnD,mBAAO,KAAK,sBAAsB,QAAQ;AAAA,UAC5C,WAAW,KAAK,uBAAuB,QAAW;AAChD,mBAAO,KAAK,mBAAmB,QAAQ;AAAA,UACzC,OAAO;AACL,kBAAM,IAAI,MAAM,gCAAgC;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AAEA,aAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAElE,YAAM,OAAO,aAAW;AACtB,cAAM,IAAI,QAAQ,IAAI;AACtB,eAAO,EAAE,YAAY;AAAA,MACvB;AACA,YAAM,OAAO,aAAW,QAAQ,IAAI;AACpC,YAAM,SAAS,OAAK,aAAW,KAAK,OAAO,MAAM;AACjD,YAAM,YAAY,OAAO,OAAO;AAChC,YAAM,SAAS,OAAO,IAAI;AAC1B,YAAM,aAAa,OAAO,QAAQ;AAClC,YAAM,qBAAqB,OAAO,iBAAiB;AACnD,YAAM,QAAQ,SAAO,OAAK,UAAU,CAAC,KAAK,KAAK,CAAC,MAAM;AAEtD,YAAM,QAAQ,aAAW,aAAa,QAAQ,QAAQ,IAAI,aAAa;AACvE,YAAM,kBAAkB,SAAO,WAAW,GAAG,IAAI,MAAM,MAAM,GAAG;AAChE,YAAM,SAAS,aAAW,SAAS,KAAK,QAAQ,IAAI,UAAU,EAAE,IAAI,aAAa,OAAO;AACxF,YAAM,aAAa,aAAW,IAAI,QAAQ,IAAI,YAAY,aAAa,OAAO;AAE9E,YAAM,SAAS,CAAC,KAAK,KAAK,UAAU;AAClC,YAAI,SAAS,KAAK,KAAK,UAAU,KAAK,KAAK,SAAS,KAAK,GAAG;AAC1D,cAAI,aAAa,KAAK,QAAQ,EAAE;AAAA,QAClC,OAAO;AACL,kBAAQ,MAAM,uCAAuC,KAAK,aAAa,OAAO,eAAe,GAAG;AAChG,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QAClD;AAAA,MACF;AACA,YAAM,MAAM,CAAC,SAAS,KAAK,UAAU;AACnC,eAAO,QAAQ,KAAK,KAAK,KAAK;AAAA,MAChC;AACA,YAAM,SAAS,CAAC,SAAS,QAAQ;AAC/B,gBAAQ,IAAI,gBAAgB,GAAG;AAAA,MACjC;AAEA,YAAM,eAAe,SAAO,mBAAmB,GAAG,KAAK,cAAc,IAAI,IAAI,IAAI;AACjF,YAAM,YAAY,WAAW,QAAQ,UAAU,YAAY,KAAK,WAAW,KAAK,UAAU,WAAW;AACrG,YAAM,cAAc,YAAY,OAAK,aAAa,QAAQ,EAAE,IAAI,YAAY,CAAC,IAAI;AACjF,YAAM,gBAAgB,OAAK;AACzB,cAAM,IAAI,YAAY,CAAC;AACvB,eAAO,aAAa,CAAC,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK;AAAA,MAC5D;AACA,YAAM,gBAAgB,OAAK,aAAa,QAAQ,EAAE,IAAI,IAAI;AAE1D,YAAM,SAAS,aAAW;AACxB,cAAM,MAAM,OAAO,OAAO,IAAI,QAAQ,IAAI,aAAa,QAAQ;AAC/D,YAAI,QAAQ,UAAa,QAAQ,QAAQ,IAAI,kBAAkB,MAAM;AACnE,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,IAAI;AAChB,eAAO,cAAc,aAAa,QAAQ,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG,SAAS,QAAQ,aAAa,CAAC;AAAA,MACpH;AAEA,YAAM,aAAa,CAAC,OAAO,WAAW,WAAW;AAC/C,YAAI,UAAU,MAAM;AACpB,cAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,eAAO,QAAQ,YAAY;AACzB,oBAAU,QAAQ;AAClB,gBAAM,KAAK,aAAa,QAAQ,OAAO;AACvC,cAAI,UAAU,EAAE,GAAG;AACjB,mBAAO,SAAS,KAAK,EAAE;AAAA,UACzB,WAAW,KAAK,EAAE,GAAG;AACnB;AAAA,UACF;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AAEA,YAAM,WAAW,CAAC,OAAO,UAAU,WAAW,WAAW,OAAO,OAAK,GAAG,GAAG,QAAQ,GAAG,MAAM;AAE5F,YAAM,cAAc,SAAO,IAAI,UAAU,UAAa,WAAW,IAAI,MAAM,gBAAgB;AAE3F,YAAM,MAAM,CAAC,SAAS,aAAa;AACjC,cAAM,MAAM,QAAQ;AACpB,cAAM,SAAS,OAAO,iBAAiB,GAAG;AAC1C,cAAM,IAAI,OAAO,iBAAiB,QAAQ;AAC1C,eAAO,MAAM,MAAM,CAAC,OAAO,OAAO,IAAI,kBAAkB,KAAK,QAAQ,IAAI;AAAA,MAC3E;AACA,YAAM,oBAAoB,CAAC,KAAK,aAAa,YAAY,GAAG,IAAI,IAAI,MAAM,iBAAiB,QAAQ,IAAI;AAEvG,YAAM,eAAe,aAAW,IAAI,SAAS,WAAW,MAAM,QAAQ,QAAQ;AAE9E,YAAM,aAAa,CAAC,OAAO,cAAc,OAAO,WAAW,KAAK,GAAG,SAAS;AAE5E,YAAM,WAAW,CAAC,OAAO,aAAa,WAAW,OAAO,OAAK,GAAG,GAAG,QAAQ,CAAC;AAE5E,YAAM,mBAAmB,aAAW,OAAO,OAAO,EAAE,OAAO,SAAS;AACpE,YAAM,qBAAqB,CAAC,SAASC,gBAAe;AAClD,cAAM,oBAAoBA,cAAa,SAAS,SAAS,OAAO,IAAI,SAAS,KAAK,OAAO;AACzF,eAAO,kBAAkB,MAAM,OAAO;AAAA,MACxC;AACA,YAAM,aAAa,MAAM,IAAI;AAC7B,YAAM,mBAAmB,CAAC,KAAK,QAAQ,QAAQ;AAC7C,aAAK,QAAQ,WAAS;AACpB,gBAAM,eAAe,aAAa,QAAQ,KAAK;AAC/C,gBAAM,yBAAyB,WAAW,YAAY;AACtD,gBAAM,kBAAkB,mBAAmB,cAAc,sBAAsB;AAC/E,gBAAM,wBAAwB,iBAAiB,eAAe;AAC9D,gCAAsB,KAAK,CAAAC,YAAU;AACnC,gBAAI,SAAS,gBAAgB,KAAK,aAAa,IAAI;AACnD,kBAAM,kBAAkB,aAAaA,OAAM;AAC3C,gBAAI,oBAAoB,KAAK;AAC3B,qBAAO,iBAAiB,KAAK;AAAA,YAC/B,OAAO;AACL,kBAAI,iBAAiB,OAAO,GAAG;AAAA,YACjC;AACA,gBAAI,aAAa,eAAe,MAAM,KAAK;AACzC,kBAAI,SAAS,gBAAgB,KAAK,aAAa,GAAG;AAAA,YACpD;AACA,gBAAI,wBAAwB;AAC1B,oBAAM,YAAY,SAAS,iBAAiB,mBAAmB;AAC/D,mBAAK,WAAW,cAAY;AAC1B,uBAAO,UAAU,KAAK;AACtB,oBAAI,SAAS,SAAS,KAAK,aAAa,IAAI;AAAA,cAC9C,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,SAAS,CAAC,QAAQ,QAAQ;AAC9B,YAAI,OAAO,UAAU,WAAW,GAAG;AACjC,2BAAiB,OAAO,KAAK,OAAO,UAAU,kBAAkB,GAAG,GAAG;AACtE,iBAAO,YAAY;AAAA,QACrB;AAAA,MACF;AAEA,YAAM,aAAa,YAAU;AAC3B,eAAO,WAAW,mBAAmB,MAAM;AACzC,iBAAO,QAAQ,KAAK;AAAA,QACtB,CAAC;AACD,eAAO,WAAW,mBAAmB,MAAM;AACzC,iBAAO,QAAQ,KAAK;AAAA,QACtB,CAAC;AAAA,MACH;AAEA,YAAM,uBAAuB,CAAC,QAAQ,QAAQ,SAAO;AACnD,cAAM,oBAAoB,OAAK;AAC7B,gBAAM,UAAU,aAAa,QAAQ,EAAE,OAAO;AAC9C,cAAI,UAAU,aAAa,OAAO,MAAM,GAAG;AAC3C,cAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,QAC9C;AACA,eAAO,GAAG,cAAc,iBAAiB;AACzC,YAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAC5C,eAAO,MAAM,OAAO,IAAI,cAAc,iBAAiB;AAAA,MACzD;AACA,YAAM,WAAW,YAAU;AACzB,eAAO,GAAG,SAAS,gBAAgB,OAAO;AAAA,UACxC,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAU,MAAM,OAAO,YAAY,iBAAiB;AAAA,UACpD,SAAS,qBAAqB,QAAQ,KAAK;AAAA,QAC7C,CAAC;AACD,eAAO,GAAG,SAAS,gBAAgB,OAAO;AAAA,UACxC,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAU,MAAM,OAAO,YAAY,iBAAiB;AAAA,UACpD,SAAS,qBAAqB,QAAQ,KAAK;AAAA,QAC7C,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,eAAO,IAAI,kBAAkB,YAAU;AACrC,qBAAW,MAAM;AACjB,mBAAS,MAAM;AAAA,QACjB,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;ACpYH;", "names": ["type", "isListItem", "parent"]}