{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/quickbars/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/quickbars/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const isString = isType('string');\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isFunction = isSimpleType('function');\n\n    const option = name => editor => editor.options.get(name);\n    const register = editor => {\n      const registerOption = editor.options.register;\n      const toolbarProcessor = defaultValue => value => {\n        const valid = isBoolean(value) || isString(value);\n        if (valid) {\n          if (isBoolean(value)) {\n            return {\n              value: value ? defaultValue : '',\n              valid\n            };\n          } else {\n            return {\n              value: value.trim(),\n              valid\n            };\n          }\n        } else {\n          return {\n            valid: false,\n            message: 'Must be a boolean or string.'\n          };\n        }\n      };\n      const defaultSelectionToolbar = 'bold italic | quicklink h2 h3 blockquote';\n      registerOption('quickbars_selection_toolbar', {\n        processor: toolbarProcessor(defaultSelectionToolbar),\n        default: defaultSelectionToolbar\n      });\n      const defaultInsertToolbar = 'quickimage quicktable';\n      registerOption('quickbars_insert_toolbar', {\n        processor: toolbarProcessor(defaultInsertToolbar),\n        default: defaultInsertToolbar\n      });\n      const defaultImageToolbar = 'alignleft aligncenter alignright';\n      registerOption('quickbars_image_toolbar', {\n        processor: toolbarProcessor(defaultImageToolbar),\n        default: defaultImageToolbar\n      });\n    };\n    const getTextSelectionToolbarItems = option('quickbars_selection_toolbar');\n    const getInsertToolbarItems = option('quickbars_insert_toolbar');\n    const getImageToolbarItems = option('quickbars_image_toolbar');\n\n    let unique = 0;\n    const generate = prefix => {\n      const date = new Date();\n      const time = date.getTime();\n      const random = Math.floor(Math.random() * 1000000000);\n      unique++;\n      return prefix + '_' + random + unique + String(time);\n    };\n\n    const insertTable = (editor, columns, rows) => {\n      editor.execCommand('mceInsertTable', false, {\n        rows,\n        columns\n      });\n    };\n    const insertBlob = (editor, base64, blob) => {\n      const blobCache = editor.editorUpload.blobCache;\n      const blobInfo = blobCache.create(generate('mceu'), blob, base64);\n      blobCache.add(blobInfo);\n      editor.insertContent(editor.dom.createHTML('img', { src: blobInfo.blobUri() }));\n    };\n\n    const blobToBase64 = blob => {\n      return new Promise(resolve => {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n          resolve(reader.result.split(',')[1]);\n        };\n        reader.readAsDataURL(blob);\n      });\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    const pickFile = editor => new Promise(resolve => {\n      let resolved = false;\n      const fileInput = document.createElement('input');\n      fileInput.type = 'file';\n      fileInput.accept = 'image/*';\n      fileInput.style.position = 'fixed';\n      fileInput.style.left = '0';\n      fileInput.style.top = '0';\n      fileInput.style.opacity = '0.001';\n      document.body.appendChild(fileInput);\n      const resolveFileInput = value => {\n        var _a;\n        if (!resolved) {\n          (_a = fileInput.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(fileInput);\n          resolved = true;\n          resolve(value);\n        }\n      };\n      const changeHandler = e => {\n        resolveFileInput(Array.prototype.slice.call(e.target.files));\n      };\n      fileInput.addEventListener('input', changeHandler);\n      fileInput.addEventListener('change', changeHandler);\n      const cancelHandler = e => {\n        const cleanup = () => {\n          resolveFileInput([]);\n        };\n        if (!resolved) {\n          if (e.type === 'focusin') {\n            global.setEditorTimeout(editor, cleanup, 1000);\n          } else {\n            cleanup();\n          }\n        }\n        editor.off('focusin remove', cancelHandler);\n      };\n      editor.on('focusin remove', cancelHandler);\n      fileInput.click();\n    });\n\n    const setupButtons = editor => {\n      editor.ui.registry.addButton('quickimage', {\n        icon: 'image',\n        tooltip: 'Insert image',\n        onAction: () => {\n          pickFile(editor).then(files => {\n            if (files.length > 0) {\n              const blob = files[0];\n              blobToBase64(blob).then(base64 => {\n                insertBlob(editor, base64, blob);\n              });\n            }\n          });\n        }\n      });\n      editor.ui.registry.addButton('quicktable', {\n        icon: 'table',\n        tooltip: 'Insert table',\n        onAction: () => {\n          insertTable(editor, 2, 2);\n        }\n      });\n    };\n\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    const never = constant(false);\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const ELEMENT = 1;\n\n    const name = element => {\n      const r = element.dom.nodeName;\n      return r.toLowerCase();\n    };\n\n    const has = (element, key) => {\n      const dom = element.dom;\n      return dom && dom.hasAttribute ? dom.hasAttribute(key) : false;\n    };\n\n    var ClosestOrAncestor = (is, ancestor, scope, a, isRoot) => {\n      if (is(scope, a)) {\n        return Optional.some(scope);\n      } else if (isFunction(isRoot) && isRoot(scope)) {\n        return Optional.none();\n      } else {\n        return ancestor(scope, a, isRoot);\n      }\n    };\n\n    const fromHtml = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        const message = 'HTML does not have a single root node';\n        console.error(message, html);\n        throw new Error(message);\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n      const doc = scope || document;\n      const node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    const fromText = (text, scope) => {\n      const doc = scope || document;\n      const node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    const fromDom = node => {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    const SugarElement = {\n      fromHtml,\n      fromTag,\n      fromText,\n      fromDom,\n      fromPoint\n    };\n\n    const is = (element, selector) => {\n      const dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        const elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n\n    const ancestor$1 = (scope, predicate, isRoot) => {\n      let element = scope.dom;\n      const stop = isFunction(isRoot) ? isRoot : never;\n      while (element.parentNode) {\n        element = element.parentNode;\n        const el = SugarElement.fromDom(element);\n        if (predicate(el)) {\n          return Optional.some(el);\n        } else if (stop(el)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    const closest$2 = (scope, predicate, isRoot) => {\n      const is = (s, test) => test(s);\n      return ClosestOrAncestor(is, ancestor$1, scope, predicate, isRoot);\n    };\n\n    const closest$1 = (scope, predicate, isRoot) => closest$2(scope, predicate, isRoot).isSome();\n\n    const ancestor = (scope, selector, isRoot) => ancestor$1(scope, e => is(e, selector), isRoot);\n    const closest = (scope, selector, isRoot) => {\n      const is$1 = (element, selector) => is(element, selector);\n      return ClosestOrAncestor(is$1, ancestor, scope, selector, isRoot);\n    };\n\n    const addToEditor$1 = editor => {\n      const insertToolbarItems = getInsertToolbarItems(editor);\n      if (insertToolbarItems.length > 0) {\n        editor.ui.registry.addContextToolbar('quickblock', {\n          predicate: node => {\n            const sugarNode = SugarElement.fromDom(node);\n            const textBlockElementsMap = editor.schema.getTextBlockElements();\n            const isRoot = elem => elem.dom === editor.getBody();\n            return !has(sugarNode, 'data-mce-bogus') && closest(sugarNode, 'table,[data-mce-bogus=\"all\"]', isRoot).fold(() => closest$1(sugarNode, elem => name(elem) in textBlockElementsMap && editor.dom.isEmpty(elem.dom), isRoot), never);\n          },\n          items: insertToolbarItems,\n          position: 'line',\n          scope: 'editor'\n        });\n      }\n    };\n\n    const addToEditor = editor => {\n      const isEditable = node => editor.dom.isEditable(node);\n      const isInEditableContext = el => isEditable(el.parentElement);\n      const isImage = node => node.nodeName === 'IMG' || node.nodeName === 'FIGURE' && /image/i.test(node.className) && isInEditableContext(node);\n      const imageToolbarItems = getImageToolbarItems(editor);\n      if (imageToolbarItems.length > 0) {\n        editor.ui.registry.addContextToolbar('imageselection', {\n          predicate: isImage,\n          items: imageToolbarItems,\n          position: 'node'\n        });\n      }\n      const textToolbarItems = getTextSelectionToolbarItems(editor);\n      if (textToolbarItems.length > 0) {\n        editor.ui.registry.addContextToolbar('textselection', {\n          predicate: node => !isImage(node) && !editor.selection.isCollapsed() && isEditable(node),\n          items: textToolbarItems,\n          position: 'selection',\n          scope: 'editor'\n        });\n      }\n    };\n\n    var Plugin = () => {\n      global$1.add('quickbars', editor => {\n        register(editor);\n        setupButtons(editor);\n        addToEditor$1(editor);\n        addToEditor(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"quickbars\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/quickbars')\n//   ES2015:\n//     import 'tinymce/plugins/quickbars'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,YAAI;AACJ,YAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,iBAAO;AAAA,QACT,OAAO;AACL,mBAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,QAC7F;AAAA,MACF;AACA,YAAM,SAAS,OAAK;AAClB,cAAM,IAAI,OAAO;AACjB,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,SAAS,UAAQ,WAAS,OAAO,KAAK,MAAM;AAClD,YAAM,eAAe,UAAQ,WAAS,OAAO,UAAU;AACvD,YAAM,WAAW,OAAO,QAAQ;AAChC,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,YAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,YAAM,aAAa,aAAa,UAAU;AAE1C,YAAM,SAAS,CAAAA,UAAQ,YAAU,OAAO,QAAQ,IAAIA,KAAI;AACxD,YAAM,WAAW,YAAU;AACzB,cAAM,iBAAiB,OAAO,QAAQ;AACtC,cAAM,mBAAmB,kBAAgB,WAAS;AAChD,gBAAM,QAAQ,UAAU,KAAK,KAAK,SAAS,KAAK;AAChD,cAAI,OAAO;AACT,gBAAI,UAAU,KAAK,GAAG;AACpB,qBAAO;AAAA,gBACL,OAAO,QAAQ,eAAe;AAAA,gBAC9B;AAAA,cACF;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,gBACL,OAAO,MAAM,KAAK;AAAA,gBAClB;AAAA,cACF;AAAA,YACF;AAAA,UACF,OAAO;AACL,mBAAO;AAAA,cACL,OAAO;AAAA,cACP,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AACA,cAAM,0BAA0B;AAChC,uBAAe,+BAA+B;AAAA,UAC5C,WAAW,iBAAiB,uBAAuB;AAAA,UACnD,SAAS;AAAA,QACX,CAAC;AACD,cAAM,uBAAuB;AAC7B,uBAAe,4BAA4B;AAAA,UACzC,WAAW,iBAAiB,oBAAoB;AAAA,UAChD,SAAS;AAAA,QACX,CAAC;AACD,cAAM,sBAAsB;AAC5B,uBAAe,2BAA2B;AAAA,UACxC,WAAW,iBAAiB,mBAAmB;AAAA,UAC/C,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,+BAA+B,OAAO,6BAA6B;AACzE,YAAM,wBAAwB,OAAO,0BAA0B;AAC/D,YAAM,uBAAuB,OAAO,yBAAyB;AAE7D,UAAI,SAAS;AACb,YAAM,WAAW,YAAU;AACzB,cAAM,OAAO,oBAAI,KAAK;AACtB,cAAM,OAAO,KAAK,QAAQ;AAC1B,cAAM,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,GAAU;AACpD;AACA,eAAO,SAAS,MAAM,SAAS,SAAS,OAAO,IAAI;AAAA,MACrD;AAEA,YAAM,cAAc,CAAC,QAAQ,SAAS,SAAS;AAC7C,eAAO,YAAY,kBAAkB,OAAO;AAAA,UAC1C;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,aAAa,CAAC,QAAQ,QAAQ,SAAS;AAC3C,cAAM,YAAY,OAAO,aAAa;AACtC,cAAM,WAAW,UAAU,OAAO,SAAS,MAAM,GAAG,MAAM,MAAM;AAChE,kBAAU,IAAI,QAAQ;AACtB,eAAO,cAAc,OAAO,IAAI,WAAW,OAAO,EAAE,KAAK,SAAS,QAAQ,EAAE,CAAC,CAAC;AAAA,MAChF;AAEA,YAAM,eAAe,UAAQ;AAC3B,eAAO,IAAI,QAAQ,aAAW;AAC5B,gBAAM,SAAS,IAAI,WAAW;AAC9B,iBAAO,YAAY,MAAM;AACvB,oBAAQ,OAAO,OAAO,MAAM,GAAG,EAAE,CAAC,CAAC;AAAA,UACrC;AACA,iBAAO,cAAc,IAAI;AAAA,QAC3B,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE5D,YAAM,WAAW,YAAU,IAAI,QAAQ,aAAW;AAChD,YAAI,WAAW;AACf,cAAM,YAAY,SAAS,cAAc,OAAO;AAChD,kBAAU,OAAO;AACjB,kBAAU,SAAS;AACnB,kBAAU,MAAM,WAAW;AAC3B,kBAAU,MAAM,OAAO;AACvB,kBAAU,MAAM,MAAM;AACtB,kBAAU,MAAM,UAAU;AAC1B,iBAAS,KAAK,YAAY,SAAS;AACnC,cAAM,mBAAmB,WAAS;AAChC,cAAI;AACJ,cAAI,CAAC,UAAU;AACb,aAAC,KAAK,UAAU,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,SAAS;AACzF,uBAAW;AACX,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF;AACA,cAAM,gBAAgB,OAAK;AACzB,2BAAiB,MAAM,UAAU,MAAM,KAAK,EAAE,OAAO,KAAK,CAAC;AAAA,QAC7D;AACA,kBAAU,iBAAiB,SAAS,aAAa;AACjD,kBAAU,iBAAiB,UAAU,aAAa;AAClD,cAAM,gBAAgB,OAAK;AACzB,gBAAM,UAAU,MAAM;AACpB,6BAAiB,CAAC,CAAC;AAAA,UACrB;AACA,cAAI,CAAC,UAAU;AACb,gBAAI,EAAE,SAAS,WAAW;AACxB,qBAAO,iBAAiB,QAAQ,SAAS,GAAI;AAAA,YAC/C,OAAO;AACL,sBAAQ;AAAA,YACV;AAAA,UACF;AACA,iBAAO,IAAI,kBAAkB,aAAa;AAAA,QAC5C;AACA,eAAO,GAAG,kBAAkB,aAAa;AACzC,kBAAU,MAAM;AAAA,MAClB,CAAC;AAED,YAAM,eAAe,YAAU;AAC7B,eAAO,GAAG,SAAS,UAAU,cAAc;AAAA,UACzC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,MAAM;AACd,qBAAS,MAAM,EAAE,KAAK,WAAS;AAC7B,kBAAI,MAAM,SAAS,GAAG;AACpB,sBAAM,OAAO,MAAM,CAAC;AACpB,6BAAa,IAAI,EAAE,KAAK,YAAU;AAChC,6BAAW,QAAQ,QAAQ,IAAI;AAAA,gBACjC,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AACD,eAAO,GAAG,SAAS,UAAU,cAAc;AAAA,UACzC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,MAAM;AACd,wBAAY,QAAQ,GAAG,CAAC;AAAA,UAC1B;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,WAAS;AACxB,eAAO,MAAM;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,QAAQ,SAAS,KAAK;AAAA,MAE5B,MAAM,SAAS;AAAA,QACb,YAAY,KAAK,OAAO;AACtB,eAAK,MAAM;AACX,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,IAAI,SAAS,MAAM,KAAK;AAAA,QACjC;AAAA,QACA,OAAO,OAAO;AACZ,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,KAAK,QAAQ,QAAQ;AACnB,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,QACA,SAAS;AACP,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,SAAS;AACP,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA,IAAI,QAAQ;AACV,cAAI,KAAK,KAAK;AACZ,mBAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,UACzC,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QACzC;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QAC1C;AAAA,QACA,OAAO,WAAW;AAChB,cAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,MAAM,aAAa;AACjB,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,GAAG,aAAa;AACd,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B;AAAA,QACA,WAAW,OAAO;AAChB,iBAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,QACvC;AAAA,QACA,QAAQ,OAAO;AACb,iBAAO,KAAK,MAAM,OAAO,MAAM;AAAA,QACjC;AAAA,QACA,SAAS,SAAS;AAChB,cAAI,CAAC,KAAK,KAAK;AACb,kBAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,UAC9F,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,cAAc,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,QACrE;AAAA,QACA,YAAY;AACV,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,iBAAiB;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AACR,iBAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,aAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAElE,YAAM,UAAU;AAEhB,YAAM,OAAO,aAAW;AACtB,cAAM,IAAI,QAAQ,IAAI;AACtB,eAAO,EAAE,YAAY;AAAA,MACvB;AAEA,YAAM,MAAM,CAAC,SAAS,QAAQ;AAC5B,cAAM,MAAM,QAAQ;AACpB,eAAO,OAAO,IAAI,eAAe,IAAI,aAAa,GAAG,IAAI;AAAA,MAC3D;AAEA,UAAI,oBAAoB,CAACC,KAAIC,WAAU,OAAO,GAAG,WAAW;AAC1D,YAAID,IAAG,OAAO,CAAC,GAAG;AAChB,iBAAO,SAAS,KAAK,KAAK;AAAA,QAC5B,WAAW,WAAW,MAAM,KAAK,OAAO,KAAK,GAAG;AAC9C,iBAAO,SAAS,KAAK;AAAA,QACvB,OAAO;AACL,iBAAOC,UAAS,OAAO,GAAG,MAAM;AAAA,QAClC;AAAA,MACF;AAEA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,IAAI,cAAc,KAAK;AACnC,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,cAAc,KAAK,IAAI,WAAW,SAAS,GAAG;AACrD,gBAAM,UAAU;AAChB,kBAAQ,MAAM,SAAS,IAAI;AAC3B,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB;AACA,eAAO,QAAQ,IAAI,WAAW,CAAC,CAAC;AAAA,MAClC;AACA,YAAM,UAAU,CAAC,KAAK,UAAU;AAC9B,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,cAAc,GAAG;AAClC,eAAO,QAAQ,IAAI;AAAA,MACrB;AACA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,eAAe,IAAI;AACpC,eAAO,QAAQ,IAAI;AAAA,MACrB;AACA,YAAM,UAAU,UAAQ;AACtB,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AACA,eAAO,EAAE,KAAK,KAAK;AAAA,MACrB;AACA,YAAM,YAAY,CAAC,QAAQ,GAAG,MAAM,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,OAAO;AAChG,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,KAAK,CAAC,SAAS,aAAa;AAChC,cAAM,MAAM,QAAQ;AACpB,YAAI,IAAI,aAAa,SAAS;AAC5B,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM,OAAO;AACb,cAAI,KAAK,YAAY,QAAW;AAC9B,mBAAO,KAAK,QAAQ,QAAQ;AAAA,UAC9B,WAAW,KAAK,sBAAsB,QAAW;AAC/C,mBAAO,KAAK,kBAAkB,QAAQ;AAAA,UACxC,WAAW,KAAK,0BAA0B,QAAW;AACnD,mBAAO,KAAK,sBAAsB,QAAQ;AAAA,UAC5C,WAAW,KAAK,uBAAuB,QAAW;AAChD,mBAAO,KAAK,mBAAmB,QAAQ;AAAA,UACzC,OAAO;AACL,kBAAM,IAAI,MAAM,gCAAgC;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AAEA,YAAM,aAAa,CAAC,OAAO,WAAW,WAAW;AAC/C,YAAI,UAAU,MAAM;AACpB,cAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,eAAO,QAAQ,YAAY;AACzB,oBAAU,QAAQ;AAClB,gBAAM,KAAK,aAAa,QAAQ,OAAO;AACvC,cAAI,UAAU,EAAE,GAAG;AACjB,mBAAO,SAAS,KAAK,EAAE;AAAA,UACzB,WAAW,KAAK,EAAE,GAAG;AACnB;AAAA,UACF;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,YAAY,CAAC,OAAO,WAAW,WAAW;AAC9C,cAAMD,MAAK,CAAC,GAAG,SAAS,KAAK,CAAC;AAC9B,eAAO,kBAAkBA,KAAI,YAAY,OAAO,WAAW,MAAM;AAAA,MACnE;AAEA,YAAM,YAAY,CAAC,OAAO,WAAW,WAAW,UAAU,OAAO,WAAW,MAAM,EAAE,OAAO;AAE3F,YAAM,WAAW,CAAC,OAAO,UAAU,WAAW,WAAW,OAAO,OAAK,GAAG,GAAG,QAAQ,GAAG,MAAM;AAC5F,YAAM,UAAU,CAAC,OAAO,UAAU,WAAW;AAC3C,cAAM,OAAO,CAAC,SAASE,cAAa,GAAG,SAASA,SAAQ;AACxD,eAAO,kBAAkB,MAAM,UAAU,OAAO,UAAU,MAAM;AAAA,MAClE;AAEA,YAAM,gBAAgB,YAAU;AAC9B,cAAM,qBAAqB,sBAAsB,MAAM;AACvD,YAAI,mBAAmB,SAAS,GAAG;AACjC,iBAAO,GAAG,SAAS,kBAAkB,cAAc;AAAA,YACjD,WAAW,UAAQ;AACjB,oBAAM,YAAY,aAAa,QAAQ,IAAI;AAC3C,oBAAM,uBAAuB,OAAO,OAAO,qBAAqB;AAChE,oBAAM,SAAS,UAAQ,KAAK,QAAQ,OAAO,QAAQ;AACnD,qBAAO,CAAC,IAAI,WAAW,gBAAgB,KAAK,QAAQ,WAAW,gCAAgC,MAAM,EAAE,KAAK,MAAM,UAAU,WAAW,UAAQ,KAAK,IAAI,KAAK,wBAAwB,OAAO,IAAI,QAAQ,KAAK,GAAG,GAAG,MAAM,GAAG,KAAK;AAAA,YACnO;AAAA,YACA,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,cAAc,YAAU;AAC5B,cAAM,aAAa,UAAQ,OAAO,IAAI,WAAW,IAAI;AACrD,cAAM,sBAAsB,QAAM,WAAW,GAAG,aAAa;AAC7D,cAAM,UAAU,UAAQ,KAAK,aAAa,SAAS,KAAK,aAAa,YAAY,SAAS,KAAK,KAAK,SAAS,KAAK,oBAAoB,IAAI;AAC1I,cAAM,oBAAoB,qBAAqB,MAAM;AACrD,YAAI,kBAAkB,SAAS,GAAG;AAChC,iBAAO,GAAG,SAAS,kBAAkB,kBAAkB;AAAA,YACrD,WAAW;AAAA,YACX,OAAO;AAAA,YACP,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AACA,cAAM,mBAAmB,6BAA6B,MAAM;AAC5D,YAAI,iBAAiB,SAAS,GAAG;AAC/B,iBAAO,GAAG,SAAS,kBAAkB,iBAAiB;AAAA,YACpD,WAAW,UAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,UAAU,YAAY,KAAK,WAAW,IAAI;AAAA,YACvF,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,aAAa,YAAU;AAClC,mBAAS,MAAM;AACf,uBAAa,MAAM;AACnB,wBAAc,MAAM;AACpB,sBAAY,MAAM;AAAA,QACpB,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;AC/aH;", "names": ["name", "is", "ancestor", "selector"]}