{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/accordion/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/accordion/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    let unique = 0;\n    const generate = prefix => {\n      const date = new Date();\n      const time = date.getTime();\n      const random = Math.floor(Math.random() * 1000000000);\n      unique++;\n      return prefix + '_' + random + unique + String(time);\n    };\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType$1 = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const isString = isType$1('string');\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n\n    const compose1 = (fbc, fab) => a => fbc(fab(a));\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    const tripleEquals = (a, b) => {\n      return a === b;\n    };\n    const never = constant(false);\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const nativeIndexOf = Array.prototype.indexOf;\n    const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);\n    const contains = (xs, x) => rawIndexOf(xs, x) > -1;\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each$1 = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const filter = (xs, pred) => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n    const foldl = (xs, f, acc) => {\n      each$1(xs, (x, i) => {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n\n    const keys = Object.keys;\n    const each = (obj, f) => {\n      const props = keys(obj);\n      for (let k = 0, len = props.length; k < len; k++) {\n        const i = props[k];\n        const x = obj[i];\n        f(x, i);\n      }\n    };\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const COMMENT = 8;\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const name = element => {\n      const r = element.dom.nodeName;\n      return r.toLowerCase();\n    };\n    const type = element => element.dom.nodeType;\n    const isType = t => element => type(element) === t;\n    const isComment = element => type(element) === COMMENT || name(element) === '#comment';\n    const isElement = isType(ELEMENT);\n    const isText = isType(TEXT);\n    const isDocument = isType(DOCUMENT);\n    const isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n\n    const rawSet = (dom, key, value) => {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    const set$2 = (element, key, value) => {\n      rawSet(element.dom, key, value);\n    };\n    const setAll = (element, attrs) => {\n      const dom = element.dom;\n      each(attrs, (v, k) => {\n        rawSet(dom, k, v);\n      });\n    };\n    const get$2 = (element, key) => {\n      const v = element.dom.getAttribute(key);\n      return v === null ? undefined : v;\n    };\n    const getOpt = (element, key) => Optional.from(get$2(element, key));\n    const remove$2 = (element, key) => {\n      element.dom.removeAttribute(key);\n    };\n    const clone = element => foldl(element.dom.attributes, (acc, attr) => {\n      acc[attr.name] = attr.value;\n      return acc;\n    }, {});\n\n    const fromHtml = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        const message = 'HTML does not have a single root node';\n        console.error(message, html);\n        throw new Error(message);\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n      const doc = scope || document;\n      const node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    const fromText = (text, scope) => {\n      const doc = scope || document;\n      const node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    const fromDom = node => {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    const SugarElement = {\n      fromHtml,\n      fromTag,\n      fromText,\n      fromDom,\n      fromPoint\n    };\n\n    const is$2 = (element, selector) => {\n      const dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        const elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n    const bypassSelector = dom => dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT || dom.childElementCount === 0;\n    const all = (selector, scope) => {\n      const base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? [] : map(base.querySelectorAll(selector), SugarElement.fromDom);\n    };\n    const one = (selector, scope) => {\n      const base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? Optional.none() : Optional.from(base.querySelector(selector)).map(SugarElement.fromDom);\n    };\n\n    const eq = (e1, e2) => e1.dom === e2.dom;\n    const is$1 = is$2;\n\n    const is = (lhs, rhs, comparator = tripleEquals) => lhs.exists(left => comparator(left, rhs));\n\n    const blank = r => s => s.replace(r, '');\n    const trim = blank(/^\\s+|\\s+$/g);\n\n    const isSupported = dom => dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n\n    const owner = element => SugarElement.fromDom(element.dom.ownerDocument);\n    const documentOrOwner = dos => isDocument(dos) ? dos : owner(dos);\n    const parent = element => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const parents = (element, isRoot) => {\n      const stop = isFunction(isRoot) ? isRoot : never;\n      let dom = element.dom;\n      const ret = [];\n      while (dom.parentNode !== null && dom.parentNode !== undefined) {\n        const rawParent = dom.parentNode;\n        const p = SugarElement.fromDom(rawParent);\n        ret.push(p);\n        if (stop(p) === true) {\n          break;\n        } else {\n          dom = rawParent;\n        }\n      }\n      return ret;\n    };\n    const prevSibling = element => Optional.from(element.dom.previousSibling).map(SugarElement.fromDom);\n    const nextSibling = element => Optional.from(element.dom.nextSibling).map(SugarElement.fromDom);\n    const children = element => map(element.dom.childNodes, SugarElement.fromDom);\n    const child = (element, index) => {\n      const cs = element.dom.childNodes;\n      return Optional.from(cs[index]).map(SugarElement.fromDom);\n    };\n    const firstChild = element => child(element, 0);\n\n    const isShadowRoot = dos => isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    const supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);\n    const getRootNode = supported ? e => SugarElement.fromDom(e.dom.getRootNode()) : documentOrOwner;\n    const getShadowRoot = e => {\n      const r = getRootNode(e);\n      return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    const getShadowHost = e => SugarElement.fromDom(e.dom.host);\n\n    const inBody = element => {\n      const dom = isText(element) ? element.dom.parentNode : element.dom;\n      if (dom === undefined || dom === null || dom.ownerDocument === null) {\n        return false;\n      }\n      const doc = dom.ownerDocument;\n      return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));\n    };\n\n    const internalSet = (dom, property, value) => {\n      if (!isString(value)) {\n        console.error('Invalid call to CSS.set. Property ', property, ':: Value ', value, ':: Element ', dom);\n        throw new Error('CSS value must be a string: ' + value);\n      }\n      if (isSupported(dom)) {\n        dom.style.setProperty(property, value);\n      }\n    };\n    const internalRemove = (dom, property) => {\n      if (isSupported(dom)) {\n        dom.style.removeProperty(property);\n      }\n    };\n    const set$1 = (element, property, value) => {\n      const dom = element.dom;\n      internalSet(dom, property, value);\n    };\n    const get$1 = (element, property) => {\n      const dom = element.dom;\n      const styles = window.getComputedStyle(dom);\n      const r = styles.getPropertyValue(property);\n      return r === '' && !inBody(element) ? getUnsafeProperty(dom, property) : r;\n    };\n    const getUnsafeProperty = (dom, property) => isSupported(dom) ? dom.style.getPropertyValue(property) : '';\n    const getRaw = (element, property) => {\n      const dom = element.dom;\n      const raw = getUnsafeProperty(dom, property);\n      return Optional.from(raw).filter(r => r.length > 0);\n    };\n    const remove$1 = (element, property) => {\n      const dom = element.dom;\n      internalRemove(dom, property);\n      if (is(getOpt(element, 'style').map(trim), '')) {\n        remove$2(element, 'style');\n      }\n    };\n\n    const before = (marker, element) => {\n      const parent$1 = parent(marker);\n      parent$1.each(v => {\n        v.dom.insertBefore(element.dom, marker.dom);\n      });\n    };\n    const after$1 = (marker, element) => {\n      const sibling = nextSibling(marker);\n      sibling.fold(() => {\n        const parent$1 = parent(marker);\n        parent$1.each(v => {\n          append$1(v, element);\n        });\n      }, v => {\n        before(v, element);\n      });\n    };\n    const prepend = (parent, element) => {\n      const firstChild$1 = firstChild(parent);\n      firstChild$1.fold(() => {\n        append$1(parent, element);\n      }, v => {\n        parent.dom.insertBefore(element.dom, v.dom);\n      });\n    };\n    const append$1 = (parent, element) => {\n      parent.dom.appendChild(element.dom);\n    };\n    const wrap = (element, wrapper) => {\n      before(element, wrapper);\n      append$1(wrapper, element);\n    };\n\n    const after = (marker, elements) => {\n      each$1(elements, (x, i) => {\n        const e = i === 0 ? marker : elements[i - 1];\n        after$1(e, x);\n      });\n    };\n    const append = (parent, elements) => {\n      each$1(elements, x => {\n        append$1(parent, x);\n      });\n    };\n\n    const descendants$1 = (scope, predicate) => {\n      let result = [];\n      each$1(children(scope), x => {\n        if (predicate(x)) {\n          result = result.concat([x]);\n        }\n        result = result.concat(descendants$1(x, predicate));\n      });\n      return result;\n    };\n\n    var ClosestOrAncestor = (is, ancestor, scope, a, isRoot) => {\n      if (is(scope, a)) {\n        return Optional.some(scope);\n      } else if (isFunction(isRoot) && isRoot(scope)) {\n        return Optional.none();\n      } else {\n        return ancestor(scope, a, isRoot);\n      }\n    };\n\n    const ancestor$1 = (scope, predicate, isRoot) => {\n      let element = scope.dom;\n      const stop = isFunction(isRoot) ? isRoot : never;\n      while (element.parentNode) {\n        element = element.parentNode;\n        const el = SugarElement.fromDom(element);\n        if (predicate(el)) {\n          return Optional.some(el);\n        } else if (stop(el)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n\n    const remove = element => {\n      const dom = element.dom;\n      if (dom.parentNode !== null) {\n        dom.parentNode.removeChild(dom);\n      }\n    };\n    const unwrap = wrapper => {\n      const children$1 = children(wrapper);\n      if (children$1.length > 0) {\n        after(wrapper, children$1);\n      }\n      remove(wrapper);\n    };\n\n    const descendants = (scope, selector) => all(selector, scope);\n\n    const ancestor = (scope, selector, isRoot) => ancestor$1(scope, e => is$2(e, selector), isRoot);\n    const descendant = (scope, selector) => one(selector, scope);\n    const closest = (scope, selector, isRoot) => {\n      const is = (element, selector) => is$2(element, selector);\n      return ClosestOrAncestor(is, ancestor, scope, selector, isRoot);\n    };\n\n    const NodeValue = (is, name) => {\n      const get = element => {\n        if (!is(element)) {\n          throw new Error('Can only get ' + name + ' value of a ' + name + ' node');\n        }\n        return getOption(element).getOr('');\n      };\n      const getOption = element => is(element) ? Optional.from(element.dom.nodeValue) : Optional.none();\n      const set = (element, value) => {\n        if (!is(element)) {\n          throw new Error('Can only set raw ' + name + ' value of a ' + name + ' node');\n        }\n        element.dom.nodeValue = value;\n      };\n      return {\n        get,\n        getOption,\n        set\n      };\n    };\n\n    const api = NodeValue(isText, 'text');\n    const get = element => api.get(element);\n    const set = (element, value) => api.set(element, value);\n\n    var TagBoundaries = [\n      'body',\n      'p',\n      'div',\n      'article',\n      'aside',\n      'figcaption',\n      'figure',\n      'footer',\n      'header',\n      'nav',\n      'section',\n      'ol',\n      'ul',\n      'li',\n      'table',\n      'thead',\n      'tbody',\n      'tfoot',\n      'caption',\n      'tr',\n      'td',\n      'th',\n      'h1',\n      'h2',\n      'h3',\n      'h4',\n      'h5',\n      'h6',\n      'blockquote',\n      'pre',\n      'address'\n    ];\n\n    var DomUniverse = () => {\n      const clone$1 = element => {\n        return SugarElement.fromDom(element.dom.cloneNode(false));\n      };\n      const document = element => documentOrOwner(element).dom;\n      const isBoundary = element => {\n        if (!isElement(element)) {\n          return false;\n        }\n        if (name(element) === 'body') {\n          return true;\n        }\n        return contains(TagBoundaries, name(element));\n      };\n      const isEmptyTag = element => {\n        if (!isElement(element)) {\n          return false;\n        }\n        return contains([\n          'br',\n          'img',\n          'hr',\n          'input'\n        ], name(element));\n      };\n      const isNonEditable = element => isElement(element) && get$2(element, 'contenteditable') === 'false';\n      const comparePosition = (element, other) => {\n        return element.dom.compareDocumentPosition(other.dom);\n      };\n      const copyAttributesTo = (source, destination) => {\n        const as = clone(source);\n        setAll(destination, as);\n      };\n      const isSpecial = element => {\n        const tag = name(element);\n        return contains([\n          'script',\n          'noscript',\n          'iframe',\n          'noframes',\n          'noembed',\n          'title',\n          'style',\n          'textarea',\n          'xmp'\n        ], tag);\n      };\n      const getLanguage = element => isElement(element) ? getOpt(element, 'lang') : Optional.none();\n      return {\n        up: constant({\n          selector: ancestor,\n          closest: closest,\n          predicate: ancestor$1,\n          all: parents\n        }),\n        down: constant({\n          selector: descendants,\n          predicate: descendants$1\n        }),\n        styles: constant({\n          get: get$1,\n          getRaw: getRaw,\n          set: set$1,\n          remove: remove$1\n        }),\n        attrs: constant({\n          get: get$2,\n          set: set$2,\n          remove: remove$2,\n          copyTo: copyAttributesTo\n        }),\n        insert: constant({\n          before: before,\n          after: after$1,\n          afterAll: after,\n          append: append$1,\n          appendAll: append,\n          prepend: prepend,\n          wrap: wrap\n        }),\n        remove: constant({\n          unwrap: unwrap,\n          remove: remove\n        }),\n        create: constant({\n          nu: SugarElement.fromTag,\n          clone: clone$1,\n          text: SugarElement.fromText\n        }),\n        query: constant({\n          comparePosition,\n          prevSibling: prevSibling,\n          nextSibling: nextSibling\n        }),\n        property: constant({\n          children: children,\n          name: name,\n          parent: parent,\n          document,\n          isText: isText,\n          isComment: isComment,\n          isElement: isElement,\n          isSpecial,\n          getLanguage,\n          getText: get,\n          setText: set,\n          isBoundary,\n          isEmptyTag,\n          isNonEditable\n        }),\n        eq: eq,\n        is: is$1\n      };\n    };\n\n    const point = (element, offset) => ({\n      element,\n      offset\n    });\n\n    const scan = (universe, element, direction) => {\n      if (universe.property().isText(element) && universe.property().getText(element).trim().length === 0 || universe.property().isComment(element)) {\n        return direction(element).bind(elem => {\n          return scan(universe, elem, direction).orThunk(() => {\n            return Optional.some(elem);\n          });\n        });\n      } else {\n        return Optional.none();\n      }\n    };\n    const toEnd = (universe, element) => {\n      if (universe.property().isText(element)) {\n        return universe.property().getText(element).length;\n      }\n      const children = universe.property().children(element);\n      return children.length;\n    };\n    const freefallRtl$2 = (universe, element) => {\n      const candidate = scan(universe, element, universe.query().prevSibling).getOr(element);\n      if (universe.property().isText(candidate)) {\n        return point(candidate, toEnd(universe, candidate));\n      }\n      const children = universe.property().children(candidate);\n      return children.length > 0 ? freefallRtl$2(universe, children[children.length - 1]) : point(candidate, toEnd(universe, candidate));\n    };\n\n    const freefallRtl$1 = freefallRtl$2;\n\n    const universe = DomUniverse();\n    const freefallRtl = element => {\n      return freefallRtl$1(universe, element);\n    };\n\n    const fireToggleAccordionEvent = (editor, element, state) => editor.dispatch('ToggledAccordion', {\n      element,\n      state\n    });\n    const fireToggleAllAccordionsEvent = (editor, elements, state) => editor.dispatch('ToggledAllAccordions', {\n      elements,\n      state\n    });\n\n    const accordionTag = 'details';\n    const accordionDetailsClass = 'mce-accordion';\n    const accordionSummaryClass = 'mce-accordion-summary';\n    const accordionBodyWrapperClass = 'mce-accordion-body';\n    const accordionBodyWrapperTag = 'div';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const isSummary = node => (node === null || node === void 0 ? void 0 : node.nodeName) === 'SUMMARY';\n    const isDetails = node => (node === null || node === void 0 ? void 0 : node.nodeName) === 'DETAILS';\n    const isOpen = details => details.hasAttribute('open');\n    const isInSummary = editor => {\n      const node = editor.selection.getNode();\n      return isSummary(node) || Boolean(editor.dom.getParent(node, isSummary));\n    };\n    const isInsertAllowed = editor => !isInSummary(editor) && editor.dom.isEditable(editor.selection.getNode());\n    const getSelectedDetails = editor => Optional.from(editor.dom.getParent(editor.selection.getNode(), isDetails));\n    const isDetailsSelected = editor => getSelectedDetails(editor).isSome();\n    const insertBogus = element => {\n      element.innerHTML = '<br data-mce-bogus=\"1\" />';\n      return element;\n    };\n    const createParagraph = editor => insertBogus(editor.dom.create('p'));\n    const createSummary = editor => insertBogus(editor.dom.create('summary'));\n    const insertAndSelectParagraphAfter = (editor, target) => {\n      const paragraph = createParagraph(editor);\n      target.insertAdjacentElement('afterend', paragraph);\n      editor.selection.setCursorLocation(paragraph, 0);\n    };\n    const normalizeContent = (editor, accordion) => {\n      if (isSummary(accordion === null || accordion === void 0 ? void 0 : accordion.lastChild)) {\n        const paragraph = createParagraph(editor);\n        accordion.appendChild(paragraph);\n        editor.selection.setCursorLocation(paragraph, 0);\n      }\n    };\n    const normalizeSummary = (editor, accordion) => {\n      if (!isSummary(accordion === null || accordion === void 0 ? void 0 : accordion.firstChild)) {\n        const summary = createSummary(editor);\n        accordion.prepend(summary);\n        editor.selection.setCursorLocation(summary, 0);\n      }\n    };\n    const normalizeAccordion = editor => accordion => {\n      normalizeContent(editor, accordion);\n      normalizeSummary(editor, accordion);\n    };\n    const normalizeDetails = editor => {\n      global$2.each(global$2.grep(editor.dom.select('details', editor.getBody())), normalizeAccordion(editor));\n    };\n\n    const insertAccordion = editor => {\n      if (!isInsertAllowed(editor)) {\n        return;\n      }\n      const editorBody = SugarElement.fromDom(editor.getBody());\n      const uid = generate('acc');\n      const summaryText = editor.dom.encode(editor.selection.getRng().toString() || editor.translate('Accordion summary...'));\n      const bodyText = editor.dom.encode(editor.translate('Accordion body...'));\n      const accordionSummaryHtml = `<summary class=\"${ accordionSummaryClass }\">${ summaryText }</summary>`;\n      const accordionBodyHtml = `<${ accordionBodyWrapperTag } class=\"${ accordionBodyWrapperClass }\"><p>${ bodyText }</p></${ accordionBodyWrapperTag }>`;\n      editor.undoManager.transact(() => {\n        editor.insertContent([\n          `<details data-mce-id=\"${ uid }\" class=\"${ accordionDetailsClass }\" open=\"open\">`,\n          accordionSummaryHtml,\n          accordionBodyHtml,\n          `</details>`\n        ].join(''));\n        descendant(editorBody, `[data-mce-id=\"${ uid }\"]`).each(detailsElm => {\n          remove$2(detailsElm, 'data-mce-id');\n          descendant(detailsElm, `summary`).each(summaryElm => {\n            const rng = editor.dom.createRng();\n            const des = freefallRtl(summaryElm);\n            rng.setStart(des.element.dom, des.offset);\n            rng.setEnd(des.element.dom, des.offset);\n            editor.selection.setRng(rng);\n          });\n        });\n      });\n    };\n    const toggleDetailsElement = (details, state) => {\n      const shouldOpen = state !== null && state !== void 0 ? state : !isOpen(details);\n      if (shouldOpen) {\n        details.setAttribute('open', 'open');\n      } else {\n        details.removeAttribute('open');\n      }\n      return shouldOpen;\n    };\n    const toggleAccordion = (editor, state) => {\n      getSelectedDetails(editor).each(details => {\n        fireToggleAccordionEvent(editor, details, toggleDetailsElement(details, state));\n      });\n    };\n    const removeAccordion = editor => {\n      getSelectedDetails(editor).each(details => {\n        const {nextSibling} = details;\n        if (nextSibling) {\n          editor.selection.select(nextSibling, true);\n          editor.selection.collapse(true);\n        } else {\n          insertAndSelectParagraphAfter(editor, details);\n        }\n        details.remove();\n      });\n    };\n    const toggleAllAccordions = (editor, state) => {\n      const accordions = Array.from(editor.getBody().querySelectorAll('details'));\n      if (accordions.length === 0) {\n        return;\n      }\n      each$1(accordions, accordion => toggleDetailsElement(accordion, state !== null && state !== void 0 ? state : !isOpen(accordion)));\n      fireToggleAllAccordionsEvent(editor, accordions, state);\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('InsertAccordion', () => insertAccordion(editor));\n      editor.addCommand('ToggleAccordion', (_ui, value) => toggleAccordion(editor, value));\n      editor.addCommand('ToggleAllAccordions', (_ui, value) => toggleAllAccordions(editor, value));\n      editor.addCommand('RemoveAccordion', () => removeAccordion(editor));\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.html.Node');\n\n    const getClassList = node => {\n      var _a, _b;\n      return (_b = (_a = node.attr('class')) === null || _a === void 0 ? void 0 : _a.split(' ')) !== null && _b !== void 0 ? _b : [];\n    };\n    const addClasses = (node, classes) => {\n      const classListSet = new Set([\n        ...getClassList(node),\n        ...classes\n      ]);\n      const newClassList = Array.from(classListSet);\n      if (newClassList.length > 0) {\n        node.attr('class', newClassList.join(' '));\n      }\n    };\n    const removeClasses = (node, classes) => {\n      const newClassList = filter(getClassList(node), clazz => !classes.has(clazz));\n      node.attr('class', newClassList.length > 0 ? newClassList.join(' ') : null);\n    };\n    const isAccordionDetailsNode = node => node.name === accordionTag && contains(getClassList(node), accordionDetailsClass);\n    const isAccordionBodyWrapperNode = node => node.name === accordionBodyWrapperTag && contains(getClassList(node), accordionBodyWrapperClass);\n    const getAccordionChildren = accordionNode => {\n      const children = accordionNode.children();\n      let summaryNode;\n      let wrapperNode;\n      const otherNodes = [];\n      for (let i = 0; i < children.length; i++) {\n        const child = children[i];\n        if (child.name === 'summary' && isNullable(summaryNode)) {\n          summaryNode = child;\n        } else if (isAccordionBodyWrapperNode(child) && isNullable(wrapperNode)) {\n          wrapperNode = child;\n        } else {\n          otherNodes.push(child);\n        }\n      }\n      return {\n        summaryNode,\n        wrapperNode,\n        otherNodes\n      };\n    };\n    const padInputNode = node => {\n      const br = new global$1('br', 1);\n      br.attr('data-mce-bogus', '1');\n      node.empty();\n      node.append(br);\n    };\n    const setup$1 = editor => {\n      editor.on('PreInit', () => {\n        const {serializer, parser} = editor;\n        parser.addNodeFilter(accordionTag, nodes => {\n          for (let i = 0; i < nodes.length; i++) {\n            const node = nodes[i];\n            if (isAccordionDetailsNode(node)) {\n              const accordionNode = node;\n              const {summaryNode, wrapperNode, otherNodes} = getAccordionChildren(accordionNode);\n              const hasSummaryNode = isNonNullable(summaryNode);\n              const newSummaryNode = hasSummaryNode ? summaryNode : new global$1('summary', 1);\n              if (isNullable(newSummaryNode.firstChild)) {\n                padInputNode(newSummaryNode);\n              }\n              addClasses(newSummaryNode, [accordionSummaryClass]);\n              if (!hasSummaryNode) {\n                if (isNonNullable(accordionNode.firstChild)) {\n                  accordionNode.insert(newSummaryNode, accordionNode.firstChild, true);\n                } else {\n                  accordionNode.append(newSummaryNode);\n                }\n              }\n              const hasWrapperNode = isNonNullable(wrapperNode);\n              const newWrapperNode = hasWrapperNode ? wrapperNode : new global$1(accordionBodyWrapperTag, 1);\n              addClasses(newWrapperNode, [accordionBodyWrapperClass]);\n              if (otherNodes.length > 0) {\n                for (let j = 0; j < otherNodes.length; j++) {\n                  const otherNode = otherNodes[j];\n                  newWrapperNode.append(otherNode);\n                }\n              }\n              if (isNullable(newWrapperNode.firstChild)) {\n                const pNode = new global$1('p', 1);\n                padInputNode(pNode);\n                newWrapperNode.append(pNode);\n              }\n              if (!hasWrapperNode) {\n                accordionNode.append(newWrapperNode);\n              }\n            }\n          }\n        });\n        serializer.addNodeFilter(accordionTag, nodes => {\n          const summaryClassRemoveSet = new Set([accordionSummaryClass]);\n          for (let i = 0; i < nodes.length; i++) {\n            const node = nodes[i];\n            if (isAccordionDetailsNode(node)) {\n              const accordionNode = node;\n              const {summaryNode, wrapperNode} = getAccordionChildren(accordionNode);\n              if (isNonNullable(summaryNode)) {\n                removeClasses(summaryNode, summaryClassRemoveSet);\n              }\n              if (isNonNullable(wrapperNode)) {\n                wrapperNode.unwrap();\n              }\n            }\n          }\n        });\n      });\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    const setupEnterKeyInSummary = editor => {\n      editor.on('keydown', event => {\n        if (event.shiftKey || event.keyCode !== global.ENTER || !isInSummary(editor)) {\n          return;\n        }\n        event.preventDefault();\n        editor.execCommand('ToggleAccordion');\n      });\n    };\n    const setup = editor => {\n      setupEnterKeyInSummary(editor);\n      editor.on('ExecCommand', e => {\n        const cmd = e.command.toLowerCase();\n        if ((cmd === 'delete' || cmd === 'forwarddelete') && isDetailsSelected(editor)) {\n          normalizeDetails(editor);\n        }\n      });\n    };\n\n    const onSetup = editor => buttonApi => {\n      const onNodeChange = () => buttonApi.setEnabled(isInsertAllowed(editor));\n      editor.on('NodeChange', onNodeChange);\n      return () => editor.off('NodeChange', onNodeChange);\n    };\n    const register = editor => {\n      const onAction = () => editor.execCommand('InsertAccordion');\n      editor.ui.registry.addButton('accordion', {\n        icon: 'accordion',\n        tooltip: 'Insert accordion',\n        onSetup: onSetup(editor),\n        onAction\n      });\n      editor.ui.registry.addMenuItem('accordion', {\n        icon: 'accordion',\n        text: 'Accordion',\n        onSetup: onSetup(editor),\n        onAction\n      });\n      editor.ui.registry.addToggleButton('accordiontoggle', {\n        icon: 'accordion-toggle',\n        tooltip: 'Toggle accordion',\n        onAction: () => editor.execCommand('ToggleAccordion')\n      });\n      editor.ui.registry.addToggleButton('accordionremove', {\n        icon: 'remove',\n        tooltip: 'Delete accordion',\n        onAction: () => editor.execCommand('RemoveAccordion')\n      });\n      editor.ui.registry.addContextToolbar('accordion', {\n        predicate: accordion => editor.dom.is(accordion, 'details') && editor.getBody().contains(accordion) && editor.dom.isEditable(accordion.parentNode),\n        items: 'accordiontoggle accordionremove',\n        scope: 'node',\n        position: 'node'\n      });\n    };\n\n    var Plugin = () => {\n      global$3.add('accordion', editor => {\n        register(editor);\n        register$1(editor);\n        setup(editor);\n        setup$1(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"accordion\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/accordion')\n//   ES2015:\n//     import 'tinymce/plugins/accordion'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,UAAI,SAAS;AACb,YAAM,WAAW,YAAU;AACzB,cAAM,OAAO,oBAAI,KAAK;AACtB,cAAM,OAAO,KAAK,QAAQ;AAC1B,cAAM,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,GAAU;AACpD;AACA,eAAO,SAAS,MAAM,SAAS,SAAS,OAAO,IAAI;AAAA,MACrD;AAEA,YAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,YAAI;AACJ,YAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,iBAAO;AAAA,QACT,OAAO;AACL,mBAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,QAC7F;AAAA,MACF;AACA,YAAM,SAAS,OAAK;AAClB,cAAM,IAAI,OAAO;AACjB,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,WAAW,CAAAA,UAAQ,WAAS,OAAO,KAAK,MAAMA;AACpD,YAAM,eAAe,CAAAA,UAAQ,WAAS,OAAO,UAAUA;AACvD,YAAM,WAAW,SAAS,QAAQ;AAClC,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,YAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,YAAM,aAAa,aAAa,UAAU;AAC1C,YAAM,WAAW,aAAa,QAAQ;AAEtC,YAAM,WAAW,CAAC,KAAK,QAAQ,OAAK,IAAI,IAAI,CAAC,CAAC;AAC9C,YAAM,WAAW,WAAS;AACxB,eAAO,MAAM;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,eAAe,CAAC,GAAG,MAAM;AAC7B,eAAO,MAAM;AAAA,MACf;AACA,YAAM,QAAQ,SAAS,KAAK;AAAA,MAE5B,MAAM,SAAS;AAAA,QACb,YAAY,KAAK,OAAO;AACtB,eAAK,MAAM;AACX,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,IAAI,SAAS,MAAM,KAAK;AAAA,QACjC;AAAA,QACA,OAAO,OAAO;AACZ,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,KAAK,QAAQ,QAAQ;AACnB,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,QACA,SAAS;AACP,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,SAAS;AACP,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA,IAAI,QAAQ;AACV,cAAI,KAAK,KAAK;AACZ,mBAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,UACzC,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QACzC;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QAC1C;AAAA,QACA,OAAO,WAAW;AAChB,cAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,MAAM,aAAa;AACjB,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,GAAG,aAAa;AACd,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B;AAAA,QACA,WAAW,OAAO;AAChB,iBAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,QACvC;AAAA,QACA,QAAQ,OAAO;AACb,iBAAO,KAAK,MAAM,OAAO,MAAM;AAAA,QACjC;AAAA,QACA,SAAS,SAAS;AAChB,cAAI,CAAC,KAAK,KAAK;AACb,kBAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,UAC9F,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,cAAc,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,QACrE;AAAA,QACA,YAAY;AACV,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,iBAAiB;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AACR,iBAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,YAAM,gBAAgB,MAAM,UAAU;AACtC,YAAM,aAAa,CAAC,IAAI,MAAM,cAAc,KAAK,IAAI,CAAC;AACtD,YAAM,WAAW,CAAC,IAAI,MAAM,WAAW,IAAI,CAAC,IAAI;AAChD,YAAM,MAAM,CAAC,IAAI,MAAM;AACrB,cAAM,MAAM,GAAG;AACf,cAAM,IAAI,IAAI,MAAM,GAAG;AACvB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,QACf;AACA,eAAO;AAAA,MACT;AACA,YAAM,SAAS,CAAC,IAAI,MAAM;AACxB,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,cAAM,IAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,cAAE,KAAK,CAAC;AAAA,UACV;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,CAAC,IAAI,GAAG,QAAQ;AAC5B,eAAO,IAAI,CAAC,GAAG,MAAM;AACnB,gBAAM,EAAE,KAAK,GAAG,CAAC;AAAA,QACnB,CAAC;AACD,eAAO;AAAA,MACT;AAEA,YAAM,OAAO,OAAO;AACpB,YAAM,OAAO,CAAC,KAAK,MAAM;AACvB,cAAM,QAAQ,KAAK,GAAG;AACtB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,gBAAM,IAAI,MAAM,CAAC;AACjB,gBAAM,IAAI,IAAI,CAAC;AACf,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AAEA,aAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAElE,YAAM,UAAU;AAChB,YAAM,WAAW;AACjB,YAAM,oBAAoB;AAC1B,YAAM,UAAU;AAChB,YAAM,OAAO;AAEb,YAAM,OAAO,aAAW;AACtB,cAAM,IAAI,QAAQ,IAAI;AACtB,eAAO,EAAE,YAAY;AAAA,MACvB;AACA,YAAM,OAAO,aAAW,QAAQ,IAAI;AACpC,YAAM,SAAS,OAAK,aAAW,KAAK,OAAO,MAAM;AACjD,YAAM,YAAY,aAAW,KAAK,OAAO,MAAM,WAAW,KAAK,OAAO,MAAM;AAC5E,YAAM,YAAY,OAAO,OAAO;AAChC,YAAM,SAAS,OAAO,IAAI;AAC1B,YAAM,aAAa,OAAO,QAAQ;AAClC,YAAM,qBAAqB,OAAO,iBAAiB;AAEnD,YAAM,SAAS,CAAC,KAAK,KAAK,UAAU;AAClC,YAAI,SAAS,KAAK,KAAK,UAAU,KAAK,KAAK,SAAS,KAAK,GAAG;AAC1D,cAAI,aAAa,KAAK,QAAQ,EAAE;AAAA,QAClC,OAAO;AACL,kBAAQ,MAAM,uCAAuC,KAAK,aAAa,OAAO,eAAe,GAAG;AAChG,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QAClD;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,SAAS,KAAK,UAAU;AACrC,eAAO,QAAQ,KAAK,KAAK,KAAK;AAAA,MAChC;AACA,YAAM,SAAS,CAAC,SAAS,UAAU;AACjC,cAAM,MAAM,QAAQ;AACpB,aAAK,OAAO,CAAC,GAAG,MAAM;AACpB,iBAAO,KAAK,GAAG,CAAC;AAAA,QAClB,CAAC;AAAA,MACH;AACA,YAAM,QAAQ,CAAC,SAAS,QAAQ;AAC9B,cAAM,IAAI,QAAQ,IAAI,aAAa,GAAG;AACtC,eAAO,MAAM,OAAO,SAAY;AAAA,MAClC;AACA,YAAM,SAAS,CAAC,SAAS,QAAQ,SAAS,KAAK,MAAM,SAAS,GAAG,CAAC;AAClE,YAAM,WAAW,CAAC,SAAS,QAAQ;AACjC,gBAAQ,IAAI,gBAAgB,GAAG;AAAA,MACjC;AACA,YAAM,QAAQ,aAAW,MAAM,QAAQ,IAAI,YAAY,CAAC,KAAK,SAAS;AACpE,YAAI,KAAK,IAAI,IAAI,KAAK;AACtB,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,IAAI,cAAc,KAAK;AACnC,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,cAAc,KAAK,IAAI,WAAW,SAAS,GAAG;AACrD,gBAAM,UAAU;AAChB,kBAAQ,MAAM,SAAS,IAAI;AAC3B,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB;AACA,eAAO,QAAQ,IAAI,WAAW,CAAC,CAAC;AAAA,MAClC;AACA,YAAM,UAAU,CAAC,KAAK,UAAU;AAC9B,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,cAAc,GAAG;AAClC,eAAO,QAAQ,IAAI;AAAA,MACrB;AACA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,eAAe,IAAI;AACpC,eAAO,QAAQ,IAAI;AAAA,MACrB;AACA,YAAM,UAAU,UAAQ;AACtB,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AACA,eAAO,EAAE,KAAK,KAAK;AAAA,MACrB;AACA,YAAM,YAAY,CAAC,QAAQ,GAAG,MAAM,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,OAAO;AAChG,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,OAAO,CAAC,SAAS,aAAa;AAClC,cAAM,MAAM,QAAQ;AACpB,YAAI,IAAI,aAAa,SAAS;AAC5B,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM,OAAO;AACb,cAAI,KAAK,YAAY,QAAW;AAC9B,mBAAO,KAAK,QAAQ,QAAQ;AAAA,UAC9B,WAAW,KAAK,sBAAsB,QAAW;AAC/C,mBAAO,KAAK,kBAAkB,QAAQ;AAAA,UACxC,WAAW,KAAK,0BAA0B,QAAW;AACnD,mBAAO,KAAK,sBAAsB,QAAQ;AAAA,UAC5C,WAAW,KAAK,uBAAuB,QAAW;AAChD,mBAAO,KAAK,mBAAmB,QAAQ;AAAA,UACzC,OAAO;AACL,kBAAM,IAAI,MAAM,gCAAgC;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AACA,YAAM,iBAAiB,SAAO,IAAI,aAAa,WAAW,IAAI,aAAa,YAAY,IAAI,aAAa,qBAAqB,IAAI,sBAAsB;AACvJ,YAAM,MAAM,CAAC,UAAU,UAAU;AAC/B,cAAM,OAAO,UAAU,SAAY,WAAW,MAAM;AACpD,eAAO,eAAe,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,iBAAiB,QAAQ,GAAG,aAAa,OAAO;AAAA,MAC9F;AACA,YAAM,MAAM,CAAC,UAAU,UAAU;AAC/B,cAAM,OAAO,UAAU,SAAY,WAAW,MAAM;AACpD,eAAO,eAAe,IAAI,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,KAAK,cAAc,QAAQ,CAAC,EAAE,IAAI,aAAa,OAAO;AAAA,MACtH;AAEA,YAAM,KAAK,CAAC,IAAI,OAAO,GAAG,QAAQ,GAAG;AACrC,YAAM,OAAO;AAEb,YAAM,KAAK,CAAC,KAAK,KAAK,aAAa,iBAAiB,IAAI,OAAO,UAAQ,WAAW,MAAM,GAAG,CAAC;AAE5F,YAAM,QAAQ,OAAK,OAAK,EAAE,QAAQ,GAAG,EAAE;AACvC,YAAM,OAAO,MAAM,YAAY;AAE/B,YAAM,cAAc,SAAO,IAAI,UAAU,UAAa,WAAW,IAAI,MAAM,gBAAgB;AAE3F,YAAM,QAAQ,aAAW,aAAa,QAAQ,QAAQ,IAAI,aAAa;AACvE,YAAM,kBAAkB,SAAO,WAAW,GAAG,IAAI,MAAM,MAAM,GAAG;AAChE,YAAM,SAAS,aAAW,SAAS,KAAK,QAAQ,IAAI,UAAU,EAAE,IAAI,aAAa,OAAO;AACxF,YAAM,UAAU,CAAC,SAAS,WAAW;AACnC,cAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,YAAI,MAAM,QAAQ;AAClB,cAAM,MAAM,CAAC;AACb,eAAO,IAAI,eAAe,QAAQ,IAAI,eAAe,QAAW;AAC9D,gBAAM,YAAY,IAAI;AACtB,gBAAM,IAAI,aAAa,QAAQ,SAAS;AACxC,cAAI,KAAK,CAAC;AACV,cAAI,KAAK,CAAC,MAAM,MAAM;AACpB;AAAA,UACF,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,cAAc,aAAW,SAAS,KAAK,QAAQ,IAAI,eAAe,EAAE,IAAI,aAAa,OAAO;AAClG,YAAM,cAAc,aAAW,SAAS,KAAK,QAAQ,IAAI,WAAW,EAAE,IAAI,aAAa,OAAO;AAC9F,YAAM,WAAW,aAAW,IAAI,QAAQ,IAAI,YAAY,aAAa,OAAO;AAC5E,YAAM,QAAQ,CAAC,SAAS,UAAU;AAChC,cAAM,KAAK,QAAQ,IAAI;AACvB,eAAO,SAAS,KAAK,GAAG,KAAK,CAAC,EAAE,IAAI,aAAa,OAAO;AAAA,MAC1D;AACA,YAAM,aAAa,aAAW,MAAM,SAAS,CAAC;AAE9C,YAAM,eAAe,SAAO,mBAAmB,GAAG,KAAK,cAAc,IAAI,IAAI,IAAI;AACjF,YAAM,YAAY,WAAW,QAAQ,UAAU,YAAY,KAAK,WAAW,KAAK,UAAU,WAAW;AACrG,YAAM,cAAc,YAAY,OAAK,aAAa,QAAQ,EAAE,IAAI,YAAY,CAAC,IAAI;AACjF,YAAM,gBAAgB,OAAK;AACzB,cAAM,IAAI,YAAY,CAAC;AACvB,eAAO,aAAa,CAAC,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK;AAAA,MAC5D;AACA,YAAM,gBAAgB,OAAK,aAAa,QAAQ,EAAE,IAAI,IAAI;AAE1D,YAAM,SAAS,aAAW;AACxB,cAAM,MAAM,OAAO,OAAO,IAAI,QAAQ,IAAI,aAAa,QAAQ;AAC/D,YAAI,QAAQ,UAAa,QAAQ,QAAQ,IAAI,kBAAkB,MAAM;AACnE,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,IAAI;AAChB,eAAO,cAAc,aAAa,QAAQ,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG,SAAS,QAAQ,aAAa,CAAC;AAAA,MACpH;AAEA,YAAM,cAAc,CAAC,KAAK,UAAU,UAAU;AAC5C,YAAI,CAAC,SAAS,KAAK,GAAG;AACpB,kBAAQ,MAAM,sCAAsC,UAAU,aAAa,OAAO,eAAe,GAAG;AACpG,gBAAM,IAAI,MAAM,iCAAiC,KAAK;AAAA,QACxD;AACA,YAAI,YAAY,GAAG,GAAG;AACpB,cAAI,MAAM,YAAY,UAAU,KAAK;AAAA,QACvC;AAAA,MACF;AACA,YAAM,iBAAiB,CAAC,KAAK,aAAa;AACxC,YAAI,YAAY,GAAG,GAAG;AACpB,cAAI,MAAM,eAAe,QAAQ;AAAA,QACnC;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,SAAS,UAAU,UAAU;AAC1C,cAAM,MAAM,QAAQ;AACpB,oBAAY,KAAK,UAAU,KAAK;AAAA,MAClC;AACA,YAAM,QAAQ,CAAC,SAAS,aAAa;AACnC,cAAM,MAAM,QAAQ;AACpB,cAAM,SAAS,OAAO,iBAAiB,GAAG;AAC1C,cAAM,IAAI,OAAO,iBAAiB,QAAQ;AAC1C,eAAO,MAAM,MAAM,CAAC,OAAO,OAAO,IAAI,kBAAkB,KAAK,QAAQ,IAAI;AAAA,MAC3E;AACA,YAAM,oBAAoB,CAAC,KAAK,aAAa,YAAY,GAAG,IAAI,IAAI,MAAM,iBAAiB,QAAQ,IAAI;AACvG,YAAM,SAAS,CAAC,SAAS,aAAa;AACpC,cAAM,MAAM,QAAQ;AACpB,cAAM,MAAM,kBAAkB,KAAK,QAAQ;AAC3C,eAAO,SAAS,KAAK,GAAG,EAAE,OAAO,OAAK,EAAE,SAAS,CAAC;AAAA,MACpD;AACA,YAAM,WAAW,CAAC,SAAS,aAAa;AACtC,cAAM,MAAM,QAAQ;AACpB,uBAAe,KAAK,QAAQ;AAC5B,YAAI,GAAG,OAAO,SAAS,OAAO,EAAE,IAAI,IAAI,GAAG,EAAE,GAAG;AAC9C,mBAAS,SAAS,OAAO;AAAA,QAC3B;AAAA,MACF;AAEA,YAAM,SAAS,CAAC,QAAQ,YAAY;AAClC,cAAM,WAAW,OAAO,MAAM;AAC9B,iBAAS,KAAK,OAAK;AACjB,YAAE,IAAI,aAAa,QAAQ,KAAK,OAAO,GAAG;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,YAAM,UAAU,CAAC,QAAQ,YAAY;AACnC,cAAM,UAAU,YAAY,MAAM;AAClC,gBAAQ,KAAK,MAAM;AACjB,gBAAM,WAAW,OAAO,MAAM;AAC9B,mBAAS,KAAK,OAAK;AACjB,qBAAS,GAAG,OAAO;AAAA,UACrB,CAAC;AAAA,QACH,GAAG,OAAK;AACN,iBAAO,GAAG,OAAO;AAAA,QACnB,CAAC;AAAA,MACH;AACA,YAAM,UAAU,CAACC,SAAQ,YAAY;AACnC,cAAM,eAAe,WAAWA,OAAM;AACtC,qBAAa,KAAK,MAAM;AACtB,mBAASA,SAAQ,OAAO;AAAA,QAC1B,GAAG,OAAK;AACN,UAAAA,QAAO,IAAI,aAAa,QAAQ,KAAK,EAAE,GAAG;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAACA,SAAQ,YAAY;AACpC,QAAAA,QAAO,IAAI,YAAY,QAAQ,GAAG;AAAA,MACpC;AACA,YAAM,OAAO,CAAC,SAAS,YAAY;AACjC,eAAO,SAAS,OAAO;AACvB,iBAAS,SAAS,OAAO;AAAA,MAC3B;AAEA,YAAM,QAAQ,CAAC,QAAQ,aAAa;AAClC,eAAO,UAAU,CAAC,GAAG,MAAM;AACzB,gBAAM,IAAI,MAAM,IAAI,SAAS,SAAS,IAAI,CAAC;AAC3C,kBAAQ,GAAG,CAAC;AAAA,QACd,CAAC;AAAA,MACH;AACA,YAAM,SAAS,CAACA,SAAQ,aAAa;AACnC,eAAO,UAAU,OAAK;AACpB,mBAASA,SAAQ,CAAC;AAAA,QACpB,CAAC;AAAA,MACH;AAEA,YAAM,gBAAgB,CAAC,OAAO,cAAc;AAC1C,YAAI,SAAS,CAAC;AACd,eAAO,SAAS,KAAK,GAAG,OAAK;AAC3B,cAAI,UAAU,CAAC,GAAG;AAChB,qBAAS,OAAO,OAAO,CAAC,CAAC,CAAC;AAAA,UAC5B;AACA,mBAAS,OAAO,OAAO,cAAc,GAAG,SAAS,CAAC;AAAA,QACpD,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,oBAAoB,CAACC,KAAIC,WAAU,OAAO,GAAG,WAAW;AAC1D,YAAID,IAAG,OAAO,CAAC,GAAG;AAChB,iBAAO,SAAS,KAAK,KAAK;AAAA,QAC5B,WAAW,WAAW,MAAM,KAAK,OAAO,KAAK,GAAG;AAC9C,iBAAO,SAAS,KAAK;AAAA,QACvB,OAAO;AACL,iBAAOC,UAAS,OAAO,GAAG,MAAM;AAAA,QAClC;AAAA,MACF;AAEA,YAAM,aAAa,CAAC,OAAO,WAAW,WAAW;AAC/C,YAAI,UAAU,MAAM;AACpB,cAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,eAAO,QAAQ,YAAY;AACzB,oBAAU,QAAQ;AAClB,gBAAM,KAAK,aAAa,QAAQ,OAAO;AACvC,cAAI,UAAU,EAAE,GAAG;AACjB,mBAAO,SAAS,KAAK,EAAE;AAAA,UACzB,WAAW,KAAK,EAAE,GAAG;AACnB;AAAA,UACF;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AAEA,YAAM,SAAS,aAAW;AACxB,cAAM,MAAM,QAAQ;AACpB,YAAI,IAAI,eAAe,MAAM;AAC3B,cAAI,WAAW,YAAY,GAAG;AAAA,QAChC;AAAA,MACF;AACA,YAAM,SAAS,aAAW;AACxB,cAAM,aAAa,SAAS,OAAO;AACnC,YAAI,WAAW,SAAS,GAAG;AACzB,gBAAM,SAAS,UAAU;AAAA,QAC3B;AACA,eAAO,OAAO;AAAA,MAChB;AAEA,YAAM,cAAc,CAAC,OAAO,aAAa,IAAI,UAAU,KAAK;AAE5D,YAAM,WAAW,CAAC,OAAO,UAAU,WAAW,WAAW,OAAO,OAAK,KAAK,GAAG,QAAQ,GAAG,MAAM;AAC9F,YAAM,aAAa,CAAC,OAAO,aAAa,IAAI,UAAU,KAAK;AAC3D,YAAM,UAAU,CAAC,OAAO,UAAU,WAAW;AAC3C,cAAMD,MAAK,CAAC,SAASE,cAAa,KAAK,SAASA,SAAQ;AACxD,eAAO,kBAAkBF,KAAI,UAAU,OAAO,UAAU,MAAM;AAAA,MAChE;AAEA,YAAM,YAAY,CAACA,KAAIG,UAAS;AAC9B,cAAMC,OAAM,aAAW;AACrB,cAAI,CAACJ,IAAG,OAAO,GAAG;AAChB,kBAAM,IAAI,MAAM,kBAAkBG,QAAO,iBAAiBA,QAAO,OAAO;AAAA,UAC1E;AACA,iBAAO,UAAU,OAAO,EAAE,MAAM,EAAE;AAAA,QACpC;AACA,cAAM,YAAY,aAAWH,IAAG,OAAO,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,IAAI,SAAS,KAAK;AAChG,cAAMK,OAAM,CAAC,SAAS,UAAU;AAC9B,cAAI,CAACL,IAAG,OAAO,GAAG;AAChB,kBAAM,IAAI,MAAM,sBAAsBG,QAAO,iBAAiBA,QAAO,OAAO;AAAA,UAC9E;AACA,kBAAQ,IAAI,YAAY;AAAA,QAC1B;AACA,eAAO;AAAA,UACL,KAAAC;AAAA,UACA;AAAA,UACA,KAAAC;AAAA,QACF;AAAA,MACF;AAEA,YAAM,MAAM,UAAU,QAAQ,MAAM;AACpC,YAAM,MAAM,aAAW,IAAI,IAAI,OAAO;AACtC,YAAM,MAAM,CAAC,SAAS,UAAU,IAAI,IAAI,SAAS,KAAK;AAEtD,UAAI,gBAAgB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,UAAI,cAAc,MAAM;AACtB,cAAM,UAAU,aAAW;AACzB,iBAAO,aAAa,QAAQ,QAAQ,IAAI,UAAU,KAAK,CAAC;AAAA,QAC1D;AACA,cAAMC,YAAW,aAAW,gBAAgB,OAAO,EAAE;AACrD,cAAM,aAAa,aAAW;AAC5B,cAAI,CAAC,UAAU,OAAO,GAAG;AACvB,mBAAO;AAAA,UACT;AACA,cAAI,KAAK,OAAO,MAAM,QAAQ;AAC5B,mBAAO;AAAA,UACT;AACA,iBAAO,SAAS,eAAe,KAAK,OAAO,CAAC;AAAA,QAC9C;AACA,cAAM,aAAa,aAAW;AAC5B,cAAI,CAAC,UAAU,OAAO,GAAG;AACvB,mBAAO;AAAA,UACT;AACA,iBAAO,SAAS;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,GAAG,KAAK,OAAO,CAAC;AAAA,QAClB;AACA,cAAM,gBAAgB,aAAW,UAAU,OAAO,KAAK,MAAM,SAAS,iBAAiB,MAAM;AAC7F,cAAM,kBAAkB,CAAC,SAAS,UAAU;AAC1C,iBAAO,QAAQ,IAAI,wBAAwB,MAAM,GAAG;AAAA,QACtD;AACA,cAAM,mBAAmB,CAAC,QAAQ,gBAAgB;AAChD,gBAAM,KAAK,MAAM,MAAM;AACvB,iBAAO,aAAa,EAAE;AAAA,QACxB;AACA,cAAM,YAAY,aAAW;AAC3B,gBAAM,MAAM,KAAK,OAAO;AACxB,iBAAO,SAAS;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,GAAG,GAAG;AAAA,QACR;AACA,cAAM,cAAc,aAAW,UAAU,OAAO,IAAI,OAAO,SAAS,MAAM,IAAI,SAAS,KAAK;AAC5F,eAAO;AAAA,UACL,IAAI,SAAS;AAAA,YACX,UAAU;AAAA,YACV;AAAA,YACA,WAAW;AAAA,YACX,KAAK;AAAA,UACP,CAAC;AAAA,UACD,MAAM,SAAS;AAAA,YACb,UAAU;AAAA,YACV,WAAW;AAAA,UACb,CAAC;AAAA,UACD,QAAQ,SAAS;AAAA,YACf,KAAK;AAAA,YACL;AAAA,YACA,KAAK;AAAA,YACL,QAAQ;AAAA,UACV,CAAC;AAAA,UACD,OAAO,SAAS;AAAA,YACd,KAAK;AAAA,YACL,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV,CAAC;AAAA,UACD,QAAQ,SAAS;AAAA,YACf;AAAA,YACA,OAAO;AAAA,YACP,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,WAAW;AAAA,YACX;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,QAAQ,SAAS;AAAA,YACf;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,QAAQ,SAAS;AAAA,YACf,IAAI,aAAa;AAAA,YACjB,OAAO;AAAA,YACP,MAAM,aAAa;AAAA,UACrB,CAAC;AAAA,UACD,OAAO,SAAS;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,UAAU,SAAS;AAAA,YACjB;AAAA,YACA;AAAA,YACA;AAAA,YACA,UAAAA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAAS;AAAA,YACT,SAAS;AAAA,YACT;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD;AAAA,UACA,IAAI;AAAA,QACN;AAAA,MACF;AAEA,YAAM,QAAQ,CAAC,SAAS,YAAY;AAAA,QAClC;AAAA,QACA;AAAA,MACF;AAEA,YAAM,OAAO,CAACC,WAAU,SAAS,cAAc;AAC7C,YAAIA,UAAS,SAAS,EAAE,OAAO,OAAO,KAAKA,UAAS,SAAS,EAAE,QAAQ,OAAO,EAAE,KAAK,EAAE,WAAW,KAAKA,UAAS,SAAS,EAAE,UAAU,OAAO,GAAG;AAC7I,iBAAO,UAAU,OAAO,EAAE,KAAK,UAAQ;AACrC,mBAAO,KAAKA,WAAU,MAAM,SAAS,EAAE,QAAQ,MAAM;AACnD,qBAAO,SAAS,KAAK,IAAI;AAAA,YAC3B,CAAC;AAAA,UACH,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,YAAM,QAAQ,CAACA,WAAU,YAAY;AACnC,YAAIA,UAAS,SAAS,EAAE,OAAO,OAAO,GAAG;AACvC,iBAAOA,UAAS,SAAS,EAAE,QAAQ,OAAO,EAAE;AAAA,QAC9C;AACA,cAAMC,YAAWD,UAAS,SAAS,EAAE,SAAS,OAAO;AACrD,eAAOC,UAAS;AAAA,MAClB;AACA,YAAM,gBAAgB,CAACD,WAAU,YAAY;AAC3C,cAAM,YAAY,KAAKA,WAAU,SAASA,UAAS,MAAM,EAAE,WAAW,EAAE,MAAM,OAAO;AACrF,YAAIA,UAAS,SAAS,EAAE,OAAO,SAAS,GAAG;AACzC,iBAAO,MAAM,WAAW,MAAMA,WAAU,SAAS,CAAC;AAAA,QACpD;AACA,cAAMC,YAAWD,UAAS,SAAS,EAAE,SAAS,SAAS;AACvD,eAAOC,UAAS,SAAS,IAAI,cAAcD,WAAUC,UAASA,UAAS,SAAS,CAAC,CAAC,IAAI,MAAM,WAAW,MAAMD,WAAU,SAAS,CAAC;AAAA,MACnI;AAEA,YAAM,gBAAgB;AAEtB,YAAM,WAAW,YAAY;AAC7B,YAAM,cAAc,aAAW;AAC7B,eAAO,cAAc,UAAU,OAAO;AAAA,MACxC;AAEA,YAAM,2BAA2B,CAAC,QAAQ,SAAS,UAAU,OAAO,SAAS,oBAAoB;AAAA,QAC/F;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,+BAA+B,CAAC,QAAQ,UAAU,UAAU,OAAO,SAAS,wBAAwB;AAAA,QACxG;AAAA,QACA;AAAA,MACF,CAAC;AAED,YAAM,eAAe;AACrB,YAAM,wBAAwB;AAC9B,YAAM,wBAAwB;AAC9B,YAAM,4BAA4B;AAClC,YAAM,0BAA0B;AAEhC,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE9D,YAAM,YAAY,WAAS,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,cAAc;AAC1F,YAAM,YAAY,WAAS,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,cAAc;AAC1F,YAAM,SAAS,aAAW,QAAQ,aAAa,MAAM;AACrD,YAAM,cAAc,YAAU;AAC5B,cAAM,OAAO,OAAO,UAAU,QAAQ;AACtC,eAAO,UAAU,IAAI,KAAK,QAAQ,OAAO,IAAI,UAAU,MAAM,SAAS,CAAC;AAAA,MACzE;AACA,YAAM,kBAAkB,YAAU,CAAC,YAAY,MAAM,KAAK,OAAO,IAAI,WAAW,OAAO,UAAU,QAAQ,CAAC;AAC1G,YAAM,qBAAqB,YAAU,SAAS,KAAK,OAAO,IAAI,UAAU,OAAO,UAAU,QAAQ,GAAG,SAAS,CAAC;AAC9G,YAAM,oBAAoB,YAAU,mBAAmB,MAAM,EAAE,OAAO;AACtE,YAAM,cAAc,aAAW;AAC7B,gBAAQ,YAAY;AACpB,eAAO;AAAA,MACT;AACA,YAAM,kBAAkB,YAAU,YAAY,OAAO,IAAI,OAAO,GAAG,CAAC;AACpE,YAAM,gBAAgB,YAAU,YAAY,OAAO,IAAI,OAAO,SAAS,CAAC;AACxE,YAAM,gCAAgC,CAAC,QAAQ,WAAW;AACxD,cAAM,YAAY,gBAAgB,MAAM;AACxC,eAAO,sBAAsB,YAAY,SAAS;AAClD,eAAO,UAAU,kBAAkB,WAAW,CAAC;AAAA,MACjD;AACA,YAAM,mBAAmB,CAAC,QAAQ,cAAc;AAC9C,YAAI,UAAU,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,SAAS,GAAG;AACxF,gBAAM,YAAY,gBAAgB,MAAM;AACxC,oBAAU,YAAY,SAAS;AAC/B,iBAAO,UAAU,kBAAkB,WAAW,CAAC;AAAA,QACjD;AAAA,MACF;AACA,YAAM,mBAAmB,CAAC,QAAQ,cAAc;AAC9C,YAAI,CAAC,UAAU,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,UAAU,GAAG;AAC1F,gBAAM,UAAU,cAAc,MAAM;AACpC,oBAAU,QAAQ,OAAO;AACzB,iBAAO,UAAU,kBAAkB,SAAS,CAAC;AAAA,QAC/C;AAAA,MACF;AACA,YAAM,qBAAqB,YAAU,eAAa;AAChD,yBAAiB,QAAQ,SAAS;AAClC,yBAAiB,QAAQ,SAAS;AAAA,MACpC;AACA,YAAM,mBAAmB,YAAU;AACjC,iBAAS,KAAK,SAAS,KAAK,OAAO,IAAI,OAAO,WAAW,OAAO,QAAQ,CAAC,CAAC,GAAG,mBAAmB,MAAM,CAAC;AAAA,MACzG;AAEA,YAAM,kBAAkB,YAAU;AAChC,YAAI,CAAC,gBAAgB,MAAM,GAAG;AAC5B;AAAA,QACF;AACA,cAAM,aAAa,aAAa,QAAQ,OAAO,QAAQ,CAAC;AACxD,cAAM,MAAM,SAAS,KAAK;AAC1B,cAAM,cAAc,OAAO,IAAI,OAAO,OAAO,UAAU,OAAO,EAAE,SAAS,KAAK,OAAO,UAAU,sBAAsB,CAAC;AACtH,cAAM,WAAW,OAAO,IAAI,OAAO,OAAO,UAAU,mBAAmB,CAAC;AACxE,cAAM,uBAAuB,mBAAoB,qBAAsB,KAAM,WAAY;AACzF,cAAM,oBAAoB,IAAK,uBAAwB,WAAY,yBAA0B,QAAS,QAAS,SAAU,uBAAwB;AACjJ,eAAO,YAAY,SAAS,MAAM;AAChC,iBAAO,cAAc;AAAA,YACnB,yBAA0B,GAAI,YAAa,qBAAsB;AAAA,YACjE;AAAA,YACA;AAAA,YACA;AAAA,UACF,EAAE,KAAK,EAAE,CAAC;AACV,qBAAW,YAAY,iBAAkB,GAAI,IAAI,EAAE,KAAK,gBAAc;AACpE,qBAAS,YAAY,aAAa;AAClC,uBAAW,YAAY,SAAS,EAAE,KAAK,gBAAc;AACnD,oBAAM,MAAM,OAAO,IAAI,UAAU;AACjC,oBAAM,MAAM,YAAY,UAAU;AAClC,kBAAI,SAAS,IAAI,QAAQ,KAAK,IAAI,MAAM;AACxC,kBAAI,OAAO,IAAI,QAAQ,KAAK,IAAI,MAAM;AACtC,qBAAO,UAAU,OAAO,GAAG;AAAA,YAC7B,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,uBAAuB,CAAC,SAAS,UAAU;AAC/C,cAAM,aAAa,UAAU,QAAQ,UAAU,SAAS,QAAQ,CAAC,OAAO,OAAO;AAC/E,YAAI,YAAY;AACd,kBAAQ,aAAa,QAAQ,MAAM;AAAA,QACrC,OAAO;AACL,kBAAQ,gBAAgB,MAAM;AAAA,QAChC;AACA,eAAO;AAAA,MACT;AACA,YAAM,kBAAkB,CAAC,QAAQ,UAAU;AACzC,2BAAmB,MAAM,EAAE,KAAK,aAAW;AACzC,mCAAyB,QAAQ,SAAS,qBAAqB,SAAS,KAAK,CAAC;AAAA,QAChF,CAAC;AAAA,MACH;AACA,YAAM,kBAAkB,YAAU;AAChC,2BAAmB,MAAM,EAAE,KAAK,aAAW;AACzC,gBAAM,EAAC,aAAAE,aAAW,IAAI;AACtB,cAAIA,cAAa;AACf,mBAAO,UAAU,OAAOA,cAAa,IAAI;AACzC,mBAAO,UAAU,SAAS,IAAI;AAAA,UAChC,OAAO;AACL,0CAA8B,QAAQ,OAAO;AAAA,UAC/C;AACA,kBAAQ,OAAO;AAAA,QACjB,CAAC;AAAA,MACH;AACA,YAAM,sBAAsB,CAAC,QAAQ,UAAU;AAC7C,cAAM,aAAa,MAAM,KAAK,OAAO,QAAQ,EAAE,iBAAiB,SAAS,CAAC;AAC1E,YAAI,WAAW,WAAW,GAAG;AAC3B;AAAA,QACF;AACA,eAAO,YAAY,eAAa,qBAAqB,WAAW,UAAU,QAAQ,UAAU,SAAS,QAAQ,CAAC,OAAO,SAAS,CAAC,CAAC;AAChI,qCAA6B,QAAQ,YAAY,KAAK;AAAA,MACxD;AAEA,YAAM,aAAa,YAAU;AAC3B,eAAO,WAAW,mBAAmB,MAAM,gBAAgB,MAAM,CAAC;AAClE,eAAO,WAAW,mBAAmB,CAAC,KAAK,UAAU,gBAAgB,QAAQ,KAAK,CAAC;AACnF,eAAO,WAAW,uBAAuB,CAAC,KAAK,UAAU,oBAAoB,QAAQ,KAAK,CAAC;AAC3F,eAAO,WAAW,mBAAmB,MAAM,gBAAgB,MAAM,CAAC;AAAA,MACpE;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,mBAAmB;AAE7D,YAAM,eAAe,UAAQ;AAC3B,YAAI,IAAI;AACR,gBAAQ,MAAM,KAAK,KAAK,KAAK,OAAO,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,GAAG,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,MAC/H;AACA,YAAM,aAAa,CAAC,MAAM,YAAY;AACpC,cAAM,eAAe,oBAAI,IAAI;AAAA,UAC3B,GAAG,aAAa,IAAI;AAAA,UACpB,GAAG;AAAA,QACL,CAAC;AACD,cAAM,eAAe,MAAM,KAAK,YAAY;AAC5C,YAAI,aAAa,SAAS,GAAG;AAC3B,eAAK,KAAK,SAAS,aAAa,KAAK,GAAG,CAAC;AAAA,QAC3C;AAAA,MACF;AACA,YAAM,gBAAgB,CAAC,MAAM,YAAY;AACvC,cAAM,eAAe,OAAO,aAAa,IAAI,GAAG,WAAS,CAAC,QAAQ,IAAI,KAAK,CAAC;AAC5E,aAAK,KAAK,SAAS,aAAa,SAAS,IAAI,aAAa,KAAK,GAAG,IAAI,IAAI;AAAA,MAC5E;AACA,YAAM,yBAAyB,UAAQ,KAAK,SAAS,gBAAgB,SAAS,aAAa,IAAI,GAAG,qBAAqB;AACvH,YAAM,6BAA6B,UAAQ,KAAK,SAAS,2BAA2B,SAAS,aAAa,IAAI,GAAG,yBAAyB;AAC1I,YAAM,uBAAuB,mBAAiB;AAC5C,cAAMD,YAAW,cAAc,SAAS;AACxC,YAAI;AACJ,YAAI;AACJ,cAAM,aAAa,CAAC;AACpB,iBAAS,IAAI,GAAG,IAAIA,UAAS,QAAQ,KAAK;AACxC,gBAAME,SAAQF,UAAS,CAAC;AACxB,cAAIE,OAAM,SAAS,aAAa,WAAW,WAAW,GAAG;AACvD,0BAAcA;AAAA,UAChB,WAAW,2BAA2BA,MAAK,KAAK,WAAW,WAAW,GAAG;AACvE,0BAAcA;AAAA,UAChB,OAAO;AACL,uBAAW,KAAKA,MAAK;AAAA,UACvB;AAAA,QACF;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,eAAe,UAAQ;AAC3B,cAAM,KAAK,IAAI,SAAS,MAAM,CAAC;AAC/B,WAAG,KAAK,kBAAkB,GAAG;AAC7B,aAAK,MAAM;AACX,aAAK,OAAO,EAAE;AAAA,MAChB;AACA,YAAM,UAAU,YAAU;AACxB,eAAO,GAAG,WAAW,MAAM;AACzB,gBAAM,EAAC,YAAY,OAAM,IAAI;AAC7B,iBAAO,cAAc,cAAc,WAAS;AAC1C,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAM,OAAO,MAAM,CAAC;AACpB,kBAAI,uBAAuB,IAAI,GAAG;AAChC,sBAAM,gBAAgB;AACtB,sBAAM,EAAC,aAAa,aAAa,WAAU,IAAI,qBAAqB,aAAa;AACjF,sBAAM,iBAAiB,cAAc,WAAW;AAChD,sBAAM,iBAAiB,iBAAiB,cAAc,IAAI,SAAS,WAAW,CAAC;AAC/E,oBAAI,WAAW,eAAe,UAAU,GAAG;AACzC,+BAAa,cAAc;AAAA,gBAC7B;AACA,2BAAW,gBAAgB,CAAC,qBAAqB,CAAC;AAClD,oBAAI,CAAC,gBAAgB;AACnB,sBAAI,cAAc,cAAc,UAAU,GAAG;AAC3C,kCAAc,OAAO,gBAAgB,cAAc,YAAY,IAAI;AAAA,kBACrE,OAAO;AACL,kCAAc,OAAO,cAAc;AAAA,kBACrC;AAAA,gBACF;AACA,sBAAM,iBAAiB,cAAc,WAAW;AAChD,sBAAM,iBAAiB,iBAAiB,cAAc,IAAI,SAAS,yBAAyB,CAAC;AAC7F,2BAAW,gBAAgB,CAAC,yBAAyB,CAAC;AACtD,oBAAI,WAAW,SAAS,GAAG;AACzB,2BAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,0BAAM,YAAY,WAAW,CAAC;AAC9B,mCAAe,OAAO,SAAS;AAAA,kBACjC;AAAA,gBACF;AACA,oBAAI,WAAW,eAAe,UAAU,GAAG;AACzC,wBAAM,QAAQ,IAAI,SAAS,KAAK,CAAC;AACjC,+BAAa,KAAK;AAClB,iCAAe,OAAO,KAAK;AAAA,gBAC7B;AACA,oBAAI,CAAC,gBAAgB;AACnB,gCAAc,OAAO,cAAc;AAAA,gBACrC;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AACD,qBAAW,cAAc,cAAc,WAAS;AAC9C,kBAAM,wBAAwB,oBAAI,IAAI,CAAC,qBAAqB,CAAC;AAC7D,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAM,OAAO,MAAM,CAAC;AACpB,kBAAI,uBAAuB,IAAI,GAAG;AAChC,sBAAM,gBAAgB;AACtB,sBAAM,EAAC,aAAa,YAAW,IAAI,qBAAqB,aAAa;AACrE,oBAAI,cAAc,WAAW,GAAG;AAC9B,gCAAc,aAAa,qBAAqB;AAAA,gBAClD;AACA,oBAAI,cAAc,WAAW,GAAG;AAC9B,8BAAY,OAAO;AAAA,gBACrB;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,iBAAiB;AAEzD,YAAM,yBAAyB,YAAU;AACvC,eAAO,GAAG,WAAW,WAAS;AAC5B,cAAI,MAAM,YAAY,MAAM,YAAY,OAAO,SAAS,CAAC,YAAY,MAAM,GAAG;AAC5E;AAAA,UACF;AACA,gBAAM,eAAe;AACrB,iBAAO,YAAY,iBAAiB;AAAA,QACtC,CAAC;AAAA,MACH;AACA,YAAM,QAAQ,YAAU;AACtB,+BAAuB,MAAM;AAC7B,eAAO,GAAG,eAAe,OAAK;AAC5B,gBAAM,MAAM,EAAE,QAAQ,YAAY;AAClC,eAAK,QAAQ,YAAY,QAAQ,oBAAoB,kBAAkB,MAAM,GAAG;AAC9E,6BAAiB,MAAM;AAAA,UACzB;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,UAAU,YAAU,eAAa;AACrC,cAAM,eAAe,MAAM,UAAU,WAAW,gBAAgB,MAAM,CAAC;AACvE,eAAO,GAAG,cAAc,YAAY;AACpC,eAAO,MAAM,OAAO,IAAI,cAAc,YAAY;AAAA,MACpD;AACA,YAAM,WAAW,YAAU;AACzB,cAAM,WAAW,MAAM,OAAO,YAAY,iBAAiB;AAC3D,eAAO,GAAG,SAAS,UAAU,aAAa;AAAA,UACxC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS,QAAQ,MAAM;AAAA,UACvB;AAAA,QACF,CAAC;AACD,eAAO,GAAG,SAAS,YAAY,aAAa;AAAA,UAC1C,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS,QAAQ,MAAM;AAAA,UACvB;AAAA,QACF,CAAC;AACD,eAAO,GAAG,SAAS,gBAAgB,mBAAmB;AAAA,UACpD,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,MAAM,OAAO,YAAY,iBAAiB;AAAA,QACtD,CAAC;AACD,eAAO,GAAG,SAAS,gBAAgB,mBAAmB;AAAA,UACpD,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,MAAM,OAAO,YAAY,iBAAiB;AAAA,QACtD,CAAC;AACD,eAAO,GAAG,SAAS,kBAAkB,aAAa;AAAA,UAChD,WAAW,eAAa,OAAO,IAAI,GAAG,WAAW,SAAS,KAAK,OAAO,QAAQ,EAAE,SAAS,SAAS,KAAK,OAAO,IAAI,WAAW,UAAU,UAAU;AAAA,UACjJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,aAAa,YAAU;AAClC,mBAAS,MAAM;AACf,qBAAW,MAAM;AACjB,gBAAM,MAAM;AACZ,kBAAQ,MAAM;AAAA,QAChB,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;AClgCH;", "names": ["type", "parent", "is", "ancestor", "selector", "name", "get", "set", "document", "universe", "children", "nextS<PERSON>ling", "child"]}