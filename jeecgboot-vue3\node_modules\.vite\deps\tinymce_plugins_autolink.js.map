{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/autolink/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/autolink/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n  'use strict';\n\n  var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n  const link = () => /(?:[A-Za-z][A-Za-z\\d.+-]{0,14}:\\/\\/(?:[-.~*+=!&;:'%@?^${}(),\\w]+@)?|www\\.|[-;:&=+$,.\\w]+@)[A-Za-z\\d-]+(?:\\.[A-Za-z\\d-]+)*(?::\\d+)?(?:\\/(?:[-.~*+=!;:'%@$(),\\/\\w]*[-~*+=%@$()\\/\\w])?)?(?:\\?(?:[-.~*+=!&;:'%@?^${}(),\\/\\w]+))?(?:#(?:[-.~*+=!&;:'%@?^${}(),\\/\\w]+))?/g;\n\n  const option = name => editor => editor.options.get(name);\n  const register = editor => {\n    const registerOption = editor.options.register;\n    registerOption('autolink_pattern', {\n      processor: 'regexp',\n      default: new RegExp('^' + link().source + '$', 'i')\n    });\n    registerOption('link_default_target', { processor: 'string' });\n    registerOption('link_default_protocol', {\n      processor: 'string',\n      default: 'https'\n    });\n  };\n  const getAutoLinkPattern = option('autolink_pattern');\n  const getDefaultLinkTarget = option('link_default_target');\n  const getDefaultLinkProtocol = option('link_default_protocol');\n  const allowUnsafeLinkTarget = option('allow_unsafe_link_target');\n\n  const hasProto = (v, constructor, predicate) => {\n    var _a;\n    if (predicate(v, constructor.prototype)) {\n      return true;\n    } else {\n      return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n    }\n  };\n  const typeOf = x => {\n    const t = typeof x;\n    if (x === null) {\n      return 'null';\n    } else if (t === 'object' && Array.isArray(x)) {\n      return 'array';\n    } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n      return 'string';\n    } else {\n      return t;\n    }\n  };\n  const isType = type => value => typeOf(value) === type;\n  const eq = t => a => t === a;\n  const isString = isType('string');\n  const isUndefined = eq(undefined);\n  const isNullable = a => a === null || a === undefined;\n  const isNonNullable = a => !isNullable(a);\n\n  const not = f => t => !f(t);\n\n  const hasOwnProperty = Object.hasOwnProperty;\n  const has = (obj, key) => hasOwnProperty.call(obj, key);\n\n  const checkRange = (str, substr, start) => substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n  const contains = (str, substr, start = 0, end) => {\n    const idx = str.indexOf(substr, start);\n    if (idx !== -1) {\n      return isUndefined(end) ? true : idx + substr.length <= end;\n    } else {\n      return false;\n    }\n  };\n  const startsWith = (str, prefix) => {\n    return checkRange(str, prefix, 0);\n  };\n\n  const zeroWidth = '\\uFEFF';\n  const isZwsp = char => char === zeroWidth;\n  const removeZwsp = s => s.replace(/\\uFEFF/g, '');\n\n  var global = tinymce.util.Tools.resolve('tinymce.dom.TextSeeker');\n\n  const isTextNode = node => node.nodeType === 3;\n  const isElement = node => node.nodeType === 1;\n  const isBracketOrSpace = char => /^[(\\[{ \\u00a0]$/.test(char);\n  const hasProtocol = url => /^([A-Za-z][A-Za-z\\d.+-]*:\\/\\/)|mailto:/.test(url);\n  const isPunctuation = char => /[?!,.;:]/.test(char);\n  const findChar = (text, index, predicate) => {\n    for (let i = index - 1; i >= 0; i--) {\n      const char = text.charAt(i);\n      if (!isZwsp(char) && predicate(char)) {\n        return i;\n      }\n    }\n    return -1;\n  };\n  const freefallRtl = (container, offset) => {\n    let tempNode = container;\n    let tempOffset = offset;\n    while (isElement(tempNode) && tempNode.childNodes[tempOffset]) {\n      tempNode = tempNode.childNodes[tempOffset];\n      tempOffset = isTextNode(tempNode) ? tempNode.data.length : tempNode.childNodes.length;\n    }\n    return {\n      container: tempNode,\n      offset: tempOffset\n    };\n  };\n\n  const parseCurrentLine = (editor, offset) => {\n    var _a;\n    const voidElements = editor.schema.getVoidElements();\n    const autoLinkPattern = getAutoLinkPattern(editor);\n    const {dom, selection} = editor;\n    if (dom.getParent(selection.getNode(), 'a[href]') !== null) {\n      return null;\n    }\n    const rng = selection.getRng();\n    const textSeeker = global(dom, node => {\n      return dom.isBlock(node) || has(voidElements, node.nodeName.toLowerCase()) || dom.getContentEditable(node) === 'false';\n    });\n    const {\n      container: endContainer,\n      offset: endOffset\n    } = freefallRtl(rng.endContainer, rng.endOffset);\n    const root = (_a = dom.getParent(endContainer, dom.isBlock)) !== null && _a !== void 0 ? _a : dom.getRoot();\n    const endSpot = textSeeker.backwards(endContainer, endOffset + offset, (node, offset) => {\n      const text = node.data;\n      const idx = findChar(text, offset, not(isBracketOrSpace));\n      return idx === -1 || isPunctuation(text[idx]) ? idx : idx + 1;\n    }, root);\n    if (!endSpot) {\n      return null;\n    }\n    let lastTextNode = endSpot.container;\n    const startSpot = textSeeker.backwards(endSpot.container, endSpot.offset, (node, offset) => {\n      lastTextNode = node;\n      const idx = findChar(node.data, offset, isBracketOrSpace);\n      return idx === -1 ? idx : idx + 1;\n    }, root);\n    const newRng = dom.createRng();\n    if (!startSpot) {\n      newRng.setStart(lastTextNode, 0);\n    } else {\n      newRng.setStart(startSpot.container, startSpot.offset);\n    }\n    newRng.setEnd(endSpot.container, endSpot.offset);\n    const rngText = removeZwsp(newRng.toString());\n    const matches = rngText.match(autoLinkPattern);\n    if (matches) {\n      let url = matches[0];\n      if (startsWith(url, 'www.')) {\n        const protocol = getDefaultLinkProtocol(editor);\n        url = protocol + '://' + url;\n      } else if (contains(url, '@') && !hasProtocol(url)) {\n        url = 'mailto:' + url;\n      }\n      return {\n        rng: newRng,\n        url\n      };\n    } else {\n      return null;\n    }\n  };\n  const convertToLink = (editor, result) => {\n    const {dom, selection} = editor;\n    const {rng, url} = result;\n    const bookmark = selection.getBookmark();\n    selection.setRng(rng);\n    const command = 'createlink';\n    const args = {\n      command,\n      ui: false,\n      value: url\n    };\n    const beforeExecEvent = editor.dispatch('BeforeExecCommand', args);\n    if (!beforeExecEvent.isDefaultPrevented()) {\n      editor.getDoc().execCommand(command, false, url);\n      editor.dispatch('ExecCommand', args);\n      const defaultLinkTarget = getDefaultLinkTarget(editor);\n      if (isString(defaultLinkTarget)) {\n        const anchor = selection.getNode();\n        dom.setAttrib(anchor, 'target', defaultLinkTarget);\n        if (defaultLinkTarget === '_blank' && !allowUnsafeLinkTarget(editor)) {\n          dom.setAttrib(anchor, 'rel', 'noopener');\n        }\n      }\n    }\n    selection.moveToBookmark(bookmark);\n    editor.nodeChanged();\n  };\n  const handleSpacebar = editor => {\n    const result = parseCurrentLine(editor, -1);\n    if (isNonNullable(result)) {\n      convertToLink(editor, result);\n    }\n  };\n  const handleBracket = handleSpacebar;\n  const handleEnter = editor => {\n    const result = parseCurrentLine(editor, 0);\n    if (isNonNullable(result)) {\n      convertToLink(editor, result);\n    }\n  };\n  const setup = editor => {\n    editor.on('keydown', e => {\n      if (e.keyCode === 13 && !e.isDefaultPrevented()) {\n        handleEnter(editor);\n      }\n    });\n    editor.on('keyup', e => {\n      if (e.keyCode === 32) {\n        handleSpacebar(editor);\n      } else if (e.keyCode === 48 && e.shiftKey || e.keyCode === 221) {\n        handleBracket(editor);\n      }\n    });\n  };\n\n  var Plugin = () => {\n    global$1.add('autolink', editor => {\n      register(editor);\n      setup(editor);\n    });\n  };\n\n  Plugin();\n\n})();\n", "// Exports the \"autolink\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/autolink')\n//   ES2015:\n//     import 'tinymce/plugins/autolink'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACX;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,OAAO,MAAM;AAEnB,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,WAAW,YAAU;AACzB,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,oBAAoB;AAAA,UACjC,WAAW;AAAA,UACX,SAAS,IAAI,OAAO,MAAM,KAAK,EAAE,SAAS,KAAK,GAAG;AAAA,QACpD,CAAC;AACD,uBAAe,uBAAuB,EAAE,WAAW,SAAS,CAAC;AAC7D,uBAAe,yBAAyB;AAAA,UACtC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,qBAAqB,OAAO,kBAAkB;AACpD,YAAM,uBAAuB,OAAO,qBAAqB;AACzD,YAAM,yBAAyB,OAAO,uBAAuB;AAC7D,YAAM,wBAAwB,OAAO,0BAA0B;AAE/D,YAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,YAAI;AACJ,YAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,iBAAO;AAAA,QACT,OAAO;AACL,mBAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,QAC7F;AAAA,MACF;AACA,YAAM,SAAS,OAAK;AAClB,cAAM,IAAI,OAAO;AACjB,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,SAAS,UAAQ,WAAS,OAAO,KAAK,MAAM;AAClD,YAAM,KAAK,OAAK,OAAK,MAAM;AAC3B,YAAM,WAAW,OAAO,QAAQ;AAChC,YAAM,cAAc,GAAG,MAAS;AAChC,YAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,YAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AAExC,YAAM,MAAM,OAAK,OAAK,CAAC,EAAE,CAAC;AAE1B,YAAM,iBAAiB,OAAO;AAC9B,YAAM,MAAM,CAAC,KAAK,QAAQ,eAAe,KAAK,KAAK,GAAG;AAEtD,YAAM,aAAa,CAAC,KAAK,QAAQ,UAAU,WAAW,MAAM,IAAI,UAAU,OAAO,UAAU,IAAI,OAAO,OAAO,QAAQ,OAAO,MAAM,MAAM;AACxI,YAAM,WAAW,CAAC,KAAK,QAAQ,QAAQ,GAAG,QAAQ;AAChD,cAAM,MAAM,IAAI,QAAQ,QAAQ,KAAK;AACrC,YAAI,QAAQ,IAAI;AACd,iBAAO,YAAY,GAAG,IAAI,OAAO,MAAM,OAAO,UAAU;AAAA,QAC1D,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,aAAa,CAAC,KAAK,WAAW;AAClC,eAAO,WAAW,KAAK,QAAQ,CAAC;AAAA,MAClC;AAEA,YAAM,YAAY;AAClB,YAAM,SAAS,UAAQ,SAAS;AAChC,YAAM,aAAa,OAAK,EAAE,QAAQ,WAAW,EAAE;AAE/C,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,wBAAwB;AAEhE,YAAM,aAAa,UAAQ,KAAK,aAAa;AAC7C,YAAM,YAAY,UAAQ,KAAK,aAAa;AAC5C,YAAM,mBAAmB,UAAQ,kBAAkB,KAAK,IAAI;AAC5D,YAAM,cAAc,SAAO,yCAAyC,KAAK,GAAG;AAC5E,YAAM,gBAAgB,UAAQ,WAAW,KAAK,IAAI;AAClD,YAAM,WAAW,CAAC,MAAM,OAAO,cAAc;AAC3C,iBAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,KAAK;AACnC,gBAAM,OAAO,KAAK,OAAO,CAAC;AAC1B,cAAI,CAAC,OAAO,IAAI,KAAK,UAAU,IAAI,GAAG;AACpC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,cAAc,CAAC,WAAW,WAAW;AACzC,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,eAAO,UAAU,QAAQ,KAAK,SAAS,WAAW,UAAU,GAAG;AAC7D,qBAAW,SAAS,WAAW,UAAU;AACzC,uBAAa,WAAW,QAAQ,IAAI,SAAS,KAAK,SAAS,SAAS,WAAW;AAAA,QACjF;AACA,eAAO;AAAA,UACL,WAAW;AAAA,UACX,QAAQ;AAAA,QACV;AAAA,MACF;AAEA,YAAM,mBAAmB,CAAC,QAAQ,WAAW;AAC3C,YAAI;AACJ,cAAM,eAAe,OAAO,OAAO,gBAAgB;AACnD,cAAM,kBAAkB,mBAAmB,MAAM;AACjD,cAAM,EAAC,KAAK,UAAS,IAAI;AACzB,YAAI,IAAI,UAAU,UAAU,QAAQ,GAAG,SAAS,MAAM,MAAM;AAC1D,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,UAAU,OAAO;AAC7B,cAAM,aAAa,OAAO,KAAK,UAAQ;AACrC,iBAAO,IAAI,QAAQ,IAAI,KAAK,IAAI,cAAc,KAAK,SAAS,YAAY,CAAC,KAAK,IAAI,mBAAmB,IAAI,MAAM;AAAA,QACjH,CAAC;AACD,cAAM;AAAA,UACJ,WAAW;AAAA,UACX,QAAQ;AAAA,QACV,IAAI,YAAY,IAAI,cAAc,IAAI,SAAS;AAC/C,cAAM,QAAQ,KAAK,IAAI,UAAU,cAAc,IAAI,OAAO,OAAO,QAAQ,OAAO,SAAS,KAAK,IAAI,QAAQ;AAC1G,cAAM,UAAU,WAAW,UAAU,cAAc,YAAY,QAAQ,CAAC,MAAMA,YAAW;AACvF,gBAAM,OAAO,KAAK;AAClB,gBAAM,MAAM,SAAS,MAAMA,SAAQ,IAAI,gBAAgB,CAAC;AACxD,iBAAO,QAAQ,MAAM,cAAc,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM;AAAA,QAC9D,GAAG,IAAI;AACP,YAAI,CAAC,SAAS;AACZ,iBAAO;AAAA,QACT;AACA,YAAI,eAAe,QAAQ;AAC3B,cAAM,YAAY,WAAW,UAAU,QAAQ,WAAW,QAAQ,QAAQ,CAAC,MAAMA,YAAW;AAC1F,yBAAe;AACf,gBAAM,MAAM,SAAS,KAAK,MAAMA,SAAQ,gBAAgB;AACxD,iBAAO,QAAQ,KAAK,MAAM,MAAM;AAAA,QAClC,GAAG,IAAI;AACP,cAAM,SAAS,IAAI,UAAU;AAC7B,YAAI,CAAC,WAAW;AACd,iBAAO,SAAS,cAAc,CAAC;AAAA,QACjC,OAAO;AACL,iBAAO,SAAS,UAAU,WAAW,UAAU,MAAM;AAAA,QACvD;AACA,eAAO,OAAO,QAAQ,WAAW,QAAQ,MAAM;AAC/C,cAAM,UAAU,WAAW,OAAO,SAAS,CAAC;AAC5C,cAAM,UAAU,QAAQ,MAAM,eAAe;AAC7C,YAAI,SAAS;AACX,cAAI,MAAM,QAAQ,CAAC;AACnB,cAAI,WAAW,KAAK,MAAM,GAAG;AAC3B,kBAAM,WAAW,uBAAuB,MAAM;AAC9C,kBAAM,WAAW,QAAQ;AAAA,UAC3B,WAAW,SAAS,KAAK,GAAG,KAAK,CAAC,YAAY,GAAG,GAAG;AAClD,kBAAM,YAAY;AAAA,UACpB;AACA,iBAAO;AAAA,YACL,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,gBAAgB,CAAC,QAAQ,WAAW;AACxC,cAAM,EAAC,KAAK,UAAS,IAAI;AACzB,cAAM,EAAC,KAAK,IAAG,IAAI;AACnB,cAAM,WAAW,UAAU,YAAY;AACvC,kBAAU,OAAO,GAAG;AACpB,cAAM,UAAU;AAChB,cAAM,OAAO;AAAA,UACX;AAAA,UACA,IAAI;AAAA,UACJ,OAAO;AAAA,QACT;AACA,cAAM,kBAAkB,OAAO,SAAS,qBAAqB,IAAI;AACjE,YAAI,CAAC,gBAAgB,mBAAmB,GAAG;AACzC,iBAAO,OAAO,EAAE,YAAY,SAAS,OAAO,GAAG;AAC/C,iBAAO,SAAS,eAAe,IAAI;AACnC,gBAAM,oBAAoB,qBAAqB,MAAM;AACrD,cAAI,SAAS,iBAAiB,GAAG;AAC/B,kBAAM,SAAS,UAAU,QAAQ;AACjC,gBAAI,UAAU,QAAQ,UAAU,iBAAiB;AACjD,gBAAI,sBAAsB,YAAY,CAAC,sBAAsB,MAAM,GAAG;AACpE,kBAAI,UAAU,QAAQ,OAAO,UAAU;AAAA,YACzC;AAAA,UACF;AAAA,QACF;AACA,kBAAU,eAAe,QAAQ;AACjC,eAAO,YAAY;AAAA,MACrB;AACA,YAAM,iBAAiB,YAAU;AAC/B,cAAM,SAAS,iBAAiB,QAAQ,EAAE;AAC1C,YAAI,cAAc,MAAM,GAAG;AACzB,wBAAc,QAAQ,MAAM;AAAA,QAC9B;AAAA,MACF;AACA,YAAM,gBAAgB;AACtB,YAAM,cAAc,YAAU;AAC5B,cAAM,SAAS,iBAAiB,QAAQ,CAAC;AACzC,YAAI,cAAc,MAAM,GAAG;AACzB,wBAAc,QAAQ,MAAM;AAAA,QAC9B;AAAA,MACF;AACA,YAAM,QAAQ,YAAU;AACtB,eAAO,GAAG,WAAW,OAAK;AACxB,cAAI,EAAE,YAAY,MAAM,CAAC,EAAE,mBAAmB,GAAG;AAC/C,wBAAY,MAAM;AAAA,UACpB;AAAA,QACF,CAAC;AACD,eAAO,GAAG,SAAS,OAAK;AACtB,cAAI,EAAE,YAAY,IAAI;AACpB,2BAAe,MAAM;AAAA,UACvB,WAAW,EAAE,YAAY,MAAM,EAAE,YAAY,EAAE,YAAY,KAAK;AAC9D,0BAAc,MAAM;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,YAAY,YAAU;AACjC,mBAAS,MAAM;AACf,gBAAM,MAAM;AAAA,QACd,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAET,GAAG;AAAA;AAAA;;;AC7NH;", "names": ["offset"]}