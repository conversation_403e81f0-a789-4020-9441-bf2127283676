import { Graph, Node, Edge, Cell } from '@antv/x6';
import { Ref } from 'vue';

/**
 * 工作流定义数据结构
 */
export interface WorkflowDefineData {
  id: string;
  businessKey: string;
  name: string;
  definitionView: string;
  isEffective: string;
  delFlag: string;
  createBy: string;
  createTime: string;
  updateBy: string;
  updateTime: string;
  definition: string;
  nodes: string;
  startNodeId: string;
  endNodeId: string;
  edges: string;
  version: string;
}

/**
 * 泳道节点配置
 */
export interface LaneNodeConfig {
  id: string;
  shape: string;
  position: { x: number; y: number };
  width: number;
  height: number;
  label: string;
  attrs?: Record<string, any>;
}

/**
 * 工作流节点配置
 */
export interface WorkflowNodeConfig {
  id: string;
  position: { x: number; y: number };
  width: number;
  height: number;
  label: string;
  parent?: string;
  shape: string;
  attrs?: Record<string, any>;
}

/**
 * 工作流边配置
 */
export interface WorkflowEdgeConfig {
  id: string;
  source: string;
  target: string;
  labels?: string[];
  attrs?: Record<string, any>;
  shape?: string;
}

/**
 * 泳道数据结构
 */
export interface SwimLaneData {
  lanes: LaneNodeConfig[];
  nodes: WorkflowNodeConfig[];
  edges: WorkflowEdgeConfig[];
}

/**
 * 连接桩配置
 */
export interface PortConfig {
  groups: {
    top: PortGroupConfig;
    right: PortGroupConfig;
    bottom: PortGroupConfig;
    left: PortGroupConfig;
  };
  items: PortItemConfig[];
}

export interface PortGroupConfig {
  position: string;
  attrs: {
    circle: {
      r: number;
      magnet: boolean;
      stroke: string;
      fill: string;
      style?: {
        visibility?: string;
      };
    };
  };
}

export interface PortItemConfig {
  id: string;
  group: string;
}

/**
 * 节点类型枚举
 */
export enum NodeType {
  CUSTOM_RECT = 'custom-rect',
  LANE = 'lane',
  LANE_RECT = 'lane-rect',
  LANE_POLYGON = 'lane-polygon',
}

/**
 * 边类型枚举
 */
export enum EdgeType {
  LANE_EDGE = 'lane-edge',
}

/**
 * 工具栏操作类型
 */
export enum ToolbarAction {
  UNDO = 'undo',
  REDO = 'redo',
  DELETE = 'delete',
  SAVE = 'save',
  ADD_LANE = 'addLane',
  GENERATE_JSON = 'generateJson',
}

/**
 * 图形实例接口
 * @link useWorkflowGraph
 */
export interface WorkflowGraphInstance {
  graph: Ref<Graph | null>;
  initGraph: (container: HTMLElement) => void;
  destroyGraph: () => void;
  resizeGraph: (container?: HTMLElement) => void;
  getGraphData: () => CompleteWorkflowData | null;
  loadGraphData: (data: SwimLaneData | CompleteWorkflowData | BackendWorkflowFormat | any) => void;
  convertToBackendFormat: (x6Data: any) => BackendWorkflowFormat;
  convertFromBackendFormat: (backendData: BackendWorkflowFormat) => any;
}

/**
 * 组件面板实例接口
 */
export interface WorkflowStencilInstance {
  stencil: any;
  initStencil: (container: HTMLElement, graph: Graph) => void;
  destroyStencil: () => void;
}

/**
 * 事件处理器接口
 */
export interface WorkflowEventHandlers {
  onCellClick: (args: { e: Event; x: number; y: number; cell: Cell; view: any }) => void;
  onNodeClick: (args: { e: Event; x: number; y: number; node: Node; view: any }) => void;
  onEdgeClick: (args: { e: Event; x: number; y: number; edge: Edge; view: any }) => void;
  onBlankClick: (args: { e: Event; x: number; y: number }) => void;
  onNodeMouseEnter: (args: { e: Event; node: Node; view: any }) => void;
  onNodeMouseLeave: (args: { e: Event; node: Node; view: any }) => void;
}

/**
 * 工作流版本信息
 */
export interface WorkflowVersion {
  id: string;
  timestamp: Date;
  data: any;
  description: string;
}

/**
 * 属性面板数据
 */
export interface NodePropertyData {
  name: string;
  assignee: string;
  assigneeOrgCode: string;
  handler: string;
  type: string;
  description?: string;
}

/**
 * 后端工作流引擎节点格式
 */
export interface BackendWorkflowNode {
  id: number | string;
  name: string;
  type: string;
  assignee?: string;
  assigneeOrgCode?: string;
  handler?: string;
  description?: string;
}

/**
 * 后端工作流引擎边格式
 */
export interface BackendWorkflowEdge {
  id: number | string;
  sourceId: number | string;
  targetId: number | string;
  condition?: string;
  label?: string;
  description?: string;
}

/**
 * 后端工作流引擎格式
 */
export interface BackendWorkflowFormat {
  nodeList: BackendWorkflowNode[];
  edgeList: BackendWorkflowEdge[];
}

/**
 * 完整的工作流数据格式（包含前端和后端格式）
 */
export interface CompleteWorkflowData {
  // 前端X6格式（用于渲染）
  x6Format: any;
  // 后端工作流引擎格式（用于执行）
  backendFormat: BackendWorkflowFormat;
  // 元数据
  metadata?: {
    name?: string;
    businessKey?: string;
    version?: string;
    description?: string;
  };
}
