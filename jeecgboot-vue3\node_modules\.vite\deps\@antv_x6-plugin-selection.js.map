{"version": 3, "sources": ["../../.pnpm/@antv+x6-plugin-selection@2.2.2_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-selection/src/selection.ts", "../../.pnpm/@antv+x6-plugin-selection@2.2.2_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-selection/src/style/raw.ts", "../../.pnpm/@antv+x6-plugin-selection@2.2.2_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-selection/src/api.ts", "../../.pnpm/@antv+x6-plugin-selection@2.2.2_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-selection/src/index.ts"], "sourcesContent": ["import {\n  <PERSON>ctangle,\n  Point,\n  Modifier<PERSON>ey,\n  FunctionExt,\n  Dom,\n  KeyValue,\n  Cell,\n  Node,\n  Edge,\n  Model,\n  Collection,\n  View,\n  CellView,\n  Graph,\n} from '@antv/x6'\n\nexport class SelectionImpl extends View<SelectionImpl.EventArgs> {\n  public readonly options: SelectionImpl.Options\n  protected readonly collection: Collection\n  protected selectionContainer: HTMLElement\n  protected selectionContent: HTMLElement\n  protected boxCount: number\n  protected boxesUpdated: boolean\n\n  public get graph() {\n    return this.options.graph\n  }\n\n  protected get boxClassName() {\n    return this.prefixClassName(Private.classNames.box)\n  }\n\n  protected get $boxes() {\n    return Dom.children(this.container, this.boxClassName)\n  }\n\n  protected get handleOptions() {\n    return this.options\n  }\n\n  constructor(options: SelectionImpl.Options) {\n    super()\n    this.options = options\n\n    if (this.options.model) {\n      this.options.collection = this.options.model.collection\n    }\n\n    if (this.options.collection) {\n      this.collection = this.options.collection\n    } else {\n      this.collection = new Collection([], {\n        comparator: Private.depthComparator,\n      })\n      this.options.collection = this.collection\n    }\n\n    this.boxCount = 0\n\n    this.createContainer()\n    this.startListening()\n  }\n\n  protected startListening() {\n    const graph = this.graph\n    const collection = this.collection\n\n    this.delegateEvents(\n      {\n        [`mousedown .${this.boxClassName}`]: 'onSelectionBoxMouseDown',\n        [`touchstart .${this.boxClassName}`]: 'onSelectionBoxMouseDown',\n      },\n      true,\n    )\n\n    graph.on('scale', this.onGraphTransformed, this)\n    graph.on('translate', this.onGraphTransformed, this)\n    graph.model.on('updated', this.onModelUpdated, this)\n\n    collection.on('added', this.onCellAdded, this)\n    collection.on('removed', this.onCellRemoved, this)\n    collection.on('reseted', this.onReseted, this)\n    collection.on('updated', this.onCollectionUpdated, this)\n    collection.on('node:change:position', this.onNodePositionChanged, this)\n    collection.on('cell:changed', this.onCellChanged, this)\n  }\n\n  protected stopListening() {\n    const graph = this.graph\n    const collection = this.collection\n\n    this.undelegateEvents()\n\n    graph.off('scale', this.onGraphTransformed, this)\n    graph.off('translate', this.onGraphTransformed, this)\n    graph.model.off('updated', this.onModelUpdated, this)\n\n    collection.off('added', this.onCellAdded, this)\n    collection.off('removed', this.onCellRemoved, this)\n    collection.off('reseted', this.onReseted, this)\n    collection.off('updated', this.onCollectionUpdated, this)\n    collection.off('node:change:position', this.onNodePositionChanged, this)\n    collection.off('cell:changed', this.onCellChanged, this)\n  }\n\n  protected onRemove() {\n    this.stopListening()\n  }\n\n  protected onGraphTransformed() {\n    this.updateSelectionBoxes()\n  }\n\n  protected onCellChanged() {\n    this.updateSelectionBoxes()\n  }\n\n  protected translating: boolean\n\n  protected onNodePositionChanged({\n    node,\n    options,\n  }: Collection.EventArgs['node:change:position']) {\n    const { showNodeSelectionBox, pointerEvents } = this.options\n    const { ui, selection, translateBy, snapped } = options\n\n    const allowTranslating =\n      (showNodeSelectionBox !== true || (pointerEvents && this.getPointerEventsValue(pointerEvents) === 'none')) &&\n      !this.translating &&\n      !selection\n\n    const translateByUi = ui && translateBy && node.id === translateBy\n\n    if (allowTranslating && (translateByUi || snapped)) {\n      this.translating = true\n      const current = node.position()\n      const previous = node.previous('position')!\n      const dx = current.x - previous.x\n      const dy = current.y - previous.y\n\n      if (dx !== 0 || dy !== 0) {\n        this.translateSelectedNodes(dx, dy, node, options)\n      }\n      this.translating = false\n    }\n  }\n\n  protected onModelUpdated({ removed }: Collection.EventArgs['updated']) {\n    if (removed && removed.length) {\n      this.unselect(removed)\n    }\n  }\n\n  isEmpty() {\n    return this.length <= 0\n  }\n\n  isSelected(cell: Cell | string) {\n    return this.collection.has(cell)\n  }\n\n  get length() {\n    return this.collection.length\n  }\n\n  get cells() {\n    return this.collection.toArray()\n  }\n\n  select(cells: Cell | Cell[], options: SelectionImpl.AddOptions = {}) {\n    options.dryrun = true\n    const items = this.filter(Array.isArray(cells) ? cells : [cells])\n    this.collection.add(items, options)\n    return this\n  }\n\n  unselect(cells: Cell | Cell[], options: SelectionImpl.RemoveOptions = {}) {\n    // dryrun to prevent cell be removed from graph\n    options.dryrun = true\n    this.collection.remove(Array.isArray(cells) ? cells : [cells], options)\n    return this\n  }\n\n  reset(cells?: Cell | Cell[], options: SelectionImpl.SetOptions = {}) {\n    if (cells) {\n      if (options.batch) {\n        const filterCells = this.filter(Array.isArray(cells) ? cells : [cells])\n        this.collection.reset(filterCells, { ...options, ui: true })\n        return this\n      }\n\n      const prev = this.cells\n      const next = this.filter(Array.isArray(cells) ? cells : [cells])\n      const prevMap: KeyValue<Cell> = {}\n      const nextMap: KeyValue<Cell> = {}\n      prev.forEach((cell) => (prevMap[cell.id] = cell))\n      next.forEach((cell) => (nextMap[cell.id] = cell))\n      const added: Cell[] = []\n      const removed: Cell[] = []\n      next.forEach((cell) => {\n        if (!prevMap[cell.id]) {\n          added.push(cell)\n        }\n      })\n      prev.forEach((cell) => {\n        if (!nextMap[cell.id]) {\n          removed.push(cell)\n        }\n      })\n\n      if (removed.length) {\n        this.unselect(removed, { ...options, ui: true })\n      }\n\n      if (added.length) {\n        this.select(added, { ...options, ui: true })\n      }\n\n      if (removed.length === 0 && added.length === 0) {\n        this.updateContainer()\n      }\n\n      return this\n    }\n\n    return this.clean(options)\n  }\n\n  clean(options: SelectionImpl.SetOptions = {}) {\n    if (this.length) {\n      if (options.batch === false) {\n        this.unselect(this.cells, options)\n      } else {\n        this.collection.reset([], { ...options, ui: true })\n      }\n    }\n    return this\n  }\n\n  setFilter(filter?: SelectionImpl.Filter) {\n    this.options.filter = filter\n  }\n\n  setContent(content?: SelectionImpl.Content) {\n    this.options.content = content\n  }\n\n  startSelecting(evt: Dom.MouseDownEvent) {\n    // Flow: startSelecting => adjustSelection => stopSelecting\n\n    evt = this.normalizeEvent(evt) // eslint-disable-line\n    this.clean()\n    let x\n    let y\n    const graphContainer = this.graph.container\n    if (\n      evt.offsetX != null &&\n      evt.offsetY != null &&\n      graphContainer.contains(evt.target)\n    ) {\n      x = evt.offsetX\n      y = evt.offsetY\n    } else {\n      const offset = Dom.offset(graphContainer)\n      const scrollLeft = graphContainer.scrollLeft\n      const scrollTop = graphContainer.scrollTop\n      x = evt.clientX - offset.left + window.pageXOffset + scrollLeft\n      y = evt.clientY - offset.top + window.pageYOffset + scrollTop\n    }\n\n    Dom.css(this.container, {\n      top: y,\n      left: x,\n      width: 1,\n      height: 1,\n    })\n\n    this.setEventData<EventData.Selecting>(evt, {\n      action: 'selecting',\n      clientX: evt.clientX,\n      clientY: evt.clientY,\n      offsetX: x,\n      offsetY: y,\n      scrollerX: 0,\n      scrollerY: 0,\n      moving: false,\n    })\n\n    this.delegateDocumentEvents(Private.documentEvents, evt.data)\n  }\n\n  filter(cells: Cell[]) {\n    const filter = this.options.filter\n\n    return cells.filter((cell) => {\n      if (Array.isArray(filter)) {\n        return filter.some((item) => {\n          if (typeof item === 'string') {\n            return cell.shape === item\n          }\n          return cell.id === item.id\n        })\n      }\n      if (typeof filter === 'function') {\n        return FunctionExt.call(filter, this.graph, cell)\n      }\n\n      return true\n    })\n  }\n\n  protected stopSelecting(evt: Dom.MouseUpEvent) {\n    const graph = this.graph\n    const eventData = this.getEventData<EventData.Common>(evt)\n    const action = eventData.action\n    switch (action) {\n      case 'selecting': {\n        let width = Dom.width(this.container)\n        let height = Dom.height(this.container)\n        const offset = Dom.offset(this.container)\n        const origin = graph.pageToLocal(offset.left, offset.top)\n        const scale = graph.transform.getScale()\n        width /= scale.sx\n        height /= scale.sy\n        const rect = new Rectangle(origin.x, origin.y, width, height)\n        const cells = this.getCellViewsInArea(rect).map((view) => view.cell)\n        this.reset(cells, { batch: true })\n        this.hideRubberband()\n        break\n      }\n\n      case 'translating': {\n        const client = graph.snapToGrid(evt.clientX, evt.clientY)\n        if (!this.options.following) {\n          const data = eventData as EventData.Translating\n          this.updateSelectedNodesPosition({\n            dx: data.clientX - data.originX,\n            dy: data.clientY - data.originY,\n          })\n        }\n        this.graph.model.stopBatch('move-selection')\n        this.notifyBoxEvent('box:mouseup', evt, client.x, client.y)\n        break\n      }\n\n      default: {\n        this.clean()\n        break\n      }\n    }\n  }\n\n  protected onMouseUp(evt: Dom.MouseUpEvent) {\n    const action = this.getEventData<EventData.Common>(evt).action\n    if (action) {\n      this.stopSelecting(evt)\n      this.undelegateDocumentEvents()\n    }\n  }\n\n  protected onSelectionBoxMouseDown(evt: Dom.MouseDownEvent) {\n    if (!this.options.following) {\n      evt.stopPropagation()\n    }\n\n    const e = this.normalizeEvent(evt)\n\n    if (this.options.movable) {\n      this.startTranslating(e)\n    }\n\n    const activeView = this.getCellViewFromElem(e.target)!\n    this.setEventData<EventData.SelectionBox>(e, { activeView })\n    const client = this.graph.snapToGrid(e.clientX, e.clientY)\n    this.notifyBoxEvent('box:mousedown', e, client.x, client.y)\n    this.delegateDocumentEvents(Private.documentEvents, e.data)\n  }\n\n  protected startTranslating(evt: Dom.MouseDownEvent) {\n    this.graph.model.startBatch('move-selection')\n    const client = this.graph.snapToGrid(evt.clientX, evt.clientY)\n    this.setEventData<EventData.Translating>(evt, {\n      action: 'translating',\n      clientX: client.x,\n      clientY: client.y,\n      originX: client.x,\n      originY: client.y,\n    })\n  }\n\n  private getRestrictArea(): Rectangle.RectangleLike | null {\n    const restrict = this.graph.options.translating.restrict\n    const area =\n      typeof restrict === 'function'\n        ? FunctionExt.call(restrict, this.graph, null)\n        : restrict\n\n    if (typeof area === 'number') {\n      return this.graph.transform.getGraphArea().inflate(area)\n    }\n\n    if (area === true) {\n      return this.graph.transform.getGraphArea()\n    }\n\n    return area || null\n  }\n\n  protected getSelectionOffset(client: Point, data: EventData.Translating) {\n    let dx = client.x - data.clientX\n    let dy = client.y - data.clientY\n    const restrict = this.getRestrictArea()\n    if (restrict) {\n      const cells = this.collection.toArray()\n      const totalBBox =\n        Cell.getCellsBBox(cells, { deep: true }) || Rectangle.create()\n      const minDx = restrict.x - totalBBox.x\n      const minDy = restrict.y - totalBBox.y\n      const maxDx =\n        restrict.x + restrict.width - (totalBBox.x + totalBBox.width)\n      const maxDy =\n        restrict.y + restrict.height - (totalBBox.y + totalBBox.height)\n\n      if (dx < minDx) {\n        dx = minDx\n      }\n      if (dy < minDy) {\n        dy = minDy\n      }\n      if (maxDx < dx) {\n        dx = maxDx\n      }\n      if (maxDy < dy) {\n        dy = maxDy\n      }\n\n      if (!this.options.following) {\n        const offsetX = client.x - data.originX\n        const offsetY = client.y - data.originY\n        dx = offsetX <= minDx || offsetX >= maxDx ? 0 : dx\n        dy = offsetY <= minDy || offsetY >= maxDy ? 0 : dy\n      }\n    }\n\n    return {\n      dx,\n      dy,\n    }\n  }\n\n  private updateElementPosition(elem: Element, dLeft: number, dTop: number) {\n    const strLeft = Dom.css(elem, 'left')\n    const strTop = Dom.css(elem, 'top')\n    const left = strLeft ? parseFloat(strLeft) : 0\n    const top = strTop ? parseFloat(strTop) : 0\n\n    Dom.css(elem, 'left', left + dLeft)\n    Dom.css(elem, 'top', top + dTop)\n  }\n\n  protected updateSelectedNodesPosition(offset: { dx: number; dy: number }) {\n    const { dx, dy } = offset\n    if (dx || dy) {\n      if ((this.translateSelectedNodes(dx, dy), this.boxesUpdated)) {\n        if (this.collection.length > 1) {\n          this.updateSelectionBoxes()\n        }\n      } else {\n        const scale = this.graph.transform.getScale()\n        for (\n          let i = 0, $boxes = this.$boxes, len = $boxes.length;\n          i < len;\n          i += 1\n        ) {\n          this.updateElementPosition($boxes[i], dx * scale.sx, dy * scale.sy)\n        }\n        this.updateElementPosition(\n          this.selectionContainer,\n          dx * scale.sx,\n          dy * scale.sy,\n        )\n      }\n    }\n  }\n\n  protected autoScrollGraph(x: number, y: number) {\n    const scroller = this.graph.getPlugin<any>('scroller')\n    if (scroller) {\n      return scroller.autoScroll(x, y)\n    }\n    return { scrollerX: 0, scrollerY: 0 }\n  }\n\n  protected adjustSelection(evt: Dom.MouseMoveEvent) {\n    const e = this.normalizeEvent(evt)\n    const eventData = this.getEventData<EventData.Common>(e)\n    const action = eventData.action\n    switch (action) {\n      case 'selecting': {\n        const data = eventData as EventData.Selecting\n        if (data.moving !== true) {\n          Dom.appendTo(this.container, this.graph.container)\n          this.showRubberband()\n          data.moving = true\n        }\n\n        const { scrollerX, scrollerY } = this.autoScrollGraph(\n          e.clientX,\n          e.clientY,\n        )\n        data.scrollerX += scrollerX\n        data.scrollerY += scrollerY\n\n        const dx = e.clientX - data.clientX + data.scrollerX\n        const dy = e.clientY - data.clientY + data.scrollerY\n\n        const left = parseInt(Dom.css(this.container, 'left') || '0', 10)\n        const top = parseInt(Dom.css(this.container, 'top') || '0', 10)\n        Dom.css(this.container, {\n          left: dx < 0 ? data.offsetX + dx : left,\n          top: dy < 0 ? data.offsetY + dy : top,\n          width: Math.abs(dx),\n          height: Math.abs(dy),\n        })\n        break\n      }\n\n      case 'translating': {\n        const client = this.graph.snapToGrid(e.clientX, e.clientY)\n        const data = eventData as EventData.Translating\n        const offset = this.getSelectionOffset(client, data)\n        if (this.options.following) {\n          this.updateSelectedNodesPosition(offset)\n        } else {\n          this.updateContainerPosition(offset)\n        }\n        if (offset.dx) {\n          data.clientX = client.x\n        }\n        if (offset.dy) {\n          data.clientY = client.y\n        }\n        this.notifyBoxEvent('box:mousemove', evt, client.x, client.y)\n        break\n      }\n\n      default:\n        break\n    }\n\n    this.boxesUpdated = false\n  }\n\n  protected translateSelectedNodes(\n    dx: number,\n    dy: number,\n    exclude?: Cell,\n    otherOptions?: KeyValue,\n  ) {\n    const map: { [id: string]: boolean } = {}\n    const excluded: Cell[] = []\n\n    if (exclude) {\n      map[exclude.id] = true\n    }\n\n    this.collection.toArray().forEach((cell) => {\n      cell.getDescendants({ deep: true }).forEach((child) => {\n        map[child.id] = true\n      })\n    })\n    if (otherOptions && otherOptions.translateBy) {\n      const currentCell = this.graph.getCellById(otherOptions.translateBy)\n      if (currentCell) {\n        map[currentCell.id] = true\n        currentCell.getDescendants({ deep: true }).forEach((child) => {\n          map[child.id] = true\n        })\n        excluded.push(currentCell)\n      }\n    }\n\n    this.collection.toArray().forEach((cell) => {\n      if (!map[cell.id]) {\n        const options = {\n          ...otherOptions,\n          selection: this.cid,\n          exclude: excluded,\n        }\n        cell.translate(dx, dy, options)\n        this.graph.model.getConnectedEdges(cell).forEach((edge) => {\n          if (!map[edge.id]) {\n            edge.translate(dx, dy, options)\n            map[edge.id] = true\n          }\n        })\n      }\n    })\n  }\n\n  protected getCellViewsInArea(rect: Rectangle) {\n    const graph = this.graph\n    const options = {\n      strict: this.options.strict,\n    }\n    let views: CellView[] = []\n\n    if (this.options.rubberNode) {\n      views = views.concat(\n        graph.model\n          .getNodesInArea(rect, options)\n          .map((node) => graph.renderer.findViewByCell(node))\n          .filter((view) => view != null) as CellView[],\n      )\n    }\n\n    if (this.options.rubberEdge) {\n      views = views.concat(\n        graph.model\n          .getEdgesInArea(rect, options)\n          .map((edge) => graph.renderer.findViewByCell(edge))\n          .filter((view) => view != null) as CellView[],\n      )\n    }\n\n    return views\n  }\n\n  protected notifyBoxEvent<\n    K extends keyof SelectionImpl.BoxEventArgs,\n    T extends Dom.EventObject,\n  >(name: K, e: T, x: number, y: number) {\n    const data = this.getEventData<EventData.SelectionBox>(e)\n    const view = data.activeView\n    this.trigger(name, { e, view, x, y, cell: view.cell })\n  }\n\n  protected getSelectedClassName(cell: Cell) {\n    return this.prefixClassName(`${cell.isNode() ? 'node' : 'edge'}-selected`)\n  }\n\n  protected addCellSelectedClassName(cell: Cell) {\n    const view = this.graph.renderer.findViewByCell(cell)\n    if (view) {\n      view.addClass(this.getSelectedClassName(cell))\n    }\n  }\n\n  protected removeCellUnSelectedClassName(cell: Cell) {\n    const view = this.graph.renderer.findViewByCell(cell)\n    if (view) {\n      view.removeClass(this.getSelectedClassName(cell))\n    }\n  }\n\n  protected destroySelectionBox(cell: Cell) {\n    this.removeCellUnSelectedClassName(cell)\n\n    if (this.canShowSelectionBox(cell)) {\n      Dom.remove(this.container.querySelector(`[data-cell-id=\"${cell.id}\"]`))\n      if (this.$boxes.length === 0) {\n        this.hide()\n      }\n      this.boxCount = Math.max(0, this.boxCount - 1)\n    }\n  }\n\n  protected destroyAllSelectionBoxes(cells: Cell[]) {\n    cells.forEach((cell) => this.removeCellUnSelectedClassName(cell))\n\n    this.hide()\n    Dom.remove(this.$boxes)\n    this.boxCount = 0\n  }\n\n  hide() {\n    Dom.removeClass(\n      this.container,\n      this.prefixClassName(Private.classNames.rubberband),\n    )\n    Dom.removeClass(\n      this.container,\n      this.prefixClassName(Private.classNames.selected),\n    )\n  }\n\n  protected showRubberband() {\n    Dom.addClass(\n      this.container,\n      this.prefixClassName(Private.classNames.rubberband),\n    )\n  }\n\n  protected hideRubberband() {\n    Dom.removeClass(\n      this.container,\n      this.prefixClassName(Private.classNames.rubberband),\n    )\n  }\n\n  protected showSelected() {\n    Dom.removeAttribute(this.container, 'style')\n    Dom.addClass(\n      this.container,\n      this.prefixClassName(Private.classNames.selected),\n    )\n  }\n\n  protected createContainer() {\n    this.container = document.createElement('div')\n    Dom.addClass(this.container, this.prefixClassName(Private.classNames.root))\n    if (this.options.className) {\n      Dom.addClass(this.container, this.options.className)\n    }\n\n    this.selectionContainer = document.createElement('div')\n    Dom.addClass(\n      this.selectionContainer,\n      this.prefixClassName(Private.classNames.inner),\n    )\n\n    this.selectionContent = document.createElement('div')\n    Dom.addClass(\n      this.selectionContent,\n      this.prefixClassName(Private.classNames.content),\n    )\n\n    Dom.append(this.selectionContainer, this.selectionContent)\n    Dom.attr(\n      this.selectionContainer,\n      'data-selection-length',\n      this.collection.length,\n    )\n\n    Dom.prepend(this.container, this.selectionContainer)\n  }\n\n  protected updateContainerPosition(offset: { dx: number; dy: number }) {\n    if (offset.dx || offset.dy) {\n      this.updateElementPosition(this.selectionContainer, offset.dx, offset.dy)\n    }\n  }\n\n  protected updateContainer() {\n    const origin = { x: Infinity, y: Infinity }\n    const corner = { x: 0, y: 0 }\n    const cells = this.collection\n      .toArray()\n      .filter((cell) => this.canShowSelectionBox(cell))\n\n    cells.forEach((cell) => {\n      const view = this.graph.renderer.findViewByCell(cell)\n      if (view) {\n        const bbox = view.getBBox({\n          useCellGeometry: true,\n        })\n        origin.x = Math.min(origin.x, bbox.x)\n        origin.y = Math.min(origin.y, bbox.y)\n        corner.x = Math.max(corner.x, bbox.x + bbox.width)\n        corner.y = Math.max(corner.y, bbox.y + bbox.height)\n      }\n    })\n\n    Dom.css(this.selectionContainer, {\n      position: 'absolute',\n      pointerEvents: 'none',\n      left: origin.x,\n      top: origin.y,\n      width: corner.x - origin.x,\n      height: corner.y - origin.y,\n    })\n    Dom.attr(\n      this.selectionContainer,\n      'data-selection-length',\n      this.collection.length,\n    )\n\n    const boxContent = this.options.content\n    if (boxContent) {\n      if (typeof boxContent === 'function') {\n        const content = FunctionExt.call(\n          boxContent,\n          this.graph,\n          this,\n          this.selectionContent,\n        )\n        if (content) {\n          this.selectionContent.innerHTML = content\n        }\n      } else {\n        this.selectionContent.innerHTML = boxContent\n      }\n    }\n\n    if (this.collection.length > 0 && !this.container.parentNode) {\n      Dom.appendTo(this.container, this.graph.container)\n    } else if (this.collection.length <= 0 && this.container.parentNode) {\n      this.container.parentNode.removeChild(this.container)\n    }\n  }\n\n  protected canShowSelectionBox(cell: Cell) {\n    return (\n      (cell.isNode() && this.options.showNodeSelectionBox === true) ||\n      (cell.isEdge() && this.options.showEdgeSelectionBox === true)\n    )\n  }\n\n  protected getPointerEventsValue(pointerEvents: 'none' | 'auto' | ((cells: Cell[]) => 'none' | 'auto')) {\n    return typeof pointerEvents === 'string'\n      ? pointerEvents\n      : pointerEvents(this.cells)\n  }\n\n  protected createSelectionBox(cell: Cell) {\n    this.addCellSelectedClassName(cell)\n\n    if (this.canShowSelectionBox(cell)) {\n      const view = this.graph.renderer.findViewByCell(cell)\n      if (view) {\n        const bbox = view.getBBox({\n          useCellGeometry: true,\n        })\n\n        const className = this.boxClassName\n        const box = document.createElement('div')\n        const pointerEvents = this.options.pointerEvents\n        Dom.addClass(box, className)\n        Dom.addClass(box, `${className}-${cell.isNode() ? 'node' : 'edge'}`)\n        Dom.attr(box, 'data-cell-id', cell.id)\n        Dom.css(box, {\n          position: 'absolute',\n          left: bbox.x,\n          top: bbox.y,\n          width: bbox.width,\n          height: bbox.height,\n          pointerEvents: pointerEvents\n            ? this.getPointerEventsValue(pointerEvents)\n            : 'auto',\n        })\n        Dom.appendTo(box, this.container)\n        this.showSelected()\n        this.boxCount += 1\n      }\n    }\n  }\n\n  protected updateSelectionBoxes() {\n    if (this.collection.length > 0) {\n      this.boxesUpdated = true\n      this.confirmUpdate()\n      // this.graph.renderer.requestViewUpdate(this as any, 1, options)\n    }\n  }\n\n  confirmUpdate() {\n    if (this.boxCount) {\n      this.hide()\n      for (\n        let i = 0, $boxes = this.$boxes, len = $boxes.length;\n        i < len;\n        i += 1\n      ) {\n        const box = $boxes[i]\n        const cellId = Dom.attr(box, 'data-cell-id')\n        Dom.remove(box)\n        this.boxCount -= 1\n        const cell = this.collection.get(cellId)\n        if (cell) {\n          this.createSelectionBox(cell)\n        }\n      }\n\n      this.updateContainer()\n    }\n    return 0\n  }\n\n  protected getCellViewFromElem(elem: Element) {\n    const id = elem.getAttribute('data-cell-id')\n    if (id) {\n      const cell = this.collection.get(id)\n      if (cell) {\n        return this.graph.renderer.findViewByCell(cell)\n      }\n    }\n    return null\n  }\n\n  protected onCellRemoved({ cell }: Collection.EventArgs['removed']) {\n    this.destroySelectionBox(cell)\n    this.updateContainer()\n  }\n\n  protected onReseted({ previous, current }: Collection.EventArgs['reseted']) {\n    this.destroyAllSelectionBoxes(previous)\n    current.forEach((cell) => {\n      this.listenCellRemoveEvent(cell)\n      this.createSelectionBox(cell)\n    })\n    this.updateContainer()\n  }\n\n  protected onCellAdded({ cell }: Collection.EventArgs['added']) {\n    // The collection do not known the cell was removed when cell was\n    // removed by interaction(such as, by \"delete\" shortcut), so we should\n    // manually listen to cell's remove event.\n    this.listenCellRemoveEvent(cell)\n    this.createSelectionBox(cell)\n    this.updateContainer()\n  }\n\n  protected listenCellRemoveEvent(cell: Cell) {\n    cell.off('removed', this.onCellRemoved, this)\n    cell.on('removed', this.onCellRemoved, this)\n  }\n\n  protected onCollectionUpdated({\n    added,\n    removed,\n    options,\n  }: Collection.EventArgs['updated']) {\n    added.forEach((cell) => {\n      this.trigger('cell:selected', { cell, options })\n      if (cell.isNode()) {\n        this.trigger('node:selected', { cell, options, node: cell })\n      } else if (cell.isEdge()) {\n        this.trigger('edge:selected', { cell, options, edge: cell })\n      }\n    })\n\n    removed.forEach((cell) => {\n      this.trigger('cell:unselected', { cell, options })\n      if (cell.isNode()) {\n        this.trigger('node:unselected', { cell, options, node: cell })\n      } else if (cell.isEdge()) {\n        this.trigger('edge:unselected', { cell, options, edge: cell })\n      }\n    })\n\n    const args = {\n      added,\n      removed,\n      options,\n      selected: this.cells.filter((cell) => !!this.graph.getCellById(cell.id)),\n    }\n    this.trigger('selection:changed', args)\n  }\n\n  // #endregion\n\n  @View.dispose()\n  dispose() {\n    this.clean()\n    this.remove()\n    this.off()\n  }\n}\n\nexport namespace SelectionImpl {\n  type SelectionEventType = 'leftMouseDown' | 'mouseWheelDown'\n\n  export interface CommonOptions {\n    model?: Model\n    collection?: Collection\n    className?: string\n    strict?: boolean\n    filter?: Filter\n    modifiers?: string | ModifierKey[] | null\n    multiple?: boolean\n    multipleSelectionModifiers?: string | ModifierKey[] | null\n\n    selectCellOnMoved?: boolean\n    selectNodeOnMoved?: boolean\n    selectEdgeOnMoved?: boolean\n\n    showEdgeSelectionBox?: boolean\n    showNodeSelectionBox?: boolean\n    movable?: boolean\n    following?: boolean\n    content?: Content\n\n    // Can select node or edge when rubberband\n    rubberband?: boolean\n    rubberNode?: boolean\n    rubberEdge?: boolean\n\n    // Whether to respond event on the selectionBox\n    pointerEvents?: 'none' | 'auto' | ((cells: Cell[]) => 'none' | 'auto')\n\n    // with which mouse button the selection can be started\n    eventTypes?: SelectionEventType[]\n  }\n\n  export interface Options extends CommonOptions {\n    graph: Graph\n  }\n\n  export type Content =\n    | null\n    | false\n    | string\n    | ((\n        this: Graph,\n        selection: SelectionImpl,\n        contentElement: HTMLElement,\n      ) => string)\n\n  export type Filter =\n    | null\n    | (string | { id: string })[]\n    | ((this: Graph, cell: Cell) => boolean)\n\n  export interface SetOptions extends Collection.SetOptions {\n    batch?: boolean\n  }\n\n  export interface AddOptions extends Collection.AddOptions {}\n\n  export interface RemoveOptions extends Collection.RemoveOptions {}\n}\n\nexport namespace SelectionImpl {\n  interface SelectionBoxEventArgs<T> {\n    e: T\n    view: CellView\n    cell: Cell\n    x: number\n    y: number\n  }\n\n  export interface BoxEventArgs {\n    'box:mousedown': SelectionBoxEventArgs<Dom.MouseDownEvent>\n    'box:mousemove': SelectionBoxEventArgs<Dom.MouseMoveEvent>\n    'box:mouseup': SelectionBoxEventArgs<Dom.MouseUpEvent>\n  }\n\n  export interface SelectionEventArgs {\n    'cell:selected': { cell: Cell; options: Model.SetOptions }\n    'node:selected': { cell: Cell; node: Node; options: Model.SetOptions }\n    'edge:selected': { cell: Cell; edge: Edge; options: Model.SetOptions }\n    'cell:unselected': { cell: Cell; options: Model.SetOptions }\n    'node:unselected': { cell: Cell; node: Node; options: Model.SetOptions }\n    'edge:unselected': { cell: Cell; edge: Edge; options: Model.SetOptions }\n    'selection:changed': {\n      added: Cell[]\n      removed: Cell[]\n      selected: Cell[]\n      options: Model.SetOptions\n    }\n  }\n\n  export interface EventArgs extends BoxEventArgs, SelectionEventArgs {}\n}\n\n// private\n// -------\nnamespace Private {\n  const base = 'widget-selection'\n\n  export const classNames = {\n    root: base,\n    inner: `${base}-inner`,\n    box: `${base}-box`,\n    content: `${base}-content`,\n    rubberband: `${base}-rubberband`,\n    selected: `${base}-selected`,\n  }\n\n  export const documentEvents = {\n    mousemove: 'adjustSelection',\n    touchmove: 'adjustSelection',\n    mouseup: 'onMouseUp',\n    touchend: 'onMouseUp',\n    touchcancel: 'onMouseUp',\n  }\n\n  export function depthComparator(cell: Cell) {\n    return cell.getAncestors().length\n  }\n}\n\nnamespace EventData {\n  export interface Common {\n    action: 'selecting' | 'translating'\n  }\n\n  export interface Selecting extends Common {\n    action: 'selecting'\n    moving?: boolean\n    clientX: number\n    clientY: number\n    offsetX: number\n    offsetY: number\n    scrollerX: number\n    scrollerY: number\n  }\n\n  export interface Translating extends Common {\n    action: 'translating'\n    clientX: number\n    clientY: number\n    originX: number\n    originY: number\n  }\n\n  export interface SelectionBox {\n    activeView: CellView\n  }\n\n  export interface Rotation {\n    rotated?: boolean\n    center: Point.PointLike\n    start: number\n    angles: { [id: string]: number }\n  }\n\n  export interface Resizing {\n    resized?: boolean\n    bbox: Rectangle\n    cells: Cell[]\n    minWidth: number\n    minHeight: number\n  }\n}\n", "/* eslint-disable */\n\n/**\n * Auto generated file, do not modify it!\n */\n\nexport const content = `.x6-widget-selection {\n  position: absolute;\n  top: 0;\n  left: 0;\n  display: none;\n  width: 0;\n  height: 0;\n  touch-action: none;\n}\n.x6-widget-selection-rubberband {\n  display: block;\n  overflow: visible;\n  opacity: 0.3;\n}\n.x6-widget-selection-selected {\n  display: block;\n}\n.x6-widget-selection-box {\n  cursor: move;\n}\n.x6-widget-selection-inner[data-selection-length='0'],\n.x6-widget-selection-inner[data-selection-length='1'] {\n  display: none;\n}\n.x6-widget-selection-content {\n  position: absolute;\n  top: 100%;\n  right: -20px;\n  left: -20px;\n  margin-top: 30px;\n  padding: 6px;\n  line-height: 14px;\n  text-align: center;\n  border-radius: 6px;\n}\n.x6-widget-selection-content:empty {\n  display: none;\n}\n.x6-widget-selection-rubberband {\n  background-color: #3498db;\n  border: 2px solid #2980b9;\n}\n.x6-widget-selection-box {\n  box-sizing: content-box !important;\n  margin-top: -4px;\n  margin-left: -4px;\n  padding-right: 4px;\n  padding-bottom: 4px;\n  border: 2px dashed #feb663;\n  box-shadow: 2px 2px 5px #d3d3d3;\n}\n.x6-widget-selection-inner {\n  box-sizing: content-box !important;\n  margin-top: -8px;\n  margin-left: -8px;\n  padding-right: 12px;\n  padding-bottom: 12px;\n  border: 2px solid #feb663;\n  box-shadow: 2px 2px 5px #d3d3d3;\n}\n.x6-widget-selection-content {\n  color: #fff;\n  font-size: 10px;\n  background-color: #6a6b8a;\n}\n`\n", "import { Graph, Cell, ModifierKey } from '@antv/x6'\nimport { Selection } from './index'\nimport { SelectionImpl } from './selection'\n\ndeclare module '@antv/x6/lib/graph/graph' {\n  interface Graph {\n    isSelectionEnabled: () => boolean\n    enableSelection: () => Graph\n    disableSelection: () => Graph\n    toggleSelection: (enabled?: boolean) => Graph\n    isMultipleSelection: () => boolean\n    enableMultipleSelection: () => Graph\n    disableMultipleSelection: () => Graph\n    toggleMultipleSelection: (multiple?: boolean) => Graph\n    isSelectionMovable: () => boolean\n    enableSelectionMovable: () => Graph\n    disableSelectionMovable: () => Graph\n    toggleSelectionMovable: (movable?: boolean) => Graph\n    isRubberbandEnabled: () => boolean\n    enableRubberband: () => Graph\n    disableRubberband: () => Graph\n    toggleRubberband: (enabled?: boolean) => Graph\n    isStrictRubberband: () => boolean\n    enableStrictRubberband: () => Graph\n    disableStrictRubberband: () => Graph\n    toggleStrictRubberband: (strict?: boolean) => Graph\n    setRubberbandModifiers: (modifiers?: string | ModifierKey[] | null) => Graph\n    setSelectionFilter: (filter?: Selection.Filter) => Graph\n    setSelectionDisplayContent: (content?: Selection.Content) => Graph\n    isSelectionEmpty: () => boolean\n    cleanSelection: (options?: Selection.SetOptions) => Graph\n    resetSelection: (\n      cells?: Cell | string | (Cell | string)[],\n      options?: Selection.SetOptions,\n    ) => Graph\n    getSelectedCells: () => Cell[]\n    getSelectedCellCount: () => number\n    isSelected: (cell: Cell | string) => boolean\n    select: (\n      cells: Cell | string | (Cell | string)[],\n      options?: Selection.AddOptions,\n    ) => Graph\n    unselect: (\n      cells: Cell | string | (Cell | string)[],\n      options?: Selection.RemoveOptions,\n    ) => Graph\n  }\n}\n\ndeclare module '@antv/x6/lib/graph/events' {\n  interface EventArgs extends SelectionImpl.SelectionEventArgs {}\n}\n\nGraph.prototype.isSelectionEnabled = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    return selection.isEnabled()\n  }\n  return false\n}\n\nGraph.prototype.enableSelection = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.enable()\n  }\n  return this\n}\n\nGraph.prototype.disableSelection = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.disable()\n  }\n  return this\n}\n\nGraph.prototype.toggleSelection = function (enabled?: boolean) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.toggleEnabled(enabled)\n  }\n  return this\n}\n\nGraph.prototype.isMultipleSelection = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    return selection.isMultipleSelection()\n  }\n  return false\n}\n\nGraph.prototype.enableMultipleSelection = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.enableMultipleSelection()\n  }\n  return this\n}\n\nGraph.prototype.disableMultipleSelection = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.disableMultipleSelection()\n  }\n  return this\n}\n\nGraph.prototype.toggleMultipleSelection = function (multiple?: boolean) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.toggleMultipleSelection(multiple)\n  }\n  return this\n}\n\nGraph.prototype.isSelectionMovable = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    return selection.isSelectionMovable()\n  }\n  return false\n}\n\nGraph.prototype.enableSelectionMovable = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.enableSelectionMovable()\n  }\n  return this\n}\n\nGraph.prototype.disableSelectionMovable = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.disableSelectionMovable()\n  }\n  return this\n}\n\nGraph.prototype.toggleSelectionMovable = function (movable?: boolean) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.toggleSelectionMovable(movable)\n  }\n  return this\n}\n\nGraph.prototype.isRubberbandEnabled = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    return selection.isRubberbandEnabled()\n  }\n  return false\n}\n\nGraph.prototype.enableRubberband = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.enableRubberband()\n  }\n  return this\n}\n\nGraph.prototype.disableRubberband = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.disableRubberband()\n  }\n  return this\n}\n\nGraph.prototype.toggleRubberband = function (enabled?: boolean) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.toggleRubberband(enabled)\n  }\n  return this\n}\n\nGraph.prototype.isStrictRubberband = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    return selection.isStrictRubberband()\n  }\n  return false\n}\n\nGraph.prototype.enableStrictRubberband = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.enableStrictRubberband()\n  }\n  return this\n}\n\nGraph.prototype.disableStrictRubberband = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.disableStrictRubberband()\n  }\n  return this\n}\n\nGraph.prototype.toggleStrictRubberband = function (strict?: boolean) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.toggleStrictRubberband(strict)\n  }\n  return this\n}\n\nGraph.prototype.setRubberbandModifiers = function (\n  modifiers?: string | ModifierKey[] | null,\n) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.setRubberbandModifiers(modifiers)\n  }\n  return this\n}\n\nGraph.prototype.setSelectionFilter = function (filter?: Selection.Filter) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.setSelectionFilter(filter)\n  }\n  return this\n}\n\nGraph.prototype.setSelectionDisplayContent = function (\n  content?: Selection.Content,\n) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.setSelectionDisplayContent(content)\n  }\n  return this\n}\n\nGraph.prototype.isSelectionEmpty = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    return selection.isEmpty()\n  }\n  return true\n}\n\nGraph.prototype.cleanSelection = function (options?: Selection.SetOptions) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.clean(options)\n  }\n  return this\n}\n\nGraph.prototype.resetSelection = function (\n  cells?: Cell | string | (Cell | string)[],\n  options?: Selection.SetOptions,\n) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.reset(cells, options)\n  }\n  return this\n}\n\nGraph.prototype.getSelectedCells = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    return selection.getSelectedCells()\n  }\n  return []\n}\n\nGraph.prototype.getSelectedCellCount = function () {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    return selection.getSelectedCellCount()\n  }\n  return 0\n}\n\nGraph.prototype.isSelected = function (cell: Cell | string) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    return selection.isSelected(cell)\n  }\n  return false\n}\n\nGraph.prototype.select = function (\n  cells: Cell | string | (Cell | string)[],\n  options?: Selection.AddOptions,\n) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.select(cells, options)\n  }\n  return this\n}\n\nGraph.prototype.unselect = function (\n  cells: Cell | string | (Cell | string)[],\n  options?: Selection.RemoveOptions,\n) {\n  const selection = this.getPlugin('selection') as Selection\n  if (selection) {\n    selection.unselect(cells, options)\n  }\n  return this\n}\n", "import {\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>ss<PERSON><PERSON>der,\n  Dom,\n  Cell,\n  EventArgs,\n  Graph,\n} from '@antv/x6'\nimport { SelectionImpl } from './selection'\nimport { content } from './style/raw'\nimport './api'\n\nexport class Selection\n  extends Basecoat<SelectionImpl.EventArgs>\n  implements Graph.Plugin\n{\n  public name = 'selection'\n\n  private graph: Graph\n  private selectionImpl: SelectionImpl\n  private readonly options: Selection.Options\n  private movedMap = new WeakMap<Cell, boolean>()\n  private unselectMap = new WeakMap<Cell, boolean>()\n\n  get rubberbandDisabled() {\n    return this.options.enabled !== true || this.options.rubberband !== true\n  }\n\n  get disabled() {\n    return this.options.enabled !== true\n  }\n\n  get length() {\n    return this.selectionImpl.length\n  }\n\n  get cells() {\n    return this.selectionImpl.cells\n  }\n\n  constructor(options: Selection.Options = {}) {\n    super()\n    this.options = {\n      enabled: true,\n      ...Selection.defaultOptions,\n      ...options,\n    }\n\n    CssLoader.ensure(this.name, content)\n  }\n\n  public init(graph: Graph) {\n    this.graph = graph\n    this.selectionImpl = new SelectionImpl({\n      ...this.options,\n      graph,\n    })\n    this.setup()\n    this.startListening()\n  }\n\n  // #region api\n\n  isEnabled() {\n    return !this.disabled\n  }\n\n  enable() {\n    if (this.disabled) {\n      this.options.enabled = true\n    }\n  }\n\n  disable() {\n    if (!this.disabled) {\n      this.options.enabled = false\n    }\n  }\n\n  toggleEnabled(enabled?: boolean) {\n    if (enabled != null) {\n      if (enabled !== this.isEnabled()) {\n        if (enabled) {\n          this.enable()\n        } else {\n          this.disable()\n        }\n      }\n    } else if (this.isEnabled()) {\n      this.disable()\n    } else {\n      this.enable()\n    }\n\n    return this\n  }\n\n  isMultipleSelection() {\n    return this.isMultiple()\n  }\n\n  enableMultipleSelection() {\n    this.enableMultiple()\n    return this\n  }\n\n  disableMultipleSelection() {\n    this.disableMultiple()\n    return this\n  }\n\n  toggleMultipleSelection(multiple?: boolean) {\n    if (multiple != null) {\n      if (multiple !== this.isMultipleSelection()) {\n        if (multiple) {\n          this.enableMultipleSelection()\n        } else {\n          this.disableMultipleSelection()\n        }\n      }\n    } else if (this.isMultipleSelection()) {\n      this.disableMultipleSelection()\n    } else {\n      this.enableMultipleSelection()\n    }\n\n    return this\n  }\n\n  isSelectionMovable() {\n    return this.options.movable !== false\n  }\n\n  enableSelectionMovable() {\n    this.selectionImpl.options.movable = true\n    return this\n  }\n\n  disableSelectionMovable() {\n    this.selectionImpl.options.movable = false\n    return this\n  }\n\n  toggleSelectionMovable(movable?: boolean) {\n    if (movable != null) {\n      if (movable !== this.isSelectionMovable()) {\n        if (movable) {\n          this.enableSelectionMovable()\n        } else {\n          this.disableSelectionMovable()\n        }\n      }\n    } else if (this.isSelectionMovable()) {\n      this.disableSelectionMovable()\n    } else {\n      this.enableSelectionMovable()\n    }\n\n    return this\n  }\n\n  isRubberbandEnabled() {\n    return !this.rubberbandDisabled\n  }\n\n  enableRubberband() {\n    if (this.rubberbandDisabled) {\n      this.options.rubberband = true\n    }\n    return this\n  }\n\n  disableRubberband() {\n    if (!this.rubberbandDisabled) {\n      this.options.rubberband = false\n    }\n    return this\n  }\n\n  toggleRubberband(enabled?: boolean) {\n    if (enabled != null) {\n      if (enabled !== this.isRubberbandEnabled()) {\n        if (enabled) {\n          this.enableRubberband()\n        } else {\n          this.disableRubberband()\n        }\n      }\n    } else if (this.isRubberbandEnabled()) {\n      this.disableRubberband()\n    } else {\n      this.enableRubberband()\n    }\n\n    return this\n  }\n\n  isStrictRubberband() {\n    return this.selectionImpl.options.strict === true\n  }\n\n  enableStrictRubberband() {\n    this.selectionImpl.options.strict = true\n    return this\n  }\n\n  disableStrictRubberband() {\n    this.selectionImpl.options.strict = false\n    return this\n  }\n\n  toggleStrictRubberband(strict?: boolean) {\n    if (strict != null) {\n      if (strict !== this.isStrictRubberband()) {\n        if (strict) {\n          this.enableStrictRubberband()\n        } else {\n          this.disableStrictRubberband()\n        }\n      }\n    } else if (this.isStrictRubberband()) {\n      this.disableStrictRubberband()\n    } else {\n      this.enableStrictRubberband()\n    }\n\n    return this\n  }\n\n  setRubberbandModifiers(modifiers?: string | ModifierKey[] | null) {\n    this.setModifiers(modifiers)\n  }\n\n  setSelectionFilter(filter?: Selection.Filter) {\n    this.setFilter(filter)\n    return this\n  }\n\n  setSelectionDisplayContent(content?: Selection.Content) {\n    this.setContent(content)\n    return this\n  }\n\n  isEmpty() {\n    return this.length <= 0\n  }\n\n  clean(options: Selection.SetOptions = {}) {\n    this.selectionImpl.clean(options)\n    return this\n  }\n\n  reset(\n    cells?: Cell | string | (Cell | string)[],\n    options: Selection.SetOptions = {},\n  ) {\n    this.selectionImpl.reset(cells ? this.getCells(cells) : [], options)\n    return this\n  }\n\n  getSelectedCells() {\n    return this.cells\n  }\n\n  getSelectedCellCount() {\n    return this.length\n  }\n\n  isSelected(cell: Cell | string) {\n    return this.selectionImpl.isSelected(cell)\n  }\n\n  select(\n    cells: Cell | string | (Cell | string)[],\n    options: Selection.AddOptions = {},\n  ) {\n    const selected = this.getCells(cells)\n    if (selected.length) {\n      if (this.isMultiple()) {\n        this.selectionImpl.select(selected, options)\n      } else {\n        this.reset(selected.slice(0, 1), options)\n      }\n    }\n    return this\n  }\n\n  unselect(\n    cells: Cell | string | (Cell | string)[],\n    options: Selection.RemoveOptions = {},\n  ) {\n    this.selectionImpl.unselect(this.getCells(cells), options)\n    return this\n  }\n\n  // #endregion\n\n  protected setup() {\n    this.selectionImpl.on('*', (name, args) => {\n      this.trigger(name, args)\n      this.graph.trigger(name, args)\n    })\n  }\n\n  protected startListening() {\n    this.graph.on('blank:mousedown', this.onBlankMouseDown, this)\n    this.graph.on('blank:click', this.onBlankClick, this)\n    this.graph.on('cell:mousemove', this.onCellMouseMove, this)\n    this.graph.on('cell:mouseup', this.onCellMouseUp, this)\n    this.selectionImpl.on('box:mousedown', this.onBoxMouseDown, this)\n  }\n\n  protected stopListening() {\n    this.graph.off('blank:mousedown', this.onBlankMouseDown, this)\n    this.graph.off('blank:click', this.onBlankClick, this)\n    this.graph.off('cell:mousemove', this.onCellMouseMove, this)\n    this.graph.off('cell:mouseup', this.onCellMouseUp, this)\n    this.selectionImpl.off('box:mousedown', this.onBoxMouseDown, this)\n  }\n\n  protected onBlankMouseDown({ e }: EventArgs['blank:mousedown']) {\n    if (!this.allowBlankMouseDown(e)) {\n      return\n    }\n\n    const allowGraphPanning = this.graph.panning.allowPanning(e, true)\n    const scroller = this.graph.getPlugin<any>('scroller')\n    const allowScrollerPanning = scroller && scroller.allowPanning(e, true)\n    if (\n      this.allowRubberband(e, true) ||\n      (this.allowRubberband(e) && !allowScrollerPanning && !allowGraphPanning)\n    ) {\n      this.startRubberband(e)\n    }\n  }\n\n  protected allowBlankMouseDown(e: Dom.MouseDownEvent) {\n    const eventTypes = this.options.eventTypes\n    return (\n      (eventTypes?.includes('leftMouseDown') && e.button === 0) ||\n      (eventTypes?.includes('mouseWheelDown') && e.button === 1)\n    )\n  }\n\n  protected onBlankClick() {\n    this.clean()\n  }\n\n  protected allowRubberband(e: Dom.MouseDownEvent, strict?: boolean) {\n    return (\n      !this.rubberbandDisabled &&\n      ModifierKey.isMatch(e, this.options.modifiers, strict)\n    )\n  }\n\n  protected allowMultipleSelection(e: Dom.MouseDownEvent | Dom.MouseUpEvent) {\n    return (\n      this.isMultiple() &&\n      ModifierKey.isMatch(e, this.options.multipleSelectionModifiers)\n    )\n  }\n\n  protected onCellMouseMove({ cell }: EventArgs['cell:mousemove']) {\n    this.movedMap.set(cell, true)\n  }\n\n  protected onCellMouseUp({ e, cell }: EventArgs['cell:mouseup']) {\n    const options = this.options\n    let disabled = this.disabled\n    if (!disabled && this.movedMap.has(cell)) {\n      disabled = options.selectCellOnMoved === false\n\n      if (!disabled) {\n        disabled = options.selectNodeOnMoved === false && cell.isNode()\n      }\n\n      if (!disabled) {\n        disabled = options.selectEdgeOnMoved === false && cell.isEdge()\n      }\n    }\n\n    if (!disabled) {\n      if (!this.allowMultipleSelection(e)) {\n        this.reset(cell)\n      } else if (this.unselectMap.has(cell)) {\n        this.unselectMap.delete(cell)\n      } else if (this.isSelected(cell)) {\n        this.unselect(cell)\n      } else {\n        this.select(cell)\n      }\n    }\n\n    this.movedMap.delete(cell)\n  }\n\n  protected onBoxMouseDown({\n    e,\n    cell,\n  }: SelectionImpl.EventArgs['box:mousedown']) {\n    if (!this.disabled) {\n      if (this.allowMultipleSelection(e)) {\n        this.unselect(cell)\n        this.unselectMap.set(cell, true)\n      }\n    }\n  }\n\n  protected getCells(cells: Cell | string | (Cell | string)[]) {\n    return (Array.isArray(cells) ? cells : [cells])\n      .map((cell) =>\n        typeof cell === 'string' ? this.graph.getCellById(cell) : cell,\n      )\n      .filter((cell) => cell != null)\n  }\n\n  protected startRubberband(e: Dom.MouseDownEvent) {\n    if (!this.rubberbandDisabled) {\n      this.selectionImpl.startSelecting(e)\n    }\n    return this\n  }\n\n  protected isMultiple() {\n    return this.options.multiple !== false\n  }\n\n  protected enableMultiple() {\n    this.options.multiple = true\n    return this\n  }\n\n  protected disableMultiple() {\n    this.options.multiple = false\n    return this\n  }\n\n  protected setModifiers(modifiers?: string | ModifierKey[] | null) {\n    this.options.modifiers = modifiers\n    return this\n  }\n\n  protected setContent(content?: Selection.Content) {\n    this.selectionImpl.setContent(content)\n    return this\n  }\n\n  protected setFilter(filter?: Selection.Filter) {\n    this.selectionImpl.setFilter(filter)\n    return this\n  }\n\n  @Basecoat.dispose()\n  dispose() {\n    this.stopListening()\n    this.off()\n    this.selectionImpl.dispose()\n    CssLoader.clean(this.name)\n  }\n}\n\nexport namespace Selection {\n  export interface EventArgs extends SelectionImpl.EventArgs {}\n  export interface Options extends SelectionImpl.CommonOptions {\n    enabled?: boolean\n  }\n\n  export type Filter = SelectionImpl.Filter\n  export type Content = SelectionImpl.Content\n\n  export type SetOptions = SelectionImpl.SetOptions\n  export type AddOptions = SelectionImpl.AddOptions\n  export type RemoveOptions = SelectionImpl.RemoveOptions\n\n  export const defaultOptions: Partial<SelectionImpl.Options> = {\n    rubberband: false,\n    rubberNode: true,\n    rubberEdge: false, // next version will set to true\n    pointerEvents: 'auto',\n    multiple: true,\n    multipleSelectionModifiers: ['ctrl', 'meta'],\n    movable: true,\n    strict: false,\n    selectCellOnMoved: false,\n    selectNodeOnMoved: false,\n    selectEdgeOnMoved: false,\n    following: true,\n    content: null,\n    eventTypes: ['leftMouseDown', 'mouseWheelDown'],\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAiBM,IAAO,gBAAP,cAA6B,KAA6B;EAQ9D,IAAW,QAAK;AACd,WAAO,KAAK,QAAQ;EACtB;EAEA,IAAc,eAAY;AACxB,WAAO,KAAK,gBAAgB,QAAQ,WAAW,GAAG;EACpD;EAEA,IAAc,SAAM;AAClB,WAAOA,cAAI,SAAS,KAAK,WAAW,KAAK,YAAY;EACvD;EAEA,IAAc,gBAAa;AACzB,WAAO,KAAK;EACd;EAEA,YAAY,SAA8B;AACxC,UAAK;AACL,SAAK,UAAU;AAEf,QAAI,KAAK,QAAQ,OAAO;AACtB,WAAK,QAAQ,aAAa,KAAK,QAAQ,MAAM;;AAG/C,QAAI,KAAK,QAAQ,YAAY;AAC3B,WAAK,aAAa,KAAK,QAAQ;WAC1B;AACL,WAAK,aAAa,IAAI,WAAW,CAAA,GAAI;QACnC,YAAY,QAAQ;OACrB;AACD,WAAK,QAAQ,aAAa,KAAK;;AAGjC,SAAK,WAAW;AAEhB,SAAK,gBAAe;AACpB,SAAK,eAAc;EACrB;EAEU,iBAAc;AACtB,UAAM,QAAQ,KAAK;AACnB,UAAM,aAAa,KAAK;AAExB,SAAK,eACH;MACE,CAAC,cAAc,KAAK,YAAY,EAAE,GAAG;MACrC,CAAC,eAAe,KAAK,YAAY,EAAE,GAAG;OAExC,IAAI;AAGN,UAAM,GAAG,SAAS,KAAK,oBAAoB,IAAI;AAC/C,UAAM,GAAG,aAAa,KAAK,oBAAoB,IAAI;AACnD,UAAM,MAAM,GAAG,WAAW,KAAK,gBAAgB,IAAI;AAEnD,eAAW,GAAG,SAAS,KAAK,aAAa,IAAI;AAC7C,eAAW,GAAG,WAAW,KAAK,eAAe,IAAI;AACjD,eAAW,GAAG,WAAW,KAAK,WAAW,IAAI;AAC7C,eAAW,GAAG,WAAW,KAAK,qBAAqB,IAAI;AACvD,eAAW,GAAG,wBAAwB,KAAK,uBAAuB,IAAI;AACtE,eAAW,GAAG,gBAAgB,KAAK,eAAe,IAAI;EACxD;EAEU,gBAAa;AACrB,UAAM,QAAQ,KAAK;AACnB,UAAM,aAAa,KAAK;AAExB,SAAK,iBAAgB;AAErB,UAAM,IAAI,SAAS,KAAK,oBAAoB,IAAI;AAChD,UAAM,IAAI,aAAa,KAAK,oBAAoB,IAAI;AACpD,UAAM,MAAM,IAAI,WAAW,KAAK,gBAAgB,IAAI;AAEpD,eAAW,IAAI,SAAS,KAAK,aAAa,IAAI;AAC9C,eAAW,IAAI,WAAW,KAAK,eAAe,IAAI;AAClD,eAAW,IAAI,WAAW,KAAK,WAAW,IAAI;AAC9C,eAAW,IAAI,WAAW,KAAK,qBAAqB,IAAI;AACxD,eAAW,IAAI,wBAAwB,KAAK,uBAAuB,IAAI;AACvE,eAAW,IAAI,gBAAgB,KAAK,eAAe,IAAI;EACzD;EAEU,WAAQ;AAChB,SAAK,cAAa;EACpB;EAEU,qBAAkB;AAC1B,SAAK,qBAAoB;EAC3B;EAEU,gBAAa;AACrB,SAAK,qBAAoB;EAC3B;EAIU,sBAAsB,EAC9B,MACA,QAAO,GACsC;AAC7C,UAAM,EAAE,sBAAsB,cAAa,IAAK,KAAK;AACrD,UAAM,EAAE,IAAI,WAAW,aAAa,QAAO,IAAK;AAEhD,UAAM,oBACH,yBAAyB,QAAS,iBAAiB,KAAK,sBAAsB,aAAa,MAAM,WAClG,CAAC,KAAK,eACN,CAAC;AAEH,UAAM,gBAAgB,MAAM,eAAe,KAAK,OAAO;AAEvD,QAAI,qBAAqB,iBAAiB,UAAU;AAClD,WAAK,cAAc;AACnB,YAAM,UAAU,KAAK,SAAQ;AAC7B,YAAM,WAAW,KAAK,SAAS,UAAU;AACzC,YAAM,KAAK,QAAQ,IAAI,SAAS;AAChC,YAAM,KAAK,QAAQ,IAAI,SAAS;AAEhC,UAAI,OAAO,KAAK,OAAO,GAAG;AACxB,aAAK,uBAAuB,IAAI,IAAI,MAAM,OAAO;;AAEnD,WAAK,cAAc;;EAEvB;EAEU,eAAe,EAAE,QAAO,GAAmC;AACnE,QAAI,WAAW,QAAQ,QAAQ;AAC7B,WAAK,SAAS,OAAO;;EAEzB;EAEA,UAAO;AACL,WAAO,KAAK,UAAU;EACxB;EAEA,WAAW,MAAmB;AAC5B,WAAO,KAAK,WAAW,IAAI,IAAI;EACjC;EAEA,IAAI,SAAM;AACR,WAAO,KAAK,WAAW;EACzB;EAEA,IAAI,QAAK;AACP,WAAO,KAAK,WAAW,QAAO;EAChC;EAEA,OAAO,OAAsB,UAAoC,CAAA,GAAE;AACjE,YAAQ,SAAS;AACjB,UAAM,QAAQ,KAAK,OAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC;AAChE,SAAK,WAAW,IAAI,OAAO,OAAO;AAClC,WAAO;EACT;EAEA,SAAS,OAAsB,UAAuC,CAAA,GAAE;AAEtE,YAAQ,SAAS;AACjB,SAAK,WAAW,OAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,GAAG,OAAO;AACtE,WAAO;EACT;EAEA,MAAM,OAAuB,UAAoC,CAAA,GAAE;AACjE,QAAI,OAAO;AACT,UAAI,QAAQ,OAAO;AACjB,cAAM,cAAc,KAAK,OAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC;AACtE,aAAK,WAAW,MAAM,aAAW,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,OAAO,GAAA,EAAE,IAAI,KAAI,CAAA,CAAA;AACzD,eAAO;;AAGT,YAAM,OAAO,KAAK;AAClB,YAAM,OAAO,KAAK,OAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC;AAC/D,YAAM,UAA0B,CAAA;AAChC,YAAM,UAA0B,CAAA;AAChC,WAAK,QAAQ,CAAC,SAAU,QAAQ,KAAK,EAAE,IAAI,IAAK;AAChD,WAAK,QAAQ,CAAC,SAAU,QAAQ,KAAK,EAAE,IAAI,IAAK;AAChD,YAAM,QAAgB,CAAA;AACtB,YAAM,UAAkB,CAAA;AACxB,WAAK,QAAQ,CAAC,SAAQ;AACpB,YAAI,CAAC,QAAQ,KAAK,EAAE,GAAG;AACrB,gBAAM,KAAK,IAAI;;MAEnB,CAAC;AACD,WAAK,QAAQ,CAAC,SAAQ;AACpB,YAAI,CAAC,QAAQ,KAAK,EAAE,GAAG;AACrB,kBAAQ,KAAK,IAAI;;MAErB,CAAC;AAED,UAAI,QAAQ,QAAQ;AAClB,aAAK,SAAS,SAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,OAAO,GAAA,EAAE,IAAI,KAAI,CAAA,CAAA;;AAG/C,UAAI,MAAM,QAAQ;AAChB,aAAK,OAAO,OAAK,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,OAAO,GAAA,EAAE,IAAI,KAAI,CAAA,CAAA;;AAG3C,UAAI,QAAQ,WAAW,KAAK,MAAM,WAAW,GAAG;AAC9C,aAAK,gBAAe;;AAGtB,aAAO;;AAGT,WAAO,KAAK,MAAM,OAAO;EAC3B;EAEA,MAAM,UAAoC,CAAA,GAAE;AAC1C,QAAI,KAAK,QAAQ;AACf,UAAI,QAAQ,UAAU,OAAO;AAC3B,aAAK,SAAS,KAAK,OAAO,OAAO;aAC5B;AACL,aAAK,WAAW,MAAM,CAAA,GAAE,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,OAAO,GAAA,EAAE,IAAI,KAAI,CAAA,CAAA;;;AAGpD,WAAO;EACT;EAEA,UAAU,QAA6B;AACrC,SAAK,QAAQ,SAAS;EACxB;EAEA,WAAWC,UAA+B;AACxC,SAAK,QAAQ,UAAUA;EACzB;EAEA,eAAe,KAAuB;AAGpC,UAAM,KAAK,eAAe,GAAG;AAC7B,SAAK,MAAK;AACV,QAAI;AACJ,QAAI;AACJ,UAAM,iBAAiB,KAAK,MAAM;AAClC,QACE,IAAI,WAAW,QACf,IAAI,WAAW,QACf,eAAe,SAAS,IAAI,MAAM,GAClC;AACA,UAAI,IAAI;AACR,UAAI,IAAI;WACH;AACL,YAAM,SAASD,cAAI,OAAO,cAAc;AACxC,YAAM,aAAa,eAAe;AAClC,YAAM,YAAY,eAAe;AACjC,UAAI,IAAI,UAAU,OAAO,OAAO,OAAO,cAAc;AACrD,UAAI,IAAI,UAAU,OAAO,MAAM,OAAO,cAAc;;AAGtD,IAAAA,cAAI,IAAI,KAAK,WAAW;MACtB,KAAK;MACL,MAAM;MACN,OAAO;MACP,QAAQ;KACT;AAED,SAAK,aAAkC,KAAK;MAC1C,QAAQ;MACR,SAAS,IAAI;MACb,SAAS,IAAI;MACb,SAAS;MACT,SAAS;MACT,WAAW;MACX,WAAW;MACX,QAAQ;KACT;AAED,SAAK,uBAAuB,QAAQ,gBAAgB,IAAI,IAAI;EAC9D;EAEA,OAAO,OAAa;AAClB,UAAM,SAAS,KAAK,QAAQ;AAE5B,WAAO,MAAM,OAAO,CAAC,SAAQ;AAC3B,UAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,eAAO,OAAO,KAAK,CAAC,SAAQ;AAC1B,cAAI,OAAO,SAAS,UAAU;AAC5B,mBAAO,KAAK,UAAU;;AAExB,iBAAO,KAAK,OAAO,KAAK;QAC1B,CAAC;;AAEH,UAAI,OAAO,WAAW,YAAY;AAChC,eAAO,aAAY,KAAK,QAAQ,KAAK,OAAO,IAAI;;AAGlD,aAAO;IACT,CAAC;EACH;EAEU,cAAc,KAAqB;AAC3C,UAAM,QAAQ,KAAK;AACnB,UAAM,YAAY,KAAK,aAA+B,GAAG;AACzD,UAAM,SAAS,UAAU;AACzB,YAAQ,QAAQ;MACd,KAAK,aAAa;AAChB,YAAI,QAAQA,cAAI,MAAM,KAAK,SAAS;AACpC,YAAI,SAASA,cAAI,OAAO,KAAK,SAAS;AACtC,cAAM,SAASA,cAAI,OAAO,KAAK,SAAS;AACxC,cAAM,SAAS,MAAM,YAAY,OAAO,MAAM,OAAO,GAAG;AACxD,cAAM,QAAQ,MAAM,UAAU,SAAQ;AACtC,iBAAS,MAAM;AACf,kBAAU,MAAM;AAChB,cAAM,OAAO,IAAI,UAAU,OAAO,GAAG,OAAO,GAAG,OAAO,MAAM;AAC5D,cAAM,QAAQ,KAAK,mBAAmB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI;AACnE,aAAK,MAAM,OAAO,EAAE,OAAO,KAAI,CAAE;AACjC,aAAK,eAAc;AACnB;;MAGF,KAAK,eAAe;AAClB,cAAM,SAAS,MAAM,WAAW,IAAI,SAAS,IAAI,OAAO;AACxD,YAAI,CAAC,KAAK,QAAQ,WAAW;AAC3B,gBAAM,OAAO;AACb,eAAK,4BAA4B;YAC/B,IAAI,KAAK,UAAU,KAAK;YACxB,IAAI,KAAK,UAAU,KAAK;WACzB;;AAEH,aAAK,MAAM,MAAM,UAAU,gBAAgB;AAC3C,aAAK,eAAe,eAAe,KAAK,OAAO,GAAG,OAAO,CAAC;AAC1D;;MAGF,SAAS;AACP,aAAK,MAAK;AACV;;;EAGN;EAEU,UAAU,KAAqB;AACvC,UAAM,SAAS,KAAK,aAA+B,GAAG,EAAE;AACxD,QAAI,QAAQ;AACV,WAAK,cAAc,GAAG;AACtB,WAAK,yBAAwB;;EAEjC;EAEU,wBAAwB,KAAuB;AACvD,QAAI,CAAC,KAAK,QAAQ,WAAW;AAC3B,UAAI,gBAAe;;AAGrB,UAAM,IAAI,KAAK,eAAe,GAAG;AAEjC,QAAI,KAAK,QAAQ,SAAS;AACxB,WAAK,iBAAiB,CAAC;;AAGzB,UAAM,aAAa,KAAK,oBAAoB,EAAE,MAAM;AACpD,SAAK,aAAqC,GAAG,EAAE,WAAU,CAAE;AAC3D,UAAM,SAAS,KAAK,MAAM,WAAW,EAAE,SAAS,EAAE,OAAO;AACzD,SAAK,eAAe,iBAAiB,GAAG,OAAO,GAAG,OAAO,CAAC;AAC1D,SAAK,uBAAuB,QAAQ,gBAAgB,EAAE,IAAI;EAC5D;EAEU,iBAAiB,KAAuB;AAChD,SAAK,MAAM,MAAM,WAAW,gBAAgB;AAC5C,UAAM,SAAS,KAAK,MAAM,WAAW,IAAI,SAAS,IAAI,OAAO;AAC7D,SAAK,aAAoC,KAAK;MAC5C,QAAQ;MACR,SAAS,OAAO;MAChB,SAAS,OAAO;MAChB,SAAS,OAAO;MAChB,SAAS,OAAO;KACjB;EACH;EAEQ,kBAAe;AACrB,UAAM,WAAW,KAAK,MAAM,QAAQ,YAAY;AAChD,UAAM,OACJ,OAAO,aAAa,aAChB,aAAY,KAAK,UAAU,KAAK,OAAO,IAAI,IAC3C;AAEN,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,KAAK,MAAM,UAAU,aAAY,EAAG,QAAQ,IAAI;;AAGzD,QAAI,SAAS,MAAM;AACjB,aAAO,KAAK,MAAM,UAAU,aAAY;;AAG1C,WAAO,QAAQ;EACjB;EAEU,mBAAmB,QAAe,MAA2B;AACrE,QAAI,KAAK,OAAO,IAAI,KAAK;AACzB,QAAI,KAAK,OAAO,IAAI,KAAK;AACzB,UAAM,WAAW,KAAK,gBAAe;AACrC,QAAI,UAAU;AACZ,YAAM,QAAQ,KAAK,WAAW,QAAO;AACrC,YAAM,YACJ,KAAK,aAAa,OAAO,EAAE,MAAM,KAAI,CAAE,KAAK,UAAU,OAAM;AAC9D,YAAM,QAAQ,SAAS,IAAI,UAAU;AACrC,YAAM,QAAQ,SAAS,IAAI,UAAU;AACrC,YAAM,QACJ,SAAS,IAAI,SAAS,SAAS,UAAU,IAAI,UAAU;AACzD,YAAM,QACJ,SAAS,IAAI,SAAS,UAAU,UAAU,IAAI,UAAU;AAE1D,UAAI,KAAK,OAAO;AACd,aAAK;;AAEP,UAAI,KAAK,OAAO;AACd,aAAK;;AAEP,UAAI,QAAQ,IAAI;AACd,aAAK;;AAEP,UAAI,QAAQ,IAAI;AACd,aAAK;;AAGP,UAAI,CAAC,KAAK,QAAQ,WAAW;AAC3B,cAAM,UAAU,OAAO,IAAI,KAAK;AAChC,cAAM,UAAU,OAAO,IAAI,KAAK;AAChC,aAAK,WAAW,SAAS,WAAW,QAAQ,IAAI;AAChD,aAAK,WAAW,SAAS,WAAW,QAAQ,IAAI;;;AAIpD,WAAO;MACL;MACA;;EAEJ;EAEQ,sBAAsB,MAAe,OAAe,MAAY;AACtE,UAAM,UAAUA,cAAI,IAAI,MAAM,MAAM;AACpC,UAAM,SAASA,cAAI,IAAI,MAAM,KAAK;AAClC,UAAM,OAAO,UAAU,WAAW,OAAO,IAAI;AAC7C,UAAM,MAAM,SAAS,WAAW,MAAM,IAAI;AAE1C,IAAAA,cAAI,IAAI,MAAM,QAAQ,OAAO,KAAK;AAClC,IAAAA,cAAI,IAAI,MAAM,OAAO,MAAM,IAAI;EACjC;EAEU,4BAA4B,QAAkC;AACtE,UAAM,EAAE,IAAI,GAAE,IAAK;AACnB,QAAI,MAAM,IAAI;AACZ,UAAK,KAAK,uBAAuB,IAAI,EAAE,GAAG,KAAK,cAAe;AAC5D,YAAI,KAAK,WAAW,SAAS,GAAG;AAC9B,eAAK,qBAAoB;;aAEtB;AACL,cAAM,QAAQ,KAAK,MAAM,UAAU,SAAQ;AAC3C,iBACM,IAAI,GAAG,SAAS,KAAK,QAAQ,MAAM,OAAO,QAC9C,IAAI,KACJ,KAAK,GACL;AACA,eAAK,sBAAsB,OAAO,CAAC,GAAG,KAAK,MAAM,IAAI,KAAK,MAAM,EAAE;;AAEpE,aAAK,sBACH,KAAK,oBACL,KAAK,MAAM,IACX,KAAK,MAAM,EAAE;;;EAIrB;EAEU,gBAAgB,GAAW,GAAS;AAC5C,UAAM,WAAW,KAAK,MAAM,UAAe,UAAU;AACrD,QAAI,UAAU;AACZ,aAAO,SAAS,WAAW,GAAG,CAAC;;AAEjC,WAAO,EAAE,WAAW,GAAG,WAAW,EAAC;EACrC;EAEU,gBAAgB,KAAuB;AAC/C,UAAM,IAAI,KAAK,eAAe,GAAG;AACjC,UAAM,YAAY,KAAK,aAA+B,CAAC;AACvD,UAAM,SAAS,UAAU;AACzB,YAAQ,QAAQ;MACd,KAAK,aAAa;AAChB,cAAM,OAAO;AACb,YAAI,KAAK,WAAW,MAAM;AACxB,UAAAA,cAAI,SAAS,KAAK,WAAW,KAAK,MAAM,SAAS;AACjD,eAAK,eAAc;AACnB,eAAK,SAAS;;AAGhB,cAAM,EAAE,WAAW,UAAS,IAAK,KAAK,gBACpC,EAAE,SACF,EAAE,OAAO;AAEX,aAAK,aAAa;AAClB,aAAK,aAAa;AAElB,cAAM,KAAK,EAAE,UAAU,KAAK,UAAU,KAAK;AAC3C,cAAM,KAAK,EAAE,UAAU,KAAK,UAAU,KAAK;AAE3C,cAAM,OAAO,SAASA,cAAI,IAAI,KAAK,WAAW,MAAM,KAAK,KAAK,EAAE;AAChE,cAAM,MAAM,SAASA,cAAI,IAAI,KAAK,WAAW,KAAK,KAAK,KAAK,EAAE;AAC9D,QAAAA,cAAI,IAAI,KAAK,WAAW;UACtB,MAAM,KAAK,IAAI,KAAK,UAAU,KAAK;UACnC,KAAK,KAAK,IAAI,KAAK,UAAU,KAAK;UAClC,OAAO,KAAK,IAAI,EAAE;UAClB,QAAQ,KAAK,IAAI,EAAE;SACpB;AACD;;MAGF,KAAK,eAAe;AAClB,cAAM,SAAS,KAAK,MAAM,WAAW,EAAE,SAAS,EAAE,OAAO;AACzD,cAAM,OAAO;AACb,cAAM,SAAS,KAAK,mBAAmB,QAAQ,IAAI;AACnD,YAAI,KAAK,QAAQ,WAAW;AAC1B,eAAK,4BAA4B,MAAM;eAClC;AACL,eAAK,wBAAwB,MAAM;;AAErC,YAAI,OAAO,IAAI;AACb,eAAK,UAAU,OAAO;;AAExB,YAAI,OAAO,IAAI;AACb,eAAK,UAAU,OAAO;;AAExB,aAAK,eAAe,iBAAiB,KAAK,OAAO,GAAG,OAAO,CAAC;AAC5D;;MAGF;AACE;;AAGJ,SAAK,eAAe;EACtB;EAEU,uBACR,IACA,IACA,SACA,cAAuB;AAEvB,UAAM,MAAiC,CAAA;AACvC,UAAM,WAAmB,CAAA;AAEzB,QAAI,SAAS;AACX,UAAI,QAAQ,EAAE,IAAI;;AAGpB,SAAK,WAAW,QAAO,EAAG,QAAQ,CAAC,SAAQ;AACzC,WAAK,eAAe,EAAE,MAAM,KAAI,CAAE,EAAE,QAAQ,CAAC,UAAS;AACpD,YAAI,MAAM,EAAE,IAAI;MAClB,CAAC;IACH,CAAC;AACD,QAAI,gBAAgB,aAAa,aAAa;AAC5C,YAAM,cAAc,KAAK,MAAM,YAAY,aAAa,WAAW;AACnE,UAAI,aAAa;AACf,YAAI,YAAY,EAAE,IAAI;AACtB,oBAAY,eAAe,EAAE,MAAM,KAAI,CAAE,EAAE,QAAQ,CAAC,UAAS;AAC3D,cAAI,MAAM,EAAE,IAAI;QAClB,CAAC;AACD,iBAAS,KAAK,WAAW;;;AAI7B,SAAK,WAAW,QAAO,EAAG,QAAQ,CAAC,SAAQ;AACzC,UAAI,CAAC,IAAI,KAAK,EAAE,GAAG;AACjB,cAAM,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GACR,YAAY,GAAA,EACf,WAAW,KAAK,KAChB,SAAS,SAAQ,CAAA;AAEnB,aAAK,UAAU,IAAI,IAAI,OAAO;AAC9B,aAAK,MAAM,MAAM,kBAAkB,IAAI,EAAE,QAAQ,CAAC,SAAQ;AACxD,cAAI,CAAC,IAAI,KAAK,EAAE,GAAG;AACjB,iBAAK,UAAU,IAAI,IAAI,OAAO;AAC9B,gBAAI,KAAK,EAAE,IAAI;;QAEnB,CAAC;;IAEL,CAAC;EACH;EAEU,mBAAmB,MAAe;AAC1C,UAAM,QAAQ,KAAK;AACnB,UAAM,UAAU;MACd,QAAQ,KAAK,QAAQ;;AAEvB,QAAI,QAAoB,CAAA;AAExB,QAAI,KAAK,QAAQ,YAAY;AAC3B,cAAQ,MAAM,OACZ,MAAM,MACH,eAAe,MAAM,OAAO,EAC5B,IAAI,CAAC,SAAS,MAAM,SAAS,eAAe,IAAI,CAAC,EACjD,OAAO,CAAC,SAAS,QAAQ,IAAI,CAAe;;AAInD,QAAI,KAAK,QAAQ,YAAY;AAC3B,cAAQ,MAAM,OACZ,MAAM,MACH,eAAe,MAAM,OAAO,EAC5B,IAAI,CAAC,SAAS,MAAM,SAAS,eAAe,IAAI,CAAC,EACjD,OAAO,CAAC,SAAS,QAAQ,IAAI,CAAe;;AAInD,WAAO;EACT;EAEU,eAGR,MAAS,GAAM,GAAW,GAAS;AACnC,UAAM,OAAO,KAAK,aAAqC,CAAC;AACxD,UAAM,OAAO,KAAK;AAClB,SAAK,QAAQ,MAAM,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,KAAK,KAAI,CAAE;EACvD;EAEU,qBAAqB,MAAU;AACvC,WAAO,KAAK,gBAAgB,GAAG,KAAK,OAAM,IAAK,SAAS,MAAM,WAAW;EAC3E;EAEU,yBAAyB,MAAU;AAC3C,UAAM,OAAO,KAAK,MAAM,SAAS,eAAe,IAAI;AACpD,QAAI,MAAM;AACR,WAAK,SAAS,KAAK,qBAAqB,IAAI,CAAC;;EAEjD;EAEU,8BAA8B,MAAU;AAChD,UAAM,OAAO,KAAK,MAAM,SAAS,eAAe,IAAI;AACpD,QAAI,MAAM;AACR,WAAK,YAAY,KAAK,qBAAqB,IAAI,CAAC;;EAEpD;EAEU,oBAAoB,MAAU;AACtC,SAAK,8BAA8B,IAAI;AAEvC,QAAI,KAAK,oBAAoB,IAAI,GAAG;AAClC,MAAAA,cAAI,OAAO,KAAK,UAAU,cAAc,kBAAkB,KAAK,EAAE,IAAI,CAAC;AACtE,UAAI,KAAK,OAAO,WAAW,GAAG;AAC5B,aAAK,KAAI;;AAEX,WAAK,WAAW,KAAK,IAAI,GAAG,KAAK,WAAW,CAAC;;EAEjD;EAEU,yBAAyB,OAAa;AAC9C,UAAM,QAAQ,CAAC,SAAS,KAAK,8BAA8B,IAAI,CAAC;AAEhE,SAAK,KAAI;AACT,IAAAA,cAAI,OAAO,KAAK,MAAM;AACtB,SAAK,WAAW;EAClB;EAEA,OAAI;AACF,IAAAA,cAAI,YACF,KAAK,WACL,KAAK,gBAAgB,QAAQ,WAAW,UAAU,CAAC;AAErD,IAAAA,cAAI,YACF,KAAK,WACL,KAAK,gBAAgB,QAAQ,WAAW,QAAQ,CAAC;EAErD;EAEU,iBAAc;AACtB,IAAAA,cAAI,SACF,KAAK,WACL,KAAK,gBAAgB,QAAQ,WAAW,UAAU,CAAC;EAEvD;EAEU,iBAAc;AACtB,IAAAA,cAAI,YACF,KAAK,WACL,KAAK,gBAAgB,QAAQ,WAAW,UAAU,CAAC;EAEvD;EAEU,eAAY;AACpB,IAAAA,cAAI,gBAAgB,KAAK,WAAW,OAAO;AAC3C,IAAAA,cAAI,SACF,KAAK,WACL,KAAK,gBAAgB,QAAQ,WAAW,QAAQ,CAAC;EAErD;EAEU,kBAAe;AACvB,SAAK,YAAY,SAAS,cAAc,KAAK;AAC7C,IAAAA,cAAI,SAAS,KAAK,WAAW,KAAK,gBAAgB,QAAQ,WAAW,IAAI,CAAC;AAC1E,QAAI,KAAK,QAAQ,WAAW;AAC1B,MAAAA,cAAI,SAAS,KAAK,WAAW,KAAK,QAAQ,SAAS;;AAGrD,SAAK,qBAAqB,SAAS,cAAc,KAAK;AACtD,IAAAA,cAAI,SACF,KAAK,oBACL,KAAK,gBAAgB,QAAQ,WAAW,KAAK,CAAC;AAGhD,SAAK,mBAAmB,SAAS,cAAc,KAAK;AACpD,IAAAA,cAAI,SACF,KAAK,kBACL,KAAK,gBAAgB,QAAQ,WAAW,OAAO,CAAC;AAGlD,IAAAA,cAAI,OAAO,KAAK,oBAAoB,KAAK,gBAAgB;AACzD,IAAAA,cAAI,KACF,KAAK,oBACL,yBACA,KAAK,WAAW,MAAM;AAGxB,IAAAA,cAAI,QAAQ,KAAK,WAAW,KAAK,kBAAkB;EACrD;EAEU,wBAAwB,QAAkC;AAClE,QAAI,OAAO,MAAM,OAAO,IAAI;AAC1B,WAAK,sBAAsB,KAAK,oBAAoB,OAAO,IAAI,OAAO,EAAE;;EAE5E;EAEU,kBAAe;AACvB,UAAM,SAAS,EAAE,GAAG,UAAU,GAAG,SAAQ;AACzC,UAAM,SAAS,EAAE,GAAG,GAAG,GAAG,EAAC;AAC3B,UAAM,QAAQ,KAAK,WAChB,QAAO,EACP,OAAO,CAAC,SAAS,KAAK,oBAAoB,IAAI,CAAC;AAElD,UAAM,QAAQ,CAAC,SAAQ;AACrB,YAAM,OAAO,KAAK,MAAM,SAAS,eAAe,IAAI;AACpD,UAAI,MAAM;AACR,cAAM,OAAO,KAAK,QAAQ;UACxB,iBAAiB;SAClB;AACD,eAAO,IAAI,KAAK,IAAI,OAAO,GAAG,KAAK,CAAC;AACpC,eAAO,IAAI,KAAK,IAAI,OAAO,GAAG,KAAK,CAAC;AACpC,eAAO,IAAI,KAAK,IAAI,OAAO,GAAG,KAAK,IAAI,KAAK,KAAK;AACjD,eAAO,IAAI,KAAK,IAAI,OAAO,GAAG,KAAK,IAAI,KAAK,MAAM;;IAEtD,CAAC;AAED,IAAAA,cAAI,IAAI,KAAK,oBAAoB;MAC/B,UAAU;MACV,eAAe;MACf,MAAM,OAAO;MACb,KAAK,OAAO;MACZ,OAAO,OAAO,IAAI,OAAO;MACzB,QAAQ,OAAO,IAAI,OAAO;KAC3B;AACD,IAAAA,cAAI,KACF,KAAK,oBACL,yBACA,KAAK,WAAW,MAAM;AAGxB,UAAM,aAAa,KAAK,QAAQ;AAChC,QAAI,YAAY;AACd,UAAI,OAAO,eAAe,YAAY;AACpC,cAAMC,WAAU,aAAY,KAC1B,YACA,KAAK,OACL,MACA,KAAK,gBAAgB;AAEvB,YAAIA,UAAS;AACX,eAAK,iBAAiB,YAAYA;;aAE/B;AACL,aAAK,iBAAiB,YAAY;;;AAItC,QAAI,KAAK,WAAW,SAAS,KAAK,CAAC,KAAK,UAAU,YAAY;AAC5D,MAAAD,cAAI,SAAS,KAAK,WAAW,KAAK,MAAM,SAAS;eACxC,KAAK,WAAW,UAAU,KAAK,KAAK,UAAU,YAAY;AACnE,WAAK,UAAU,WAAW,YAAY,KAAK,SAAS;;EAExD;EAEU,oBAAoB,MAAU;AACtC,WACG,KAAK,OAAM,KAAM,KAAK,QAAQ,yBAAyB,QACvD,KAAK,OAAM,KAAM,KAAK,QAAQ,yBAAyB;EAE5D;EAEU,sBAAsB,eAAqE;AACnG,WAAO,OAAO,kBAAkB,WAC5B,gBACA,cAAc,KAAK,KAAK;EAC9B;EAEU,mBAAmB,MAAU;AACrC,SAAK,yBAAyB,IAAI;AAElC,QAAI,KAAK,oBAAoB,IAAI,GAAG;AAClC,YAAM,OAAO,KAAK,MAAM,SAAS,eAAe,IAAI;AACpD,UAAI,MAAM;AACR,cAAM,OAAO,KAAK,QAAQ;UACxB,iBAAiB;SAClB;AAED,cAAM,YAAY,KAAK;AACvB,cAAM,MAAM,SAAS,cAAc,KAAK;AACxC,cAAM,gBAAgB,KAAK,QAAQ;AACnC,QAAAA,cAAI,SAAS,KAAK,SAAS;AAC3B,QAAAA,cAAI,SAAS,KAAK,GAAG,SAAS,IAAI,KAAK,OAAM,IAAK,SAAS,MAAM,EAAE;AACnE,QAAAA,cAAI,KAAK,KAAK,gBAAgB,KAAK,EAAE;AACrC,QAAAA,cAAI,IAAI,KAAK;UACX,UAAU;UACV,MAAM,KAAK;UACX,KAAK,KAAK;UACV,OAAO,KAAK;UACZ,QAAQ,KAAK;UACb,eAAe,gBACX,KAAK,sBAAsB,aAAa,IACxC;SACL;AACD,QAAAA,cAAI,SAAS,KAAK,KAAK,SAAS;AAChC,aAAK,aAAY;AACjB,aAAK,YAAY;;;EAGvB;EAEU,uBAAoB;AAC5B,QAAI,KAAK,WAAW,SAAS,GAAG;AAC9B,WAAK,eAAe;AACpB,WAAK,cAAa;;EAGtB;EAEA,gBAAa;AACX,QAAI,KAAK,UAAU;AACjB,WAAK,KAAI;AACT,eACM,IAAI,GAAG,SAAS,KAAK,QAAQ,MAAM,OAAO,QAC9C,IAAI,KACJ,KAAK,GACL;AACA,cAAM,MAAM,OAAO,CAAC;AACpB,cAAM,SAASA,cAAI,KAAK,KAAK,cAAc;AAC3C,QAAAA,cAAI,OAAO,GAAG;AACd,aAAK,YAAY;AACjB,cAAM,OAAO,KAAK,WAAW,IAAI,MAAM;AACvC,YAAI,MAAM;AACR,eAAK,mBAAmB,IAAI;;;AAIhC,WAAK,gBAAe;;AAEtB,WAAO;EACT;EAEU,oBAAoB,MAAa;AACzC,UAAM,KAAK,KAAK,aAAa,cAAc;AAC3C,QAAI,IAAI;AACN,YAAM,OAAO,KAAK,WAAW,IAAI,EAAE;AACnC,UAAI,MAAM;AACR,eAAO,KAAK,MAAM,SAAS,eAAe,IAAI;;;AAGlD,WAAO;EACT;EAEU,cAAc,EAAE,KAAI,GAAmC;AAC/D,SAAK,oBAAoB,IAAI;AAC7B,SAAK,gBAAe;EACtB;EAEU,UAAU,EAAE,UAAU,QAAO,GAAmC;AACxE,SAAK,yBAAyB,QAAQ;AACtC,YAAQ,QAAQ,CAAC,SAAQ;AACvB,WAAK,sBAAsB,IAAI;AAC/B,WAAK,mBAAmB,IAAI;IAC9B,CAAC;AACD,SAAK,gBAAe;EACtB;EAEU,YAAY,EAAE,KAAI,GAAiC;AAI3D,SAAK,sBAAsB,IAAI;AAC/B,SAAK,mBAAmB,IAAI;AAC5B,SAAK,gBAAe;EACtB;EAEU,sBAAsB,MAAU;AACxC,SAAK,IAAI,WAAW,KAAK,eAAe,IAAI;AAC5C,SAAK,GAAG,WAAW,KAAK,eAAe,IAAI;EAC7C;EAEU,oBAAoB,EAC5B,OACA,SACA,QAAO,GACyB;AAChC,UAAM,QAAQ,CAAC,SAAQ;AACrB,WAAK,QAAQ,iBAAiB,EAAE,MAAM,QAAO,CAAE;AAC/C,UAAI,KAAK,OAAM,GAAI;AACjB,aAAK,QAAQ,iBAAiB,EAAE,MAAM,SAAS,MAAM,KAAI,CAAE;iBAClD,KAAK,OAAM,GAAI;AACxB,aAAK,QAAQ,iBAAiB,EAAE,MAAM,SAAS,MAAM,KAAI,CAAE;;IAE/D,CAAC;AAED,YAAQ,QAAQ,CAAC,SAAQ;AACvB,WAAK,QAAQ,mBAAmB,EAAE,MAAM,QAAO,CAAE;AACjD,UAAI,KAAK,OAAM,GAAI;AACjB,aAAK,QAAQ,mBAAmB,EAAE,MAAM,SAAS,MAAM,KAAI,CAAE;iBACpD,KAAK,OAAM,GAAI;AACxB,aAAK,QAAQ,mBAAmB,EAAE,MAAM,SAAS,MAAM,KAAI,CAAE;;IAEjE,CAAC;AAED,UAAM,OAAO;MACX;MACA;MACA;MACA,UAAU,KAAK,MAAM,OAAO,CAAC,SAAS,CAAC,CAAC,KAAK,MAAM,YAAY,KAAK,EAAE,CAAC;;AAEzE,SAAK,QAAQ,qBAAqB,IAAI;EACxC;;EAKA,UAAO;AACL,SAAK,MAAK;AACV,SAAK,OAAM;AACX,SAAK,IAAG;EACV;;AAJA,WAAA;EADC,KAAK,QAAO;;AA0Gf,IAAU;CAAV,SAAUE,UAAO;AACf,QAAM,OAAO;AAEA,EAAAA,SAAA,aAAa;IACxB,MAAM;IACN,OAAO,GAAG,IAAI;IACd,KAAK,GAAG,IAAI;IACZ,SAAS,GAAG,IAAI;IAChB,YAAY,GAAG,IAAI;IACnB,UAAU,GAAG,IAAI;;AAGN,EAAAA,SAAA,iBAAiB;IAC5B,WAAW;IACX,WAAW;IACX,SAAS;IACT,UAAU;IACV,aAAa;;AAGf,WAAgB,gBAAgB,MAAU;AACxC,WAAO,KAAK,aAAY,EAAG;EAC7B;AAFgB,EAAAA,SAAA,kBAAe;AAGjC,GAvBU,YAAA,UAAO,CAAA,EAAA;;;AC5hCV,IAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+CvB,MAAM,UAAU,qBAAqB,WAAA;AACnC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,UAAS;;AAE5B,SAAO;AACT;AAEA,MAAM,UAAU,kBAAkB,WAAA;AAChC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,OAAM;;AAElB,SAAO;AACT;AAEA,MAAM,UAAU,mBAAmB,WAAA;AACjC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,QAAO;;AAEnB,SAAO;AACT;AAEA,MAAM,UAAU,kBAAkB,SAAU,SAAiB;AAC3D,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,cAAc,OAAO;;AAEjC,SAAO;AACT;AAEA,MAAM,UAAU,sBAAsB,WAAA;AACpC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,oBAAmB;;AAEtC,SAAO;AACT;AAEA,MAAM,UAAU,0BAA0B,WAAA;AACxC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,wBAAuB;;AAEnC,SAAO;AACT;AAEA,MAAM,UAAU,2BAA2B,WAAA;AACzC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,yBAAwB;;AAEpC,SAAO;AACT;AAEA,MAAM,UAAU,0BAA0B,SAAU,UAAkB;AACpE,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,wBAAwB,QAAQ;;AAE5C,SAAO;AACT;AAEA,MAAM,UAAU,qBAAqB,WAAA;AACnC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,mBAAkB;;AAErC,SAAO;AACT;AAEA,MAAM,UAAU,yBAAyB,WAAA;AACvC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,uBAAsB;;AAElC,SAAO;AACT;AAEA,MAAM,UAAU,0BAA0B,WAAA;AACxC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,wBAAuB;;AAEnC,SAAO;AACT;AAEA,MAAM,UAAU,yBAAyB,SAAU,SAAiB;AAClE,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,uBAAuB,OAAO;;AAE1C,SAAO;AACT;AAEA,MAAM,UAAU,sBAAsB,WAAA;AACpC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,oBAAmB;;AAEtC,SAAO;AACT;AAEA,MAAM,UAAU,mBAAmB,WAAA;AACjC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,iBAAgB;;AAE5B,SAAO;AACT;AAEA,MAAM,UAAU,oBAAoB,WAAA;AAClC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,kBAAiB;;AAE7B,SAAO;AACT;AAEA,MAAM,UAAU,mBAAmB,SAAU,SAAiB;AAC5D,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,iBAAiB,OAAO;;AAEpC,SAAO;AACT;AAEA,MAAM,UAAU,qBAAqB,WAAA;AACnC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,mBAAkB;;AAErC,SAAO;AACT;AAEA,MAAM,UAAU,yBAAyB,WAAA;AACvC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,uBAAsB;;AAElC,SAAO;AACT;AAEA,MAAM,UAAU,0BAA0B,WAAA;AACxC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,wBAAuB;;AAEnC,SAAO;AACT;AAEA,MAAM,UAAU,yBAAyB,SAAU,QAAgB;AACjE,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,uBAAuB,MAAM;;AAEzC,SAAO;AACT;AAEA,MAAM,UAAU,yBAAyB,SACvC,WAAyC;AAEzC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,uBAAuB,SAAS;;AAE5C,SAAO;AACT;AAEA,MAAM,UAAU,qBAAqB,SAAU,QAAyB;AACtE,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,mBAAmB,MAAM;;AAErC,SAAO;AACT;AAEA,MAAM,UAAU,6BAA6B,SAC3CC,UAA2B;AAE3B,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,2BAA2BA,QAAO;;AAE9C,SAAO;AACT;AAEA,MAAM,UAAU,mBAAmB,WAAA;AACjC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,QAAO;;AAE1B,SAAO;AACT;AAEA,MAAM,UAAU,iBAAiB,SAAU,SAA8B;AACvE,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,MAAM,OAAO;;AAEzB,SAAO;AACT;AAEA,MAAM,UAAU,iBAAiB,SAC/B,OACA,SAA8B;AAE9B,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,MAAM,OAAO,OAAO;;AAEhC,SAAO;AACT;AAEA,MAAM,UAAU,mBAAmB,WAAA;AACjC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,iBAAgB;;AAEnC,SAAO,CAAA;AACT;AAEA,MAAM,UAAU,uBAAuB,WAAA;AACrC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,qBAAoB;;AAEvC,SAAO;AACT;AAEA,MAAM,UAAU,aAAa,SAAU,MAAmB;AACxD,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,WAAW,IAAI;;AAElC,SAAO;AACT;AAEA,MAAM,UAAU,SAAS,SACvB,OACA,SAA8B;AAE9B,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,OAAO,OAAO,OAAO;;AAEjC,SAAO;AACT;AAEA,MAAM,UAAU,WAAW,SACzB,OACA,SAAiC;AAEjC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,SAAS,OAAO,OAAO;;AAEnC,SAAO;AACT;;;;;;;;;AC3SM,IAAO,YAAP,MAAO,mBACH,SAAiC;EAWzC,IAAI,qBAAkB;AACpB,WAAO,KAAK,QAAQ,YAAY,QAAQ,KAAK,QAAQ,eAAe;EACtE;EAEA,IAAI,WAAQ;AACV,WAAO,KAAK,QAAQ,YAAY;EAClC;EAEA,IAAI,SAAM;AACR,WAAO,KAAK,cAAc;EAC5B;EAEA,IAAI,QAAK;AACP,WAAO,KAAK,cAAc;EAC5B;EAEA,YAAY,UAA6B,CAAA,GAAE;AACzC,UAAK;AAzBA,SAAA,OAAO;AAKN,SAAA,WAAW,oBAAI,QAAO;AACtB,SAAA,cAAc,oBAAI,QAAO;AAoB/B,SAAK,UAAO,OAAA,OAAA,OAAA,OAAA,EACV,SAAS,KAAI,GACV,WAAU,cAAc,GACxB,OAAO;AAGZ,mBAAU,OAAO,KAAK,MAAM,OAAO;EACrC;EAEO,KAAK,OAAY;AACtB,SAAK,QAAQ;AACb,SAAK,gBAAgB,IAAI,cAAa,OAAA,OAAA,OAAA,OAAA,CAAA,GACjC,KAAK,OAAO,GAAA,EACf,MAAK,CAAA,CAAA;AAEP,SAAK,MAAK;AACV,SAAK,eAAc;EACrB;;EAIA,YAAS;AACP,WAAO,CAAC,KAAK;EACf;EAEA,SAAM;AACJ,QAAI,KAAK,UAAU;AACjB,WAAK,QAAQ,UAAU;;EAE3B;EAEA,UAAO;AACL,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,QAAQ,UAAU;;EAE3B;EAEA,cAAc,SAAiB;AAC7B,QAAI,WAAW,MAAM;AACnB,UAAI,YAAY,KAAK,UAAS,GAAI;AAChC,YAAI,SAAS;AACX,eAAK,OAAM;eACN;AACL,eAAK,QAAO;;;eAGP,KAAK,UAAS,GAAI;AAC3B,WAAK,QAAO;WACP;AACL,WAAK,OAAM;;AAGb,WAAO;EACT;EAEA,sBAAmB;AACjB,WAAO,KAAK,WAAU;EACxB;EAEA,0BAAuB;AACrB,SAAK,eAAc;AACnB,WAAO;EACT;EAEA,2BAAwB;AACtB,SAAK,gBAAe;AACpB,WAAO;EACT;EAEA,wBAAwB,UAAkB;AACxC,QAAI,YAAY,MAAM;AACpB,UAAI,aAAa,KAAK,oBAAmB,GAAI;AAC3C,YAAI,UAAU;AACZ,eAAK,wBAAuB;eACvB;AACL,eAAK,yBAAwB;;;eAGxB,KAAK,oBAAmB,GAAI;AACrC,WAAK,yBAAwB;WACxB;AACL,WAAK,wBAAuB;;AAG9B,WAAO;EACT;EAEA,qBAAkB;AAChB,WAAO,KAAK,QAAQ,YAAY;EAClC;EAEA,yBAAsB;AACpB,SAAK,cAAc,QAAQ,UAAU;AACrC,WAAO;EACT;EAEA,0BAAuB;AACrB,SAAK,cAAc,QAAQ,UAAU;AACrC,WAAO;EACT;EAEA,uBAAuB,SAAiB;AACtC,QAAI,WAAW,MAAM;AACnB,UAAI,YAAY,KAAK,mBAAkB,GAAI;AACzC,YAAI,SAAS;AACX,eAAK,uBAAsB;eACtB;AACL,eAAK,wBAAuB;;;eAGvB,KAAK,mBAAkB,GAAI;AACpC,WAAK,wBAAuB;WACvB;AACL,WAAK,uBAAsB;;AAG7B,WAAO;EACT;EAEA,sBAAmB;AACjB,WAAO,CAAC,KAAK;EACf;EAEA,mBAAgB;AACd,QAAI,KAAK,oBAAoB;AAC3B,WAAK,QAAQ,aAAa;;AAE5B,WAAO;EACT;EAEA,oBAAiB;AACf,QAAI,CAAC,KAAK,oBAAoB;AAC5B,WAAK,QAAQ,aAAa;;AAE5B,WAAO;EACT;EAEA,iBAAiB,SAAiB;AAChC,QAAI,WAAW,MAAM;AACnB,UAAI,YAAY,KAAK,oBAAmB,GAAI;AAC1C,YAAI,SAAS;AACX,eAAK,iBAAgB;eAChB;AACL,eAAK,kBAAiB;;;eAGjB,KAAK,oBAAmB,GAAI;AACrC,WAAK,kBAAiB;WACjB;AACL,WAAK,iBAAgB;;AAGvB,WAAO;EACT;EAEA,qBAAkB;AAChB,WAAO,KAAK,cAAc,QAAQ,WAAW;EAC/C;EAEA,yBAAsB;AACpB,SAAK,cAAc,QAAQ,SAAS;AACpC,WAAO;EACT;EAEA,0BAAuB;AACrB,SAAK,cAAc,QAAQ,SAAS;AACpC,WAAO;EACT;EAEA,uBAAuB,QAAgB;AACrC,QAAI,UAAU,MAAM;AAClB,UAAI,WAAW,KAAK,mBAAkB,GAAI;AACxC,YAAI,QAAQ;AACV,eAAK,uBAAsB;eACtB;AACL,eAAK,wBAAuB;;;eAGvB,KAAK,mBAAkB,GAAI;AACpC,WAAK,wBAAuB;WACvB;AACL,WAAK,uBAAsB;;AAG7B,WAAO;EACT;EAEA,uBAAuB,WAAyC;AAC9D,SAAK,aAAa,SAAS;EAC7B;EAEA,mBAAmB,QAAyB;AAC1C,SAAK,UAAU,MAAM;AACrB,WAAO;EACT;EAEA,2BAA2BC,UAA2B;AACpD,SAAK,WAAWA,QAAO;AACvB,WAAO;EACT;EAEA,UAAO;AACL,WAAO,KAAK,UAAU;EACxB;EAEA,MAAM,UAAgC,CAAA,GAAE;AACtC,SAAK,cAAc,MAAM,OAAO;AAChC,WAAO;EACT;EAEA,MACE,OACA,UAAgC,CAAA,GAAE;AAElC,SAAK,cAAc,MAAM,QAAQ,KAAK,SAAS,KAAK,IAAI,CAAA,GAAI,OAAO;AACnE,WAAO;EACT;EAEA,mBAAgB;AACd,WAAO,KAAK;EACd;EAEA,uBAAoB;AAClB,WAAO,KAAK;EACd;EAEA,WAAW,MAAmB;AAC5B,WAAO,KAAK,cAAc,WAAW,IAAI;EAC3C;EAEA,OACE,OACA,UAAgC,CAAA,GAAE;AAElC,UAAM,WAAW,KAAK,SAAS,KAAK;AACpC,QAAI,SAAS,QAAQ;AACnB,UAAI,KAAK,WAAU,GAAI;AACrB,aAAK,cAAc,OAAO,UAAU,OAAO;aACtC;AACL,aAAK,MAAM,SAAS,MAAM,GAAG,CAAC,GAAG,OAAO;;;AAG5C,WAAO;EACT;EAEA,SACE,OACA,UAAmC,CAAA,GAAE;AAErC,SAAK,cAAc,SAAS,KAAK,SAAS,KAAK,GAAG,OAAO;AACzD,WAAO;EACT;;EAIU,QAAK;AACb,SAAK,cAAc,GAAG,KAAK,CAAC,MAAM,SAAQ;AACxC,WAAK,QAAQ,MAAM,IAAI;AACvB,WAAK,MAAM,QAAQ,MAAM,IAAI;IAC/B,CAAC;EACH;EAEU,iBAAc;AACtB,SAAK,MAAM,GAAG,mBAAmB,KAAK,kBAAkB,IAAI;AAC5D,SAAK,MAAM,GAAG,eAAe,KAAK,cAAc,IAAI;AACpD,SAAK,MAAM,GAAG,kBAAkB,KAAK,iBAAiB,IAAI;AAC1D,SAAK,MAAM,GAAG,gBAAgB,KAAK,eAAe,IAAI;AACtD,SAAK,cAAc,GAAG,iBAAiB,KAAK,gBAAgB,IAAI;EAClE;EAEU,gBAAa;AACrB,SAAK,MAAM,IAAI,mBAAmB,KAAK,kBAAkB,IAAI;AAC7D,SAAK,MAAM,IAAI,eAAe,KAAK,cAAc,IAAI;AACrD,SAAK,MAAM,IAAI,kBAAkB,KAAK,iBAAiB,IAAI;AAC3D,SAAK,MAAM,IAAI,gBAAgB,KAAK,eAAe,IAAI;AACvD,SAAK,cAAc,IAAI,iBAAiB,KAAK,gBAAgB,IAAI;EACnE;EAEU,iBAAiB,EAAE,EAAC,GAAgC;AAC5D,QAAI,CAAC,KAAK,oBAAoB,CAAC,GAAG;AAChC;;AAGF,UAAM,oBAAoB,KAAK,MAAM,QAAQ,aAAa,GAAG,IAAI;AACjE,UAAM,WAAW,KAAK,MAAM,UAAe,UAAU;AACrD,UAAM,uBAAuB,YAAY,SAAS,aAAa,GAAG,IAAI;AACtE,QACE,KAAK,gBAAgB,GAAG,IAAI,KAC3B,KAAK,gBAAgB,CAAC,KAAK,CAAC,wBAAwB,CAAC,mBACtD;AACA,WAAK,gBAAgB,CAAC;;EAE1B;EAEU,oBAAoB,GAAqB;AACjD,UAAM,aAAa,KAAK,QAAQ;AAChC,YACG,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,SAAS,eAAe,MAAK,EAAE,WAAW,MACtD,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,SAAS,gBAAgB,MAAK,EAAE,WAAW;EAE5D;EAEU,eAAY;AACpB,SAAK,MAAK;EACZ;EAEU,gBAAgB,GAAuB,QAAgB;AAC/D,WACE,CAAC,KAAK,sBACN,YAAY,QAAQ,GAAG,KAAK,QAAQ,WAAW,MAAM;EAEzD;EAEU,uBAAuB,GAAwC;AACvE,WACE,KAAK,WAAU,KACf,YAAY,QAAQ,GAAG,KAAK,QAAQ,0BAA0B;EAElE;EAEU,gBAAgB,EAAE,KAAI,GAA+B;AAC7D,SAAK,SAAS,IAAI,MAAM,IAAI;EAC9B;EAEU,cAAc,EAAE,GAAG,KAAI,GAA6B;AAC5D,UAAM,UAAU,KAAK;AACrB,QAAI,WAAW,KAAK;AACpB,QAAI,CAAC,YAAY,KAAK,SAAS,IAAI,IAAI,GAAG;AACxC,iBAAW,QAAQ,sBAAsB;AAEzC,UAAI,CAAC,UAAU;AACb,mBAAW,QAAQ,sBAAsB,SAAS,KAAK,OAAM;;AAG/D,UAAI,CAAC,UAAU;AACb,mBAAW,QAAQ,sBAAsB,SAAS,KAAK,OAAM;;;AAIjE,QAAI,CAAC,UAAU;AACb,UAAI,CAAC,KAAK,uBAAuB,CAAC,GAAG;AACnC,aAAK,MAAM,IAAI;iBACN,KAAK,YAAY,IAAI,IAAI,GAAG;AACrC,aAAK,YAAY,OAAO,IAAI;iBACnB,KAAK,WAAW,IAAI,GAAG;AAChC,aAAK,SAAS,IAAI;aACb;AACL,aAAK,OAAO,IAAI;;;AAIpB,SAAK,SAAS,OAAO,IAAI;EAC3B;EAEU,eAAe,EACvB,GACA,KAAI,GACqC;AACzC,QAAI,CAAC,KAAK,UAAU;AAClB,UAAI,KAAK,uBAAuB,CAAC,GAAG;AAClC,aAAK,SAAS,IAAI;AAClB,aAAK,YAAY,IAAI,MAAM,IAAI;;;EAGrC;EAEU,SAAS,OAAwC;AACzD,YAAQ,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,GAC1C,IAAI,CAAC,SACJ,OAAO,SAAS,WAAW,KAAK,MAAM,YAAY,IAAI,IAAI,IAAI,EAE/D,OAAO,CAAC,SAAS,QAAQ,IAAI;EAClC;EAEU,gBAAgB,GAAqB;AAC7C,QAAI,CAAC,KAAK,oBAAoB;AAC5B,WAAK,cAAc,eAAe,CAAC;;AAErC,WAAO;EACT;EAEU,aAAU;AAClB,WAAO,KAAK,QAAQ,aAAa;EACnC;EAEU,iBAAc;AACtB,SAAK,QAAQ,WAAW;AACxB,WAAO;EACT;EAEU,kBAAe;AACvB,SAAK,QAAQ,WAAW;AACxB,WAAO;EACT;EAEU,aAAa,WAAyC;AAC9D,SAAK,QAAQ,YAAY;AACzB,WAAO;EACT;EAEU,WAAWA,UAA2B;AAC9C,SAAK,cAAc,WAAWA,QAAO;AACrC,WAAO;EACT;EAEU,UAAU,QAAyB;AAC3C,SAAK,cAAc,UAAU,MAAM;AACnC,WAAO;EACT;EAGA,UAAO;AACL,SAAK,cAAa;AAClB,SAAK,IAAG;AACR,SAAK,cAAc,QAAO;AAC1B,mBAAU,MAAM,KAAK,IAAI;EAC3B;;AALAC,YAAA;EADC,SAAS,QAAO;;CASnB,SAAiBC,YAAS;AAaX,EAAAA,WAAA,iBAAiD;IAC5D,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,eAAe;IACf,UAAU;IACV,4BAA4B,CAAC,QAAQ,MAAM;IAC3C,SAAS;IACT,QAAQ;IACR,mBAAmB;IACnB,mBAAmB;IACnB,mBAAmB;IACnB,WAAW;IACX,SAAS;IACT,YAAY,CAAC,iBAAiB,gBAAgB;;AAElD,GA7BiB,cAAA,YAAS,CAAA,EAAA;", "names": ["main_exports", "content", "Private", "content", "content", "__decorate", "Selection"]}