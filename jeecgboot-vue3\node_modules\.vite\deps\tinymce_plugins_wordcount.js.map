{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/wordcount/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/wordcount/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const eq = t => a => t === a;\n    const isNull = eq(null);\n\n    const identity = x => {\n      return x;\n    };\n\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n\n    const punctuationStr = `[~\\u2116|!-*+-\\\\/:;?@\\\\[-\\`{}\\u00A1\\u00AB\\u00B7\\u00BB\\u00BF;\\u00B7\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061E\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1361-\\u1368\\u1400\\u166D\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u3008\\u3009\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30\\u2E31\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]`;\n    const regExps = {\n      aletter: '[A-Za-z\\xaa\\xb5\\xba\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0370-\\u0374\\u0376\\u0377\\u037a-\\u037d\\u0386\\u0388-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u048a-\\u0527\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05d0-\\u05ea\\u05f0-\\u05F3\\u0620-\\u064a\\u066e\\u066f\\u0671-\\u06d3\\u06d5\\u06e5\\u06e6\\u06ee\\u06ef\\u06fa-\\u06fc\\u06ff\\u0710\\u0712-\\u072f\\u074d-\\u07a5\\u07b1\\u07ca-\\u07ea\\u07f4\\u07f5\\u07fa\\u0800-\\u0815\\u081a\\u0824\\u0828\\u0840-\\u0858\\u0904-\\u0939\\u093d\\u0950\\u0958-\\u0961\\u0971-\\u0977\\u0979-\\u097f\\u0985-\\u098c\\u098f\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bd\\u09ce\\u09dc\\u09dd\\u09df-\\u09e1\\u09f0\\u09f1\\u0a05-\\u0a0a\\u0a0f\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32\\u0a33\\u0a35\\u0a36\\u0a38\\u0a39\\u0a59-\\u0a5c\\u0a5e\\u0a72-\\u0a74\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2\\u0ab3\\u0ab5-\\u0ab9\\u0abd\\u0ad0\\u0ae0\\u0ae1\\u0b05-\\u0b0c\\u0b0f\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32\\u0b33\\u0b35-\\u0b39\\u0b3d\\u0b5c\\u0b5d\\u0b5f-\\u0b61\\u0b71\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99\\u0b9a\\u0b9c\\u0b9e\\u0b9f\\u0ba3\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bd0\\u0c05-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c33\\u0c35-\\u0c39\\u0c3d\\u0c58\\u0c59\\u0c60\\u0c61\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbd\\u0cde\\u0ce0\\u0ce1\\u0cf1\\u0cf2\\u0d05-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d3a\\u0d3d\\u0d4e\\u0d60\\u0d61\\u0d7a-\\u0d7f\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0f00\\u0f40-\\u0f47\\u0f49-\\u0f6c\\u0f88-\\u0f8c\\u10a0-\\u10c5\\u10d0-\\u10fa\\u10fc\\u1100-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u1380-\\u138f\\u13a0-\\u13f4\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f0\\u1700-\\u170c\\u170e-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176c\\u176e-\\u1770\\u1820-\\u1877\\u1880-\\u18a8\\u18aa\\u18b0-\\u18f5\\u1900-\\u191c\\u1a00-\\u1a16\\u1b05-\\u1b33\\u1b45-\\u1b4b\\u1b83-\\u1ba0\\u1bae\\u1baf\\u1bc0-\\u1be5\\u1c00-\\u1c23\\u1c4d-\\u1c4f\\u1c5a-\\u1c7d\\u1ce9-\\u1cec\\u1cee-\\u1cf1\\u1d00-\\u1dbf\\u1e00-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u2071\\u207f\\u2090-\\u209c\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2119-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u212d\\u212f-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u24B6-\\u24E9\\u2c00-\\u2c2e\\u2c30-\\u2c5e\\u2c60-\\u2ce4\\u2ceb-\\u2cee\\u2d00-\\u2d25\\u2d30-\\u2d65\\u2d6f\\u2d80-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u2e2f\\u3005\\u303b\\u303c\\u3105-\\u312d\\u3131-\\u318e\\u31a0-\\u31ba\\ua000-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua61f\\ua62a\\ua62b\\ua640-\\ua66e\\ua67f-\\ua697\\ua6a0-\\ua6ef\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua78e\\ua790\\ua791\\ua7a0-\\ua7a9\\ua7fa-\\ua801\\ua803-\\ua805\\ua807-\\ua80a\\ua80c-\\ua822\\ua840-\\ua873\\ua882-\\ua8b3\\ua8f2-\\ua8f7\\ua8fb\\ua90a-\\ua925\\ua930-\\ua946\\ua960-\\ua97c\\ua984-\\ua9b2\\ua9cf\\uaa00-\\uaa28\\uaa40-\\uaa42\\uaa44-\\uaa4b\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uabc0-\\uabe2\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d\\ufb1f-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40\\ufb41\\ufb43\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff21-\\uff3a\\uff41-\\uff5a\\uffa0-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc]',\n      midnumlet: `[-'\\\\.\\u2018\\u2019\\u2024\\uFE52\\uFF07\\uFF0E]`,\n      midletter: '[:\\xB7\\xB7\\u05F4\\u2027\\uFE13\\uFE55\\uFF1A]',\n      midnum: '[\\xB1+*/,;;\\u0589\\u060C\\u060D\\u066C\\u07F8\\u2044\\uFE10\\uFE14\\uFE50\\uFE54\\uFF0C\\uFF1B]',\n      numeric: '[0-9\\u0660-\\u0669\\u066B\\u06f0-\\u06f9\\u07c0-\\u07c9\\u0966-\\u096f\\u09e6-\\u09ef\\u0a66-\\u0a6f\\u0ae6-\\u0aef\\u0b66-\\u0b6f\\u0be6-\\u0bef\\u0c66-\\u0c6f\\u0ce6-\\u0cef\\u0d66-\\u0d6f\\u0e50-\\u0e59\\u0ed0-\\u0ed9\\u0f20-\\u0f29\\u1040-\\u1049\\u1090-\\u1099\\u17e0-\\u17e9\\u1810-\\u1819\\u1946-\\u194f\\u19d0-\\u19d9\\u1a80-\\u1a89\\u1a90-\\u1a99\\u1b50-\\u1b59\\u1bb0-\\u1bb9\\u1c40-\\u1c49\\u1c50-\\u1c59\\ua620-\\ua629\\ua8d0-\\ua8d9\\ua900-\\ua909\\ua9d0-\\ua9d9\\uaa50-\\uaa59\\uabf0-\\uabf9]',\n      cr: '\\\\r',\n      lf: '\\\\n',\n      newline: '[\\x0B\\f\\x85\\u2028\\u2029]',\n      extend: '[\\u0300-\\u036f\\u0483-\\u0489\\u0591-\\u05bd\\u05bf\\u05c1\\u05c2\\u05c4\\u05c5\\u05c7\\u0610-\\u061a\\u064b-\\u065f\\u0670\\u06d6-\\u06dc\\u06df-\\u06e4\\u06e7\\u06e8\\u06ea-\\u06ed\\u0711\\u0730-\\u074a\\u07a6-\\u07b0\\u07eb-\\u07f3\\u0816-\\u0819\\u081b-\\u0823\\u0825-\\u0827\\u0829-\\u082d\\u0859-\\u085b\\u0900-\\u0903\\u093a-\\u093c\\u093e-\\u094f\\u0951-\\u0957\\u0962\\u0963\\u0981-\\u0983\\u09bc\\u09be-\\u09c4\\u09c7\\u09c8\\u09cb-\\u09cd\\u09d7\\u09e2\\u09e3\\u0a01-\\u0a03\\u0a3c\\u0a3e-\\u0a42\\u0a47\\u0a48\\u0a4b-\\u0a4d\\u0a51\\u0a70\\u0a71\\u0a75\\u0a81-\\u0a83\\u0abc\\u0abe-\\u0ac5\\u0ac7-\\u0ac9\\u0acb-\\u0acd\\u0ae2\\u0ae3\\u0b01-\\u0b03\\u0b3c\\u0b3e-\\u0b44\\u0b47\\u0b48\\u0b4b-\\u0b4d\\u0b56\\u0b57\\u0b62\\u0b63\\u0b82\\u0bbe-\\u0bc2\\u0bc6-\\u0bc8\\u0bca-\\u0bcd\\u0bd7\\u0c01-\\u0c03\\u0c3e-\\u0c44\\u0c46-\\u0c48\\u0c4a-\\u0c4d\\u0c55\\u0c56\\u0c62\\u0c63\\u0c82\\u0c83\\u0cbc\\u0cbe-\\u0cc4\\u0cc6-\\u0cc8\\u0cca-\\u0ccd\\u0cd5\\u0cd6\\u0ce2\\u0ce3\\u0d02\\u0d03\\u0d3e-\\u0d44\\u0d46-\\u0d48\\u0d4a-\\u0d4d\\u0d57\\u0d62\\u0d63\\u0d82\\u0d83\\u0dca\\u0dcf-\\u0dd4\\u0dd6\\u0dd8-\\u0ddf\\u0df2\\u0df3\\u0e31\\u0e34-\\u0e3a\\u0e47-\\u0e4e\\u0eb1\\u0eb4-\\u0eb9\\u0ebb\\u0ebc\\u0ec8-\\u0ecd\\u0f18\\u0f19\\u0f35\\u0f37\\u0f39\\u0f3e\\u0f3f\\u0f71-\\u0f84\\u0f86\\u0f87\\u0f8d-\\u0f97\\u0f99-\\u0fbc\\u0fc6\\u102b-\\u103e\\u1056-\\u1059\\u105e-\\u1060\\u1062-\\u1064\\u1067-\\u106d\\u1071-\\u1074\\u1082-\\u108d\\u108f\\u109a-\\u109d\\u135d-\\u135f\\u1712-\\u1714\\u1732-\\u1734\\u1752\\u1753\\u1772\\u1773\\u17b6-\\u17d3\\u17dd\\u180b-\\u180d\\u18a9\\u1920-\\u192b\\u1930-\\u193b\\u19b0-\\u19c0\\u19c8\\u19c9\\u1a17-\\u1a1b\\u1a55-\\u1a5e\\u1a60-\\u1a7c\\u1a7f\\u1b00-\\u1b04\\u1b34-\\u1b44\\u1b6b-\\u1b73\\u1b80-\\u1b82\\u1ba1-\\u1baa\\u1be6-\\u1bf3\\u1c24-\\u1c37\\u1cd0-\\u1cd2\\u1cd4-\\u1ce8\\u1ced\\u1cf2\\u1dc0-\\u1de6\\u1dfc-\\u1dff\\u200c\\u200d\\u20d0-\\u20f0\\u2cef-\\u2cf1\\u2d7f\\u2de0-\\u2dff\\u302a-\\u302f\\u3099\\u309a\\ua66f-\\uA672\\ua67c\\ua67d\\ua6f0\\ua6f1\\ua802\\ua806\\ua80b\\ua823-\\ua827\\ua880\\ua881\\ua8b4-\\ua8c4\\ua8e0-\\ua8f1\\ua926-\\ua92d\\ua947-\\ua953\\ua980-\\ua983\\ua9b3-\\ua9c0\\uaa29-\\uaa36\\uaa43\\uaa4c\\uaa4d\\uaa7b\\uaab0\\uaab2-\\uaab4\\uaab7\\uaab8\\uaabe\\uaabf\\uaac1\\uabe3-\\uabea\\uabec\\uabed\\ufb1e\\ufe00-\\ufe0f\\ufe20-\\ufe26\\uff9e\\uff9f]',\n      format: '[\\xAD\\u0600-\\u0603\\u06DD\\u070F\\u17b4\\u17b5\\u200E\\u200F\\u202A-\\u202E\\u2060-\\u2064\\u206A-\\u206F\\uFEFF\\uFFF9-\\uFFFB]',\n      katakana: '[\\u3031-\\u3035\\u309B\\u309C\\u30A0-\\u30fa\\u30fc-\\u30ff\\u31f0-\\u31ff\\u32D0-\\u32FE\\u3300-\\u3357\\uff66-\\uff9d]',\n      extendnumlet: '[=_\\u203f\\u2040\\u2054\\ufe33\\ufe34\\ufe4d-\\ufe4f\\uff3f\\u2200-\\u22FF<>]',\n      punctuation: punctuationStr\n    };\n    const characterIndices = {\n      ALETTER: 0,\n      MIDNUMLET: 1,\n      MIDLETTER: 2,\n      MIDNUM: 3,\n      NUMERIC: 4,\n      CR: 5,\n      LF: 6,\n      NEWLINE: 7,\n      EXTEND: 8,\n      FORMAT: 9,\n      KATAKANA: 10,\n      EXTENDNUMLET: 11,\n      AT: 12,\n      OTHER: 13\n    };\n    const SETS$1 = [\n      new RegExp(regExps.aletter),\n      new RegExp(regExps.midnumlet),\n      new RegExp(regExps.midletter),\n      new RegExp(regExps.midnum),\n      new RegExp(regExps.numeric),\n      new RegExp(regExps.cr),\n      new RegExp(regExps.lf),\n      new RegExp(regExps.newline),\n      new RegExp(regExps.extend),\n      new RegExp(regExps.format),\n      new RegExp(regExps.katakana),\n      new RegExp(regExps.extendnumlet),\n      new RegExp('@')\n    ];\n    const EMPTY_STRING$1 = '';\n    const PUNCTUATION$1 = new RegExp('^' + regExps.punctuation + '$');\n    const WHITESPACE$1 = /^\\s+$/;\n\n    const SETS = SETS$1;\n    const OTHER = characterIndices.OTHER;\n    const getType = char => {\n      let type = OTHER;\n      const setsLength = SETS.length;\n      for (let j = 0; j < setsLength; ++j) {\n        const set = SETS[j];\n        if (set && set.test(char)) {\n          type = j;\n          break;\n        }\n      }\n      return type;\n    };\n    const memoize = func => {\n      const cache = {};\n      return char => {\n        if (cache[char]) {\n          return cache[char];\n        } else {\n          const result = func(char);\n          cache[char] = result;\n          return result;\n        }\n      };\n    };\n    const classify = characters => {\n      const memoized = memoize(getType);\n      return map(characters, memoized);\n    };\n\n    const isWordBoundary = (map, index) => {\n      const type = map[index];\n      const nextType = map[index + 1];\n      if (index < 0 || index > map.length - 1 && index !== 0) {\n        return false;\n      }\n      if (type === characterIndices.ALETTER && nextType === characterIndices.ALETTER) {\n        return false;\n      }\n      const nextNextType = map[index + 2];\n      if (type === characterIndices.ALETTER && (nextType === characterIndices.MIDLETTER || nextType === characterIndices.MIDNUMLET || nextType === characterIndices.AT) && nextNextType === characterIndices.ALETTER) {\n        return false;\n      }\n      const prevType = map[index - 1];\n      if ((type === characterIndices.MIDLETTER || type === characterIndices.MIDNUMLET || nextType === characterIndices.AT) && nextType === characterIndices.ALETTER && prevType === characterIndices.ALETTER) {\n        return false;\n      }\n      if ((type === characterIndices.NUMERIC || type === characterIndices.ALETTER) && (nextType === characterIndices.NUMERIC || nextType === characterIndices.ALETTER)) {\n        return false;\n      }\n      if ((type === characterIndices.MIDNUM || type === characterIndices.MIDNUMLET) && nextType === characterIndices.NUMERIC && prevType === characterIndices.NUMERIC) {\n        return false;\n      }\n      if (type === characterIndices.NUMERIC && (nextType === characterIndices.MIDNUM || nextType === characterIndices.MIDNUMLET) && nextNextType === characterIndices.NUMERIC) {\n        return false;\n      }\n      if ((type === characterIndices.EXTEND || type === characterIndices.FORMAT) && (nextType === characterIndices.ALETTER || nextType === characterIndices.NUMERIC || nextType === characterIndices.KATAKANA || nextType === characterIndices.EXTEND || nextType === characterIndices.FORMAT) || (nextType === characterIndices.EXTEND || nextType === characterIndices.FORMAT && (nextNextType === characterIndices.ALETTER || nextNextType === characterIndices.NUMERIC || nextNextType === characterIndices.KATAKANA || nextNextType === characterIndices.EXTEND || nextNextType === characterIndices.FORMAT)) && (type === characterIndices.ALETTER || type === characterIndices.NUMERIC || type === characterIndices.KATAKANA || type === characterIndices.EXTEND || type === characterIndices.FORMAT)) {\n        return false;\n      }\n      if (type === characterIndices.CR && nextType === characterIndices.LF) {\n        return false;\n      }\n      if (type === characterIndices.NEWLINE || type === characterIndices.CR || type === characterIndices.LF) {\n        return true;\n      }\n      if (nextType === characterIndices.NEWLINE || nextType === characterIndices.CR || nextType === characterIndices.LF) {\n        return true;\n      }\n      if (type === characterIndices.KATAKANA && nextType === characterIndices.KATAKANA) {\n        return false;\n      }\n      if (nextType === characterIndices.EXTENDNUMLET && (type === characterIndices.ALETTER || type === characterIndices.NUMERIC || type === characterIndices.KATAKANA || type === characterIndices.EXTENDNUMLET)) {\n        return false;\n      }\n      if (type === characterIndices.EXTENDNUMLET && (nextType === characterIndices.ALETTER || nextType === characterIndices.NUMERIC || nextType === characterIndices.KATAKANA)) {\n        return false;\n      }\n      if (type === characterIndices.AT) {\n        return false;\n      }\n      return true;\n    };\n\n    const EMPTY_STRING = EMPTY_STRING$1;\n    const WHITESPACE = WHITESPACE$1;\n    const PUNCTUATION = PUNCTUATION$1;\n    const isProtocol = str => str === 'http' || str === 'https';\n    const findWordEnd = (characters, startIndex) => {\n      let i;\n      for (i = startIndex; i < characters.length; i++) {\n        if (WHITESPACE.test(characters[i])) {\n          break;\n        }\n      }\n      return i;\n    };\n    const findUrlEnd = (characters, startIndex) => {\n      const endIndex = findWordEnd(characters, startIndex + 1);\n      const peakedWord = characters.slice(startIndex + 1, endIndex).join(EMPTY_STRING);\n      return peakedWord.substr(0, 3) === '://' ? endIndex : startIndex;\n    };\n    const findWordsWithIndices = (chars, sChars, characterMap, options) => {\n      const words = [];\n      const indices = [];\n      let word = [];\n      for (let i = 0; i < characterMap.length; ++i) {\n        word.push(chars[i]);\n        if (isWordBoundary(characterMap, i)) {\n          const ch = sChars[i];\n          if ((options.includeWhitespace || !WHITESPACE.test(ch)) && (options.includePunctuation || !PUNCTUATION.test(ch))) {\n            const startOfWord = i - word.length + 1;\n            const endOfWord = i + 1;\n            const str = sChars.slice(startOfWord, endOfWord).join(EMPTY_STRING);\n            if (isProtocol(str)) {\n              const endOfUrl = findUrlEnd(sChars, i);\n              const url = chars.slice(endOfWord, endOfUrl);\n              Array.prototype.push.apply(word, url);\n              i = endOfUrl;\n            }\n            words.push(word);\n            indices.push({\n              start: startOfWord,\n              end: endOfWord\n            });\n          }\n          word = [];\n        }\n      }\n      return {\n        words,\n        indices\n      };\n    };\n    const getDefaultOptions = () => ({\n      includeWhitespace: false,\n      includePunctuation: false\n    });\n    const getWordsWithIndices = (chars, extract, options) => {\n      options = {\n        ...getDefaultOptions(),\n        ...options\n      };\n      const extractedChars = map(chars, extract);\n      const characterMap = classify(extractedChars);\n      return findWordsWithIndices(chars, extractedChars, characterMap, options);\n    };\n    const getWords$1 = (chars, extract, options) => getWordsWithIndices(chars, extract, options).words;\n\n    const getWords = getWords$1;\n\n    const removeZwsp$1 = s => s.replace(/\\uFEFF/g, '');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker');\n\n    const getText = (node, schema) => {\n      const blockElements = schema.getBlockElements();\n      const voidElements = schema.getVoidElements();\n      const isNewline = node => blockElements[node.nodeName] || voidElements[node.nodeName];\n      const textBlocks = [];\n      let txt = '';\n      const treeWalker = new global$1(node, node);\n      let tempNode;\n      while (tempNode = treeWalker.next()) {\n        if (tempNode.nodeType === 3) {\n          txt += removeZwsp$1(tempNode.data);\n        } else if (isNewline(tempNode) && txt.length) {\n          textBlocks.push(txt);\n          txt = '';\n        }\n      }\n      if (txt.length) {\n        textBlocks.push(txt);\n      }\n      return textBlocks;\n    };\n\n    const removeZwsp = text => text.replace(/\\u200B/g, '');\n    const strLen = str => str.replace(/[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g, '_').length;\n    const countWords = (node, schema) => {\n      const text = removeZwsp(getText(node, schema).join('\\n'));\n      return getWords(text.split(''), identity).length;\n    };\n    const countCharacters = (node, schema) => {\n      const text = getText(node, schema).join('');\n      return strLen(text);\n    };\n    const countCharactersWithoutSpaces = (node, schema) => {\n      const text = getText(node, schema).join('').replace(/\\s/g, '');\n      return strLen(text);\n    };\n\n    const createBodyCounter = (editor, count) => () => count(editor.getBody(), editor.schema);\n    const createSelectionCounter = (editor, count) => () => count(editor.selection.getRng().cloneContents(), editor.schema);\n    const createBodyWordCounter = editor => createBodyCounter(editor, countWords);\n    const get = editor => ({\n      body: {\n        getWordCount: createBodyWordCounter(editor),\n        getCharacterCount: createBodyCounter(editor, countCharacters),\n        getCharacterCountWithoutSpaces: createBodyCounter(editor, countCharactersWithoutSpaces)\n      },\n      selection: {\n        getWordCount: createSelectionCounter(editor, countWords),\n        getCharacterCount: createSelectionCounter(editor, countCharacters),\n        getCharacterCountWithoutSpaces: createSelectionCounter(editor, countCharactersWithoutSpaces)\n      },\n      getCount: createBodyWordCounter(editor)\n    });\n\n    const open = (editor, api) => {\n      editor.windowManager.open({\n        title: 'Word Count',\n        body: {\n          type: 'panel',\n          items: [{\n              type: 'table',\n              header: [\n                'Count',\n                'Document',\n                'Selection'\n              ],\n              cells: [\n                [\n                  'Words',\n                  String(api.body.getWordCount()),\n                  String(api.selection.getWordCount())\n                ],\n                [\n                  'Characters (no spaces)',\n                  String(api.body.getCharacterCountWithoutSpaces()),\n                  String(api.selection.getCharacterCountWithoutSpaces())\n                ],\n                [\n                  'Characters',\n                  String(api.body.getCharacterCount()),\n                  String(api.selection.getCharacterCount())\n                ]\n              ]\n            }]\n        },\n        buttons: [{\n            type: 'cancel',\n            name: 'close',\n            text: 'Close',\n            primary: true\n          }]\n      });\n    };\n\n    const register$1 = (editor, api) => {\n      editor.addCommand('mceWordCount', () => open(editor, api));\n    };\n\n    const first = (fn, rate) => {\n      let timer = null;\n      const cancel = () => {\n        if (!isNull(timer)) {\n          clearTimeout(timer);\n          timer = null;\n        }\n      };\n      const throttle = (...args) => {\n        if (isNull(timer)) {\n          timer = setTimeout(() => {\n            timer = null;\n            fn.apply(null, args);\n          }, rate);\n        }\n      };\n      return {\n        cancel,\n        throttle\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    const fireWordCountUpdate = (editor, api) => {\n      editor.dispatch('wordCountUpdate', {\n        wordCount: {\n          words: api.body.getWordCount(),\n          characters: api.body.getCharacterCount(),\n          charactersWithoutSpaces: api.body.getCharacterCountWithoutSpaces()\n        }\n      });\n    };\n\n    const updateCount = (editor, api) => {\n      fireWordCountUpdate(editor, api);\n    };\n    const setup = (editor, api, delay) => {\n      const debouncedUpdate = first(() => updateCount(editor, api), delay);\n      editor.on('init', () => {\n        updateCount(editor, api);\n        global.setEditorTimeout(editor, () => {\n          editor.on('SetContent BeforeAddUndo Undo Redo ViewUpdate keyup', debouncedUpdate.throttle);\n        }, 0);\n        editor.on('remove', debouncedUpdate.cancel);\n      });\n    };\n\n    const register = editor => {\n      const onAction = () => editor.execCommand('mceWordCount');\n      editor.ui.registry.addButton('wordcount', {\n        tooltip: 'Word count',\n        icon: 'character-count',\n        onAction\n      });\n      editor.ui.registry.addMenuItem('wordcount', {\n        text: 'Word count',\n        icon: 'character-count',\n        onAction\n      });\n    };\n\n    var Plugin = (delay = 300) => {\n      global$2.add('wordcount', editor => {\n        const api = get(editor);\n        register$1(editor, api);\n        register(editor);\n        setup(editor, api, delay);\n        return api;\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"wordcount\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/wordcount')\n//   ES2015:\n//     import 'tinymce/plugins/wordcount'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,KAAK,OAAK,OAAK,MAAM;AAC3B,YAAM,SAAS,GAAG,IAAI;AAEtB,YAAM,WAAW,OAAK;AACpB,eAAO;AAAA,MACT;AAEA,YAAM,MAAM,CAAC,IAAI,MAAM;AACrB,cAAM,MAAM,GAAG;AACf,cAAM,IAAI,IAAI,MAAM,GAAG;AACvB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,QACf;AACA,eAAO;AAAA,MACT;AAEA,YAAM,iBAAiB;AACvB,YAAM,UAAU;AAAA,QACd,SAAS;AAAA,QACT,WAAW;AAAA,QACX,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,cAAc;AAAA,QACd,aAAa;AAAA,MACf;AACA,YAAM,mBAAmB;AAAA,QACvB,SAAS;AAAA,QACT,WAAW;AAAA,QACX,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,cAAc;AAAA,QACd,IAAI;AAAA,QACJ,OAAO;AAAA,MACT;AACA,YAAM,SAAS;AAAA,QACb,IAAI,OAAO,QAAQ,OAAO;AAAA,QAC1B,IAAI,OAAO,QAAQ,SAAS;AAAA,QAC5B,IAAI,OAAO,QAAQ,SAAS;AAAA,QAC5B,IAAI,OAAO,QAAQ,MAAM;AAAA,QACzB,IAAI,OAAO,QAAQ,OAAO;AAAA,QAC1B,IAAI,OAAO,QAAQ,EAAE;AAAA,QACrB,IAAI,OAAO,QAAQ,EAAE;AAAA,QACrB,IAAI,OAAO,QAAQ,OAAO;AAAA,QAC1B,IAAI,OAAO,QAAQ,MAAM;AAAA,QACzB,IAAI,OAAO,QAAQ,MAAM;AAAA,QACzB,IAAI,OAAO,QAAQ,QAAQ;AAAA,QAC3B,IAAI,OAAO,QAAQ,YAAY;AAAA,QAC/B,IAAI,OAAO,GAAG;AAAA,MAChB;AACA,YAAM,iBAAiB;AACvB,YAAM,gBAAgB,IAAI,OAAO,MAAM,QAAQ,cAAc,GAAG;AAChE,YAAM,eAAe;AAErB,YAAM,OAAO;AACb,YAAM,QAAQ,iBAAiB;AAC/B,YAAM,UAAU,UAAQ;AACtB,YAAI,OAAO;AACX,cAAM,aAAa,KAAK;AACxB,iBAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,gBAAM,MAAM,KAAK,CAAC;AAClB,cAAI,OAAO,IAAI,KAAK,IAAI,GAAG;AACzB,mBAAO;AACP;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,UAAU,UAAQ;AACtB,cAAM,QAAQ,CAAC;AACf,eAAO,UAAQ;AACb,cAAI,MAAM,IAAI,GAAG;AACf,mBAAO,MAAM,IAAI;AAAA,UACnB,OAAO;AACL,kBAAM,SAAS,KAAK,IAAI;AACxB,kBAAM,IAAI,IAAI;AACd,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW,gBAAc;AAC7B,cAAM,WAAW,QAAQ,OAAO;AAChC,eAAO,IAAI,YAAY,QAAQ;AAAA,MACjC;AAEA,YAAM,iBAAiB,CAACA,MAAK,UAAU;AACrC,cAAM,OAAOA,KAAI,KAAK;AACtB,cAAM,WAAWA,KAAI,QAAQ,CAAC;AAC9B,YAAI,QAAQ,KAAK,QAAQA,KAAI,SAAS,KAAK,UAAU,GAAG;AACtD,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,iBAAiB,WAAW,aAAa,iBAAiB,SAAS;AAC9E,iBAAO;AAAA,QACT;AACA,cAAM,eAAeA,KAAI,QAAQ,CAAC;AAClC,YAAI,SAAS,iBAAiB,YAAY,aAAa,iBAAiB,aAAa,aAAa,iBAAiB,aAAa,aAAa,iBAAiB,OAAO,iBAAiB,iBAAiB,SAAS;AAC9M,iBAAO;AAAA,QACT;AACA,cAAM,WAAWA,KAAI,QAAQ,CAAC;AAC9B,aAAK,SAAS,iBAAiB,aAAa,SAAS,iBAAiB,aAAa,aAAa,iBAAiB,OAAO,aAAa,iBAAiB,WAAW,aAAa,iBAAiB,SAAS;AACtM,iBAAO;AAAA,QACT;AACA,aAAK,SAAS,iBAAiB,WAAW,SAAS,iBAAiB,aAAa,aAAa,iBAAiB,WAAW,aAAa,iBAAiB,UAAU;AAChK,iBAAO;AAAA,QACT;AACA,aAAK,SAAS,iBAAiB,UAAU,SAAS,iBAAiB,cAAc,aAAa,iBAAiB,WAAW,aAAa,iBAAiB,SAAS;AAC/J,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,iBAAiB,YAAY,aAAa,iBAAiB,UAAU,aAAa,iBAAiB,cAAc,iBAAiB,iBAAiB,SAAS;AACvK,iBAAO;AAAA,QACT;AACA,aAAK,SAAS,iBAAiB,UAAU,SAAS,iBAAiB,YAAY,aAAa,iBAAiB,WAAW,aAAa,iBAAiB,WAAW,aAAa,iBAAiB,YAAY,aAAa,iBAAiB,UAAU,aAAa,iBAAiB,YAAY,aAAa,iBAAiB,UAAU,aAAa,iBAAiB,WAAW,iBAAiB,iBAAiB,WAAW,iBAAiB,iBAAiB,WAAW,iBAAiB,iBAAiB,YAAY,iBAAiB,iBAAiB,UAAU,iBAAiB,iBAAiB,aAAa,SAAS,iBAAiB,WAAW,SAAS,iBAAiB,WAAW,SAAS,iBAAiB,YAAY,SAAS,iBAAiB,UAAU,SAAS,iBAAiB,SAAS;AACtwB,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,iBAAiB,MAAM,aAAa,iBAAiB,IAAI;AACpE,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,iBAAiB,WAAW,SAAS,iBAAiB,MAAM,SAAS,iBAAiB,IAAI;AACrG,iBAAO;AAAA,QACT;AACA,YAAI,aAAa,iBAAiB,WAAW,aAAa,iBAAiB,MAAM,aAAa,iBAAiB,IAAI;AACjH,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,iBAAiB,YAAY,aAAa,iBAAiB,UAAU;AAChF,iBAAO;AAAA,QACT;AACA,YAAI,aAAa,iBAAiB,iBAAiB,SAAS,iBAAiB,WAAW,SAAS,iBAAiB,WAAW,SAAS,iBAAiB,YAAY,SAAS,iBAAiB,eAAe;AAC1M,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,iBAAiB,iBAAiB,aAAa,iBAAiB,WAAW,aAAa,iBAAiB,WAAW,aAAa,iBAAiB,WAAW;AACxK,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,iBAAiB,IAAI;AAChC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAEA,YAAM,eAAe;AACrB,YAAM,aAAa;AACnB,YAAM,cAAc;AACpB,YAAM,aAAa,SAAO,QAAQ,UAAU,QAAQ;AACpD,YAAM,cAAc,CAAC,YAAY,eAAe;AAC9C,YAAI;AACJ,aAAK,IAAI,YAAY,IAAI,WAAW,QAAQ,KAAK;AAC/C,cAAI,WAAW,KAAK,WAAW,CAAC,CAAC,GAAG;AAClC;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,aAAa,CAAC,YAAY,eAAe;AAC7C,cAAM,WAAW,YAAY,YAAY,aAAa,CAAC;AACvD,cAAM,aAAa,WAAW,MAAM,aAAa,GAAG,QAAQ,EAAE,KAAK,YAAY;AAC/E,eAAO,WAAW,OAAO,GAAG,CAAC,MAAM,QAAQ,WAAW;AAAA,MACxD;AACA,YAAM,uBAAuB,CAAC,OAAO,QAAQ,cAAc,YAAY;AACrE,cAAM,QAAQ,CAAC;AACf,cAAM,UAAU,CAAC;AACjB,YAAI,OAAO,CAAC;AACZ,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAC5C,eAAK,KAAK,MAAM,CAAC,CAAC;AAClB,cAAI,eAAe,cAAc,CAAC,GAAG;AACnC,kBAAM,KAAK,OAAO,CAAC;AACnB,iBAAK,QAAQ,qBAAqB,CAAC,WAAW,KAAK,EAAE,OAAO,QAAQ,sBAAsB,CAAC,YAAY,KAAK,EAAE,IAAI;AAChH,oBAAM,cAAc,IAAI,KAAK,SAAS;AACtC,oBAAM,YAAY,IAAI;AACtB,oBAAM,MAAM,OAAO,MAAM,aAAa,SAAS,EAAE,KAAK,YAAY;AAClE,kBAAI,WAAW,GAAG,GAAG;AACnB,sBAAM,WAAW,WAAW,QAAQ,CAAC;AACrC,sBAAM,MAAM,MAAM,MAAM,WAAW,QAAQ;AAC3C,sBAAM,UAAU,KAAK,MAAM,MAAM,GAAG;AACpC,oBAAI;AAAA,cACN;AACA,oBAAM,KAAK,IAAI;AACf,sBAAQ,KAAK;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,cACP,CAAC;AAAA,YACH;AACA,mBAAO,CAAC;AAAA,UACV;AAAA,QACF;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,oBAAoB,OAAO;AAAA,QAC/B,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,MACtB;AACA,YAAM,sBAAsB,CAAC,OAAO,SAAS,YAAY;AACvD,kBAAU;AAAA,UACR,GAAG,kBAAkB;AAAA,UACrB,GAAG;AAAA,QACL;AACA,cAAM,iBAAiB,IAAI,OAAO,OAAO;AACzC,cAAM,eAAe,SAAS,cAAc;AAC5C,eAAO,qBAAqB,OAAO,gBAAgB,cAAc,OAAO;AAAA,MAC1E;AACA,YAAM,aAAa,CAAC,OAAO,SAAS,YAAY,oBAAoB,OAAO,SAAS,OAAO,EAAE;AAE7F,YAAM,WAAW;AAEjB,YAAM,eAAe,OAAK,EAAE,QAAQ,WAAW,EAAE;AAEjD,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,wBAAwB;AAElE,YAAM,UAAU,CAAC,MAAM,WAAW;AAChC,cAAM,gBAAgB,OAAO,iBAAiB;AAC9C,cAAM,eAAe,OAAO,gBAAgB;AAC5C,cAAM,YAAY,CAAAC,UAAQ,cAAcA,MAAK,QAAQ,KAAK,aAAaA,MAAK,QAAQ;AACpF,cAAM,aAAa,CAAC;AACpB,YAAI,MAAM;AACV,cAAM,aAAa,IAAI,SAAS,MAAM,IAAI;AAC1C,YAAI;AACJ,eAAO,WAAW,WAAW,KAAK,GAAG;AACnC,cAAI,SAAS,aAAa,GAAG;AAC3B,mBAAO,aAAa,SAAS,IAAI;AAAA,UACnC,WAAW,UAAU,QAAQ,KAAK,IAAI,QAAQ;AAC5C,uBAAW,KAAK,GAAG;AACnB,kBAAM;AAAA,UACR;AAAA,QACF;AACA,YAAI,IAAI,QAAQ;AACd,qBAAW,KAAK,GAAG;AAAA,QACrB;AACA,eAAO;AAAA,MACT;AAEA,YAAM,aAAa,UAAQ,KAAK,QAAQ,WAAW,EAAE;AACrD,YAAM,SAAS,SAAO,IAAI,QAAQ,mCAAmC,GAAG,EAAE;AAC1E,YAAM,aAAa,CAAC,MAAM,WAAW;AACnC,cAAM,OAAO,WAAW,QAAQ,MAAM,MAAM,EAAE,KAAK,IAAI,CAAC;AACxD,eAAO,SAAS,KAAK,MAAM,EAAE,GAAG,QAAQ,EAAE;AAAA,MAC5C;AACA,YAAM,kBAAkB,CAAC,MAAM,WAAW;AACxC,cAAM,OAAO,QAAQ,MAAM,MAAM,EAAE,KAAK,EAAE;AAC1C,eAAO,OAAO,IAAI;AAAA,MACpB;AACA,YAAM,+BAA+B,CAAC,MAAM,WAAW;AACrD,cAAM,OAAO,QAAQ,MAAM,MAAM,EAAE,KAAK,EAAE,EAAE,QAAQ,OAAO,EAAE;AAC7D,eAAO,OAAO,IAAI;AAAA,MACpB;AAEA,YAAM,oBAAoB,CAAC,QAAQ,UAAU,MAAM,MAAM,OAAO,QAAQ,GAAG,OAAO,MAAM;AACxF,YAAM,yBAAyB,CAAC,QAAQ,UAAU,MAAM,MAAM,OAAO,UAAU,OAAO,EAAE,cAAc,GAAG,OAAO,MAAM;AACtH,YAAM,wBAAwB,YAAU,kBAAkB,QAAQ,UAAU;AAC5E,YAAM,MAAM,aAAW;AAAA,QACrB,MAAM;AAAA,UACJ,cAAc,sBAAsB,MAAM;AAAA,UAC1C,mBAAmB,kBAAkB,QAAQ,eAAe;AAAA,UAC5D,gCAAgC,kBAAkB,QAAQ,4BAA4B;AAAA,QACxF;AAAA,QACA,WAAW;AAAA,UACT,cAAc,uBAAuB,QAAQ,UAAU;AAAA,UACvD,mBAAmB,uBAAuB,QAAQ,eAAe;AAAA,UACjE,gCAAgC,uBAAuB,QAAQ,4BAA4B;AAAA,QAC7F;AAAA,QACA,UAAU,sBAAsB,MAAM;AAAA,MACxC;AAEA,YAAM,OAAO,CAAC,QAAQ,QAAQ;AAC5B,eAAO,cAAc,KAAK;AAAA,UACxB,OAAO;AAAA,UACP,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO,CAAC;AAAA,cACJ,MAAM;AAAA,cACN,QAAQ;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA;AAAA,cACF;AAAA,cACA,OAAO;AAAA,gBACL;AAAA,kBACE;AAAA,kBACA,OAAO,IAAI,KAAK,aAAa,CAAC;AAAA,kBAC9B,OAAO,IAAI,UAAU,aAAa,CAAC;AAAA,gBACrC;AAAA,gBACA;AAAA,kBACE;AAAA,kBACA,OAAO,IAAI,KAAK,+BAA+B,CAAC;AAAA,kBAChD,OAAO,IAAI,UAAU,+BAA+B,CAAC;AAAA,gBACvD;AAAA,gBACA;AAAA,kBACE;AAAA,kBACA,OAAO,IAAI,KAAK,kBAAkB,CAAC;AAAA,kBACnC,OAAO,IAAI,UAAU,kBAAkB,CAAC;AAAA,gBAC1C;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACL;AAAA,UACA,SAAS,CAAC;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACX,CAAC;AAAA,QACL,CAAC;AAAA,MACH;AAEA,YAAM,aAAa,CAAC,QAAQ,QAAQ;AAClC,eAAO,WAAW,gBAAgB,MAAM,KAAK,QAAQ,GAAG,CAAC;AAAA,MAC3D;AAEA,YAAM,QAAQ,CAAC,IAAI,SAAS;AAC1B,YAAI,QAAQ;AACZ,cAAM,SAAS,MAAM;AACnB,cAAI,CAAC,OAAO,KAAK,GAAG;AAClB,yBAAa,KAAK;AAClB,oBAAQ;AAAA,UACV;AAAA,QACF;AACA,cAAM,WAAW,IAAI,SAAS;AAC5B,cAAI,OAAO,KAAK,GAAG;AACjB,oBAAQ,WAAW,MAAM;AACvB,sBAAQ;AACR,iBAAG,MAAM,MAAM,IAAI;AAAA,YACrB,GAAG,IAAI;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE5D,YAAM,sBAAsB,CAAC,QAAQ,QAAQ;AAC3C,eAAO,SAAS,mBAAmB;AAAA,UACjC,WAAW;AAAA,YACT,OAAO,IAAI,KAAK,aAAa;AAAA,YAC7B,YAAY,IAAI,KAAK,kBAAkB;AAAA,YACvC,yBAAyB,IAAI,KAAK,+BAA+B;AAAA,UACnE;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,cAAc,CAAC,QAAQ,QAAQ;AACnC,4BAAoB,QAAQ,GAAG;AAAA,MACjC;AACA,YAAM,QAAQ,CAAC,QAAQ,KAAK,UAAU;AACpC,cAAM,kBAAkB,MAAM,MAAM,YAAY,QAAQ,GAAG,GAAG,KAAK;AACnE,eAAO,GAAG,QAAQ,MAAM;AACtB,sBAAY,QAAQ,GAAG;AACvB,iBAAO,iBAAiB,QAAQ,MAAM;AACpC,mBAAO,GAAG,uDAAuD,gBAAgB,QAAQ;AAAA,UAC3F,GAAG,CAAC;AACJ,iBAAO,GAAG,UAAU,gBAAgB,MAAM;AAAA,QAC5C,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,YAAU;AACzB,cAAM,WAAW,MAAM,OAAO,YAAY,cAAc;AACxD,eAAO,GAAG,SAAS,UAAU,aAAa;AAAA,UACxC,SAAS;AAAA,UACT,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AACD,eAAO,GAAG,SAAS,YAAY,aAAa;AAAA,UAC1C,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,CAAC,QAAQ,QAAQ;AAC5B,iBAAS,IAAI,aAAa,YAAU;AAClC,gBAAM,MAAM,IAAI,MAAM;AACtB,qBAAW,QAAQ,GAAG;AACtB,mBAAS,MAAM;AACf,gBAAM,QAAQ,KAAK,KAAK;AACxB,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;AC9YH;", "names": ["map", "node"]}