{"version": 3, "sources": ["../../.pnpm/codemirror@5.65.19/node_modules/codemirror/mode/css/css.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nCodeMirror.defineMode(\"css\", function(config, parserConfig) {\n  var inline = parserConfig.inline\n  if (!parserConfig.propertyKeywords) parserConfig = CodeMirror.resolveMode(\"text/css\");\n\n  var indentUnit = config.indentUnit,\n      tokenHooks = parserConfig.tokenHooks,\n      documentTypes = parserConfig.documentTypes || {},\n      mediaTypes = parserConfig.mediaTypes || {},\n      mediaFeatures = parserConfig.mediaFeatures || {},\n      mediaValueKeywords = parserConfig.mediaValueKeywords || {},\n      propertyKeywords = parserConfig.propertyKeywords || {},\n      nonStandardPropertyKeywords = parserConfig.nonStandardPropertyKeywords || {},\n      fontProperties = parserConfig.fontProperties || {},\n      counterDescriptors = parserConfig.counterDescriptors || {},\n      colorKeywords = parserConfig.colorKeywords || {},\n      valueKeywords = parserConfig.valueKeywords || {},\n      allowNested = parserConfig.allowNested,\n      lineComment = parserConfig.lineComment,\n      supportsAtComponent = parserConfig.supportsAtComponent === true,\n      highlightNonStandardPropertyKeywords = config.highlightNonStandardPropertyKeywords !== false;\n\n  var type, override;\n  function ret(style, tp) { type = tp; return style; }\n\n  // Tokenizers\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (tokenHooks[ch]) {\n      var result = tokenHooks[ch](stream, state);\n      if (result !== false) return result;\n    }\n    if (ch == \"@\") {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"def\", stream.current());\n    } else if (ch == \"=\" || (ch == \"~\" || ch == \"|\") && stream.eat(\"=\")) {\n      return ret(null, \"compare\");\n    } else if (ch == \"\\\"\" || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    } else if (ch == \"#\") {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"atom\", \"hash\");\n    } else if (ch == \"!\") {\n      stream.match(/^\\s*\\w*/);\n      return ret(\"keyword\", \"important\");\n    } else if (/\\d/.test(ch) || ch == \".\" && stream.eat(/\\d/)) {\n      stream.eatWhile(/[\\w.%]/);\n      return ret(\"number\", \"unit\");\n    } else if (ch === \"-\") {\n      if (/[\\d.]/.test(stream.peek())) {\n        stream.eatWhile(/[\\w.%]/);\n        return ret(\"number\", \"unit\");\n      } else if (stream.match(/^-[\\w\\\\\\-]*/)) {\n        stream.eatWhile(/[\\w\\\\\\-]/);\n        if (stream.match(/^\\s*:/, false))\n          return ret(\"variable-2\", \"variable-definition\");\n        return ret(\"variable-2\", \"variable\");\n      } else if (stream.match(/^\\w+-/)) {\n        return ret(\"meta\", \"meta\");\n      }\n    } else if (/[,+>*\\/]/.test(ch)) {\n      return ret(null, \"select-op\");\n    } else if (ch == \".\" && stream.match(/^-?[_a-z][_a-z0-9-]*/i)) {\n      return ret(\"qualifier\", \"qualifier\");\n    } else if (/[:;{}\\[\\]\\(\\)]/.test(ch)) {\n      return ret(null, ch);\n    } else if (stream.match(/^[\\w-.]+(?=\\()/)) {\n      if (/^(url(-prefix)?|domain|regexp)$/i.test(stream.current())) {\n        state.tokenize = tokenParenthesized;\n      }\n      return ret(\"variable callee\", \"variable\");\n    } else if (/[\\w\\\\\\-]/.test(ch)) {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"property\", \"word\");\n    } else {\n      return ret(null, null);\n    }\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == quote && !escaped) {\n          if (quote == \")\") stream.backUp(1);\n          break;\n        }\n        escaped = !escaped && ch == \"\\\\\";\n      }\n      if (ch == quote || !escaped && quote != \")\") state.tokenize = null;\n      return ret(\"string\", \"string\");\n    };\n  }\n\n  function tokenParenthesized(stream, state) {\n    stream.next(); // Must be '('\n    if (!stream.match(/^\\s*[\\\"\\')]/, false))\n      state.tokenize = tokenString(\")\");\n    else\n      state.tokenize = null;\n    return ret(null, \"(\");\n  }\n\n  // Context management\n\n  function Context(type, indent, prev) {\n    this.type = type;\n    this.indent = indent;\n    this.prev = prev;\n  }\n\n  function pushContext(state, stream, type, indent) {\n    state.context = new Context(type, stream.indentation() + (indent === false ? 0 : indentUnit), state.context);\n    return type;\n  }\n\n  function popContext(state) {\n    if (state.context.prev)\n      state.context = state.context.prev;\n    return state.context.type;\n  }\n\n  function pass(type, stream, state) {\n    return states[state.context.type](type, stream, state);\n  }\n  function popAndPass(type, stream, state, n) {\n    for (var i = n || 1; i > 0; i--)\n      state.context = state.context.prev;\n    return pass(type, stream, state);\n  }\n\n  // Parser\n\n  function wordAsValue(stream) {\n    var word = stream.current().toLowerCase();\n    if (valueKeywords.hasOwnProperty(word))\n      override = \"atom\";\n    else if (colorKeywords.hasOwnProperty(word))\n      override = \"keyword\";\n    else\n      override = \"variable\";\n  }\n\n  var states = {};\n\n  states.top = function(type, stream, state) {\n    if (type == \"{\") {\n      return pushContext(state, stream, \"block\");\n    } else if (type == \"}\" && state.context.prev) {\n      return popContext(state);\n    } else if (supportsAtComponent && /@component/i.test(type)) {\n      return pushContext(state, stream, \"atComponentBlock\");\n    } else if (/^@(-moz-)?document$/i.test(type)) {\n      return pushContext(state, stream, \"documentTypes\");\n    } else if (/^@(media|supports|(-moz-)?document|import)$/i.test(type)) {\n      return pushContext(state, stream, \"atBlock\");\n    } else if (/^@(font-face|counter-style)/i.test(type)) {\n      state.stateArg = type;\n      return \"restricted_atBlock_before\";\n    } else if (/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(type)) {\n      return \"keyframes\";\n    } else if (type && type.charAt(0) == \"@\") {\n      return pushContext(state, stream, \"at\");\n    } else if (type == \"hash\") {\n      override = \"builtin\";\n    } else if (type == \"word\") {\n      override = \"tag\";\n    } else if (type == \"variable-definition\") {\n      return \"maybeprop\";\n    } else if (type == \"interpolation\") {\n      return pushContext(state, stream, \"interpolation\");\n    } else if (type == \":\") {\n      return \"pseudo\";\n    } else if (allowNested && type == \"(\") {\n      return pushContext(state, stream, \"parens\");\n    }\n    return state.context.type;\n  };\n\n  states.block = function(type, stream, state) {\n    if (type == \"word\") {\n      var word = stream.current().toLowerCase();\n      if (propertyKeywords.hasOwnProperty(word)) {\n        override = \"property\";\n        return \"maybeprop\";\n      } else if (nonStandardPropertyKeywords.hasOwnProperty(word)) {\n        override = highlightNonStandardPropertyKeywords ? \"string-2\" : \"property\";\n        return \"maybeprop\";\n      } else if (allowNested) {\n        override = stream.match(/^\\s*:(?:\\s|$)/, false) ? \"property\" : \"tag\";\n        return \"block\";\n      } else {\n        override += \" error\";\n        return \"maybeprop\";\n      }\n    } else if (type == \"meta\") {\n      return \"block\";\n    } else if (!allowNested && (type == \"hash\" || type == \"qualifier\")) {\n      override = \"error\";\n      return \"block\";\n    } else {\n      return states.top(type, stream, state);\n    }\n  };\n\n  states.maybeprop = function(type, stream, state) {\n    if (type == \":\") return pushContext(state, stream, \"prop\");\n    return pass(type, stream, state);\n  };\n\n  states.prop = function(type, stream, state) {\n    if (type == \";\") return popContext(state);\n    if (type == \"{\" && allowNested) return pushContext(state, stream, \"propBlock\");\n    if (type == \"}\" || type == \"{\") return popAndPass(type, stream, state);\n    if (type == \"(\") return pushContext(state, stream, \"parens\");\n\n    if (type == \"hash\" && !/^#([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(stream.current())) {\n      override += \" error\";\n    } else if (type == \"word\") {\n      wordAsValue(stream);\n    } else if (type == \"interpolation\") {\n      return pushContext(state, stream, \"interpolation\");\n    }\n    return \"prop\";\n  };\n\n  states.propBlock = function(type, _stream, state) {\n    if (type == \"}\") return popContext(state);\n    if (type == \"word\") { override = \"property\"; return \"maybeprop\"; }\n    return state.context.type;\n  };\n\n  states.parens = function(type, stream, state) {\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state);\n    if (type == \")\") return popContext(state);\n    if (type == \"(\") return pushContext(state, stream, \"parens\");\n    if (type == \"interpolation\") return pushContext(state, stream, \"interpolation\");\n    if (type == \"word\") wordAsValue(stream);\n    return \"parens\";\n  };\n\n  states.pseudo = function(type, stream, state) {\n    if (type == \"meta\") return \"pseudo\";\n\n    if (type == \"word\") {\n      override = \"variable-3\";\n      return state.context.type;\n    }\n    return pass(type, stream, state);\n  };\n\n  states.documentTypes = function(type, stream, state) {\n    if (type == \"word\" && documentTypes.hasOwnProperty(stream.current())) {\n      override = \"tag\";\n      return state.context.type;\n    } else {\n      return states.atBlock(type, stream, state);\n    }\n  };\n\n  states.atBlock = function(type, stream, state) {\n    if (type == \"(\") return pushContext(state, stream, \"atBlock_parens\");\n    if (type == \"}\" || type == \";\") return popAndPass(type, stream, state);\n    if (type == \"{\") return popContext(state) && pushContext(state, stream, allowNested ? \"block\" : \"top\");\n\n    if (type == \"interpolation\") return pushContext(state, stream, \"interpolation\");\n\n    if (type == \"word\") {\n      var word = stream.current().toLowerCase();\n      if (word == \"only\" || word == \"not\" || word == \"and\" || word == \"or\")\n        override = \"keyword\";\n      else if (mediaTypes.hasOwnProperty(word))\n        override = \"attribute\";\n      else if (mediaFeatures.hasOwnProperty(word))\n        override = \"property\";\n      else if (mediaValueKeywords.hasOwnProperty(word))\n        override = \"keyword\";\n      else if (propertyKeywords.hasOwnProperty(word))\n        override = \"property\";\n      else if (nonStandardPropertyKeywords.hasOwnProperty(word))\n        override = highlightNonStandardPropertyKeywords ? \"string-2\" : \"property\";\n      else if (valueKeywords.hasOwnProperty(word))\n        override = \"atom\";\n      else if (colorKeywords.hasOwnProperty(word))\n        override = \"keyword\";\n      else\n        override = \"error\";\n    }\n    return state.context.type;\n  };\n\n  states.atComponentBlock = function(type, stream, state) {\n    if (type == \"}\")\n      return popAndPass(type, stream, state);\n    if (type == \"{\")\n      return popContext(state) && pushContext(state, stream, allowNested ? \"block\" : \"top\", false);\n    if (type == \"word\")\n      override = \"error\";\n    return state.context.type;\n  };\n\n  states.atBlock_parens = function(type, stream, state) {\n    if (type == \")\") return popContext(state);\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state, 2);\n    return states.atBlock(type, stream, state);\n  };\n\n  states.restricted_atBlock_before = function(type, stream, state) {\n    if (type == \"{\")\n      return pushContext(state, stream, \"restricted_atBlock\");\n    if (type == \"word\" && state.stateArg == \"@counter-style\") {\n      override = \"variable\";\n      return \"restricted_atBlock_before\";\n    }\n    return pass(type, stream, state);\n  };\n\n  states.restricted_atBlock = function(type, stream, state) {\n    if (type == \"}\") {\n      state.stateArg = null;\n      return popContext(state);\n    }\n    if (type == \"word\") {\n      if ((state.stateArg == \"@font-face\" && !fontProperties.hasOwnProperty(stream.current().toLowerCase())) ||\n          (state.stateArg == \"@counter-style\" && !counterDescriptors.hasOwnProperty(stream.current().toLowerCase())))\n        override = \"error\";\n      else\n        override = \"property\";\n      return \"maybeprop\";\n    }\n    return \"restricted_atBlock\";\n  };\n\n  states.keyframes = function(type, stream, state) {\n    if (type == \"word\") { override = \"variable\"; return \"keyframes\"; }\n    if (type == \"{\") return pushContext(state, stream, \"top\");\n    return pass(type, stream, state);\n  };\n\n  states.at = function(type, stream, state) {\n    if (type == \";\") return popContext(state);\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state);\n    if (type == \"word\") override = \"tag\";\n    else if (type == \"hash\") override = \"builtin\";\n    return \"at\";\n  };\n\n  states.interpolation = function(type, stream, state) {\n    if (type == \"}\") return popContext(state);\n    if (type == \"{\" || type == \";\") return popAndPass(type, stream, state);\n    if (type == \"word\") override = \"variable\";\n    else if (type != \"variable\" && type != \"(\" && type != \")\") override = \"error\";\n    return \"interpolation\";\n  };\n\n  return {\n    startState: function(base) {\n      return {tokenize: null,\n              state: inline ? \"block\" : \"top\",\n              stateArg: null,\n              context: new Context(inline ? \"block\" : \"top\", base || 0, null)};\n    },\n\n    token: function(stream, state) {\n      if (!state.tokenize && stream.eatSpace()) return null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style && typeof style == \"object\") {\n        type = style[1];\n        style = style[0];\n      }\n      override = style;\n      if (type != \"comment\")\n        state.state = states[state.state](type, stream, state);\n      return override;\n    },\n\n    indent: function(state, textAfter) {\n      var cx = state.context, ch = textAfter && textAfter.charAt(0);\n      var indent = cx.indent;\n      if (cx.type == \"prop\" && (ch == \"}\" || ch == \")\")) cx = cx.prev;\n      if (cx.prev) {\n        if (ch == \"}\" && (cx.type == \"block\" || cx.type == \"top\" ||\n                          cx.type == \"interpolation\" || cx.type == \"restricted_atBlock\")) {\n          // Resume indentation from parent context.\n          cx = cx.prev;\n          indent = cx.indent;\n        } else if (ch == \")\" && (cx.type == \"parens\" || cx.type == \"atBlock_parens\") ||\n            ch == \"{\" && (cx.type == \"at\" || cx.type == \"atBlock\")) {\n          // Dedent relative to current context.\n          indent = Math.max(0, cx.indent - indentUnit);\n        }\n      }\n      return indent;\n    },\n\n    electricChars: \"}\",\n    blockCommentStart: \"/*\",\n    blockCommentEnd: \"*/\",\n    blockCommentContinue: \" * \",\n    lineComment: lineComment,\n    fold: \"brace\"\n  };\n});\n\n  function keySet(array) {\n    var keys = {};\n    for (var i = 0; i < array.length; ++i) {\n      keys[array[i].toLowerCase()] = true;\n    }\n    return keys;\n  }\n\n  var documentTypes_ = [\n    \"domain\", \"regexp\", \"url\", \"url-prefix\"\n  ], documentTypes = keySet(documentTypes_);\n\n  var mediaTypes_ = [\n    \"all\", \"aural\", \"braille\", \"handheld\", \"print\", \"projection\", \"screen\",\n    \"tty\", \"tv\", \"embossed\"\n  ], mediaTypes = keySet(mediaTypes_);\n\n  var mediaFeatures_ = [\n    \"width\", \"min-width\", \"max-width\", \"height\", \"min-height\", \"max-height\",\n    \"device-width\", \"min-device-width\", \"max-device-width\", \"device-height\",\n    \"min-device-height\", \"max-device-height\", \"aspect-ratio\",\n    \"min-aspect-ratio\", \"max-aspect-ratio\", \"device-aspect-ratio\",\n    \"min-device-aspect-ratio\", \"max-device-aspect-ratio\", \"color\", \"min-color\",\n    \"max-color\", \"color-index\", \"min-color-index\", \"max-color-index\",\n    \"monochrome\", \"min-monochrome\", \"max-monochrome\", \"resolution\",\n    \"min-resolution\", \"max-resolution\", \"scan\", \"grid\", \"orientation\",\n    \"device-pixel-ratio\", \"min-device-pixel-ratio\", \"max-device-pixel-ratio\",\n    \"pointer\", \"any-pointer\", \"hover\", \"any-hover\", \"prefers-color-scheme\",\n    \"dynamic-range\", \"video-dynamic-range\"\n  ], mediaFeatures = keySet(mediaFeatures_);\n\n  var mediaValueKeywords_ = [\n    \"landscape\", \"portrait\", \"none\", \"coarse\", \"fine\", \"on-demand\", \"hover\",\n    \"interlace\", \"progressive\",\n    \"dark\", \"light\",\n    \"standard\", \"high\"\n  ], mediaValueKeywords = keySet(mediaValueKeywords_);\n\n  var propertyKeywords_ = [\n    \"align-content\", \"align-items\", \"align-self\", \"alignment-adjust\",\n    \"alignment-baseline\", \"all\", \"anchor-point\", \"animation\", \"animation-delay\",\n    \"animation-direction\", \"animation-duration\", \"animation-fill-mode\",\n    \"animation-iteration-count\", \"animation-name\", \"animation-play-state\",\n    \"animation-timing-function\", \"appearance\", \"azimuth\", \"backdrop-filter\",\n    \"backface-visibility\", \"background\", \"background-attachment\",\n    \"background-blend-mode\", \"background-clip\", \"background-color\",\n    \"background-image\", \"background-origin\", \"background-position\",\n    \"background-position-x\", \"background-position-y\", \"background-repeat\",\n    \"background-size\", \"baseline-shift\", \"binding\", \"bleed\", \"block-size\",\n    \"bookmark-label\", \"bookmark-level\", \"bookmark-state\", \"bookmark-target\",\n    \"border\", \"border-bottom\", \"border-bottom-color\", \"border-bottom-left-radius\",\n    \"border-bottom-right-radius\", \"border-bottom-style\", \"border-bottom-width\",\n    \"border-collapse\", \"border-color\", \"border-image\", \"border-image-outset\",\n    \"border-image-repeat\", \"border-image-slice\", \"border-image-source\",\n    \"border-image-width\", \"border-left\", \"border-left-color\", \"border-left-style\",\n    \"border-left-width\", \"border-radius\", \"border-right\", \"border-right-color\",\n    \"border-right-style\", \"border-right-width\", \"border-spacing\", \"border-style\",\n    \"border-top\", \"border-top-color\", \"border-top-left-radius\",\n    \"border-top-right-radius\", \"border-top-style\", \"border-top-width\",\n    \"border-width\", \"bottom\", \"box-decoration-break\", \"box-shadow\", \"box-sizing\",\n    \"break-after\", \"break-before\", \"break-inside\", \"caption-side\", \"caret-color\",\n    \"clear\", \"clip\", \"color\", \"color-profile\", \"column-count\", \"column-fill\",\n    \"column-gap\", \"column-rule\", \"column-rule-color\", \"column-rule-style\",\n    \"column-rule-width\", \"column-span\", \"column-width\", \"columns\", \"contain\",\n    \"content\", \"counter-increment\", \"counter-reset\", \"crop\", \"cue\", \"cue-after\",\n    \"cue-before\", \"cursor\", \"direction\", \"display\", \"dominant-baseline\",\n    \"drop-initial-after-adjust\", \"drop-initial-after-align\",\n    \"drop-initial-before-adjust\", \"drop-initial-before-align\", \"drop-initial-size\",\n    \"drop-initial-value\", \"elevation\", \"empty-cells\", \"fit\", \"fit-content\", \"fit-position\",\n    \"flex\", \"flex-basis\", \"flex-direction\", \"flex-flow\", \"flex-grow\",\n    \"flex-shrink\", \"flex-wrap\", \"float\", \"float-offset\", \"flow-from\", \"flow-into\",\n    \"font\", \"font-family\", \"font-feature-settings\", \"font-kerning\",\n    \"font-language-override\", \"font-optical-sizing\", \"font-size\",\n    \"font-size-adjust\", \"font-stretch\", \"font-style\", \"font-synthesis\",\n    \"font-variant\", \"font-variant-alternates\", \"font-variant-caps\",\n    \"font-variant-east-asian\", \"font-variant-ligatures\", \"font-variant-numeric\",\n    \"font-variant-position\", \"font-variation-settings\", \"font-weight\", \"gap\",\n    \"grid\", \"grid-area\", \"grid-auto-columns\", \"grid-auto-flow\", \"grid-auto-rows\",\n    \"grid-column\", \"grid-column-end\", \"grid-column-gap\", \"grid-column-start\",\n    \"grid-gap\", \"grid-row\", \"grid-row-end\", \"grid-row-gap\", \"grid-row-start\",\n    \"grid-template\", \"grid-template-areas\", \"grid-template-columns\",\n    \"grid-template-rows\", \"hanging-punctuation\", \"height\", \"hyphens\", \"icon\",\n    \"image-orientation\", \"image-rendering\", \"image-resolution\", \"inline-box-align\",\n    \"inset\", \"inset-block\", \"inset-block-end\", \"inset-block-start\", \"inset-inline\",\n    \"inset-inline-end\", \"inset-inline-start\", \"isolation\", \"justify-content\",\n    \"justify-items\", \"justify-self\", \"left\", \"letter-spacing\", \"line-break\",\n    \"line-height\", \"line-height-step\", \"line-stacking\", \"line-stacking-ruby\",\n    \"line-stacking-shift\", \"line-stacking-strategy\", \"list-style\",\n    \"list-style-image\", \"list-style-position\", \"list-style-type\", \"margin\",\n    \"margin-bottom\", \"margin-left\", \"margin-right\", \"margin-top\", \"marks\",\n    \"marquee-direction\", \"marquee-loop\", \"marquee-play-count\", \"marquee-speed\",\n    \"marquee-style\", \"mask-clip\", \"mask-composite\", \"mask-image\", \"mask-mode\",\n    \"mask-origin\", \"mask-position\", \"mask-repeat\", \"mask-size\",\"mask-type\",\n    \"max-block-size\", \"max-height\", \"max-inline-size\",\n    \"max-width\", \"min-block-size\", \"min-height\", \"min-inline-size\", \"min-width\",\n    \"mix-blend-mode\", \"move-to\", \"nav-down\", \"nav-index\", \"nav-left\", \"nav-right\",\n    \"nav-up\", \"object-fit\", \"object-position\", \"offset\", \"offset-anchor\",\n    \"offset-distance\", \"offset-path\", \"offset-position\", \"offset-rotate\",\n    \"opacity\", \"order\", \"orphans\", \"outline\", \"outline-color\", \"outline-offset\",\n    \"outline-style\", \"outline-width\", \"overflow\", \"overflow-style\",\n    \"overflow-wrap\", \"overflow-x\", \"overflow-y\", \"padding\", \"padding-bottom\",\n    \"padding-left\", \"padding-right\", \"padding-top\", \"page\", \"page-break-after\",\n    \"page-break-before\", \"page-break-inside\", \"page-policy\", \"pause\",\n    \"pause-after\", \"pause-before\", \"perspective\", \"perspective-origin\", \"pitch\",\n    \"pitch-range\", \"place-content\", \"place-items\", \"place-self\", \"play-during\",\n    \"position\", \"presentation-level\", \"punctuation-trim\", \"quotes\",\n    \"region-break-after\", \"region-break-before\", \"region-break-inside\",\n    \"region-fragment\", \"rendering-intent\", \"resize\", \"rest\", \"rest-after\",\n    \"rest-before\", \"richness\", \"right\", \"rotate\", \"rotation\", \"rotation-point\",\n    \"row-gap\", \"ruby-align\", \"ruby-overhang\", \"ruby-position\", \"ruby-span\",\n    \"scale\", \"scroll-behavior\", \"scroll-margin\", \"scroll-margin-block\",\n    \"scroll-margin-block-end\", \"scroll-margin-block-start\", \"scroll-margin-bottom\",\n    \"scroll-margin-inline\", \"scroll-margin-inline-end\",\n    \"scroll-margin-inline-start\", \"scroll-margin-left\", \"scroll-margin-right\",\n    \"scroll-margin-top\", \"scroll-padding\", \"scroll-padding-block\",\n    \"scroll-padding-block-end\", \"scroll-padding-block-start\",\n    \"scroll-padding-bottom\", \"scroll-padding-inline\", \"scroll-padding-inline-end\",\n    \"scroll-padding-inline-start\", \"scroll-padding-left\", \"scroll-padding-right\",\n    \"scroll-padding-top\", \"scroll-snap-align\", \"scroll-snap-type\",\n    \"shape-image-threshold\", \"shape-inside\", \"shape-margin\", \"shape-outside\",\n    \"size\", \"speak\", \"speak-as\", \"speak-header\", \"speak-numeral\",\n    \"speak-punctuation\", \"speech-rate\", \"stress\", \"string-set\", \"tab-size\",\n    \"table-layout\", \"target\", \"target-name\", \"target-new\", \"target-position\",\n    \"text-align\", \"text-align-last\", \"text-combine-upright\", \"text-decoration\",\n    \"text-decoration-color\", \"text-decoration-line\", \"text-decoration-skip\",\n    \"text-decoration-skip-ink\", \"text-decoration-style\", \"text-emphasis\",\n    \"text-emphasis-color\", \"text-emphasis-position\", \"text-emphasis-style\",\n    \"text-height\", \"text-indent\", \"text-justify\", \"text-orientation\",\n    \"text-outline\", \"text-overflow\", \"text-rendering\", \"text-shadow\",\n    \"text-size-adjust\", \"text-space-collapse\", \"text-transform\",\n    \"text-underline-position\", \"text-wrap\", \"top\", \"touch-action\", \"transform\", \"transform-origin\",\n    \"transform-style\", \"transition\", \"transition-delay\", \"transition-duration\",\n    \"transition-property\", \"transition-timing-function\", \"translate\",\n    \"unicode-bidi\", \"user-select\", \"vertical-align\", \"visibility\", \"voice-balance\",\n    \"voice-duration\", \"voice-family\", \"voice-pitch\", \"voice-range\", \"voice-rate\",\n    \"voice-stress\", \"voice-volume\", \"volume\", \"white-space\", \"widows\", \"width\",\n    \"will-change\", \"word-break\", \"word-spacing\", \"word-wrap\", \"writing-mode\", \"z-index\",\n    // SVG-specific\n    \"clip-path\", \"clip-rule\", \"mask\", \"enable-background\", \"filter\", \"flood-color\",\n    \"flood-opacity\", \"lighting-color\", \"stop-color\", \"stop-opacity\", \"pointer-events\",\n    \"color-interpolation\", \"color-interpolation-filters\",\n    \"color-rendering\", \"fill\", \"fill-opacity\", \"fill-rule\", \"image-rendering\",\n    \"marker\", \"marker-end\", \"marker-mid\", \"marker-start\", \"paint-order\", \"shape-rendering\", \"stroke\",\n    \"stroke-dasharray\", \"stroke-dashoffset\", \"stroke-linecap\", \"stroke-linejoin\",\n    \"stroke-miterlimit\", \"stroke-opacity\", \"stroke-width\", \"text-rendering\",\n    \"baseline-shift\", \"dominant-baseline\", \"glyph-orientation-horizontal\",\n    \"glyph-orientation-vertical\", \"text-anchor\", \"writing-mode\",\n  ], propertyKeywords = keySet(propertyKeywords_);\n\n  var nonStandardPropertyKeywords_ = [\n    \"accent-color\", \"aspect-ratio\", \"border-block\", \"border-block-color\", \"border-block-end\",\n    \"border-block-end-color\", \"border-block-end-style\", \"border-block-end-width\",\n    \"border-block-start\", \"border-block-start-color\", \"border-block-start-style\",\n    \"border-block-start-width\", \"border-block-style\", \"border-block-width\",\n    \"border-inline\", \"border-inline-color\", \"border-inline-end\",\n    \"border-inline-end-color\", \"border-inline-end-style\",\n    \"border-inline-end-width\", \"border-inline-start\", \"border-inline-start-color\",\n    \"border-inline-start-style\", \"border-inline-start-width\",\n    \"border-inline-style\", \"border-inline-width\", \"content-visibility\", \"margin-block\",\n    \"margin-block-end\", \"margin-block-start\", \"margin-inline\", \"margin-inline-end\",\n    \"margin-inline-start\", \"overflow-anchor\", \"overscroll-behavior\", \"padding-block\", \"padding-block-end\",\n    \"padding-block-start\", \"padding-inline\", \"padding-inline-end\",\n    \"padding-inline-start\", \"scroll-snap-stop\", \"scrollbar-3d-light-color\",\n    \"scrollbar-arrow-color\", \"scrollbar-base-color\", \"scrollbar-dark-shadow-color\",\n    \"scrollbar-face-color\", \"scrollbar-highlight-color\", \"scrollbar-shadow-color\",\n    \"scrollbar-track-color\", \"searchfield-cancel-button\", \"searchfield-decoration\",\n    \"searchfield-results-button\", \"searchfield-results-decoration\", \"shape-inside\", \"zoom\"\n  ], nonStandardPropertyKeywords = keySet(nonStandardPropertyKeywords_);\n\n  var fontProperties_ = [\n    \"font-display\", \"font-family\", \"src\", \"unicode-range\", \"font-variant\",\n     \"font-feature-settings\", \"font-stretch\", \"font-weight\", \"font-style\"\n  ], fontProperties = keySet(fontProperties_);\n\n  var counterDescriptors_ = [\n    \"additive-symbols\", \"fallback\", \"negative\", \"pad\", \"prefix\", \"range\",\n    \"speak-as\", \"suffix\", \"symbols\", \"system\"\n  ], counterDescriptors = keySet(counterDescriptors_);\n\n  var colorKeywords_ = [\n    \"aliceblue\", \"antiquewhite\", \"aqua\", \"aquamarine\", \"azure\", \"beige\",\n    \"bisque\", \"black\", \"blanchedalmond\", \"blue\", \"blueviolet\", \"brown\",\n    \"burlywood\", \"cadetblue\", \"chartreuse\", \"chocolate\", \"coral\", \"cornflowerblue\",\n    \"cornsilk\", \"crimson\", \"cyan\", \"darkblue\", \"darkcyan\", \"darkgoldenrod\",\n    \"darkgray\", \"darkgreen\", \"darkgrey\", \"darkkhaki\", \"darkmagenta\", \"darkolivegreen\",\n    \"darkorange\", \"darkorchid\", \"darkred\", \"darksalmon\", \"darkseagreen\",\n    \"darkslateblue\", \"darkslategray\", \"darkslategrey\", \"darkturquoise\", \"darkviolet\",\n    \"deeppink\", \"deepskyblue\", \"dimgray\", \"dimgrey\", \"dodgerblue\", \"firebrick\",\n    \"floralwhite\", \"forestgreen\", \"fuchsia\", \"gainsboro\", \"ghostwhite\",\n    \"gold\", \"goldenrod\", \"gray\", \"grey\", \"green\", \"greenyellow\", \"honeydew\",\n    \"hotpink\", \"indianred\", \"indigo\", \"ivory\", \"khaki\", \"lavender\",\n    \"lavenderblush\", \"lawngreen\", \"lemonchiffon\", \"lightblue\", \"lightcoral\",\n    \"lightcyan\", \"lightgoldenrodyellow\", \"lightgray\", \"lightgreen\", \"lightgrey\", \"lightpink\",\n    \"lightsalmon\", \"lightseagreen\", \"lightskyblue\", \"lightslategray\", \"lightslategrey\",\n    \"lightsteelblue\", \"lightyellow\", \"lime\", \"limegreen\", \"linen\", \"magenta\",\n    \"maroon\", \"mediumaquamarine\", \"mediumblue\", \"mediumorchid\", \"mediumpurple\",\n    \"mediumseagreen\", \"mediumslateblue\", \"mediumspringgreen\", \"mediumturquoise\",\n    \"mediumvioletred\", \"midnightblue\", \"mintcream\", \"mistyrose\", \"moccasin\",\n    \"navajowhite\", \"navy\", \"oldlace\", \"olive\", \"olivedrab\", \"orange\", \"orangered\",\n    \"orchid\", \"palegoldenrod\", \"palegreen\", \"paleturquoise\", \"palevioletred\",\n    \"papayawhip\", \"peachpuff\", \"peru\", \"pink\", \"plum\", \"powderblue\",\n    \"purple\", \"rebeccapurple\", \"red\", \"rosybrown\", \"royalblue\", \"saddlebrown\",\n    \"salmon\", \"sandybrown\", \"seagreen\", \"seashell\", \"sienna\", \"silver\", \"skyblue\",\n    \"slateblue\", \"slategray\", \"slategrey\", \"snow\", \"springgreen\", \"steelblue\", \"tan\",\n    \"teal\", \"thistle\", \"tomato\", \"turquoise\", \"violet\", \"wheat\", \"white\",\n    \"whitesmoke\", \"yellow\", \"yellowgreen\"\n  ], colorKeywords = keySet(colorKeywords_);\n\n  var valueKeywords_ = [\n    \"above\", \"absolute\", \"activeborder\", \"additive\", \"activecaption\", \"afar\",\n    \"after-white-space\", \"ahead\", \"alias\", \"all\", \"all-scroll\", \"alphabetic\", \"alternate\",\n    \"always\", \"amharic\", \"amharic-abegede\", \"antialiased\", \"appworkspace\",\n    \"arabic-indic\", \"armenian\", \"asterisks\", \"attr\", \"auto\", \"auto-flow\", \"avoid\", \"avoid-column\", \"avoid-page\",\n    \"avoid-region\", \"axis-pan\", \"background\", \"backwards\", \"baseline\", \"below\", \"bidi-override\", \"binary\",\n    \"bengali\", \"blink\", \"block\", \"block-axis\", \"blur\", \"bold\", \"bolder\", \"border\", \"border-box\",\n    \"both\", \"bottom\", \"break\", \"break-all\", \"break-word\", \"brightness\", \"bullets\", \"button\",\n    \"buttonface\", \"buttonhighlight\", \"buttonshadow\", \"buttontext\", \"calc\", \"cambodian\",\n    \"capitalize\", \"caps-lock-indicator\", \"caption\", \"captiontext\", \"caret\",\n    \"cell\", \"center\", \"checkbox\", \"circle\", \"cjk-decimal\", \"cjk-earthly-branch\",\n    \"cjk-heavenly-stem\", \"cjk-ideographic\", \"clear\", \"clip\", \"close-quote\",\n    \"col-resize\", \"collapse\", \"color\", \"color-burn\", \"color-dodge\", \"column\", \"column-reverse\",\n    \"compact\", \"condensed\", \"conic-gradient\", \"contain\", \"content\", \"contents\",\n    \"content-box\", \"context-menu\", \"continuous\", \"contrast\", \"copy\", \"counter\", \"counters\", \"cover\", \"crop\",\n    \"cross\", \"crosshair\", \"cubic-bezier\", \"currentcolor\", \"cursive\", \"cyclic\", \"darken\", \"dashed\", \"decimal\",\n    \"decimal-leading-zero\", \"default\", \"default-button\", \"dense\", \"destination-atop\",\n    \"destination-in\", \"destination-out\", \"destination-over\", \"devanagari\", \"difference\",\n    \"disc\", \"discard\", \"disclosure-closed\", \"disclosure-open\", \"document\",\n    \"dot-dash\", \"dot-dot-dash\",\n    \"dotted\", \"double\", \"down\", \"drop-shadow\", \"e-resize\", \"ease\", \"ease-in\", \"ease-in-out\", \"ease-out\",\n    \"element\", \"ellipse\", \"ellipsis\", \"embed\", \"end\", \"ethiopic\", \"ethiopic-abegede\",\n    \"ethiopic-abegede-am-et\", \"ethiopic-abegede-gez\", \"ethiopic-abegede-ti-er\",\n    \"ethiopic-abegede-ti-et\", \"ethiopic-halehame-aa-er\",\n    \"ethiopic-halehame-aa-et\", \"ethiopic-halehame-am-et\",\n    \"ethiopic-halehame-gez\", \"ethiopic-halehame-om-et\",\n    \"ethiopic-halehame-sid-et\", \"ethiopic-halehame-so-et\",\n    \"ethiopic-halehame-ti-er\", \"ethiopic-halehame-ti-et\", \"ethiopic-halehame-tig\",\n    \"ethiopic-numeric\", \"ew-resize\", \"exclusion\", \"expanded\", \"extends\", \"extra-condensed\",\n    \"extra-expanded\", \"fantasy\", \"fast\", \"fill\", \"fill-box\", \"fixed\", \"flat\", \"flex\", \"flex-end\", \"flex-start\", \"footnotes\",\n    \"forwards\", \"from\", \"geometricPrecision\", \"georgian\", \"grayscale\", \"graytext\", \"grid\", \"groove\",\n    \"gujarati\", \"gurmukhi\", \"hand\", \"hangul\", \"hangul-consonant\", \"hard-light\", \"hebrew\",\n    \"help\", \"hidden\", \"hide\", \"higher\", \"highlight\", \"highlighttext\",\n    \"hiragana\", \"hiragana-iroha\", \"horizontal\", \"hsl\", \"hsla\", \"hue\", \"hue-rotate\", \"icon\", \"ignore\",\n    \"inactiveborder\", \"inactivecaption\", \"inactivecaptiontext\", \"infinite\",\n    \"infobackground\", \"infotext\", \"inherit\", \"initial\", \"inline\", \"inline-axis\",\n    \"inline-block\", \"inline-flex\", \"inline-grid\", \"inline-table\", \"inset\", \"inside\", \"intrinsic\", \"invert\",\n    \"italic\", \"japanese-formal\", \"japanese-informal\", \"justify\", \"kannada\",\n    \"katakana\", \"katakana-iroha\", \"keep-all\", \"khmer\",\n    \"korean-hangul-formal\", \"korean-hanja-formal\", \"korean-hanja-informal\",\n    \"landscape\", \"lao\", \"large\", \"larger\", \"left\", \"level\", \"lighter\", \"lighten\",\n    \"line-through\", \"linear\", \"linear-gradient\", \"lines\", \"list-item\", \"listbox\", \"listitem\",\n    \"local\", \"logical\", \"loud\", \"lower\", \"lower-alpha\", \"lower-armenian\",\n    \"lower-greek\", \"lower-hexadecimal\", \"lower-latin\", \"lower-norwegian\",\n    \"lower-roman\", \"lowercase\", \"ltr\", \"luminosity\", \"malayalam\", \"manipulation\", \"match\", \"matrix\", \"matrix3d\",\n    \"media-play-button\", \"media-slider\", \"media-sliderthumb\",\n    \"media-volume-slider\", \"media-volume-sliderthumb\", \"medium\",\n    \"menu\", \"menulist\", \"menulist-button\",\n    \"menutext\", \"message-box\", \"middle\", \"min-intrinsic\",\n    \"mix\", \"mongolian\", \"monospace\", \"move\", \"multiple\", \"multiple_mask_images\", \"multiply\", \"myanmar\", \"n-resize\",\n    \"narrower\", \"ne-resize\", \"nesw-resize\", \"no-close-quote\", \"no-drop\",\n    \"no-open-quote\", \"no-repeat\", \"none\", \"normal\", \"not-allowed\", \"nowrap\",\n    \"ns-resize\", \"numbers\", \"numeric\", \"nw-resize\", \"nwse-resize\", \"oblique\", \"octal\", \"opacity\", \"open-quote\",\n    \"optimizeLegibility\", \"optimizeSpeed\", \"oriya\", \"oromo\", \"outset\",\n    \"outside\", \"outside-shape\", \"overlay\", \"overline\", \"padding\", \"padding-box\",\n    \"painted\", \"page\", \"paused\", \"persian\", \"perspective\", \"pinch-zoom\", \"plus-darker\", \"plus-lighter\",\n    \"pointer\", \"polygon\", \"portrait\", \"pre\", \"pre-line\", \"pre-wrap\", \"preserve-3d\",\n    \"progress\", \"push-button\", \"radial-gradient\", \"radio\", \"read-only\",\n    \"read-write\", \"read-write-plaintext-only\", \"rectangle\", \"region\",\n    \"relative\", \"repeat\", \"repeating-linear-gradient\", \"repeating-radial-gradient\",\n    \"repeating-conic-gradient\", \"repeat-x\", \"repeat-y\", \"reset\", \"reverse\",\n    \"rgb\", \"rgba\", \"ridge\", \"right\", \"rotate\", \"rotate3d\", \"rotateX\", \"rotateY\",\n    \"rotateZ\", \"round\", \"row\", \"row-resize\", \"row-reverse\", \"rtl\", \"run-in\", \"running\",\n    \"s-resize\", \"sans-serif\", \"saturate\", \"saturation\", \"scale\", \"scale3d\", \"scaleX\", \"scaleY\", \"scaleZ\", \"screen\",\n    \"scroll\", \"scrollbar\", \"scroll-position\", \"se-resize\", \"searchfield\",\n    \"searchfield-cancel-button\", \"searchfield-decoration\",\n    \"searchfield-results-button\", \"searchfield-results-decoration\", \"self-start\", \"self-end\",\n    \"semi-condensed\", \"semi-expanded\", \"separate\", \"sepia\", \"serif\", \"show\", \"sidama\",\n    \"simp-chinese-formal\", \"simp-chinese-informal\", \"single\",\n    \"skew\", \"skewX\", \"skewY\", \"skip-white-space\", \"slide\", \"slider-horizontal\",\n    \"slider-vertical\", \"sliderthumb-horizontal\", \"sliderthumb-vertical\", \"slow\",\n    \"small\", \"small-caps\", \"small-caption\", \"smaller\", \"soft-light\", \"solid\", \"somali\",\n    \"source-atop\", \"source-in\", \"source-out\", \"source-over\", \"space\", \"space-around\", \"space-between\", \"space-evenly\", \"spell-out\", \"square\",\n    \"square-button\", \"start\", \"static\", \"status-bar\", \"stretch\", \"stroke\", \"stroke-box\", \"sub\",\n    \"subpixel-antialiased\", \"svg_masks\", \"super\", \"sw-resize\", \"symbolic\", \"symbols\", \"system-ui\", \"table\",\n    \"table-caption\", \"table-cell\", \"table-column\", \"table-column-group\",\n    \"table-footer-group\", \"table-header-group\", \"table-row\", \"table-row-group\",\n    \"tamil\",\n    \"telugu\", \"text\", \"text-bottom\", \"text-top\", \"textarea\", \"textfield\", \"thai\",\n    \"thick\", \"thin\", \"threeddarkshadow\", \"threedface\", \"threedhighlight\",\n    \"threedlightshadow\", \"threedshadow\", \"tibetan\", \"tigre\", \"tigrinya-er\",\n    \"tigrinya-er-abegede\", \"tigrinya-et\", \"tigrinya-et-abegede\", \"to\", \"top\",\n    \"trad-chinese-formal\", \"trad-chinese-informal\", \"transform\",\n    \"translate\", \"translate3d\", \"translateX\", \"translateY\", \"translateZ\",\n    \"transparent\", \"ultra-condensed\", \"ultra-expanded\", \"underline\", \"unidirectional-pan\", \"unset\", \"up\",\n    \"upper-alpha\", \"upper-armenian\", \"upper-greek\", \"upper-hexadecimal\",\n    \"upper-latin\", \"upper-norwegian\", \"upper-roman\", \"uppercase\", \"urdu\", \"url\",\n    \"var\", \"vertical\", \"vertical-text\", \"view-box\", \"visible\", \"visibleFill\", \"visiblePainted\",\n    \"visibleStroke\", \"visual\", \"w-resize\", \"wait\", \"wave\", \"wider\",\n    \"window\", \"windowframe\", \"windowtext\", \"words\", \"wrap\", \"wrap-reverse\", \"x-large\", \"x-small\", \"xor\",\n    \"xx-large\", \"xx-small\"\n  ], valueKeywords = keySet(valueKeywords_);\n\n  var allWords = documentTypes_.concat(mediaTypes_).concat(mediaFeatures_).concat(mediaValueKeywords_)\n    .concat(propertyKeywords_).concat(nonStandardPropertyKeywords_).concat(colorKeywords_)\n    .concat(valueKeywords_);\n  CodeMirror.registerHelper(\"hintWords\", \"css\", allWords);\n\n  function tokenCComment(stream, state) {\n    var maybeEnd = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (maybeEnd && ch == \"/\") {\n        state.tokenize = null;\n        break;\n      }\n      maybeEnd = (ch == \"*\");\n    }\n    return [\"comment\", \"comment\"];\n  }\n\n  CodeMirror.defineMIME(\"text/css\", {\n    documentTypes: documentTypes,\n    mediaTypes: mediaTypes,\n    mediaFeatures: mediaFeatures,\n    mediaValueKeywords: mediaValueKeywords,\n    propertyKeywords: propertyKeywords,\n    nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n    fontProperties: fontProperties,\n    counterDescriptors: counterDescriptors,\n    colorKeywords: colorKeywords,\n    valueKeywords: valueKeywords,\n    tokenHooks: {\n      \"/\": function(stream, state) {\n        if (!stream.eat(\"*\")) return false;\n        state.tokenize = tokenCComment;\n        return tokenCComment(stream, state);\n      }\n    },\n    name: \"css\"\n  });\n\n  CodeMirror.defineMIME(\"text/x-scss\", {\n    mediaTypes: mediaTypes,\n    mediaFeatures: mediaFeatures,\n    mediaValueKeywords: mediaValueKeywords,\n    propertyKeywords: propertyKeywords,\n    nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n    colorKeywords: colorKeywords,\n    valueKeywords: valueKeywords,\n    fontProperties: fontProperties,\n    allowNested: true,\n    lineComment: \"//\",\n    tokenHooks: {\n      \"/\": function(stream, state) {\n        if (stream.eat(\"/\")) {\n          stream.skipToEnd();\n          return [\"comment\", \"comment\"];\n        } else if (stream.eat(\"*\")) {\n          state.tokenize = tokenCComment;\n          return tokenCComment(stream, state);\n        } else {\n          return [\"operator\", \"operator\"];\n        }\n      },\n      \":\": function(stream) {\n        if (stream.match(/^\\s*\\{/, false))\n          return [null, null]\n        return false;\n      },\n      \"$\": function(stream) {\n        stream.match(/^[\\w-]+/);\n        if (stream.match(/^\\s*:/, false))\n          return [\"variable-2\", \"variable-definition\"];\n        return [\"variable-2\", \"variable\"];\n      },\n      \"#\": function(stream) {\n        if (!stream.eat(\"{\")) return false;\n        return [null, \"interpolation\"];\n      }\n    },\n    name: \"css\",\n    helperType: \"scss\"\n  });\n\n  CodeMirror.defineMIME(\"text/x-less\", {\n    mediaTypes: mediaTypes,\n    mediaFeatures: mediaFeatures,\n    mediaValueKeywords: mediaValueKeywords,\n    propertyKeywords: propertyKeywords,\n    nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n    colorKeywords: colorKeywords,\n    valueKeywords: valueKeywords,\n    fontProperties: fontProperties,\n    allowNested: true,\n    lineComment: \"//\",\n    tokenHooks: {\n      \"/\": function(stream, state) {\n        if (stream.eat(\"/\")) {\n          stream.skipToEnd();\n          return [\"comment\", \"comment\"];\n        } else if (stream.eat(\"*\")) {\n          state.tokenize = tokenCComment;\n          return tokenCComment(stream, state);\n        } else {\n          return [\"operator\", \"operator\"];\n        }\n      },\n      \"@\": function(stream) {\n        if (stream.eat(\"{\")) return [null, \"interpolation\"];\n        if (stream.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\\b/i, false)) return false;\n        stream.eatWhile(/[\\w\\\\\\-]/);\n        if (stream.match(/^\\s*:/, false))\n          return [\"variable-2\", \"variable-definition\"];\n        return [\"variable-2\", \"variable\"];\n      },\n      \"&\": function() {\n        return [\"atom\", \"atom\"];\n      }\n    },\n    name: \"css\",\n    helperType: \"less\"\n  });\n\n  CodeMirror.defineMIME(\"text/x-gss\", {\n    documentTypes: documentTypes,\n    mediaTypes: mediaTypes,\n    mediaFeatures: mediaFeatures,\n    propertyKeywords: propertyKeywords,\n    nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n    fontProperties: fontProperties,\n    counterDescriptors: counterDescriptors,\n    colorKeywords: colorKeywords,\n    valueKeywords: valueKeywords,\n    supportsAtComponent: true,\n    tokenHooks: {\n      \"/\": function(stream, state) {\n        if (!stream.eat(\"*\")) return false;\n        state.tokenize = tokenCComment;\n        return tokenCComment(stream, state);\n      }\n    },\n    name: \"css\",\n    helperType: \"gss\"\n  });\n\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACxB;AAEA,MAAAA,YAAW,WAAW,OAAO,SAAS,QAAQ,cAAc;AAC1D,YAAI,SAAS,aAAa;AAC1B,YAAI,CAAC,aAAa,iBAAkB,gBAAeA,YAAW,YAAY,UAAU;AAEpF,YAAI,aAAa,OAAO,YACpB,aAAa,aAAa,YAC1BC,iBAAgB,aAAa,iBAAiB,CAAC,GAC/CC,cAAa,aAAa,cAAc,CAAC,GACzCC,iBAAgB,aAAa,iBAAiB,CAAC,GAC/CC,sBAAqB,aAAa,sBAAsB,CAAC,GACzDC,oBAAmB,aAAa,oBAAoB,CAAC,GACrDC,+BAA8B,aAAa,+BAA+B,CAAC,GAC3EC,kBAAiB,aAAa,kBAAkB,CAAC,GACjDC,sBAAqB,aAAa,sBAAsB,CAAC,GACzDC,iBAAgB,aAAa,iBAAiB,CAAC,GAC/CC,iBAAgB,aAAa,iBAAiB,CAAC,GAC/C,cAAc,aAAa,aAC3B,cAAc,aAAa,aAC3B,sBAAsB,aAAa,wBAAwB,MAC3D,uCAAuC,OAAO,yCAAyC;AAE3F,YAAI,MAAM;AACV,iBAAS,IAAI,OAAO,IAAI;AAAE,iBAAO;AAAI,iBAAO;AAAA,QAAO;AAInD,iBAAS,UAAU,QAAQ,OAAO;AAChC,cAAI,KAAK,OAAO,KAAK;AACrB,cAAI,WAAW,EAAE,GAAG;AAClB,gBAAI,SAAS,WAAW,EAAE,EAAE,QAAQ,KAAK;AACzC,gBAAI,WAAW,MAAO,QAAO;AAAA,UAC/B;AACA,cAAI,MAAM,KAAK;AACb,mBAAO,SAAS,UAAU;AAC1B,mBAAO,IAAI,OAAO,OAAO,QAAQ,CAAC;AAAA,UACpC,WAAW,MAAM,QAAQ,MAAM,OAAO,MAAM,QAAQ,OAAO,IAAI,GAAG,GAAG;AACnE,mBAAO,IAAI,MAAM,SAAS;AAAA,UAC5B,WAAW,MAAM,OAAQ,MAAM,KAAK;AAClC,kBAAM,WAAW,YAAY,EAAE;AAC/B,mBAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,UACrC,WAAW,MAAM,KAAK;AACpB,mBAAO,SAAS,UAAU;AAC1B,mBAAO,IAAI,QAAQ,MAAM;AAAA,UAC3B,WAAW,MAAM,KAAK;AACpB,mBAAO,MAAM,SAAS;AACtB,mBAAO,IAAI,WAAW,WAAW;AAAA,UACnC,WAAW,KAAK,KAAK,EAAE,KAAK,MAAM,OAAO,OAAO,IAAI,IAAI,GAAG;AACzD,mBAAO,SAAS,QAAQ;AACxB,mBAAO,IAAI,UAAU,MAAM;AAAA,UAC7B,WAAW,OAAO,KAAK;AACrB,gBAAI,QAAQ,KAAK,OAAO,KAAK,CAAC,GAAG;AAC/B,qBAAO,SAAS,QAAQ;AACxB,qBAAO,IAAI,UAAU,MAAM;AAAA,YAC7B,WAAW,OAAO,MAAM,aAAa,GAAG;AACtC,qBAAO,SAAS,UAAU;AAC1B,kBAAI,OAAO,MAAM,SAAS,KAAK;AAC7B,uBAAO,IAAI,cAAc,qBAAqB;AAChD,qBAAO,IAAI,cAAc,UAAU;AAAA,YACrC,WAAW,OAAO,MAAM,OAAO,GAAG;AAChC,qBAAO,IAAI,QAAQ,MAAM;AAAA,YAC3B;AAAA,UACF,WAAW,WAAW,KAAK,EAAE,GAAG;AAC9B,mBAAO,IAAI,MAAM,WAAW;AAAA,UAC9B,WAAW,MAAM,OAAO,OAAO,MAAM,uBAAuB,GAAG;AAC7D,mBAAO,IAAI,aAAa,WAAW;AAAA,UACrC,WAAW,iBAAiB,KAAK,EAAE,GAAG;AACpC,mBAAO,IAAI,MAAM,EAAE;AAAA,UACrB,WAAW,OAAO,MAAM,gBAAgB,GAAG;AACzC,gBAAI,mCAAmC,KAAK,OAAO,QAAQ,CAAC,GAAG;AAC7D,oBAAM,WAAW;AAAA,YACnB;AACA,mBAAO,IAAI,mBAAmB,UAAU;AAAA,UAC1C,WAAW,WAAW,KAAK,EAAE,GAAG;AAC9B,mBAAO,SAAS,UAAU;AAC1B,mBAAO,IAAI,YAAY,MAAM;AAAA,UAC/B,OAAO;AACL,mBAAO,IAAI,MAAM,IAAI;AAAA,UACvB;AAAA,QACF;AAEA,iBAAS,YAAY,OAAO;AAC1B,iBAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,UAAU,OAAO;AACrB,oBAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,kBAAI,MAAM,SAAS,CAAC,SAAS;AAC3B,oBAAI,SAAS,IAAK,QAAO,OAAO,CAAC;AACjC;AAAA,cACF;AACA,wBAAU,CAAC,WAAW,MAAM;AAAA,YAC9B;AACA,gBAAI,MAAM,SAAS,CAAC,WAAW,SAAS,IAAK,OAAM,WAAW;AAC9D,mBAAO,IAAI,UAAU,QAAQ;AAAA,UAC/B;AAAA,QACF;AAEA,iBAAS,mBAAmB,QAAQ,OAAO;AACzC,iBAAO,KAAK;AACZ,cAAI,CAAC,OAAO,MAAM,eAAe,KAAK;AACpC,kBAAM,WAAW,YAAY,GAAG;AAAA;AAEhC,kBAAM,WAAW;AACnB,iBAAO,IAAI,MAAM,GAAG;AAAA,QACtB;AAIA,iBAAS,QAAQC,OAAM,QAAQ,MAAM;AACnC,eAAK,OAAOA;AACZ,eAAK,SAAS;AACd,eAAK,OAAO;AAAA,QACd;AAEA,iBAAS,YAAY,OAAO,QAAQA,OAAM,QAAQ;AAChD,gBAAM,UAAU,IAAI,QAAQA,OAAM,OAAO,YAAY,KAAK,WAAW,QAAQ,IAAI,aAAa,MAAM,OAAO;AAC3G,iBAAOA;AAAA,QACT;AAEA,iBAAS,WAAW,OAAO;AACzB,cAAI,MAAM,QAAQ;AAChB,kBAAM,UAAU,MAAM,QAAQ;AAChC,iBAAO,MAAM,QAAQ;AAAA,QACvB;AAEA,iBAAS,KAAKA,OAAM,QAAQ,OAAO;AACjC,iBAAO,OAAO,MAAM,QAAQ,IAAI,EAAEA,OAAM,QAAQ,KAAK;AAAA,QACvD;AACA,iBAAS,WAAWA,OAAM,QAAQ,OAAO,GAAG;AAC1C,mBAAS,IAAI,KAAK,GAAG,IAAI,GAAG;AAC1B,kBAAM,UAAU,MAAM,QAAQ;AAChC,iBAAO,KAAKA,OAAM,QAAQ,KAAK;AAAA,QACjC;AAIA,iBAAS,YAAY,QAAQ;AAC3B,cAAI,OAAO,OAAO,QAAQ,EAAE,YAAY;AACxC,cAAID,eAAc,eAAe,IAAI;AACnC,uBAAW;AAAA,mBACJD,eAAc,eAAe,IAAI;AACxC,uBAAW;AAAA;AAEX,uBAAW;AAAA,QACf;AAEA,YAAI,SAAS,CAAC;AAEd,eAAO,MAAM,SAASE,OAAM,QAAQ,OAAO;AACzC,cAAIA,SAAQ,KAAK;AACf,mBAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,UAC3C,WAAWA,SAAQ,OAAO,MAAM,QAAQ,MAAM;AAC5C,mBAAO,WAAW,KAAK;AAAA,UACzB,WAAW,uBAAuB,cAAc,KAAKA,KAAI,GAAG;AAC1D,mBAAO,YAAY,OAAO,QAAQ,kBAAkB;AAAA,UACtD,WAAW,uBAAuB,KAAKA,KAAI,GAAG;AAC5C,mBAAO,YAAY,OAAO,QAAQ,eAAe;AAAA,UACnD,WAAW,+CAA+C,KAAKA,KAAI,GAAG;AACpE,mBAAO,YAAY,OAAO,QAAQ,SAAS;AAAA,UAC7C,WAAW,+BAA+B,KAAKA,KAAI,GAAG;AACpD,kBAAM,WAAWA;AACjB,mBAAO;AAAA,UACT,WAAW,sCAAsC,KAAKA,KAAI,GAAG;AAC3D,mBAAO;AAAA,UACT,WAAWA,SAAQA,MAAK,OAAO,CAAC,KAAK,KAAK;AACxC,mBAAO,YAAY,OAAO,QAAQ,IAAI;AAAA,UACxC,WAAWA,SAAQ,QAAQ;AACzB,uBAAW;AAAA,UACb,WAAWA,SAAQ,QAAQ;AACzB,uBAAW;AAAA,UACb,WAAWA,SAAQ,uBAAuB;AACxC,mBAAO;AAAA,UACT,WAAWA,SAAQ,iBAAiB;AAClC,mBAAO,YAAY,OAAO,QAAQ,eAAe;AAAA,UACnD,WAAWA,SAAQ,KAAK;AACtB,mBAAO;AAAA,UACT,WAAW,eAAeA,SAAQ,KAAK;AACrC,mBAAO,YAAY,OAAO,QAAQ,QAAQ;AAAA,UAC5C;AACA,iBAAO,MAAM,QAAQ;AAAA,QACvB;AAEA,eAAO,QAAQ,SAASA,OAAM,QAAQ,OAAO;AAC3C,cAAIA,SAAQ,QAAQ;AAClB,gBAAI,OAAO,OAAO,QAAQ,EAAE,YAAY;AACxC,gBAAIN,kBAAiB,eAAe,IAAI,GAAG;AACzC,yBAAW;AACX,qBAAO;AAAA,YACT,WAAWC,6BAA4B,eAAe,IAAI,GAAG;AAC3D,yBAAW,uCAAuC,aAAa;AAC/D,qBAAO;AAAA,YACT,WAAW,aAAa;AACtB,yBAAW,OAAO,MAAM,iBAAiB,KAAK,IAAI,aAAa;AAC/D,qBAAO;AAAA,YACT,OAAO;AACL,0BAAY;AACZ,qBAAO;AAAA,YACT;AAAA,UACF,WAAWK,SAAQ,QAAQ;AACzB,mBAAO;AAAA,UACT,WAAW,CAAC,gBAAgBA,SAAQ,UAAUA,SAAQ,cAAc;AAClE,uBAAW;AACX,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,OAAO,IAAIA,OAAM,QAAQ,KAAK;AAAA,UACvC;AAAA,QACF;AAEA,eAAO,YAAY,SAASA,OAAM,QAAQ,OAAO;AAC/C,cAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,MAAM;AACzD,iBAAO,KAAKA,OAAM,QAAQ,KAAK;AAAA,QACjC;AAEA,eAAO,OAAO,SAASA,OAAM,QAAQ,OAAO;AAC1C,cAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK;AACxC,cAAIA,SAAQ,OAAO,YAAa,QAAO,YAAY,OAAO,QAAQ,WAAW;AAC7E,cAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,WAAWA,OAAM,QAAQ,KAAK;AACrE,cAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,QAAQ;AAE3D,cAAIA,SAAQ,UAAU,CAAC,sDAAsD,KAAK,OAAO,QAAQ,CAAC,GAAG;AACnG,wBAAY;AAAA,UACd,WAAWA,SAAQ,QAAQ;AACzB,wBAAY,MAAM;AAAA,UACpB,WAAWA,SAAQ,iBAAiB;AAClC,mBAAO,YAAY,OAAO,QAAQ,eAAe;AAAA,UACnD;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,YAAY,SAASA,OAAM,SAAS,OAAO;AAChD,cAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK;AACxC,cAAIA,SAAQ,QAAQ;AAAE,uBAAW;AAAY,mBAAO;AAAA,UAAa;AACjE,iBAAO,MAAM,QAAQ;AAAA,QACvB;AAEA,eAAO,SAAS,SAASA,OAAM,QAAQ,OAAO;AAC5C,cAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,WAAWA,OAAM,QAAQ,KAAK;AACrE,cAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK;AACxC,cAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,QAAQ;AAC3D,cAAIA,SAAQ,gBAAiB,QAAO,YAAY,OAAO,QAAQ,eAAe;AAC9E,cAAIA,SAAQ,OAAQ,aAAY,MAAM;AACtC,iBAAO;AAAA,QACT;AAEA,eAAO,SAAS,SAASA,OAAM,QAAQ,OAAO;AAC5C,cAAIA,SAAQ,OAAQ,QAAO;AAE3B,cAAIA,SAAQ,QAAQ;AAClB,uBAAW;AACX,mBAAO,MAAM,QAAQ;AAAA,UACvB;AACA,iBAAO,KAAKA,OAAM,QAAQ,KAAK;AAAA,QACjC;AAEA,eAAO,gBAAgB,SAASA,OAAM,QAAQ,OAAO;AACnD,cAAIA,SAAQ,UAAUV,eAAc,eAAe,OAAO,QAAQ,CAAC,GAAG;AACpE,uBAAW;AACX,mBAAO,MAAM,QAAQ;AAAA,UACvB,OAAO;AACL,mBAAO,OAAO,QAAQU,OAAM,QAAQ,KAAK;AAAA,UAC3C;AAAA,QACF;AAEA,eAAO,UAAU,SAASA,OAAM,QAAQ,OAAO;AAC7C,cAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,gBAAgB;AACnE,cAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,WAAWA,OAAM,QAAQ,KAAK;AACrE,cAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK,KAAK,YAAY,OAAO,QAAQ,cAAc,UAAU,KAAK;AAErG,cAAIA,SAAQ,gBAAiB,QAAO,YAAY,OAAO,QAAQ,eAAe;AAE9E,cAAIA,SAAQ,QAAQ;AAClB,gBAAI,OAAO,OAAO,QAAQ,EAAE,YAAY;AACxC,gBAAI,QAAQ,UAAU,QAAQ,SAAS,QAAQ,SAAS,QAAQ;AAC9D,yBAAW;AAAA,qBACJT,YAAW,eAAe,IAAI;AACrC,yBAAW;AAAA,qBACJC,eAAc,eAAe,IAAI;AACxC,yBAAW;AAAA,qBACJC,oBAAmB,eAAe,IAAI;AAC7C,yBAAW;AAAA,qBACJC,kBAAiB,eAAe,IAAI;AAC3C,yBAAW;AAAA,qBACJC,6BAA4B,eAAe,IAAI;AACtD,yBAAW,uCAAuC,aAAa;AAAA,qBACxDI,eAAc,eAAe,IAAI;AACxC,yBAAW;AAAA,qBACJD,eAAc,eAAe,IAAI;AACxC,yBAAW;AAAA;AAEX,yBAAW;AAAA,UACf;AACA,iBAAO,MAAM,QAAQ;AAAA,QACvB;AAEA,eAAO,mBAAmB,SAASE,OAAM,QAAQ,OAAO;AACtD,cAAIA,SAAQ;AACV,mBAAO,WAAWA,OAAM,QAAQ,KAAK;AACvC,cAAIA,SAAQ;AACV,mBAAO,WAAW,KAAK,KAAK,YAAY,OAAO,QAAQ,cAAc,UAAU,OAAO,KAAK;AAC7F,cAAIA,SAAQ;AACV,uBAAW;AACb,iBAAO,MAAM,QAAQ;AAAA,QACvB;AAEA,eAAO,iBAAiB,SAASA,OAAM,QAAQ,OAAO;AACpD,cAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK;AACxC,cAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,WAAWA,OAAM,QAAQ,OAAO,CAAC;AACxE,iBAAO,OAAO,QAAQA,OAAM,QAAQ,KAAK;AAAA,QAC3C;AAEA,eAAO,4BAA4B,SAASA,OAAM,QAAQ,OAAO;AAC/D,cAAIA,SAAQ;AACV,mBAAO,YAAY,OAAO,QAAQ,oBAAoB;AACxD,cAAIA,SAAQ,UAAU,MAAM,YAAY,kBAAkB;AACxD,uBAAW;AACX,mBAAO;AAAA,UACT;AACA,iBAAO,KAAKA,OAAM,QAAQ,KAAK;AAAA,QACjC;AAEA,eAAO,qBAAqB,SAASA,OAAM,QAAQ,OAAO;AACxD,cAAIA,SAAQ,KAAK;AACf,kBAAM,WAAW;AACjB,mBAAO,WAAW,KAAK;AAAA,UACzB;AACA,cAAIA,SAAQ,QAAQ;AAClB,gBAAK,MAAM,YAAY,gBAAgB,CAACJ,gBAAe,eAAe,OAAO,QAAQ,EAAE,YAAY,CAAC,KAC/F,MAAM,YAAY,oBAAoB,CAACC,oBAAmB,eAAe,OAAO,QAAQ,EAAE,YAAY,CAAC;AAC1G,yBAAW;AAAA;AAEX,yBAAW;AACb,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,YAAY,SAASG,OAAM,QAAQ,OAAO;AAC/C,cAAIA,SAAQ,QAAQ;AAAE,uBAAW;AAAY,mBAAO;AAAA,UAAa;AACjE,cAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,KAAK;AACxD,iBAAO,KAAKA,OAAM,QAAQ,KAAK;AAAA,QACjC;AAEA,eAAO,KAAK,SAASA,OAAM,QAAQ,OAAO;AACxC,cAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK;AACxC,cAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,WAAWA,OAAM,QAAQ,KAAK;AACrE,cAAIA,SAAQ,OAAQ,YAAW;AAAA,mBACtBA,SAAQ,OAAQ,YAAW;AACpC,iBAAO;AAAA,QACT;AAEA,eAAO,gBAAgB,SAASA,OAAM,QAAQ,OAAO;AACnD,cAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK;AACxC,cAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,WAAWA,OAAM,QAAQ,KAAK;AACrE,cAAIA,SAAQ,OAAQ,YAAW;AAAA,mBACtBA,SAAQ,cAAcA,SAAQ,OAAOA,SAAQ,IAAK,YAAW;AACtE,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,UACL,YAAY,SAAS,MAAM;AACzB,mBAAO;AAAA,cAAC,UAAU;AAAA,cACV,OAAO,SAAS,UAAU;AAAA,cAC1B,UAAU;AAAA,cACV,SAAS,IAAI,QAAQ,SAAS,UAAU,OAAO,QAAQ,GAAG,IAAI;AAAA,YAAC;AAAA,UACzE;AAAA,UAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,CAAC,MAAM,YAAY,OAAO,SAAS,EAAG,QAAO;AACjD,gBAAI,SAAS,MAAM,YAAY,WAAW,QAAQ,KAAK;AACvD,gBAAI,SAAS,OAAO,SAAS,UAAU;AACrC,qBAAO,MAAM,CAAC;AACd,sBAAQ,MAAM,CAAC;AAAA,YACjB;AACA,uBAAW;AACX,gBAAI,QAAQ;AACV,oBAAM,QAAQ,OAAO,MAAM,KAAK,EAAE,MAAM,QAAQ,KAAK;AACvD,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,OAAO,WAAW;AACjC,gBAAI,KAAK,MAAM,SAAS,KAAK,aAAa,UAAU,OAAO,CAAC;AAC5D,gBAAI,SAAS,GAAG;AAChB,gBAAI,GAAG,QAAQ,WAAW,MAAM,OAAO,MAAM,KAAM,MAAK,GAAG;AAC3D,gBAAI,GAAG,MAAM;AACX,kBAAI,MAAM,QAAQ,GAAG,QAAQ,WAAW,GAAG,QAAQ,SACjC,GAAG,QAAQ,mBAAmB,GAAG,QAAQ,uBAAuB;AAEhF,qBAAK,GAAG;AACR,yBAAS,GAAG;AAAA,cACd,WAAW,MAAM,QAAQ,GAAG,QAAQ,YAAY,GAAG,QAAQ,qBACvD,MAAM,QAAQ,GAAG,QAAQ,QAAQ,GAAG,QAAQ,YAAY;AAE1D,yBAAS,KAAK,IAAI,GAAG,GAAG,SAAS,UAAU;AAAA,cAC7C;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,eAAe;AAAA,UACf,mBAAmB;AAAA,UACnB,iBAAiB;AAAA,UACjB,sBAAsB;AAAA,UACtB;AAAA,UACA,MAAM;AAAA,QACR;AAAA,MACF,CAAC;AAEC,eAAS,OAAO,OAAO;AACrB,YAAI,OAAO,CAAC;AACZ,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,eAAK,MAAM,CAAC,EAAE,YAAY,CAAC,IAAI;AAAA,QACjC;AACA,eAAO;AAAA,MACT;AAEA,UAAI,iBAAiB;AAAA,QACnB;AAAA,QAAU;AAAA,QAAU;AAAA,QAAO;AAAA,MAC7B,GAAG,gBAAgB,OAAO,cAAc;AAExC,UAAI,cAAc;AAAA,QAChB;AAAA,QAAO;AAAA,QAAS;AAAA,QAAW;AAAA,QAAY;AAAA,QAAS;AAAA,QAAc;AAAA,QAC9D;AAAA,QAAO;AAAA,QAAM;AAAA,MACf,GAAG,aAAa,OAAO,WAAW;AAElC,UAAI,iBAAiB;AAAA,QACnB;AAAA,QAAS;AAAA,QAAa;AAAA,QAAa;AAAA,QAAU;AAAA,QAAc;AAAA,QAC3D;AAAA,QAAgB;AAAA,QAAoB;AAAA,QAAoB;AAAA,QACxD;AAAA,QAAqB;AAAA,QAAqB;AAAA,QAC1C;AAAA,QAAoB;AAAA,QAAoB;AAAA,QACxC;AAAA,QAA2B;AAAA,QAA2B;AAAA,QAAS;AAAA,QAC/D;AAAA,QAAa;AAAA,QAAe;AAAA,QAAmB;AAAA,QAC/C;AAAA,QAAc;AAAA,QAAkB;AAAA,QAAkB;AAAA,QAClD;AAAA,QAAkB;AAAA,QAAkB;AAAA,QAAQ;AAAA,QAAQ;AAAA,QACpD;AAAA,QAAsB;AAAA,QAA0B;AAAA,QAChD;AAAA,QAAW;AAAA,QAAe;AAAA,QAAS;AAAA,QAAa;AAAA,QAChD;AAAA,QAAiB;AAAA,MACnB,GAAG,gBAAgB,OAAO,cAAc;AAExC,UAAI,sBAAsB;AAAA,QACxB;AAAA,QAAa;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAa;AAAA,QAChE;AAAA,QAAa;AAAA,QACb;AAAA,QAAQ;AAAA,QACR;AAAA,QAAY;AAAA,MACd,GAAG,qBAAqB,OAAO,mBAAmB;AAElD,UAAI,oBAAoB;AAAA,QACtB;AAAA,QAAiB;AAAA,QAAe;AAAA,QAAc;AAAA,QAC9C;AAAA,QAAsB;AAAA,QAAO;AAAA,QAAgB;AAAA,QAAa;AAAA,QAC1D;AAAA,QAAuB;AAAA,QAAsB;AAAA,QAC7C;AAAA,QAA6B;AAAA,QAAkB;AAAA,QAC/C;AAAA,QAA6B;AAAA,QAAc;AAAA,QAAW;AAAA,QACtD;AAAA,QAAuB;AAAA,QAAc;AAAA,QACrC;AAAA,QAAyB;AAAA,QAAmB;AAAA,QAC5C;AAAA,QAAoB;AAAA,QAAqB;AAAA,QACzC;AAAA,QAAyB;AAAA,QAAyB;AAAA,QAClD;AAAA,QAAmB;AAAA,QAAkB;AAAA,QAAW;AAAA,QAAS;AAAA,QACzD;AAAA,QAAkB;AAAA,QAAkB;AAAA,QAAkB;AAAA,QACtD;AAAA,QAAU;AAAA,QAAiB;AAAA,QAAuB;AAAA,QAClD;AAAA,QAA8B;AAAA,QAAuB;AAAA,QACrD;AAAA,QAAmB;AAAA,QAAgB;AAAA,QAAgB;AAAA,QACnD;AAAA,QAAuB;AAAA,QAAsB;AAAA,QAC7C;AAAA,QAAsB;AAAA,QAAe;AAAA,QAAqB;AAAA,QAC1D;AAAA,QAAqB;AAAA,QAAiB;AAAA,QAAgB;AAAA,QACtD;AAAA,QAAsB;AAAA,QAAsB;AAAA,QAAkB;AAAA,QAC9D;AAAA,QAAc;AAAA,QAAoB;AAAA,QAClC;AAAA,QAA2B;AAAA,QAAoB;AAAA,QAC/C;AAAA,QAAgB;AAAA,QAAU;AAAA,QAAwB;AAAA,QAAc;AAAA,QAChE;AAAA,QAAe;AAAA,QAAgB;AAAA,QAAgB;AAAA,QAAgB;AAAA,QAC/D;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAiB;AAAA,QAAgB;AAAA,QAC3D;AAAA,QAAc;AAAA,QAAe;AAAA,QAAqB;AAAA,QAClD;AAAA,QAAqB;AAAA,QAAe;AAAA,QAAgB;AAAA,QAAW;AAAA,QAC/D;AAAA,QAAW;AAAA,QAAqB;AAAA,QAAiB;AAAA,QAAQ;AAAA,QAAO;AAAA,QAChE;AAAA,QAAc;AAAA,QAAU;AAAA,QAAa;AAAA,QAAW;AAAA,QAChD;AAAA,QAA6B;AAAA,QAC7B;AAAA,QAA8B;AAAA,QAA6B;AAAA,QAC3D;AAAA,QAAsB;AAAA,QAAa;AAAA,QAAe;AAAA,QAAO;AAAA,QAAe;AAAA,QACxE;AAAA,QAAQ;AAAA,QAAc;AAAA,QAAkB;AAAA,QAAa;AAAA,QACrD;AAAA,QAAe;AAAA,QAAa;AAAA,QAAS;AAAA,QAAgB;AAAA,QAAa;AAAA,QAClE;AAAA,QAAQ;AAAA,QAAe;AAAA,QAAyB;AAAA,QAChD;AAAA,QAA0B;AAAA,QAAuB;AAAA,QACjD;AAAA,QAAoB;AAAA,QAAgB;AAAA,QAAc;AAAA,QAClD;AAAA,QAAgB;AAAA,QAA2B;AAAA,QAC3C;AAAA,QAA2B;AAAA,QAA0B;AAAA,QACrD;AAAA,QAAyB;AAAA,QAA2B;AAAA,QAAe;AAAA,QACnE;AAAA,QAAQ;AAAA,QAAa;AAAA,QAAqB;AAAA,QAAkB;AAAA,QAC5D;AAAA,QAAe;AAAA,QAAmB;AAAA,QAAmB;AAAA,QACrD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAgB;AAAA,QAAgB;AAAA,QACxD;AAAA,QAAiB;AAAA,QAAuB;AAAA,QACxC;AAAA,QAAsB;AAAA,QAAuB;AAAA,QAAU;AAAA,QAAW;AAAA,QAClE;AAAA,QAAqB;AAAA,QAAmB;AAAA,QAAoB;AAAA,QAC5D;AAAA,QAAS;AAAA,QAAe;AAAA,QAAmB;AAAA,QAAqB;AAAA,QAChE;AAAA,QAAoB;AAAA,QAAsB;AAAA,QAAa;AAAA,QACvD;AAAA,QAAiB;AAAA,QAAgB;AAAA,QAAQ;AAAA,QAAkB;AAAA,QAC3D;AAAA,QAAe;AAAA,QAAoB;AAAA,QAAiB;AAAA,QACpD;AAAA,QAAuB;AAAA,QAA0B;AAAA,QACjD;AAAA,QAAoB;AAAA,QAAuB;AAAA,QAAmB;AAAA,QAC9D;AAAA,QAAiB;AAAA,QAAe;AAAA,QAAgB;AAAA,QAAc;AAAA,QAC9D;AAAA,QAAqB;AAAA,QAAgB;AAAA,QAAsB;AAAA,QAC3D;AAAA,QAAiB;AAAA,QAAa;AAAA,QAAkB;AAAA,QAAc;AAAA,QAC9D;AAAA,QAAe;AAAA,QAAiB;AAAA,QAAe;AAAA,QAAY;AAAA,QAC3D;AAAA,QAAkB;AAAA,QAAc;AAAA,QAChC;AAAA,QAAa;AAAA,QAAkB;AAAA,QAAc;AAAA,QAAmB;AAAA,QAChE;AAAA,QAAkB;AAAA,QAAW;AAAA,QAAY;AAAA,QAAa;AAAA,QAAY;AAAA,QAClE;AAAA,QAAU;AAAA,QAAc;AAAA,QAAmB;AAAA,QAAU;AAAA,QACrD;AAAA,QAAmB;AAAA,QAAe;AAAA,QAAmB;AAAA,QACrD;AAAA,QAAW;AAAA,QAAS;AAAA,QAAW;AAAA,QAAW;AAAA,QAAiB;AAAA,QAC3D;AAAA,QAAiB;AAAA,QAAiB;AAAA,QAAY;AAAA,QAC9C;AAAA,QAAiB;AAAA,QAAc;AAAA,QAAc;AAAA,QAAW;AAAA,QACxD;AAAA,QAAgB;AAAA,QAAiB;AAAA,QAAe;AAAA,QAAQ;AAAA,QACxD;AAAA,QAAqB;AAAA,QAAqB;AAAA,QAAe;AAAA,QACzD;AAAA,QAAe;AAAA,QAAgB;AAAA,QAAe;AAAA,QAAsB;AAAA,QACpE;AAAA,QAAe;AAAA,QAAiB;AAAA,QAAe;AAAA,QAAc;AAAA,QAC7D;AAAA,QAAY;AAAA,QAAsB;AAAA,QAAoB;AAAA,QACtD;AAAA,QAAsB;AAAA,QAAuB;AAAA,QAC7C;AAAA,QAAmB;AAAA,QAAoB;AAAA,QAAU;AAAA,QAAQ;AAAA,QACzD;AAAA,QAAe;AAAA,QAAY;AAAA,QAAS;AAAA,QAAU;AAAA,QAAY;AAAA,QAC1D;AAAA,QAAW;AAAA,QAAc;AAAA,QAAiB;AAAA,QAAiB;AAAA,QAC3D;AAAA,QAAS;AAAA,QAAmB;AAAA,QAAiB;AAAA,QAC7C;AAAA,QAA2B;AAAA,QAA6B;AAAA,QACxD;AAAA,QAAwB;AAAA,QACxB;AAAA,QAA8B;AAAA,QAAsB;AAAA,QACpD;AAAA,QAAqB;AAAA,QAAkB;AAAA,QACvC;AAAA,QAA4B;AAAA,QAC5B;AAAA,QAAyB;AAAA,QAAyB;AAAA,QAClD;AAAA,QAA+B;AAAA,QAAuB;AAAA,QACtD;AAAA,QAAsB;AAAA,QAAqB;AAAA,QAC3C;AAAA,QAAyB;AAAA,QAAgB;AAAA,QAAgB;AAAA,QACzD;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAY;AAAA,QAAgB;AAAA,QAC7C;AAAA,QAAqB;AAAA,QAAe;AAAA,QAAU;AAAA,QAAc;AAAA,QAC5D;AAAA,QAAgB;AAAA,QAAU;AAAA,QAAe;AAAA,QAAc;AAAA,QACvD;AAAA,QAAc;AAAA,QAAmB;AAAA,QAAwB;AAAA,QACzD;AAAA,QAAyB;AAAA,QAAwB;AAAA,QACjD;AAAA,QAA4B;AAAA,QAAyB;AAAA,QACrD;AAAA,QAAuB;AAAA,QAA0B;AAAA,QACjD;AAAA,QAAe;AAAA,QAAe;AAAA,QAAgB;AAAA,QAC9C;AAAA,QAAgB;AAAA,QAAiB;AAAA,QAAkB;AAAA,QACnD;AAAA,QAAoB;AAAA,QAAuB;AAAA,QAC3C;AAAA,QAA2B;AAAA,QAAa;AAAA,QAAO;AAAA,QAAgB;AAAA,QAAa;AAAA,QAC5E;AAAA,QAAmB;AAAA,QAAc;AAAA,QAAoB;AAAA,QACrD;AAAA,QAAuB;AAAA,QAA8B;AAAA,QACrD;AAAA,QAAgB;AAAA,QAAe;AAAA,QAAkB;AAAA,QAAc;AAAA,QAC/D;AAAA,QAAkB;AAAA,QAAgB;AAAA,QAAe;AAAA,QAAe;AAAA,QAChE;AAAA,QAAgB;AAAA,QAAgB;AAAA,QAAU;AAAA,QAAe;AAAA,QAAU;AAAA,QACnE;AAAA,QAAe;AAAA,QAAc;AAAA,QAAgB;AAAA,QAAa;AAAA,QAAgB;AAAA;AAAA,QAE1E;AAAA,QAAa;AAAA,QAAa;AAAA,QAAQ;AAAA,QAAqB;AAAA,QAAU;AAAA,QACjE;AAAA,QAAiB;AAAA,QAAkB;AAAA,QAAc;AAAA,QAAgB;AAAA,QACjE;AAAA,QAAuB;AAAA,QACvB;AAAA,QAAmB;AAAA,QAAQ;AAAA,QAAgB;AAAA,QAAa;AAAA,QACxD;AAAA,QAAU;AAAA,QAAc;AAAA,QAAc;AAAA,QAAgB;AAAA,QAAe;AAAA,QAAmB;AAAA,QACxF;AAAA,QAAoB;AAAA,QAAqB;AAAA,QAAkB;AAAA,QAC3D;AAAA,QAAqB;AAAA,QAAkB;AAAA,QAAgB;AAAA,QACvD;AAAA,QAAkB;AAAA,QAAqB;AAAA,QACvC;AAAA,QAA8B;AAAA,QAAe;AAAA,MAC/C,GAAG,mBAAmB,OAAO,iBAAiB;AAE9C,UAAI,+BAA+B;AAAA,QACjC;AAAA,QAAgB;AAAA,QAAgB;AAAA,QAAgB;AAAA,QAAsB;AAAA,QACtE;AAAA,QAA0B;AAAA,QAA0B;AAAA,QACpD;AAAA,QAAsB;AAAA,QAA4B;AAAA,QAClD;AAAA,QAA4B;AAAA,QAAsB;AAAA,QAClD;AAAA,QAAiB;AAAA,QAAuB;AAAA,QACxC;AAAA,QAA2B;AAAA,QAC3B;AAAA,QAA2B;AAAA,QAAuB;AAAA,QAClD;AAAA,QAA6B;AAAA,QAC7B;AAAA,QAAuB;AAAA,QAAuB;AAAA,QAAsB;AAAA,QACpE;AAAA,QAAoB;AAAA,QAAsB;AAAA,QAAiB;AAAA,QAC3D;AAAA,QAAuB;AAAA,QAAmB;AAAA,QAAuB;AAAA,QAAiB;AAAA,QAClF;AAAA,QAAuB;AAAA,QAAkB;AAAA,QACzC;AAAA,QAAwB;AAAA,QAAoB;AAAA,QAC5C;AAAA,QAAyB;AAAA,QAAwB;AAAA,QACjD;AAAA,QAAwB;AAAA,QAA6B;AAAA,QACrD;AAAA,QAAyB;AAAA,QAA6B;AAAA,QACtD;AAAA,QAA8B;AAAA,QAAkC;AAAA,QAAgB;AAAA,MAClF,GAAG,8BAA8B,OAAO,4BAA4B;AAEpE,UAAI,kBAAkB;AAAA,QACpB;AAAA,QAAgB;AAAA,QAAe;AAAA,QAAO;AAAA,QAAiB;AAAA,QACtD;AAAA,QAAyB;AAAA,QAAgB;AAAA,QAAe;AAAA,MAC3D,GAAG,iBAAiB,OAAO,eAAe;AAE1C,UAAI,sBAAsB;AAAA,QACxB;AAAA,QAAoB;AAAA,QAAY;AAAA,QAAY;AAAA,QAAO;AAAA,QAAU;AAAA,QAC7D;AAAA,QAAY;AAAA,QAAU;AAAA,QAAW;AAAA,MACnC,GAAG,qBAAqB,OAAO,mBAAmB;AAElD,UAAI,iBAAiB;AAAA,QACnB;AAAA,QAAa;AAAA,QAAgB;AAAA,QAAQ;AAAA,QAAc;AAAA,QAAS;AAAA,QAC5D;AAAA,QAAU;AAAA,QAAS;AAAA,QAAkB;AAAA,QAAQ;AAAA,QAAc;AAAA,QAC3D;AAAA,QAAa;AAAA,QAAa;AAAA,QAAc;AAAA,QAAa;AAAA,QAAS;AAAA,QAC9D;AAAA,QAAY;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAY;AAAA,QAAY;AAAA,QACvD;AAAA,QAAY;AAAA,QAAa;AAAA,QAAY;AAAA,QAAa;AAAA,QAAe;AAAA,QACjE;AAAA,QAAc;AAAA,QAAc;AAAA,QAAW;AAAA,QAAc;AAAA,QACrD;AAAA,QAAiB;AAAA,QAAiB;AAAA,QAAiB;AAAA,QAAiB;AAAA,QACpE;AAAA,QAAY;AAAA,QAAe;AAAA,QAAW;AAAA,QAAW;AAAA,QAAc;AAAA,QAC/D;AAAA,QAAe;AAAA,QAAe;AAAA,QAAW;AAAA,QAAa;AAAA,QACtD;AAAA,QAAQ;AAAA,QAAa;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAe;AAAA,QAC7D;AAAA,QAAW;AAAA,QAAa;AAAA,QAAU;AAAA,QAAS;AAAA,QAAS;AAAA,QACpD;AAAA,QAAiB;AAAA,QAAa;AAAA,QAAgB;AAAA,QAAa;AAAA,QAC3D;AAAA,QAAa;AAAA,QAAwB;AAAA,QAAa;AAAA,QAAc;AAAA,QAAa;AAAA,QAC7E;AAAA,QAAe;AAAA,QAAiB;AAAA,QAAgB;AAAA,QAAkB;AAAA,QAClE;AAAA,QAAkB;AAAA,QAAe;AAAA,QAAQ;AAAA,QAAa;AAAA,QAAS;AAAA,QAC/D;AAAA,QAAU;AAAA,QAAoB;AAAA,QAAc;AAAA,QAAgB;AAAA,QAC5D;AAAA,QAAkB;AAAA,QAAmB;AAAA,QAAqB;AAAA,QAC1D;AAAA,QAAmB;AAAA,QAAgB;AAAA,QAAa;AAAA,QAAa;AAAA,QAC7D;AAAA,QAAe;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAS;AAAA,QAAa;AAAA,QAAU;AAAA,QAClE;AAAA,QAAU;AAAA,QAAiB;AAAA,QAAa;AAAA,QAAiB;AAAA,QACzD;AAAA,QAAc;AAAA,QAAa;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QACnD;AAAA,QAAU;AAAA,QAAiB;AAAA,QAAO;AAAA,QAAa;AAAA,QAAa;AAAA,QAC5D;AAAA,QAAU;AAAA,QAAc;AAAA,QAAY;AAAA,QAAY;AAAA,QAAU;AAAA,QAAU;AAAA,QACpE;AAAA,QAAa;AAAA,QAAa;AAAA,QAAa;AAAA,QAAQ;AAAA,QAAe;AAAA,QAAa;AAAA,QAC3E;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAU;AAAA,QAAa;AAAA,QAAU;AAAA,QAAS;AAAA,QAC7D;AAAA,QAAc;AAAA,QAAU;AAAA,MAC1B,GAAG,gBAAgB,OAAO,cAAc;AAExC,UAAI,iBAAiB;AAAA,QACnB;AAAA,QAAS;AAAA,QAAY;AAAA,QAAgB;AAAA,QAAY;AAAA,QAAiB;AAAA,QAClE;AAAA,QAAqB;AAAA,QAAS;AAAA,QAAS;AAAA,QAAO;AAAA,QAAc;AAAA,QAAc;AAAA,QAC1E;AAAA,QAAU;AAAA,QAAW;AAAA,QAAmB;AAAA,QAAe;AAAA,QACvD;AAAA,QAAgB;AAAA,QAAY;AAAA,QAAa;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAa;AAAA,QAAS;AAAA,QAAgB;AAAA,QAC/F;AAAA,QAAgB;AAAA,QAAY;AAAA,QAAc;AAAA,QAAa;AAAA,QAAY;AAAA,QAAS;AAAA,QAAiB;AAAA,QAC7F;AAAA,QAAW;AAAA,QAAS;AAAA,QAAS;AAAA,QAAc;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAU;AAAA,QAC/E;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAS;AAAA,QAAa;AAAA,QAAc;AAAA,QAAc;AAAA,QAAW;AAAA,QAC/E;AAAA,QAAc;AAAA,QAAmB;AAAA,QAAgB;AAAA,QAAc;AAAA,QAAQ;AAAA,QACvE;AAAA,QAAc;AAAA,QAAuB;AAAA,QAAW;AAAA,QAAe;AAAA,QAC/D;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAY;AAAA,QAAU;AAAA,QAAe;AAAA,QACvD;AAAA,QAAqB;AAAA,QAAmB;AAAA,QAAS;AAAA,QAAQ;AAAA,QACzD;AAAA,QAAc;AAAA,QAAY;AAAA,QAAS;AAAA,QAAc;AAAA,QAAe;AAAA,QAAU;AAAA,QAC1E;AAAA,QAAW;AAAA,QAAa;AAAA,QAAkB;AAAA,QAAW;AAAA,QAAW;AAAA,QAChE;AAAA,QAAe;AAAA,QAAgB;AAAA,QAAc;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAY;AAAA,QAAS;AAAA,QACjG;AAAA,QAAS;AAAA,QAAa;AAAA,QAAgB;AAAA,QAAgB;AAAA,QAAW;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAC/F;AAAA,QAAwB;AAAA,QAAW;AAAA,QAAkB;AAAA,QAAS;AAAA,QAC9D;AAAA,QAAkB;AAAA,QAAmB;AAAA,QAAoB;AAAA,QAAc;AAAA,QACvE;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAqB;AAAA,QAAmB;AAAA,QAC3D;AAAA,QAAY;AAAA,QACZ;AAAA,QAAU;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAe;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAe;AAAA,QACzF;AAAA,QAAW;AAAA,QAAW;AAAA,QAAY;AAAA,QAAS;AAAA,QAAO;AAAA,QAAY;AAAA,QAC9D;AAAA,QAA0B;AAAA,QAAwB;AAAA,QAClD;AAAA,QAA0B;AAAA,QAC1B;AAAA,QAA2B;AAAA,QAC3B;AAAA,QAAyB;AAAA,QACzB;AAAA,QAA4B;AAAA,QAC5B;AAAA,QAA2B;AAAA,QAA2B;AAAA,QACtD;AAAA,QAAoB;AAAA,QAAa;AAAA,QAAa;AAAA,QAAY;AAAA,QAAW;AAAA,QACrE;AAAA,QAAkB;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAY;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAY;AAAA,QAAc;AAAA,QAC5G;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAsB;AAAA,QAAY;AAAA,QAAa;AAAA,QAAY;AAAA,QAAQ;AAAA,QACvF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAoB;AAAA,QAAc;AAAA,QAC5E;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAa;AAAA,QACjD;AAAA,QAAY;AAAA,QAAkB;AAAA,QAAc;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAO;AAAA,QAAc;AAAA,QAAQ;AAAA,QACxF;AAAA,QAAkB;AAAA,QAAmB;AAAA,QAAuB;AAAA,QAC5D;AAAA,QAAkB;AAAA,QAAY;AAAA,QAAW;AAAA,QAAW;AAAA,QAAU;AAAA,QAC9D;AAAA,QAAgB;AAAA,QAAe;AAAA,QAAe;AAAA,QAAgB;AAAA,QAAS;AAAA,QAAU;AAAA,QAAa;AAAA,QAC9F;AAAA,QAAU;AAAA,QAAmB;AAAA,QAAqB;AAAA,QAAW;AAAA,QAC7D;AAAA,QAAY;AAAA,QAAkB;AAAA,QAAY;AAAA,QAC1C;AAAA,QAAwB;AAAA,QAAuB;AAAA,QAC/C;AAAA,QAAa;AAAA,QAAO;AAAA,QAAS;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAW;AAAA,QACnE;AAAA,QAAgB;AAAA,QAAU;AAAA,QAAmB;AAAA,QAAS;AAAA,QAAa;AAAA,QAAW;AAAA,QAC9E;AAAA,QAAS;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAe;AAAA,QACpD;AAAA,QAAe;AAAA,QAAqB;AAAA,QAAe;AAAA,QACnD;AAAA,QAAe;AAAA,QAAa;AAAA,QAAO;AAAA,QAAc;AAAA,QAAa;AAAA,QAAgB;AAAA,QAAS;AAAA,QAAU;AAAA,QACjG;AAAA,QAAqB;AAAA,QAAgB;AAAA,QACrC;AAAA,QAAuB;AAAA,QAA4B;AAAA,QACnD;AAAA,QAAQ;AAAA,QAAY;AAAA,QACpB;AAAA,QAAY;AAAA,QAAe;AAAA,QAAU;AAAA,QACrC;AAAA,QAAO;AAAA,QAAa;AAAA,QAAa;AAAA,QAAQ;AAAA,QAAY;AAAA,QAAwB;AAAA,QAAY;AAAA,QAAW;AAAA,QACpG;AAAA,QAAY;AAAA,QAAa;AAAA,QAAe;AAAA,QAAkB;AAAA,QAC1D;AAAA,QAAiB;AAAA,QAAa;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAe;AAAA,QAC/D;AAAA,QAAa;AAAA,QAAW;AAAA,QAAW;AAAA,QAAa;AAAA,QAAe;AAAA,QAAW;AAAA,QAAS;AAAA,QAAW;AAAA,QAC9F;AAAA,QAAsB;AAAA,QAAiB;AAAA,QAAS;AAAA,QAAS;AAAA,QACzD;AAAA,QAAW;AAAA,QAAiB;AAAA,QAAW;AAAA,QAAY;AAAA,QAAW;AAAA,QAC9D;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAW;AAAA,QAAe;AAAA,QAAc;AAAA,QAAe;AAAA,QACpF;AAAA,QAAW;AAAA,QAAW;AAAA,QAAY;AAAA,QAAO;AAAA,QAAY;AAAA,QAAY;AAAA,QACjE;AAAA,QAAY;AAAA,QAAe;AAAA,QAAmB;AAAA,QAAS;AAAA,QACvD;AAAA,QAAc;AAAA,QAA6B;AAAA,QAAa;AAAA,QACxD;AAAA,QAAY;AAAA,QAAU;AAAA,QAA6B;AAAA,QACnD;AAAA,QAA4B;AAAA,QAAY;AAAA,QAAY;AAAA,QAAS;AAAA,QAC7D;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAS;AAAA,QAAU;AAAA,QAAY;AAAA,QAAW;AAAA,QAClE;AAAA,QAAW;AAAA,QAAS;AAAA,QAAO;AAAA,QAAc;AAAA,QAAe;AAAA,QAAO;AAAA,QAAU;AAAA,QACzE;AAAA,QAAY;AAAA,QAAc;AAAA,QAAY;AAAA,QAAc;AAAA,QAAS;AAAA,QAAW;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QACtG;AAAA,QAAU;AAAA,QAAa;AAAA,QAAmB;AAAA,QAAa;AAAA,QACvD;AAAA,QAA6B;AAAA,QAC7B;AAAA,QAA8B;AAAA,QAAkC;AAAA,QAAc;AAAA,QAC9E;AAAA,QAAkB;AAAA,QAAiB;AAAA,QAAY;AAAA,QAAS;AAAA,QAAS;AAAA,QAAQ;AAAA,QACzE;AAAA,QAAuB;AAAA,QAAyB;AAAA,QAChD;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAS;AAAA,QAAoB;AAAA,QAAS;AAAA,QACvD;AAAA,QAAmB;AAAA,QAA0B;AAAA,QAAwB;AAAA,QACrE;AAAA,QAAS;AAAA,QAAc;AAAA,QAAiB;AAAA,QAAW;AAAA,QAAc;AAAA,QAAS;AAAA,QAC1E;AAAA,QAAe;AAAA,QAAa;AAAA,QAAc;AAAA,QAAe;AAAA,QAAS;AAAA,QAAgB;AAAA,QAAiB;AAAA,QAAgB;AAAA,QAAa;AAAA,QAChI;AAAA,QAAiB;AAAA,QAAS;AAAA,QAAU;AAAA,QAAc;AAAA,QAAW;AAAA,QAAU;AAAA,QAAc;AAAA,QACrF;AAAA,QAAwB;AAAA,QAAa;AAAA,QAAS;AAAA,QAAa;AAAA,QAAY;AAAA,QAAW;AAAA,QAAa;AAAA,QAC/F;AAAA,QAAiB;AAAA,QAAc;AAAA,QAAgB;AAAA,QAC/C;AAAA,QAAsB;AAAA,QAAsB;AAAA,QAAa;AAAA,QACzD;AAAA,QACA;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAe;AAAA,QAAY;AAAA,QAAY;AAAA,QAAa;AAAA,QACtE;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAoB;AAAA,QAAc;AAAA,QACnD;AAAA,QAAqB;AAAA,QAAgB;AAAA,QAAW;AAAA,QAAS;AAAA,QACzD;AAAA,QAAuB;AAAA,QAAe;AAAA,QAAuB;AAAA,QAAM;AAAA,QACnE;AAAA,QAAuB;AAAA,QAAyB;AAAA,QAChD;AAAA,QAAa;AAAA,QAAe;AAAA,QAAc;AAAA,QAAc;AAAA,QACxD;AAAA,QAAe;AAAA,QAAmB;AAAA,QAAkB;AAAA,QAAa;AAAA,QAAsB;AAAA,QAAS;AAAA,QAChG;AAAA,QAAe;AAAA,QAAkB;AAAA,QAAe;AAAA,QAChD;AAAA,QAAe;AAAA,QAAmB;AAAA,QAAe;AAAA,QAAa;AAAA,QAAQ;AAAA,QACtE;AAAA,QAAO;AAAA,QAAY;AAAA,QAAiB;AAAA,QAAY;AAAA,QAAW;AAAA,QAAe;AAAA,QAC1E;AAAA,QAAiB;AAAA,QAAU;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAQ;AAAA,QACvD;AAAA,QAAU;AAAA,QAAe;AAAA,QAAc;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAgB;AAAA,QAAW;AAAA,QAAW;AAAA,QAC9F;AAAA,QAAY;AAAA,MACd,GAAG,gBAAgB,OAAO,cAAc;AAExC,UAAI,WAAW,eAAe,OAAO,WAAW,EAAE,OAAO,cAAc,EAAE,OAAO,mBAAmB,EAChG,OAAO,iBAAiB,EAAE,OAAO,4BAA4B,EAAE,OAAO,cAAc,EACpF,OAAO,cAAc;AACxB,MAAAX,YAAW,eAAe,aAAa,OAAO,QAAQ;AAEtD,eAAS,cAAc,QAAQ,OAAO;AACpC,YAAI,WAAW,OAAO;AACtB,gBAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,cAAI,YAAY,MAAM,KAAK;AACzB,kBAAM,WAAW;AACjB;AAAA,UACF;AACA,qBAAY,MAAM;AAAA,QACpB;AACA,eAAO,CAAC,WAAW,SAAS;AAAA,MAC9B;AAEA,MAAAA,YAAW,WAAW,YAAY;AAAA,QAChC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAY;AAAA,UACV,KAAK,SAAS,QAAQ,OAAO;AAC3B,gBAAI,CAAC,OAAO,IAAI,GAAG,EAAG,QAAO;AAC7B,kBAAM,WAAW;AACjB,mBAAO,cAAc,QAAQ,KAAK;AAAA,UACpC;AAAA,QACF;AAAA,QACA,MAAM;AAAA,MACR,CAAC;AAED,MAAAA,YAAW,WAAW,eAAe;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa;AAAA,QACb,aAAa;AAAA,QACb,YAAY;AAAA,UACV,KAAK,SAAS,QAAQ,OAAO;AAC3B,gBAAI,OAAO,IAAI,GAAG,GAAG;AACnB,qBAAO,UAAU;AACjB,qBAAO,CAAC,WAAW,SAAS;AAAA,YAC9B,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,oBAAM,WAAW;AACjB,qBAAO,cAAc,QAAQ,KAAK;AAAA,YACpC,OAAO;AACL,qBAAO,CAAC,YAAY,UAAU;AAAA,YAChC;AAAA,UACF;AAAA,UACA,KAAK,SAAS,QAAQ;AACpB,gBAAI,OAAO,MAAM,UAAU,KAAK;AAC9B,qBAAO,CAAC,MAAM,IAAI;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,KAAK,SAAS,QAAQ;AACpB,mBAAO,MAAM,SAAS;AACtB,gBAAI,OAAO,MAAM,SAAS,KAAK;AAC7B,qBAAO,CAAC,cAAc,qBAAqB;AAC7C,mBAAO,CAAC,cAAc,UAAU;AAAA,UAClC;AAAA,UACA,KAAK,SAAS,QAAQ;AACpB,gBAAI,CAAC,OAAO,IAAI,GAAG,EAAG,QAAO;AAC7B,mBAAO,CAAC,MAAM,eAAe;AAAA,UAC/B;AAAA,QACF;AAAA,QACA,MAAM;AAAA,QACN,YAAY;AAAA,MACd,CAAC;AAED,MAAAA,YAAW,WAAW,eAAe;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa;AAAA,QACb,aAAa;AAAA,QACb,YAAY;AAAA,UACV,KAAK,SAAS,QAAQ,OAAO;AAC3B,gBAAI,OAAO,IAAI,GAAG,GAAG;AACnB,qBAAO,UAAU;AACjB,qBAAO,CAAC,WAAW,SAAS;AAAA,YAC9B,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,oBAAM,WAAW;AACjB,qBAAO,cAAc,QAAQ,KAAK;AAAA,YACpC,OAAO;AACL,qBAAO,CAAC,YAAY,UAAU;AAAA,YAChC;AAAA,UACF;AAAA,UACA,KAAK,SAAS,QAAQ;AACpB,gBAAI,OAAO,IAAI,GAAG,EAAG,QAAO,CAAC,MAAM,eAAe;AAClD,gBAAI,OAAO,MAAM,yGAAyG,KAAK,EAAG,QAAO;AACzI,mBAAO,SAAS,UAAU;AAC1B,gBAAI,OAAO,MAAM,SAAS,KAAK;AAC7B,qBAAO,CAAC,cAAc,qBAAqB;AAC7C,mBAAO,CAAC,cAAc,UAAU;AAAA,UAClC;AAAA,UACA,KAAK,WAAW;AACd,mBAAO,CAAC,QAAQ,MAAM;AAAA,UACxB;AAAA,QACF;AAAA,QACA,MAAM;AAAA,QACN,YAAY;AAAA,MACd,CAAC;AAED,MAAAA,YAAW,WAAW,cAAc;AAAA,QAClC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,qBAAqB;AAAA,QACrB,YAAY;AAAA,UACV,KAAK,SAAS,QAAQ,OAAO;AAC3B,gBAAI,CAAC,OAAO,IAAI,GAAG,EAAG,QAAO;AAC7B,kBAAM,WAAW;AACjB,mBAAO,cAAc,QAAQ,KAAK;AAAA,UACpC;AAAA,QACF;AAAA,QACA,MAAM;AAAA,QACN,YAAY;AAAA,MACd,CAAC;AAAA,IAEH,CAAC;AAAA;AAAA;", "names": ["CodeMirror", "documentTypes", "mediaTypes", "mediaFeatures", "mediaValueKeywords", "propertyKeywords", "nonStandardPropertyKeywords", "fontProperties", "counterDescriptors", "colorKeywords", "valueKeywords", "type"]}