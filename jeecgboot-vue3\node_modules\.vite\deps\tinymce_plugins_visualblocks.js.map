{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/visualblocks/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/visualblocks/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const fireVisualBlocks = (editor, state) => {\n      editor.dispatch('VisualBlocks', { state });\n    };\n\n    const toggleVisualBlocks = (editor, pluginUrl, enabledState) => {\n      const dom = editor.dom;\n      dom.toggleClass(editor.getBody(), 'mce-visualblocks');\n      enabledState.set(!enabledState.get());\n      fireVisualBlocks(editor, enabledState.get());\n    };\n\n    const register$2 = (editor, pluginUrl, enabledState) => {\n      editor.addCommand('mceVisualBlocks', () => {\n        toggleVisualBlocks(editor, pluginUrl, enabledState);\n      });\n    };\n\n    const option = name => editor => editor.options.get(name);\n    const register$1 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('visualblocks_default_state', {\n        processor: 'boolean',\n        default: false\n      });\n    };\n    const isEnabledByDefault = option('visualblocks_default_state');\n\n    const setup = (editor, pluginUrl, enabledState) => {\n      editor.on('PreviewFormats AfterPreviewFormats', e => {\n        if (enabledState.get()) {\n          editor.dom.toggleClass(editor.getBody(), 'mce-visualblocks', e.type === 'afterpreviewformats');\n        }\n      });\n      editor.on('init', () => {\n        if (isEnabledByDefault(editor)) {\n          toggleVisualBlocks(editor, pluginUrl, enabledState);\n        }\n      });\n    };\n\n    const toggleActiveState = (editor, enabledState) => api => {\n      api.setActive(enabledState.get());\n      const editorEventCallback = e => api.setActive(e.state);\n      editor.on('VisualBlocks', editorEventCallback);\n      return () => editor.off('VisualBlocks', editorEventCallback);\n    };\n    const register = (editor, enabledState) => {\n      const onAction = () => editor.execCommand('mceVisualBlocks');\n      editor.ui.registry.addToggleButton('visualblocks', {\n        icon: 'visualblocks',\n        tooltip: 'Show blocks',\n        onAction,\n        onSetup: toggleActiveState(editor, enabledState)\n      });\n      editor.ui.registry.addToggleMenuItem('visualblocks', {\n        text: 'Show blocks',\n        icon: 'visualblocks',\n        onAction,\n        onSetup: toggleActiveState(editor, enabledState)\n      });\n    };\n\n    var Plugin = () => {\n      global.add('visualblocks', (editor, pluginUrl) => {\n        register$1(editor);\n        const enabledState = Cell(false);\n        register$2(editor, pluginUrl, enabledState);\n        register(editor, enabledState);\n        setup(editor, pluginUrl, enabledState);\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"visualblocks\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/visualblocks')\n//   ES2015:\n//     import 'tinymce/plugins/visualblocks'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,YAAM,OAAO,aAAW;AACtB,YAAI,QAAQ;AACZ,cAAM,MAAM,MAAM;AAChB,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,OAAK;AACf,kBAAQ;AAAA,QACV;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAE/D,YAAM,mBAAmB,CAAC,QAAQ,UAAU;AAC1C,eAAO,SAAS,gBAAgB,EAAE,MAAM,CAAC;AAAA,MAC3C;AAEA,YAAM,qBAAqB,CAAC,QAAQ,WAAW,iBAAiB;AAC9D,cAAM,MAAM,OAAO;AACnB,YAAI,YAAY,OAAO,QAAQ,GAAG,kBAAkB;AACpD,qBAAa,IAAI,CAAC,aAAa,IAAI,CAAC;AACpC,yBAAiB,QAAQ,aAAa,IAAI,CAAC;AAAA,MAC7C;AAEA,YAAM,aAAa,CAAC,QAAQ,WAAW,iBAAiB;AACtD,eAAO,WAAW,mBAAmB,MAAM;AACzC,6BAAmB,QAAQ,WAAW,YAAY;AAAA,QACpD,CAAC;AAAA,MACH;AAEA,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,aAAa,YAAU;AAC3B,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,8BAA8B;AAAA,UAC3C,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,qBAAqB,OAAO,4BAA4B;AAE9D,YAAM,QAAQ,CAAC,QAAQ,WAAW,iBAAiB;AACjD,eAAO,GAAG,sCAAsC,OAAK;AACnD,cAAI,aAAa,IAAI,GAAG;AACtB,mBAAO,IAAI,YAAY,OAAO,QAAQ,GAAG,oBAAoB,EAAE,SAAS,qBAAqB;AAAA,UAC/F;AAAA,QACF,CAAC;AACD,eAAO,GAAG,QAAQ,MAAM;AACtB,cAAI,mBAAmB,MAAM,GAAG;AAC9B,+BAAmB,QAAQ,WAAW,YAAY;AAAA,UACpD;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,oBAAoB,CAAC,QAAQ,iBAAiB,SAAO;AACzD,YAAI,UAAU,aAAa,IAAI,CAAC;AAChC,cAAM,sBAAsB,OAAK,IAAI,UAAU,EAAE,KAAK;AACtD,eAAO,GAAG,gBAAgB,mBAAmB;AAC7C,eAAO,MAAM,OAAO,IAAI,gBAAgB,mBAAmB;AAAA,MAC7D;AACA,YAAM,WAAW,CAAC,QAAQ,iBAAiB;AACzC,cAAM,WAAW,MAAM,OAAO,YAAY,iBAAiB;AAC3D,eAAO,GAAG,SAAS,gBAAgB,gBAAgB;AAAA,UACjD,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA,UACA,SAAS,kBAAkB,QAAQ,YAAY;AAAA,QACjD,CAAC;AACD,eAAO,GAAG,SAAS,kBAAkB,gBAAgB;AAAA,UACnD,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,SAAS,kBAAkB,QAAQ,YAAY;AAAA,QACjD,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,eAAO,IAAI,gBAAgB,CAAC,QAAQ,cAAc;AAChD,qBAAW,MAAM;AACjB,gBAAM,eAAe,KAAK,KAAK;AAC/B,qBAAW,QAAQ,WAAW,YAAY;AAC1C,mBAAS,QAAQ,YAAY;AAC7B,gBAAM,QAAQ,WAAW,YAAY;AAAA,QACvC,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;AC3FH;", "names": []}