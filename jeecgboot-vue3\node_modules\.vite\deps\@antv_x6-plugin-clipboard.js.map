{"version": 3, "sources": ["../../.pnpm/@antv+x6-plugin-clipboard@2.1.6_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-clipboard/src/clipboard.ts", "../../.pnpm/@antv+x6-plugin-clipboard@2.1.6_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-clipboard/src/api.ts", "../../.pnpm/@antv+x6-plugin-clipboard@2.1.6_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-clipboard/src/index.ts"], "sourcesContent": ["import { Config, Graph, Cell, Node, Edge, Model, ArrayExt } from '@antv/x6'\n\nexport class ClipboardImpl {\n  protected options: ClipboardImpl.Options\n  public cells: Cell[] = []\n\n  copy(\n    cells: Cell[],\n    graph: Graph | Model,\n    options: ClipboardImpl.CopyOptions = {},\n  ) {\n    this.options = { ...options }\n    const model = Model.isModel(graph) ? graph : graph.model\n    const cloned = model.cloneSubGraph(cells, options)\n\n    // sort asc by cell type\n    this.cells = ArrayExt.sortBy(\n      Object.keys(cloned).map((key) => cloned[key]),\n      (cell) => (cell.isEdge() ? 2 : 1),\n    )\n\n    this.serialize(options)\n  }\n\n  cut(\n    cells: Cell[],\n    graph: Graph | Model,\n    options: ClipboardImpl.CopyOptions = {},\n  ) {\n    this.copy(cells, graph, options)\n    const model = Graph.isGraph(graph) ? graph.model : graph\n    model.batchUpdate('cut', () => {\n      cells.forEach((cell) => cell.remove())\n    })\n  }\n\n  paste(graph: Graph | Model, options: ClipboardImpl.PasteOptions = {}) {\n    const localOptions = { ...this.options, ...options }\n    const { offset, edgeProps, nodeProps } = localOptions\n\n    let dx = 20\n    let dy = 20\n    if (offset) {\n      dx = typeof offset === 'number' ? offset : offset.dx\n      dy = typeof offset === 'number' ? offset : offset.dy\n    }\n\n    this.deserialize(localOptions)\n    const cells = this.cells\n\n    cells.forEach((cell) => {\n      cell.model = null\n      cell.removeProp('zIndex')\n      if (dx || dy) {\n        cell.translate(dx, dy)\n      }\n\n      if (nodeProps && cell.isNode()) {\n        cell.prop(nodeProps)\n      }\n\n      if (edgeProps && cell.isEdge()) {\n        cell.prop(edgeProps)\n      }\n    })\n\n    const model = Graph.isGraph(graph) ? graph.model : graph\n    model.batchUpdate('paste', () => {\n      model.addCells(this.cells)\n    })\n\n    this.copy(cells, graph, options)\n\n    return cells\n  }\n\n  serialize(options: ClipboardImpl.PasteOptions) {\n    if (options.useLocalStorage !== false) {\n      Storage.save(this.cells)\n    }\n  }\n\n  deserialize(options: ClipboardImpl.PasteOptions) {\n    if (options.useLocalStorage) {\n      const cells = Storage.fetch()\n      if (cells) {\n        this.cells = cells\n      }\n    }\n  }\n\n  isEmpty(options: ClipboardImpl.Options = {}) {\n    if (options.useLocalStorage) {\n      // With useLocalStorage turned on, no real cells can be obtained without deserialize first\n      // https://github.com/antvis/X6/issues/2573\n      this.deserialize(options)\n    }\n    return this.cells.length <= 0\n  }\n\n  clean() {\n    this.options = {}\n    this.cells = []\n    Storage.clean()\n  }\n}\n\nexport namespace ClipboardImpl {\n  export interface Options {\n    useLocalStorage?: boolean\n  }\n\n  export interface CopyOptions extends Options {\n    deep?: boolean\n  }\n\n  export interface PasteOptions extends Options {\n    /**\n     * Set of properties to be set on each copied node on every `paste()` call.\n     * It is defined as an object. e.g. `{ zIndex: 1 }`.\n     */\n    nodeProps?: Node.Properties\n    /**\n     * Set of properties to be set on each copied edge on every `paste()` call.\n     * It is defined as an object. e.g. `{ zIndex: 1 }`.\n     */\n    edgeProps?: Edge.Properties\n\n    /**\n     * An increment that is added to the pasted cells position on every\n     * `paste()` call. It can be either a number or an object with `dx`\n     * and `dy` attributes. It defaults to `{ dx: 20, dy: 20 }`.\n     */\n    offset?: number | { dx: number; dy: number }\n  }\n}\n\nnamespace Storage {\n  const LOCAL_STORAGE_KEY = `${Config.prefixCls}.clipboard.cells`\n\n  export function save(cells: Cell[]) {\n    if (window.localStorage) {\n      const data = cells.map((cell) => cell.toJSON())\n      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(data))\n    }\n  }\n\n  export function fetch() {\n    if (window.localStorage) {\n      const raw = localStorage.getItem(LOCAL_STORAGE_KEY)\n      const cells = raw ? JSON.parse(raw) : []\n      if (cells) {\n        return Model.fromJSON(cells)\n      }\n    }\n  }\n\n  export function clean() {\n    if (window.localStorage) {\n      localStorage.removeItem(LOCAL_STORAGE_KEY)\n    }\n  }\n}\n", "import { Graph, Cell } from '@antv/x6'\nimport { Clipboard } from './index'\n\ndeclare module '@antv/x6/lib/graph/graph' {\n  interface Graph {\n    isClipboardEnabled: () => boolean\n    enableClipboard: () => Graph\n    disableClipboard: () => Graph\n    toggleClipboard: (enabled?: boolean) => Graph\n    isClipboardEmpty: (options?: Clipboard.Options) => boolean\n    getCellsInClipboard: () => Cell[]\n    cleanClipboard: () => Graph\n    copy: (cells: Cell[], options?: Clipboard.CopyOptions) => Graph\n    cut: (cells: Cell[], options?: Clipboard.CopyOptions) => Graph\n    paste: (options?: Clipboard.PasteOptions, graph?: Graph) => Cell[]\n  }\n}\n\ndeclare module '@antv/x6/lib/graph/events' {\n  interface EventArgs {\n    'clipboard:changed': { cells: Cell[] }\n  }\n}\n\nGraph.prototype.isClipboardEnabled = function () {\n  const clipboard = this.getPlugin('clipboard') as Clipboard\n  if (clipboard) {\n    return clipboard.isEnabled()\n  }\n  return false\n}\n\nGraph.prototype.enableClipboard = function () {\n  const clipboard = this.getPlugin('clipboard') as Clipboard\n  if (clipboard) {\n    clipboard.enable()\n  }\n  return this\n}\n\nGraph.prototype.disableClipboard = function () {\n  const clipboard = this.getPlugin('clipboard') as Clipboard\n  if (clipboard) {\n    clipboard.disable()\n  }\n  return this\n}\n\nGraph.prototype.toggleClipboard = function (enabled?: boolean) {\n  const clipboard = this.getPlugin('clipboard') as Clipboard\n  if (clipboard) {\n    clipboard.toggleEnabled(enabled)\n  }\n\n  return this\n}\n\nGraph.prototype.isClipboardEmpty = function (options?: Clipboard.Options) {\n  const clipboard = this.getPlugin('clipboard') as Clipboard\n  if (clipboard) {\n    return clipboard.isEmpty(options)\n  }\n  return true\n}\n\nGraph.prototype.getCellsInClipboard = function () {\n  const clipboard = this.getPlugin('clipboard') as Clipboard\n  if (clipboard) {\n    return clipboard.getCellsInClipboard()\n  }\n  return []\n}\n\nGraph.prototype.cleanClipboard = function () {\n  const clipboard = this.getPlugin('clipboard') as Clipboard\n  if (clipboard) {\n    clipboard.clean()\n  }\n  return this\n}\n\nGraph.prototype.copy = function (\n  cells: Cell[],\n  options?: Clipboard.CopyOptions,\n) {\n  const clipboard = this.getPlugin('clipboard') as Clipboard\n  if (clipboard) {\n    clipboard.copy(cells, options)\n  }\n  return this\n}\n\nGraph.prototype.cut = function (\n  cells: Cell[],\n  options?: Clipboard.CopyOptions,\n) {\n  const clipboard = this.getPlugin('clipboard') as Clipboard\n  if (clipboard) {\n    clipboard.cut(cells, options)\n  }\n  return this\n}\n\nGraph.prototype.paste = function (\n  options?: Clipboard.PasteOptions,\n  graph?: Graph,\n) {\n  const clipboard = this.getPlugin('clipboard') as Clipboard\n  if (clipboard) {\n    return clipboard.paste(options, graph)\n  }\n  return []\n}\n", "import { Cell, Graph, Basecoat } from '@antv/x6'\nimport { ClipboardImpl } from './clipboard'\nimport './api'\n\nexport class Clipboard\n  extends Basecoat<Clipboard.EventArgs>\n  implements Graph.Plugin\n{\n  public name = 'clipboard'\n  private clipboardImpl: ClipboardImpl\n  private graph: Graph\n  public options: Clipboard.Options\n\n  get disabled() {\n    return this.options.enabled !== true\n  }\n\n  get cells() {\n    return this.clipboardImpl.cells\n  }\n\n  constructor(options: Clipboard.Options = {}) {\n    super()\n    this.options = { enabled: true, ...options }\n  }\n\n  init(graph: Graph) {\n    this.graph = graph\n    this.clipboardImpl = new ClipboardImpl()\n    this.clipboardImpl.deserialize(this.options)\n  }\n\n  // #region api\n\n  isEnabled() {\n    return !this.disabled\n  }\n\n  enable() {\n    if (this.disabled) {\n      this.options.enabled = true\n    }\n  }\n\n  disable() {\n    if (!this.disabled) {\n      this.options.enabled = false\n    }\n  }\n\n  toggleEnabled(enabled?: boolean) {\n    if (enabled != null) {\n      if (enabled !== this.isEnabled()) {\n        if (enabled) {\n          this.enable()\n        } else {\n          this.disable()\n        }\n      }\n    } else if (this.isEnabled()) {\n      this.disable()\n    } else {\n      this.enable()\n    }\n\n    return this\n  }\n\n  isEmpty(options: Clipboard.Options = {}) {\n    return this.clipboardImpl.isEmpty(options)\n  }\n\n  getCellsInClipboard() {\n    return this.cells\n  }\n\n  clean(force?: boolean) {\n    if (!this.disabled || force) {\n      this.clipboardImpl.clean()\n      this.notify('clipboard:changed', { cells: [] })\n    }\n    return this\n  }\n\n  copy(cells: Cell[], options: Clipboard.CopyOptions = {}) {\n    if (!this.disabled) {\n      this.clipboardImpl.copy(cells, this.graph, {\n        ...this.commonOptions,\n        ...options,\n      })\n      this.notify('clipboard:changed', { cells })\n    }\n    return this\n  }\n\n  cut(cells: Cell[], options: Clipboard.CopyOptions = {}) {\n    if (!this.disabled) {\n      this.clipboardImpl.cut(cells, this.graph, {\n        ...this.commonOptions,\n        ...options,\n      })\n      this.notify('clipboard:changed', { cells })\n    }\n    return this\n  }\n\n  paste(options: Clipboard.PasteOptions = {}, graph: Graph = this.graph) {\n    if (!this.disabled) {\n      return this.clipboardImpl.paste(graph, {\n        ...this.commonOptions,\n        ...options,\n      })\n    }\n    return []\n  }\n\n  // #endregion\n\n  protected get commonOptions() {\n    const { enabled, ...others } = this.options\n    return others\n  }\n\n  protected notify<K extends keyof Clipboard.EventArgs>(\n    name: K,\n    args: Clipboard.EventArgs[K],\n  ) {\n    this.trigger(name, args)\n    this.graph.trigger(name, args)\n  }\n\n  @Basecoat.dispose()\n  dispose() {\n    this.clean(true)\n    this.off()\n  }\n}\n\nexport namespace Clipboard {\n  export interface EventArgs {\n    'clipboard:changed': {\n      cells: Cell[]\n    }\n  }\n\n  export interface Options extends ClipboardImpl.Options {\n    enabled?: boolean\n  }\n\n  export interface CopyOptions extends ClipboardImpl.CopyOptions {}\n  export interface PasteOptions extends ClipboardImpl.PasteOptions {}\n}\n"], "mappings": ";;;;;;;;;;;;AAEM,IAAO,gBAAP,MAAoB;EAA1B,cAAA;AAES,SAAA,QAAgB,CAAA;EAqGzB;EAnGE,KACE,OACA,OACA,UAAqC,CAAA,GAAE;AAEvC,SAAK,UAAO,OAAA,OAAA,CAAA,GAAQ,OAAO;AAC3B,UAAM,QAAQ,MAAM,QAAQ,KAAK,IAAI,QAAQ,MAAM;AACnD,UAAM,SAAS,MAAM,cAAc,OAAO,OAAO;AAGjD,SAAK,QAAQ,cAAS,OACpB,OAAO,KAAK,MAAM,EAAE,IAAI,CAAC,QAAQ,OAAO,GAAG,CAAC,GAC5C,CAAC,SAAU,KAAK,OAAM,IAAK,IAAI,CAAE;AAGnC,SAAK,UAAU,OAAO;EACxB;EAEA,IACE,OACA,OACA,UAAqC,CAAA,GAAE;AAEvC,SAAK,KAAK,OAAO,OAAO,OAAO;AAC/B,UAAM,QAAQ,MAAM,QAAQ,KAAK,IAAI,MAAM,QAAQ;AACnD,UAAM,YAAY,OAAO,MAAK;AAC5B,YAAM,QAAQ,CAAC,SAAS,KAAK,OAAM,CAAE;IACvC,CAAC;EACH;EAEA,MAAM,OAAsB,UAAsC,CAAA,GAAE;AAClE,UAAM,eAAY,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,KAAK,OAAO,GAAK,OAAO;AAClD,UAAM,EAAE,QAAQ,WAAW,UAAS,IAAK;AAEzC,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,QAAQ;AACV,WAAK,OAAO,WAAW,WAAW,SAAS,OAAO;AAClD,WAAK,OAAO,WAAW,WAAW,SAAS,OAAO;;AAGpD,SAAK,YAAY,YAAY;AAC7B,UAAM,QAAQ,KAAK;AAEnB,UAAM,QAAQ,CAAC,SAAQ;AACrB,WAAK,QAAQ;AACb,WAAK,WAAW,QAAQ;AACxB,UAAI,MAAM,IAAI;AACZ,aAAK,UAAU,IAAI,EAAE;;AAGvB,UAAI,aAAa,KAAK,OAAM,GAAI;AAC9B,aAAK,KAAK,SAAS;;AAGrB,UAAI,aAAa,KAAK,OAAM,GAAI;AAC9B,aAAK,KAAK,SAAS;;IAEvB,CAAC;AAED,UAAM,QAAQ,MAAM,QAAQ,KAAK,IAAI,MAAM,QAAQ;AACnD,UAAM,YAAY,SAAS,MAAK;AAC9B,YAAM,SAAS,KAAK,KAAK;IAC3B,CAAC;AAED,SAAK,KAAK,OAAO,OAAO,OAAO;AAE/B,WAAO;EACT;EAEA,UAAU,SAAmC;AAC3C,QAAI,QAAQ,oBAAoB,OAAO;AACrC,cAAQ,KAAK,KAAK,KAAK;;EAE3B;EAEA,YAAY,SAAmC;AAC7C,QAAI,QAAQ,iBAAiB;AAC3B,YAAM,QAAQ,QAAQ,MAAK;AAC3B,UAAI,OAAO;AACT,aAAK,QAAQ;;;EAGnB;EAEA,QAAQ,UAAiC,CAAA,GAAE;AACzC,QAAI,QAAQ,iBAAiB;AAG3B,WAAK,YAAY,OAAO;;AAE1B,WAAO,KAAK,MAAM,UAAU;EAC9B;EAEA,QAAK;AACH,SAAK,UAAU,CAAA;AACf,SAAK,QAAQ,CAAA;AACb,YAAQ,MAAK;EACf;;AAiCF,IAAU;CAAV,SAAUA,UAAO;AACf,QAAM,oBAAoB,GAAG,OAAO,SAAS;AAE7C,WAAgB,KAAK,OAAa;AAChC,QAAI,OAAO,cAAc;AACvB,YAAM,OAAO,MAAM,IAAI,CAAC,SAAS,KAAK,OAAM,CAAE;AAC9C,mBAAa,QAAQ,mBAAmB,KAAK,UAAU,IAAI,CAAC;;EAEhE;AALgB,EAAAA,SAAA,OAAI;AAOpB,WAAgB,QAAK;AACnB,QAAI,OAAO,cAAc;AACvB,YAAM,MAAM,aAAa,QAAQ,iBAAiB;AAClD,YAAM,QAAQ,MAAM,KAAK,MAAM,GAAG,IAAI,CAAA;AACtC,UAAI,OAAO;AACT,eAAO,MAAM,SAAS,KAAK;;;EAGjC;AARgB,EAAAA,SAAA,QAAK;AAUrB,WAAgB,QAAK;AACnB,QAAI,OAAO,cAAc;AACvB,mBAAa,WAAW,iBAAiB;;EAE7C;AAJgB,EAAAA,SAAA,QAAK;AAKvB,GAzBU,YAAA,UAAO,CAAA,EAAA;;;ACjHjB,MAAM,UAAU,qBAAqB,WAAA;AACnC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,UAAS;;AAE5B,SAAO;AACT;AAEA,MAAM,UAAU,kBAAkB,WAAA;AAChC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,OAAM;;AAElB,SAAO;AACT;AAEA,MAAM,UAAU,mBAAmB,WAAA;AACjC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,QAAO;;AAEnB,SAAO;AACT;AAEA,MAAM,UAAU,kBAAkB,SAAU,SAAiB;AAC3D,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,cAAc,OAAO;;AAGjC,SAAO;AACT;AAEA,MAAM,UAAU,mBAAmB,SAAU,SAA2B;AACtE,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,QAAQ,OAAO;;AAElC,SAAO;AACT;AAEA,MAAM,UAAU,sBAAsB,WAAA;AACpC,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,oBAAmB;;AAEtC,SAAO,CAAA;AACT;AAEA,MAAM,UAAU,iBAAiB,WAAA;AAC/B,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,MAAK;;AAEjB,SAAO;AACT;AAEA,MAAM,UAAU,OAAO,SACrB,OACA,SAA+B;AAE/B,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,KAAK,OAAO,OAAO;;AAE/B,SAAO;AACT;AAEA,MAAM,UAAU,MAAM,SACpB,OACA,SAA+B;AAE/B,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,cAAU,IAAI,OAAO,OAAO;;AAE9B,SAAO;AACT;AAEA,MAAM,UAAU,QAAQ,SACtB,SACA,OAAa;AAEb,QAAM,YAAY,KAAK,UAAU,WAAW;AAC5C,MAAI,WAAW;AACb,WAAO,UAAU,MAAM,SAAS,KAAK;;AAEvC,SAAO,CAAA;AACT;;;;;;;;;;;;;;;;;;;;AC5GM,IAAO,YAAP,cACI,SAA6B;EAQrC,IAAI,WAAQ;AACV,WAAO,KAAK,QAAQ,YAAY;EAClC;EAEA,IAAI,QAAK;AACP,WAAO,KAAK,cAAc;EAC5B;EAEA,YAAY,UAA6B,CAAA,GAAE;AACzC,UAAK;AAdA,SAAA,OAAO;AAeZ,SAAK,UAAO,OAAA,OAAA,EAAK,SAAS,KAAI,GAAK,OAAO;EAC5C;EAEA,KAAK,OAAY;AACf,SAAK,QAAQ;AACb,SAAK,gBAAgB,IAAI,cAAa;AACtC,SAAK,cAAc,YAAY,KAAK,OAAO;EAC7C;;EAIA,YAAS;AACP,WAAO,CAAC,KAAK;EACf;EAEA,SAAM;AACJ,QAAI,KAAK,UAAU;AACjB,WAAK,QAAQ,UAAU;;EAE3B;EAEA,UAAO;AACL,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,QAAQ,UAAU;;EAE3B;EAEA,cAAc,SAAiB;AAC7B,QAAI,WAAW,MAAM;AACnB,UAAI,YAAY,KAAK,UAAS,GAAI;AAChC,YAAI,SAAS;AACX,eAAK,OAAM;eACN;AACL,eAAK,QAAO;;;eAGP,KAAK,UAAS,GAAI;AAC3B,WAAK,QAAO;WACP;AACL,WAAK,OAAM;;AAGb,WAAO;EACT;EAEA,QAAQ,UAA6B,CAAA,GAAE;AACrC,WAAO,KAAK,cAAc,QAAQ,OAAO;EAC3C;EAEA,sBAAmB;AACjB,WAAO,KAAK;EACd;EAEA,MAAM,OAAe;AACnB,QAAI,CAAC,KAAK,YAAY,OAAO;AAC3B,WAAK,cAAc,MAAK;AACxB,WAAK,OAAO,qBAAqB,EAAE,OAAO,CAAA,EAAE,CAAE;;AAEhD,WAAO;EACT;EAEA,KAAK,OAAe,UAAiC,CAAA,GAAE;AACrD,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,cAAc,KAAK,OAAO,KAAK,OAAK,OAAA,OAAA,OAAA,OAAA,CAAA,GACpC,KAAK,aAAa,GAClB,OAAO,CAAA;AAEZ,WAAK,OAAO,qBAAqB,EAAE,MAAK,CAAE;;AAE5C,WAAO;EACT;EAEA,IAAI,OAAe,UAAiC,CAAA,GAAE;AACpD,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,cAAc,IAAI,OAAO,KAAK,OAAK,OAAA,OAAA,OAAA,OAAA,CAAA,GACnC,KAAK,aAAa,GAClB,OAAO,CAAA;AAEZ,WAAK,OAAO,qBAAqB,EAAE,MAAK,CAAE;;AAE5C,WAAO;EACT;EAEA,MAAM,UAAkC,CAAA,GAAI,QAAe,KAAK,OAAK;AACnE,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO,KAAK,cAAc,MAAM,OAAK,OAAA,OAAA,OAAA,OAAA,CAAA,GAChC,KAAK,aAAa,GAClB,OAAO,CAAA;;AAGd,WAAO,CAAA;EACT;;EAIA,IAAc,gBAAa;AACzB,UAAM,KAAyB,KAAK,SAA9B,EAAE,QAAO,IAAA,IAAK,SAAM,OAAA,IAApB,CAAA,SAAA,CAAsB;AAC5B,WAAO;EACT;EAEU,OACR,MACA,MAA4B;AAE5B,SAAK,QAAQ,MAAM,IAAI;AACvB,SAAK,MAAM,QAAQ,MAAM,IAAI;EAC/B;EAGA,UAAO;AACL,SAAK,MAAM,IAAI;AACf,SAAK,IAAG;EACV;;AAHA,WAAA;EADC,SAAS,QAAO;;", "names": ["Storage"]}