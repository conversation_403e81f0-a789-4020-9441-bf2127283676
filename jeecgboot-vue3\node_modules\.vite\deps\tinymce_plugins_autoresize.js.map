{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/autoresize/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/autoresize/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.Env');\n\n    const fireResizeEditor = editor => editor.dispatch('ResizeEditor');\n\n    const option = name => editor => editor.options.get(name);\n    const register$1 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('autoresize_overflow_padding', {\n        processor: 'number',\n        default: 1\n      });\n      registerOption('autoresize_bottom_margin', {\n        processor: 'number',\n        default: 50\n      });\n    };\n    const getMinHeight = option('min_height');\n    const getMaxHeight = option('max_height');\n    const getAutoResizeOverflowPadding = option('autoresize_overflow_padding');\n    const getAutoResizeBottomMargin = option('autoresize_bottom_margin');\n\n    const isFullscreen = editor => editor.plugins.fullscreen && editor.plugins.fullscreen.isFullscreen();\n    const toggleScrolling = (editor, state) => {\n      const body = editor.getBody();\n      if (body) {\n        body.style.overflowY = state ? '' : 'hidden';\n        if (!state) {\n          body.scrollTop = 0;\n        }\n      }\n    };\n    const parseCssValueToInt = (dom, elm, name, computed) => {\n      var _a;\n      const value = parseInt((_a = dom.getStyle(elm, name, computed)) !== null && _a !== void 0 ? _a : '', 10);\n      return isNaN(value) ? 0 : value;\n    };\n    const shouldScrollIntoView = trigger => {\n      if ((trigger === null || trigger === void 0 ? void 0 : trigger.type.toLowerCase()) === 'setcontent') {\n        const setContentEvent = trigger;\n        return setContentEvent.selection === true || setContentEvent.paste === true;\n      } else {\n        return false;\n      }\n    };\n    const resize = (editor, oldSize, trigger, getExtraMarginBottom) => {\n      var _a;\n      const dom = editor.dom;\n      const doc = editor.getDoc();\n      if (!doc) {\n        return;\n      }\n      if (isFullscreen(editor)) {\n        toggleScrolling(editor, true);\n        return;\n      }\n      const docEle = doc.documentElement;\n      const resizeBottomMargin = getExtraMarginBottom ? getExtraMarginBottom() : getAutoResizeOverflowPadding(editor);\n      const minHeight = (_a = getMinHeight(editor)) !== null && _a !== void 0 ? _a : editor.getElement().offsetHeight;\n      let resizeHeight = minHeight;\n      const marginTop = parseCssValueToInt(dom, docEle, 'margin-top', true);\n      const marginBottom = parseCssValueToInt(dom, docEle, 'margin-bottom', true);\n      let contentHeight = docEle.offsetHeight + marginTop + marginBottom + resizeBottomMargin;\n      if (contentHeight < 0) {\n        contentHeight = 0;\n      }\n      const containerHeight = editor.getContainer().offsetHeight;\n      const contentAreaHeight = editor.getContentAreaContainer().offsetHeight;\n      const chromeHeight = containerHeight - contentAreaHeight;\n      if (contentHeight + chromeHeight > minHeight) {\n        resizeHeight = contentHeight + chromeHeight;\n      }\n      const maxHeight = getMaxHeight(editor);\n      if (maxHeight && resizeHeight > maxHeight) {\n        resizeHeight = maxHeight;\n        toggleScrolling(editor, true);\n      } else {\n        toggleScrolling(editor, false);\n      }\n      if (resizeHeight !== oldSize.get()) {\n        const deltaSize = resizeHeight - oldSize.get();\n        dom.setStyle(editor.getContainer(), 'height', resizeHeight + 'px');\n        oldSize.set(resizeHeight);\n        fireResizeEditor(editor);\n        if (global.browser.isSafari() && (global.os.isMacOS() || global.os.isiOS())) {\n          const win = editor.getWin();\n          win.scrollTo(win.pageXOffset, win.pageYOffset);\n        }\n        if (editor.hasFocus() && shouldScrollIntoView(trigger)) {\n          editor.selection.scrollIntoView();\n        }\n        if ((global.browser.isSafari() || global.browser.isChromium()) && deltaSize < 0) {\n          resize(editor, oldSize, trigger, getExtraMarginBottom);\n        }\n      }\n    };\n    const setup = (editor, oldSize) => {\n      let getExtraMarginBottom = () => getAutoResizeBottomMargin(editor);\n      let resizeCounter;\n      let sizeAfterFirstResize;\n      editor.on('init', e => {\n        resizeCounter = 0;\n        const overflowPadding = getAutoResizeOverflowPadding(editor);\n        const dom = editor.dom;\n        dom.setStyles(editor.getDoc().documentElement, { height: 'auto' });\n        if (global.browser.isEdge() || global.browser.isIE()) {\n          dom.setStyles(editor.getBody(), {\n            'paddingLeft': overflowPadding,\n            'paddingRight': overflowPadding,\n            'min-height': 0\n          });\n        } else {\n          dom.setStyles(editor.getBody(), {\n            paddingLeft: overflowPadding,\n            paddingRight: overflowPadding\n          });\n        }\n        resize(editor, oldSize, e, getExtraMarginBottom);\n        resizeCounter += 1;\n      });\n      editor.on('NodeChange SetContent keyup FullscreenStateChanged ResizeContent', e => {\n        if (resizeCounter === 1) {\n          sizeAfterFirstResize = editor.getContainer().offsetHeight;\n          resize(editor, oldSize, e, getExtraMarginBottom);\n          resizeCounter += 1;\n        } else if (resizeCounter === 2) {\n          const isLooping = sizeAfterFirstResize < editor.getContainer().offsetHeight;\n          if (isLooping) {\n            const dom = editor.dom;\n            const doc = editor.getDoc();\n            dom.setStyles(doc.documentElement, { 'min-height': 0 });\n            dom.setStyles(editor.getBody(), { 'min-height': 'inherit' });\n          }\n          getExtraMarginBottom = isLooping ? constant(0) : getExtraMarginBottom;\n          resizeCounter += 1;\n        } else {\n          resize(editor, oldSize, e, getExtraMarginBottom);\n        }\n      });\n    };\n\n    const register = (editor, oldSize) => {\n      editor.addCommand('mceAutoResize', () => {\n        resize(editor, oldSize);\n      });\n    };\n\n    var Plugin = () => {\n      global$1.add('autoresize', editor => {\n        register$1(editor);\n        if (!editor.options.isSet('resize')) {\n          editor.options.set('resize', false);\n        }\n        if (!editor.inline) {\n          const oldSize = Cell(0);\n          register(editor, oldSize);\n          setup(editor, oldSize);\n        }\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"autoresize\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/autoresize')\n//   ES2015:\n//     import 'tinymce/plugins/autoresize'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,YAAM,OAAO,aAAW;AACtB,YAAI,QAAQ;AACZ,cAAM,MAAM,MAAM;AAChB,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,OAAK;AACf,kBAAQ;AAAA,QACV;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,WAAW,WAAS;AACxB,eAAO,MAAM;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,aAAa;AAErD,YAAM,mBAAmB,YAAU,OAAO,SAAS,cAAc;AAEjE,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,aAAa,YAAU;AAC3B,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,+BAA+B;AAAA,UAC5C,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,4BAA4B;AAAA,UACzC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,eAAe,OAAO,YAAY;AACxC,YAAM,eAAe,OAAO,YAAY;AACxC,YAAM,+BAA+B,OAAO,6BAA6B;AACzE,YAAM,4BAA4B,OAAO,0BAA0B;AAEnE,YAAM,eAAe,YAAU,OAAO,QAAQ,cAAc,OAAO,QAAQ,WAAW,aAAa;AACnG,YAAM,kBAAkB,CAAC,QAAQ,UAAU;AACzC,cAAM,OAAO,OAAO,QAAQ;AAC5B,YAAI,MAAM;AACR,eAAK,MAAM,YAAY,QAAQ,KAAK;AACpC,cAAI,CAAC,OAAO;AACV,iBAAK,YAAY;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AACA,YAAM,qBAAqB,CAAC,KAAK,KAAK,MAAM,aAAa;AACvD,YAAI;AACJ,cAAM,QAAQ,UAAU,KAAK,IAAI,SAAS,KAAK,MAAM,QAAQ,OAAO,QAAQ,OAAO,SAAS,KAAK,IAAI,EAAE;AACvG,eAAO,MAAM,KAAK,IAAI,IAAI;AAAA,MAC5B;AACA,YAAM,uBAAuB,aAAW;AACtC,aAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK,YAAY,OAAO,cAAc;AACnG,gBAAM,kBAAkB;AACxB,iBAAO,gBAAgB,cAAc,QAAQ,gBAAgB,UAAU;AAAA,QACzE,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,SAAS,CAAC,QAAQ,SAAS,SAAS,yBAAyB;AACjE,YAAI;AACJ,cAAM,MAAM,OAAO;AACnB,cAAM,MAAM,OAAO,OAAO;AAC1B,YAAI,CAAC,KAAK;AACR;AAAA,QACF;AACA,YAAI,aAAa,MAAM,GAAG;AACxB,0BAAgB,QAAQ,IAAI;AAC5B;AAAA,QACF;AACA,cAAM,SAAS,IAAI;AACnB,cAAM,qBAAqB,uBAAuB,qBAAqB,IAAI,6BAA6B,MAAM;AAC9G,cAAM,aAAa,KAAK,aAAa,MAAM,OAAO,QAAQ,OAAO,SAAS,KAAK,OAAO,WAAW,EAAE;AACnG,YAAI,eAAe;AACnB,cAAM,YAAY,mBAAmB,KAAK,QAAQ,cAAc,IAAI;AACpE,cAAM,eAAe,mBAAmB,KAAK,QAAQ,iBAAiB,IAAI;AAC1E,YAAI,gBAAgB,OAAO,eAAe,YAAY,eAAe;AACrE,YAAI,gBAAgB,GAAG;AACrB,0BAAgB;AAAA,QAClB;AACA,cAAM,kBAAkB,OAAO,aAAa,EAAE;AAC9C,cAAM,oBAAoB,OAAO,wBAAwB,EAAE;AAC3D,cAAM,eAAe,kBAAkB;AACvC,YAAI,gBAAgB,eAAe,WAAW;AAC5C,yBAAe,gBAAgB;AAAA,QACjC;AACA,cAAM,YAAY,aAAa,MAAM;AACrC,YAAI,aAAa,eAAe,WAAW;AACzC,yBAAe;AACf,0BAAgB,QAAQ,IAAI;AAAA,QAC9B,OAAO;AACL,0BAAgB,QAAQ,KAAK;AAAA,QAC/B;AACA,YAAI,iBAAiB,QAAQ,IAAI,GAAG;AAClC,gBAAM,YAAY,eAAe,QAAQ,IAAI;AAC7C,cAAI,SAAS,OAAO,aAAa,GAAG,UAAU,eAAe,IAAI;AACjE,kBAAQ,IAAI,YAAY;AACxB,2BAAiB,MAAM;AACvB,cAAI,OAAO,QAAQ,SAAS,MAAM,OAAO,GAAG,QAAQ,KAAK,OAAO,GAAG,MAAM,IAAI;AAC3E,kBAAM,MAAM,OAAO,OAAO;AAC1B,gBAAI,SAAS,IAAI,aAAa,IAAI,WAAW;AAAA,UAC/C;AACA,cAAI,OAAO,SAAS,KAAK,qBAAqB,OAAO,GAAG;AACtD,mBAAO,UAAU,eAAe;AAAA,UAClC;AACA,eAAK,OAAO,QAAQ,SAAS,KAAK,OAAO,QAAQ,WAAW,MAAM,YAAY,GAAG;AAC/E,mBAAO,QAAQ,SAAS,SAAS,oBAAoB;AAAA,UACvD;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,QAAQ,YAAY;AACjC,YAAI,uBAAuB,MAAM,0BAA0B,MAAM;AACjE,YAAI;AACJ,YAAI;AACJ,eAAO,GAAG,QAAQ,OAAK;AACrB,0BAAgB;AAChB,gBAAM,kBAAkB,6BAA6B,MAAM;AAC3D,gBAAM,MAAM,OAAO;AACnB,cAAI,UAAU,OAAO,OAAO,EAAE,iBAAiB,EAAE,QAAQ,OAAO,CAAC;AACjE,cAAI,OAAO,QAAQ,OAAO,KAAK,OAAO,QAAQ,KAAK,GAAG;AACpD,gBAAI,UAAU,OAAO,QAAQ,GAAG;AAAA,cAC9B,eAAe;AAAA,cACf,gBAAgB;AAAA,cAChB,cAAc;AAAA,YAChB,CAAC;AAAA,UACH,OAAO;AACL,gBAAI,UAAU,OAAO,QAAQ,GAAG;AAAA,cAC9B,aAAa;AAAA,cACb,cAAc;AAAA,YAChB,CAAC;AAAA,UACH;AACA,iBAAO,QAAQ,SAAS,GAAG,oBAAoB;AAC/C,2BAAiB;AAAA,QACnB,CAAC;AACD,eAAO,GAAG,oEAAoE,OAAK;AACjF,cAAI,kBAAkB,GAAG;AACvB,mCAAuB,OAAO,aAAa,EAAE;AAC7C,mBAAO,QAAQ,SAAS,GAAG,oBAAoB;AAC/C,6BAAiB;AAAA,UACnB,WAAW,kBAAkB,GAAG;AAC9B,kBAAM,YAAY,uBAAuB,OAAO,aAAa,EAAE;AAC/D,gBAAI,WAAW;AACb,oBAAM,MAAM,OAAO;AACnB,oBAAM,MAAM,OAAO,OAAO;AAC1B,kBAAI,UAAU,IAAI,iBAAiB,EAAE,cAAc,EAAE,CAAC;AACtD,kBAAI,UAAU,OAAO,QAAQ,GAAG,EAAE,cAAc,UAAU,CAAC;AAAA,YAC7D;AACA,mCAAuB,YAAY,SAAS,CAAC,IAAI;AACjD,6BAAiB;AAAA,UACnB,OAAO;AACL,mBAAO,QAAQ,SAAS,GAAG,oBAAoB;AAAA,UACjD;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,CAAC,QAAQ,YAAY;AACpC,eAAO,WAAW,iBAAiB,MAAM;AACvC,iBAAO,QAAQ,OAAO;AAAA,QACxB,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,cAAc,YAAU;AACnC,qBAAW,MAAM;AACjB,cAAI,CAAC,OAAO,QAAQ,MAAM,QAAQ,GAAG;AACnC,mBAAO,QAAQ,IAAI,UAAU,KAAK;AAAA,UACpC;AACA,cAAI,CAAC,OAAO,QAAQ;AAClB,kBAAM,UAAU,KAAK,CAAC;AACtB,qBAAS,QAAQ,OAAO;AACxB,kBAAM,QAAQ,OAAO;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;ACzLH;", "names": []}